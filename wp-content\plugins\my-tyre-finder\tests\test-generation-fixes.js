/**
 * Test Script for Generation Display Fixes
 * Tests both bugs related to generation display:
 * 1. Generation name vs ID in search results
 * 2. Single generation auto-fill not showing modification instead of generation name
 */

console.log('=== Generation Display Fixes Test ===');

// Test configuration
const GENERATION_TEST_CONFIG = {
    testGenerationNameDisplay: true,
    testSingleGenerationAutoFill: true,
    testGenerationCache: true,
    verbose: true
};

// Helper function for logging
function logGeneration(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [GEN-${type.toUpperCase()}]`;
    
    switch(type) {
        case 'error':
            console.error(`${prefix} ${message}`);
            break;
        case 'warn':
            console.warn(`${prefix} ${message}`);
            break;
        case 'success':
            console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');
            break;
        default:
            console.log(`${prefix} ${message}`);
    }
}

// Test 1: Generation name resolution
function testGenerationNameResolution() {
    logGeneration('Testing generation name resolution...', 'info');
    
    const tests = [];
    
    // Check if getGenerationName function exists
    tests.push({
        name: 'getGenerationName function exists',
        test: () => {
            if (window.wheelFitWidget && typeof window.wheelFitWidget.getGenerationName === 'function') {
                return true;
            }
            return false;
        },
        expected: true
    });
    
    // Test generation name resolution with mock data
    tests.push({
        name: 'Generation name resolution works',
        test: () => {
            if (!window.wheelFitWidget || typeof window.wheelFitWidget.getGenerationName !== 'function') {
                return false; // Skip if function doesn't exist
            }
            
            // Mock generation data in cache
            const mockGenerations = [
                { slug: 'gb', name: 'GB', title: null },
                { slug: '8x', name: '8X', title: null },
                { slug: '8x-facelift', name: '8X Facelift', title: null }
            ];
            
            // Mock cache
            if (window.wheelFitWidget.cache && typeof window.wheelFitWidget.cache.set === 'function') {
                window.wheelFitWidget.cache.set('generations_audi_a1', mockGenerations);
                
                // Set mock selected data
                window.wheelFitWidget.selectedData = {
                    make: 'audi',
                    model: 'a1'
                };
                
                // Test resolution
                const result = window.wheelFitWidget.getGenerationName('gb');
                return result === 'GB';
            }
            
            return false;
        },
        expected: true
    });
    
    // Test fallback behavior
    tests.push({
        name: 'Generation name fallback works',
        test: () => {
            if (!window.wheelFitWidget || typeof window.wheelFitWidget.getGenerationName !== 'function') {
                return false;
            }
            
            // Test with non-existent generation
            const result = window.wheelFitWidget.getGenerationName('non-existent-gen');
            return result === 'non-existent-gen'; // Should fallback to slug
        },
        expected: true
    });
    
    return runGenerationTests('Generation Name Resolution', tests);
}

// Test 2: Vehicle label generation
function testVehicleLabelGeneration() {
    logGeneration('Testing vehicle label generation...', 'info');
    
    const tests = [];
    
    // Check if getVehicleLabel function exists
    tests.push({
        name: 'getVehicleLabel function exists',
        test: () => {
            if (window.wheelFitWidget && typeof window.wheelFitWidget.getVehicleLabel === 'function') {
                return true;
            }
            return false;
        },
        expected: true
    });
    
    // Test generation-based label
    tests.push({
        name: 'Generation-based vehicle label works',
        test: () => {
            if (!window.wheelFitWidget || typeof window.wheelFitWidget.getVehicleLabel !== 'function') {
                return false;
            }
            
            // Mock widget state for generation flow
            window.wheelFitWidget.mode = 'byGeneration';
            window.wheelFitWidget.selectedData = {
                make: 'audi',
                model: 'a1',
                generation: 'gb'
            };
            
            // Mock generation cache
            const mockGenerations = [{ slug: 'gb', name: 'GB' }];
            if (window.wheelFitWidget.cache && typeof window.wheelFitWidget.cache.set === 'function') {
                window.wheelFitWidget.cache.set('generations_audi_a1', mockGenerations);
            }
            
            const label = window.wheelFitWidget.getVehicleLabel();
            logGeneration(`Generated label: "${label}"`, 'info');
            
            // Should contain generation name, not ID
            return label.includes('GB') && !label.includes('gb');
        },
        expected: true
    });
    
    return runGenerationTests('Vehicle Label Generation', tests);
}

// Test 3: Single generation auto-fill
function testSingleGenerationAutoFill() {
    logGeneration('Testing single generation auto-fill...', 'info');
    
    const tests = [];
    
    // Check if populateGenerations function exists
    tests.push({
        name: 'populateGenerations function exists',
        test: () => {
            if (window.wheelFitWidget && typeof window.wheelFitWidget.populateGenerations === 'function') {
                return true;
            }
            return false;
        },
        expected: true
    });
    
    // Test single generation auto-fill
    tests.push({
        name: 'Single generation auto-fill preserves generation name',
        test: () => {
            if (!window.wheelFitWidget || typeof window.wheelFitWidget.populateGenerations !== 'function') {
                return false;
            }
            
            // Create mock generation selector
            const mockSelect = document.createElement('select');
            mockSelect.id = 'wf-generation';
            document.body.appendChild(mockSelect);
            
            try {
                // Mock single generation data
                const singleGeneration = [{ slug: 'gb', name: 'GB' }];
                
                // Mock onGenerationSelect to prevent actual AJAX calls
                const originalOnGenerationSelect = window.wheelFitWidget.onGenerationSelect;
                window.wheelFitWidget.onGenerationSelect = function(value) {
                    logGeneration(`onGenerationSelect called with: ${value}`, 'info');
                    return Promise.resolve();
                };
                
                // Call populateGenerations
                window.wheelFitWidget.populateGenerations(singleGeneration);
                
                // Check if generation is auto-selected
                const isAutoSelected = mockSelect.value === 'gb';
                const selectedText = mockSelect.options[mockSelect.selectedIndex]?.text;
                
                logGeneration(`Auto-fill result: value="${mockSelect.value}", text="${selectedText}"`, 'info');
                
                // Restore original function
                window.wheelFitWidget.onGenerationSelect = originalOnGenerationSelect;
                
                return isAutoSelected && selectedText === 'GB';
            } finally {
                // Cleanup
                document.body.removeChild(mockSelect);
            }
        },
        expected: true
    });
    
    return runGenerationTests('Single Generation Auto-Fill', tests);
}

// Test 4: DOM element state preservation
function testDOMStatePreservation() {
    logGeneration('Testing DOM element state preservation...', 'info');
    
    const tests = [];
    
    // Test that generation selector is not affected by modification population
    tests.push({
        name: 'Generation selector preserved during modification population',
        test: () => {
            if (!window.wheelFitWidget || typeof window.wheelFitWidget.populateModifications !== 'function') {
                return false;
            }
            
            // Create mock selectors
            const genSelect = document.createElement('select');
            genSelect.id = 'wf-generation';
            genSelect.innerHTML = '<option value="">Choose generation</option><option value="gb">GB</option>';
            genSelect.value = 'gb';
            
            const modSelect = document.createElement('select');
            modSelect.id = 'wf-modification';
            
            document.body.appendChild(genSelect);
            document.body.appendChild(modSelect);
            
            try {
                // Set widget state
                window.wheelFitWidget.selectedData = { generation: 'gb' };
                
                // Mock single modification
                const singleModification = [{ slug: 'test-mod', name: 'Test Modification' }];
                
                // Mock onModificationSelect to prevent side effects
                const originalOnModificationSelect = window.wheelFitWidget.onModificationSelect;
                window.wheelFitWidget.onModificationSelect = function(value) {
                    logGeneration(`onModificationSelect called with: ${value}`, 'info');
                };
                
                // Call populateModifications
                window.wheelFitWidget.populateModifications(singleModification);
                
                // Check if generation selector is still correct
                const genValuePreserved = genSelect.value === 'gb';
                const genTextPreserved = genSelect.options[genSelect.selectedIndex]?.text === 'GB';
                
                logGeneration(`Generation preservation: value="${genSelect.value}", text="${genSelect.options[genSelect.selectedIndex]?.text}"`, 'info');
                
                // Restore original function
                window.wheelFitWidget.onModificationSelect = originalOnModificationSelect;
                
                return genValuePreserved && genTextPreserved;
            } finally {
                // Cleanup
                document.body.removeChild(genSelect);
                document.body.removeChild(modSelect);
            }
        },
        expected: true
    });
    
    return runGenerationTests('DOM State Preservation', tests);
}

// Helper function to run tests
function runGenerationTests(suiteName, tests) {
    logGeneration(`Running ${suiteName} tests...`, 'info');
    
    let passed = 0;
    let failed = 0;
    
    tests.forEach(test => {
        try {
            const result = test.test();
            if (result === test.expected) {
                logGeneration(`✓ ${test.name}`, 'success');
                passed++;
            } else {
                logGeneration(`✗ ${test.name} (expected: ${test.expected}, got: ${result})`, 'error');
                failed++;
            }
        } catch (error) {
            logGeneration(`✗ ${test.name} (error: ${error.message})`, 'error');
            failed++;
        }
    });
    
    const total = passed + failed;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    logGeneration(`${suiteName} Results: ${passed}/${total} passed (${percentage}%)`, 
        percentage === 100 ? 'success' : 'warn');
    
    return { passed, failed, total, percentage };
}

// Main test runner
function runAllGenerationTests() {
    logGeneration('Starting generation fixes test suite...', 'info');
    
    const results = [];
    
    if (GENERATION_TEST_CONFIG.testGenerationNameDisplay) {
        results.push(testGenerationNameResolution());
        results.push(testVehicleLabelGeneration());
    }
    
    if (GENERATION_TEST_CONFIG.testSingleGenerationAutoFill) {
        results.push(testSingleGenerationAutoFill());
    }
    
    results.push(testDOMStatePreservation());
    
    // Summary
    const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);
    const totalTests = results.reduce((sum, r) => sum + r.total, 0);
    const overallPercentage = totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0;
    
    logGeneration('=== GENERATION TEST SUMMARY ===', 'info');
    logGeneration(`Overall Results: ${totalPassed}/${totalTests} passed (${overallPercentage}%)`, 
        overallPercentage >= 80 ? 'success' : 'warn');
    
    if (overallPercentage >= 80) {
        logGeneration('Generation fixes appear to be working correctly!', 'success');
    } else {
        logGeneration('Some issues detected with generation fixes. Please review the failed tests above.', 'warn');
    }
    
    return results;
}

// Debug information
function showGenerationDebugInfo() {
    logGeneration('=== GENERATION DEBUG INFORMATION ===', 'info');
    
    logGeneration('Document ready state: ' + document.readyState, 'info');
    
    // Check widget availability
    logGeneration(`Widget available: ${!!window.wheelFitWidget}`, 'info');
    
    if (window.wheelFitWidget) {
        logGeneration(`Widget mode: ${window.wheelFitWidget.mode || 'unknown'}`, 'info');
        logGeneration(`Selected data: ${JSON.stringify(window.wheelFitWidget.selectedData || {})}`, 'info');
        
        // Check function availability
        const functions = ['getGenerationName', 'getVehicleLabel', 'populateGenerations', 'populateModifications'];
        functions.forEach(funcName => {
            const available = typeof window.wheelFitWidget[funcName] === 'function';
            logGeneration(`Function ${funcName}: ${available ? 'available' : 'missing'}`, available ? 'info' : 'warn');
        });
        
        // Check cache
        if (window.wheelFitWidget.cache) {
            logGeneration('Cache system available', 'info');
        } else {
            logGeneration('Cache system missing', 'warn');
        }
    }
    
    // Check DOM elements
    const genSelect = document.getElementById('wf-generation');
    const modSelect = document.getElementById('wf-modification');
    
    logGeneration(`Generation selector: ${genSelect ? 'found' : 'missing'}`, 'info');
    logGeneration(`Modification selector: ${modSelect ? 'found' : 'missing'}`, 'info');
    
    if (genSelect) {
        logGeneration(`Generation selector value: "${genSelect.value}"`, 'info');
        logGeneration(`Generation selector text: "${genSelect.options[genSelect.selectedIndex]?.text || 'none'}"`, 'info');
        logGeneration(`Generation options count: ${genSelect.options.length}`, 'info');
    }
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            showGenerationDebugInfo();
            runAllGenerationTests();
        }, 1000);
    });
} else {
    setTimeout(() => {
        showGenerationDebugInfo();
        runAllGenerationTests();
    }, 1000);
}

// Export functions for manual testing
window.testGenerationFixes = {
    runAll: runAllGenerationTests,
    nameResolution: testGenerationNameResolution,
    labelGeneration: testVehicleLabelGeneration,
    autoFill: testSingleGenerationAutoFill,
    domPreservation: testDOMStatePreservation,
    debug: showGenerationDebugInfo
};

logGeneration('Generation test script loaded. Use window.testGenerationFixes.runAll() to run tests manually.', 'info');
