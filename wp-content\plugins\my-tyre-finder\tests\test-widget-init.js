// Test script to verify widget initialization fixes
console.log('=== Testing Widget DOM Protection Fixes ===');

// Test 1: Check document ready state
console.log('1. Document ready state:', document.readyState);

// Test 2: Check if widget exists
console.log('2. Widget availability:');
console.log('   window.wheelFitWidget:', !!window.wheelFitWidget);
console.log('   window.wheelFitWidgetReady:', !!window.wheelFitWidgetReady);

if (window.wheelFitWidget) {
    console.log('   Widget details:', {
        mode: window.wheelFitWidget.mode,
        flowOrder: window.wheelFitWidget.flowOrder,
        loadFromHistory: typeof window.wheelFitWidget.loadFromHistory,
        storage: !!window.wheelFitWidget.storage
    });
} else {
    console.log('   ❌ Widget not found - this should not happen after the fix');
}

// Test 3: Check LocalStorageHandler availability
console.log('3. LocalStorageHandler:', typeof LocalStorageHandler);

// Test 4: Test storage access patterns
console.log('4. Testing storage access patterns:');

if (typeof LocalStorageHandler !== 'undefined') {
    // Pattern 1: Direct LocalStorageHandler
    try {
        const storage1 = new LocalStorageHandler();
        console.log('   ✅ Direct LocalStorageHandler works');
    } catch (error) {
        console.log('   ❌ Direct LocalStorageHandler failed:', error);
    }
    
    // Pattern 2: Widget storage (if available)
    if (window.wheelFitWidget?.storage) {
        console.log('   ✅ Widget storage available');
    } else {
        console.log('   ⚠️ Widget storage not available');
    }
    
    // Pattern 3: Fallback pattern (what we use in performGarageLoad)
    try {
        const storage3 = window.wheelFitWidget?.storage || new LocalStorageHandler();
        console.log('   ✅ Fallback storage pattern works');
        
        // Test garage operations
        const garage = storage3.getGarage();
        console.log('   Garage items count:', garage.length);
    } catch (error) {
        console.log('   ❌ Fallback storage pattern failed:', error);
    }
}

// Test 5: Test widget readiness detection
console.log('5. Testing widget readiness detection:');

if (typeof isWidgetReady === 'function') {
    const ready = isWidgetReady();
    console.log('   isWidgetReady():', ready);
    
    if (ready) {
        console.log('   ✅ Widget is ready for operations');
    } else {
        console.log('   ❌ Widget not ready - investigating...');
        
        // Check individual components
        console.log('   Debug info:', {
            hasWidget: !!window.wheelFitWidget,
            hasLoadMethod: !!(window.wheelFitWidget && typeof window.wheelFitWidget.loadFromHistory === 'function'),
            hasReadyFlag: !!window.wheelFitWidgetReady,
            documentReady: document.readyState
        });
    }
} else {
    console.log('   ❌ isWidgetReady function not available');
}

// Test 6: Simulate garage load operation
console.log('6. Testing garage load simulation:');

if (typeof performGarageLoad === 'function' && window.wheelFitWidget) {
    console.log('   performGarageLoad function available');
    
    // Create mock garage item
    const mockItem = {
        id: 'test-init-fix',
        make: 'bmw',
        model: 'x5',
        year: '2020',
        modification: 'xdrive30d',
        tire_full: '275/45R20',
        garage_version: '2.0'
    };
    
    console.log('   Testing with mock item...');
    
    try {
        // This should not throw an error now
        performGarageLoad(mockItem)
            .then(() => {
                console.log('   ✅ Mock garage load completed successfully');
            })
            .catch(error => {
                console.log('   ❌ Mock garage load failed:', error);
            });
    } catch (error) {
        console.log('   ❌ Error calling performGarageLoad:', error);
    }
} else {
    console.log('   ❌ performGarageLoad not available or widget missing');
}

// Test 7: Check initialization timing
console.log('7. Initialization timing test:');

// Check if we can detect when the widget was initialized
if (window.wheelFitWidget) {
    console.log('   Widget exists immediately - good!');
    console.log('   This means the IIFE initialization worked correctly');
} else {
    console.log('   Widget not found immediately');
    console.log('   Waiting for initialization...');
    
    let attempts = 0;
    const checkInterval = setInterval(() => {
        attempts++;
        if (window.wheelFitWidget) {
            console.log(`   ✅ Widget appeared after ${attempts * 100}ms`);
            clearInterval(checkInterval);
        } else if (attempts >= 50) {
            console.log('   ❌ Widget never appeared after 5 seconds');
            clearInterval(checkInterval);
        }
    }, 100);
}

// Test 8: Check DOM element protection
console.log('8. Testing DOM element protection:');

const domElements = [
    'history-list',
    'history-empty',
    'make-loader',
    'model-loader',
    'year-loader',
    'generation-loader',
    'modification-loader',
    'search-loader',
    'search-text',
    'no-results'
];

console.log('   DOM elements availability:');
domElements.forEach(id => {
    const element = document.getElementById(id);
    console.log(`   ${id}: ${element ? '✅ found' : '❌ missing (protected)'}`);
});

console.log('\n=== Test Summary ===');
console.log('Key fixes implemented:');
console.log('1. ✅ IIFE initialization pattern to handle late script loading');
console.log('2. ✅ Storage access fix in performGarageLoad');
console.log('3. ✅ Increased timeout from 5s to 8s');
console.log('4. ✅ DOM element protection in loadSavedSearches');
console.log('5. ✅ DOM element protection in showLoader/hideLoader');
console.log('6. ✅ DOM element protection in showSearchLoader/hideSearchLoader');
console.log('7. ✅ DOM element protection in displayResults');
console.log('');
console.log('Expected results:');
console.log('- window.wheelFitWidget should exist immediately');
console.log('- isWidgetReady() should return true');
console.log('- Garage Load button should work without "Widget failed to initialize" error');

// Auto-run widget check after a delay
setTimeout(() => {
    console.log('\n=== Final Widget Check (after 1 second) ===');
    console.log('Widget exists:', !!window.wheelFitWidget);
    if (typeof isWidgetReady === 'function') {
        console.log('Widget ready:', isWidgetReady());
    }
}, 1000);
