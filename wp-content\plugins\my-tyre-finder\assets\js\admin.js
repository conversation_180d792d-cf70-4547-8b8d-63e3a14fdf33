(function($){
    $(document).ready(function(){
        const $color    = $('#primary_color');
        const $font     = $('#font_family');
        const $title    = $('#widget_title');
        const $showLogo = $('#show_logo');
        const $logoUrl  = $('#logo_url');
        const $preview  = $('#widget-preview');

        // If поле font_family отсутствует (значит мы не на старой странице настройки виджета),
        // выходим, чтобы не перетирать содержимое #widget-preview актуальной формы.
        if ($font.length === 0) {
            return;
        }

        function updatePreview(){
            const primary = $color.val();
            const font    = $font.val();
            const title   = $title.val();
            const showLogo = $showLogo.is(':checked');
            const logoUrl = $logoUrl.val();

            let html = `<div style="border:1px solid #e5e7eb;padding:24px;text-align:center;background:#fff;max-width:420px;margin:0 auto;font-family:'${font}',sans-serif;">
                ${showLogo && logoUrl ? `<img src="${logoUrl}" alt="logo" style="height:48px;margin:0 auto 16px;">` : ''}
                <h2 style="font-size:20px;font-weight:600;color:${primary};margin-bottom:20px;">${title}</h2>
                <button style="background:${primary};border:none;color:#fff;padding:10px 20px;font-size:14px;border-radius:6px;cursor:pointer;">Example Button</button>
            </div>`;
            $preview.html(html);
        }

        // Initial render for legacy preview
        updatePreview();

        // Bind events
        $color.on('input change', updatePreview);
        $font.on('change', updatePreview);
        $title.on('input', updatePreview);
        $showLogo.on('change', updatePreview);
        $logoUrl.on('input', updatePreview);

        // Media uploader for logo
        $('#upload_logo_button').on('click', function(e){
            e.preventDefault();
            const frame = wp.media({
                title: 'Выберите логотип',
                button: { text: 'Использовать логотип' },
                multiple: false,
                library: { type: 'image' }
            });
            frame.on('select', function(){
                const attachment = frame.state().get('selection').first().toJSON();
                $logoUrl.val(attachment.url).trigger('input');
            });
            frame.open();
        });
    });
})(jQuery); 