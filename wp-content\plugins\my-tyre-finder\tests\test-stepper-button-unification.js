/**
 * Test script to verify Step-by-Step button size and spacing unification
 * between frontend and Live Preview in admin
 */

console.log('🧪 Testing Step-by-Step button unification...');

function testStepperButtonUnification() {
    console.log('\n📍 Testing Step-by-Step button unification...');
    
    // Find the Step-by-Step form
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm || !stepperForm.classList.contains('space-y-6')) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    console.log('✅ Found Step-by-Step form');
    
    // Find search button
    const searchButton = stepperForm.querySelector('.btn-primary');
    if (!searchButton) {
        console.log('❌ Search button not found');
        return false;
    }
    
    console.log('✅ Found search button');
    
    // Find a dropdown for comparison
    const dropdown = stepperForm.querySelector('select');
    if (!dropdown) {
        console.log('❌ Dropdown not found for comparison');
        return false;
    }
    
    console.log('✅ Found dropdown for comparison');
    
    // Get computed heights
    const buttonStyle = window.getComputedStyle(searchButton);
    const dropdownStyle = window.getComputedStyle(dropdown);
    
    const buttonHeight = parseFloat(buttonStyle.height);
    const dropdownHeight = parseFloat(dropdownStyle.height);
    const expectedHeight = 44; // --ws-control-height
    
    console.log(`   Search button height: ${buttonHeight}px`);
    console.log(`   Dropdown height: ${dropdownHeight}px`);
    console.log(`   Expected height: ${expectedHeight}px`);
    
    // Check if heights match (allow 2px tolerance)
    const heightsMatch = Math.abs(buttonHeight - dropdownHeight) <= 2;
    const correctHeight = Math.abs(buttonHeight - expectedHeight) <= 2;
    
    console.log(`${heightsMatch ? '✅' : '❌'} Button and dropdown heights match (±2px tolerance)`);
    console.log(`${correctHeight ? '✅' : '❌'} Button has correct height (44px ±2px)`);
    
    return heightsMatch && correctHeight;
}

function testStepperButtonPadding() {
    console.log('\n📐 Testing Step-by-Step button padding...');
    
    const searchButton = document.querySelector('#wheel-fit-form .btn-primary');
    if (!searchButton) {
        console.log('❌ Search button not found');
        return false;
    }
    
    const style = window.getComputedStyle(searchButton);
    
    const paddingTop = parseFloat(style.paddingTop);
    const paddingBottom = parseFloat(style.paddingBottom);
    const paddingLeft = parseFloat(style.paddingLeft);
    const paddingRight = parseFloat(style.paddingRight);
    
    console.log(`   Padding top: ${paddingTop}px`);
    console.log(`   Padding bottom: ${paddingBottom}px`);
    console.log(`   Padding left: ${paddingLeft}px`);
    console.log(`   Padding right: ${paddingRight}px`);
    
    // Check vertical padding is minimal (should be 0)
    const verticalPaddingMinimal = paddingTop <= 2 && paddingBottom <= 2;
    
    // Check horizontal padding is correct (should be 40px = 2.5rem)
    const horizontalPaddingCorrect = Math.abs(paddingLeft - 40) <= 2 && Math.abs(paddingRight - 40) <= 2;
    
    console.log(`${verticalPaddingMinimal ? '✅' : '❌'} Vertical padding is minimal (≤2px)`);
    console.log(`${horizontalPaddingCorrect ? '✅' : '❌'} Horizontal padding is correct (40px ±2px)`);
    
    return verticalPaddingMinimal && horizontalPaddingCorrect;
}

function testStepperSpacing() {
    console.log('\n📏 Testing Step-by-Step spacing...');
    
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    // Find the button container with mt-6 class
    const buttonContainer = stepperForm.querySelector('.mt-6');
    if (!buttonContainer) {
        console.log('❌ Button container with mt-6 not found');
        return false;
    }
    
    const style = window.getComputedStyle(buttonContainer);
    const marginTop = parseFloat(style.marginTop);
    
    console.log(`   Button container margin-top: ${marginTop}px`);
    
    // Should be reduced from 24px (mt-6) to 16px (mt-4)
    const expectedMargin = 16;
    const spacingCorrect = Math.abs(marginTop - expectedMargin) <= 4; // Allow 4px tolerance
    
    console.log(`${spacingCorrect ? '✅' : '❌'} Spacing is reduced (16px ±4px)`);
    
    return spacingCorrect;
}

function testStepperGarageSpacing() {
    console.log('\n🚗 Testing Step-by-Step Garage button spacing...');
    
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    const garageButton = stepperForm.querySelector('[data-garage-trigger]');
    if (!garageButton) {
        console.log('ℹ️ Garage button not found (may be disabled)');
        return true; // Not an error if garage is disabled
    }
    
    console.log('✅ Found Garage button');
    
    // Check if garage button is in same container as search button
    const searchContainer = stepperForm.querySelector('.mt-6');
    const garageInSameContainer = searchContainer && searchContainer.contains(garageButton);
    
    console.log(`${garageInSameContainer ? '✅' : '❌'} Garage button is in same container as search button`);
    
    if (garageInSameContainer) {
        // Check spacing between search and garage buttons
        const searchButton = searchContainer.querySelector('.btn-primary');
        const garageContainer = garageButton.parentElement;
        
        if (searchButton && garageContainer) {
            const searchRect = searchButton.getBoundingClientRect();
            const garageRect = garageContainer.getBoundingClientRect();
            
            const spacing = garageRect.top - searchRect.bottom;
            console.log(`   Spacing between search and garage: ${spacing}px`);
            
            // Should be tight spacing (mt-2 = 8px)
            const expectedSpacing = 8;
            const spacingCorrect = Math.abs(spacing - expectedSpacing) <= 4;
            
            console.log(`${spacingCorrect ? '✅' : '❌'} Garage spacing is tight (8px ±4px)`);
            
            return garageInSameContainer && spacingCorrect;
        }
    }
    
    return garageInSameContainer;
}

function runStepperButtonUnificationTests() {
    console.log('🚀 Starting Step-by-Step button unification tests...\n');
    
    const results = {
        buttonUnification: testStepperButtonUnification(),
        buttonPadding: testStepperButtonPadding(),
        spacing: testStepperSpacing(),
        garageSpacing: testStepperGarageSpacing()
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Step-by-Step button is now unified with Live Preview.');
    } else {
        console.log('⚠️ Some tests failed. Check the details above.');
    }
    
    console.log('\n📋 Unification Summary:');
    console.log('• Button height: 44px (matches dropdown fields)');
    console.log('• Vertical padding: removed (0px)');
    console.log('• Horizontal padding: 40px (px-10 equivalent)');
    console.log('• Container spacing: reduced from 24px to 16px');
    console.log('• Garage spacing: tight connection (8px)');
    
    return results;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runStepperButtonUnificationTests);
} else {
    runStepperButtonUnificationTests();
}

// Export for manual testing
window.testStepperButtonUnification = runStepperButtonUnificationTests;
