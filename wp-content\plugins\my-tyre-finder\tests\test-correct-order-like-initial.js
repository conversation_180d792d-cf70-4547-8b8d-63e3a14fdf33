/**
 * Тест правильного порядка как при первой загрузке
 * Проверяет, что AJAX-обновления используют тот же порядок, что и первая загрузка
 */

(function() {
    'use strict';

    console.log('[Order Test] 🔍 Тест правильного порядка как при первой загрузке...');

    // Тестовые переводы
    const testTranslations = {
        ru: {
            'select_make_placeholder': 'Выберите бренд',
            'select_model_placeholder': 'Выберите модель',
            'select_year_placeholder': 'Выберите год',
            'select_mods_placeholder': 'Выберите модификацию',
            'select_gen_placeholder': 'Выберите поколение'
        }
    };

    // Функция проверки состояния селекторов
    function checkSelectorStates() {
        console.log('[Order Test] === Проверка состояния селекторов ===');
        
        const selectors = ['wf-make', 'wf-model', 'wf-year', 'wf-modification', 'wf-generation'];
        const results = {};
        
        selectors.forEach(id => {
            const select = document.getElementById(id);
            if (select) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    const text = placeholderOption.textContent.trim();
                    const dataI18n = placeholderOption.getAttribute('data-i18n');
                    const isRussian = text.includes('Выберите') || text.includes('Загрузка') || text.includes('Сначала');
                    const isEnglish = text.includes('Choose') || text.includes('Select') || text.includes('Loading');
                    
                    results[id] = {
                        text,
                        dataI18n,
                        isRussian,
                        isEnglish,
                        status: isRussian ? '✅ RU' : isEnglish ? '❌ EN' : '⚠️ OTHER'
                    };
                    
                    console.log(`[Order Test] ${id}: ${results[id].status} "${text}" (data-i18n: ${dataI18n})`);
                }
            }
        });
        
        return results;
    }

    // Функция симуляции правильного порядка
    function simulateCorrectOrder() {
        console.log('[Order Test] === Симуляция правильного порядка ===');
        
        // Устанавливаем русские переводы
        window.WheelFitI18n = testTranslations.ru;
        console.log('[Order Test] 1. Установлены переводы');
        
        // Симулируем правильный порядок как при первой загрузке:
        // 1. Сначала применяем переводы к статическим элементам
        console.log('[Order Test] 2. Применяем переводы к статическим элементам...');
        if (typeof applyStaticTranslations === 'function') {
            const container = document.getElementById('widget-preview');
            if (container) {
                applyStaticTranslations(container);
                console.log('[Order Test] ✅ Переводы к статическим элементам применены');
            }
        }
        
        // 2. Потом инициализируем виджет
        console.log('[Order Test] 3. Инициализируем виджет...');
        if (typeof WheelFitWidget !== 'undefined') {
            try {
                // Очищаем существующий виджет
                if (window.wheelFitWidget) {
                    window.wheelFitWidget = null;
                }
                
                // Создаем новый виджет
                window.wheelFitWidget = new WheelFitWidget();
                console.log('[Order Test] ✅ Виджет инициализирован');
            } catch(e) {
                console.error('[Order Test] ❌ Ошибка инициализации виджета:', e);
            }
        }
        
        // 3. Дополнительное применение переводов
        console.log('[Order Test] 4. Дополнительное применение переводов...');
        if (window.translationManager && typeof window.translationManager.updateTranslations === 'function') {
            window.translationManager.updateTranslations(testTranslations.ru, 'ru');
            console.log('[Order Test] ✅ Translation Manager обновлен');
        }
        
        console.log('[Order Test] ✅ Правильный порядок выполнен');
    }

    // Функция мониторинга изменений настроек
    function monitorSettingsChanges() {
        console.log('[Order Test] === Запуск мониторинга изменений настроек ===');
        
        // Слушаем изменения Search Flow и Form Layout
        document.addEventListener('change', function(e) {
            if (e.target && (e.target.id === 'search_flow' || e.target.id === 'form_layout')) {
                console.log(`[Order Test] 🔄 Обнаружено изменение ${e.target.id}: ${e.target.value}`);
                
                // Проверяем состояние до изменения
                console.log('[Order Test] Состояние ДО изменения:');
                const beforeState = checkSelectorStates();
                
                // Проверяем состояние через разные интервалы после изменения
                setTimeout(() => {
                    console.log('[Order Test] Состояние через 1 секунду:');
                    const after1s = checkSelectorStates();
                    
                    // Анализируем изменения
                    const russianCount1s = Object.values(after1s).filter(s => s.isRussian).length;
                    const englishCount1s = Object.values(after1s).filter(s => s.isEnglish).length;
                    
                    if (englishCount1s > 0) {
                        console.error(`[Order Test] ❌ Через 1 секунду: ${englishCount1s} селекторов на английском!`);
                    } else {
                        console.log(`[Order Test] ✅ Через 1 секунду: ${russianCount1s} селекторов на русском`);
                    }
                }, 1000);
                
                setTimeout(() => {
                    console.log('[Order Test] Состояние через 3 секунды:');
                    const after3s = checkSelectorStates();
                    
                    const russianCount3s = Object.values(after3s).filter(s => s.isRussian).length;
                    const englishCount3s = Object.values(after3s).filter(s => s.isEnglish).length;
                    
                    if (englishCount3s > 0) {
                        console.error(`[Order Test] ❌ Через 3 секунды: ${englishCount3s} селекторов на английском!`);
                        console.error('[Order Test] 🚨 ПРОБЛЕМА НЕ РЕШЕНА - селекторы все еще сбрасываются!');
                    } else {
                        console.log(`[Order Test] ✅ Через 3 секунды: ${russianCount3s} селекторов на русском`);
                        console.log('[Order Test] 🎉 ПРОБЛЕМА РЕШЕНА - селекторы остаются переведенными!');
                    }
                }, 3000);
            }
        });
        
        console.log('[Order Test] Мониторинг настроен. Измените Search Flow или Form Layout для тестирования.');
    }

    // Функция сравнения с первой загрузкой
    function compareWithInitialLoad() {
        console.log('[Order Test] === Сравнение с первой загрузкой ===');
        
        // Проверяем, как работает первая загрузка
        console.log('[Order Test] Анализируем логику первой загрузки...');
        
        // Из кода первой загрузки:
        // 1. applyStaticTranslations(previewContainer)
        // 2. new WheelFitWidget()
        
        console.log('[Order Test] Первая загрузка использует порядок:');
        console.log('[Order Test] 1. applyStaticTranslations()');
        console.log('[Order Test] 2. new WheelFitWidget()');
        
        // Проверяем, использует ли AJAX тот же порядок
        console.log('[Order Test] AJAX-обновление теперь использует тот же порядок:');
        console.log('[Order Test] 1. applyStaticTranslations(previewContainer)');
        console.log('[Order Test] 2. new WheelFitWidget()');
        console.log('[Order Test] 3. translationManager.updateTranslations()');
        
        console.log('[Order Test] ✅ Порядок синхронизирован с первой загрузкой');
    }

    // Основная функция тестирования
    function runOrderTest() {
        console.log('[Order Test] 🚀 ЗАПУСК ТЕСТА ПРАВИЛЬНОГО ПОРЯДКА');
        
        // 1. Проверяем текущее состояние
        console.log('[Order Test] 1. Проверка текущего состояния...');
        checkSelectorStates();
        
        // 2. Сравниваем с первой загрузкой
        console.log('[Order Test] 2. Сравнение с первой загрузкой...');
        compareWithInitialLoad();
        
        // 3. Симулируем правильный порядок
        setTimeout(() => {
            console.log('[Order Test] 3. Симуляция правильного порядка...');
            simulateCorrectOrder();
            
            // Проверяем результат
            setTimeout(() => {
                console.log('[Order Test] Результат симуляции:');
                checkSelectorStates();
            }, 1000);
        }, 2000);
        
        // 4. Запускаем мониторинг
        setTimeout(() => {
            console.log('[Order Test] 4. Запуск мониторинга изменений...');
            monitorSettingsChanges();
        }, 4000);
    }

    // Глобальные функции для ручного тестирования
    window.testCorrectOrderLikeInitial = {
        runTest: runOrderTest,
        checkStates: checkSelectorStates,
        simulate: simulateCorrectOrder,
        monitor: monitorSettingsChanges,
        compare: compareWithInitialLoad
    };

    // Автоматический запуск через 2 секунды
    setTimeout(() => {
        console.log('[Order Test] Автоматический запуск теста...');
        runOrderTest();
    }, 2000);

    console.log('[Order Test] Тест загружен. Доступные функции:');
    console.log('- testCorrectOrderLikeInitial.runTest() - полный тест');
    console.log('- testCorrectOrderLikeInitial.checkStates() - проверка состояния');
    console.log('- testCorrectOrderLikeInitial.simulate() - симуляция правильного порядка');

})();
