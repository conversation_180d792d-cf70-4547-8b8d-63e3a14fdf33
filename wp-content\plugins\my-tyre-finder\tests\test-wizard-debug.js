// Debug script for wizard issues
console.log('=== WIZARD DEBUG SCRIPT ===');

// Check if wizard container exists
const wizardContainer = document.getElementById('wheel-fit-wizard');
console.log('Wizard container found:', !!wizardContainer);

if (wizardContainer) {
    console.log('Wizard container classes:', wizardContainer.className);
    
    // Check all required elements
    const requiredElements = [
        'wizard-makes-grid',
        'wizard-models-list', 
        'wizard-years-list',
        'wizard-modifications-list',
        'wizard-results',
        'wizard-back-btn',
        'wizard-next-btn',
        'wizard-progress-bar',
        'wizard-nav',
        'wizard-selection-summary'
    ];
    
    console.log('=== CHECKING REQUIRED ELEMENTS ===');
    requiredElements.forEach(id => {
        const element = document.getElementById(id);
        console.log(`${id}:`, !!element, element ? 'found' : 'MISSING');
    });
    
    // Check wizard steps
    const steps = document.querySelectorAll('.wizard-step');
    console.log('Wizard steps found:', steps.length);
    steps.forEach((step, index) => {
        console.log(`Step ${index + 1}:`, step.id, step.classList.contains('hidden') ? 'hidden' : 'visible');
    });
    
    // Check if WheelWizard class exists
    console.log('WheelWizard class available:', typeof WheelWizard !== 'undefined');
    
    // Check if wizard instance exists
    console.log('wheelFitWizard instance:', !!window.wheelFitWizard);
    
    if (window.wheelFitWizard) {
        console.log('Wizard current step:', window.wheelFitWizard.currentStep);
        console.log('Wizard selection:', window.wheelFitWizard.selection);
    }
    
    // Check AJAX data
    console.log('WheelFitData available:', !!window.WheelFitData);
    if (window.WheelFitData) {
        console.log('AJAX URL:', window.WheelFitData.ajaxurl);
        console.log('Nonce:', window.WheelFitData.nonce ? 'present' : 'missing');
    }
    
} else {
    console.log('❌ Wizard container not found!');
}

// Test make selection click
function testMakeSelection() {
    console.log('=== TESTING MAKE SELECTION ===');
    const makeButtons = document.querySelectorAll('#wizard-makes-grid button');
    console.log('Make buttons found:', makeButtons.length);
    
    if (makeButtons.length > 0) {
        console.log('Clicking first make button...');
        makeButtons[0].click();
    }
}

// Make test function available globally
window.testMakeSelection = testMakeSelection;

console.log('=== DEBUG SCRIPT LOADED ===');
console.log('Run testMakeSelection() to test make selection');
