// Test script to verify the garage load fix
console.log('=== Testing Garage Load Fix ===');

// Test 1: Check if performGarageLoad function exists and accepts correct parameters
if (typeof performGarageLoad === 'function') {
    console.log('✅ performGarageLoad function exists');
    
    // Check function signature
    const funcStr = performGarageLoad.toString();
    const hasDefaultParam = funcStr.includes('itemToLoad = null');
    console.log('✅ Function has default parameter:', hasDefaultParam);
    
    // Test with mock data
    const mockItem = {
        id: 'test-123',
        make: 'bmw',
        model: 'x5',
        year: '2020',
        modification: 'xdrive30d',
        tire_full: '275/45R20',
        garage_version: '2.0'
    };
    
    console.log('🧪 Testing function call with single parameter...');
    
    // Mock the widget for testing
    if (!window.wheelFitWidget) {
        window.wheelFitWidget = {
            mode: 'byVehicle',
            flowOrder: ['make', 'model', 'year', 'modification'],
            loadFromHistory: async (data) => {
                console.log('Mock loadFromHistory called with:', data);
                return Promise.resolve();
            }
        };
        console.log('🔧 Created mock widget for testing');
    }
    
    // Test the function
    try {
        performGarageLoad(mockItem)
            .then(() => {
                console.log('✅ performGarageLoad test completed successfully');
            })
            .catch(error => {
                console.error('❌ performGarageLoad test failed:', error);
            });
    } catch (error) {
        console.error('❌ Error calling performGarageLoad:', error);
    }
    
} else {
    console.error('❌ performGarageLoad function not found');
}

// Test 2: Check if the garage load button handler is properly set up
console.log('\n=== Testing Load Button Handler ===');

const loadButtons = document.querySelectorAll('.garage-load');
console.log('Found load buttons:', loadButtons.length);

if (loadButtons.length > 0) {
    console.log('✅ Load buttons found in DOM');
    
    // Check if event listeners are attached
    const firstButton = loadButtons[0];
    const hasClickHandler = firstButton.onclick !== null || 
                           firstButton.addEventListener !== undefined;
    console.log('✅ Button has event handling capability:', hasClickHandler);
} else {
    console.log('ℹ️ No load buttons found (garage may be empty or not rendered)');
}

// Test 3: Check widget readiness detection
console.log('\n=== Testing Widget Readiness ===');

if (typeof isWidgetReady === 'function') {
    const ready = isWidgetReady();
    console.log('Widget ready status:', ready);
    
    if (ready) {
        console.log('✅ Widget is ready for garage operations');
    } else {
        console.log('⚠️ Widget not ready, testing wait mechanism...');
        
        if (typeof waitForWidget === 'function') {
            waitForWidget(1000)
                .then(() => {
                    console.log('✅ Widget became ready through wait mechanism');
                })
                .catch(error => {
                    console.log('⚠️ Widget wait timeout (expected in test environment)');
                });
        }
    }
} else {
    console.error('❌ isWidgetReady function not found');
}

console.log('\n=== Test Summary ===');
console.log('The main fix was correcting the undefined itemToLoad variable');
console.log('in the garage load button click handler.');
console.log('');
console.log('Before: performGarageLoad(item, itemToLoad) // itemToLoad undefined');
console.log('After:  performGarageLoad(item)             // uses default parameter');
console.log('');
console.log('This should eliminate the "Widget failed to initialize" error');
console.log('that was caused by the undefined variable, not widget readiness.');
