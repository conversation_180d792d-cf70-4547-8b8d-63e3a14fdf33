<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Width Test Preview</title>
    <link rel="stylesheet" href="../assets/css/live-preview-width-fix.css">
    <style>
        /* Simulate WordPress admin environment */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f1f1f1;
        }
        
        /* Simulate admin container */
        .admin-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        /* Basic widget styles */
        .wheel-fit-widget {
            background: #ffffff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        
        /* Simulate form elements */
        select, input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        /* Simulate search results */
        #search-results {
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        /* Tailwind-like classes */
        .max-w-4xl {
            max-width: 56rem; /* 896px */
        }
        
        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }
        
        .w-full {
            width: 100%;
        }
        
        .grid {
            display: grid;
        }
        
        .grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }
        
        .gap-4 {
            gap: 1rem;
        }
        
        .mb-4 {
            margin-bottom: 1rem;
        }
        
        .text-lg {
            font-size: 1.125rem;
        }
        
        .font-semibold {
            font-weight: 600;
        }
        
        /* Measurement helpers */
        .measurement-box {
            border: 2px dashed #007cba;
            background: rgba(0, 124, 186, 0.1);
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
            color: #007cba;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <h1>Widget Width Test Preview</h1>
        <p>This page simulates the WordPress admin Live Preview environment to test widget width matching.</p>
        
        <!-- Measurement reference -->
        <div class="measurement-box">
            Reference: max-w-4xl = 56rem = 896px
        </div>
        
        <!-- Simulated Live Preview container -->
        <div id="widget-preview">
            <h2>Live Preview</h2>
            
            <!-- Widget container -->
            <div class="wheel-fit-widget">
                <h3>Tyre Finder Widget</h3>
                
                <!-- Form elements -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label>Make:</label>
                        <select class="w-full">
                            <option>Select Make</option>
                            <option>BMW</option>
                            <option>Mercedes</option>
                        </select>
                    </div>
                    <div>
                        <label>Model:</label>
                        <select class="w-full">
                            <option>Select Model</option>
                            <option>3 Series</option>
                            <option>5 Series</option>
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label>Year:</label>
                        <select class="w-full">
                            <option>Select Year</option>
                            <option>2020</option>
                            <option>2021</option>
                        </select>
                    </div>
                    <div>
                        <label>Modification:</label>
                        <select class="w-full">
                            <option>Select Modification</option>
                            <option>320i</option>
                            <option>330i</option>
                        </select>
                    </div>
                </div>
                
                <!-- Navigation steps simulation -->
                <div id="wizard-header" class="mb-4">
                    <div class="wizard-step-name">Step Navigation</div>
                </div>
                
                <!-- Makes grid simulation -->
                <div id="wizard-makes-grid" class="grid grid-cols-4 gap-4 mb-4">
                    <div>BMW</div>
                    <div>Mercedes</div>
                    <div>Audi</div>
                    <div>Volkswagen</div>
                </div>
            </div>
            
            <!-- Search Results simulation -->
            <div class="max-w-4xl mx-auto">
                <section id="search-results">
                    <h2 class="text-lg font-semibold mb-4">Search Results</h2>
                    <p>This is the search results block that should have the same width as the widget above.</p>
                    <div class="measurement-box">
                        Search Results Width: max-w-4xl (56rem / 896px)
                    </div>
                </section>
            </div>
        </div>
        
        <!-- Measurement tools -->
        <div style="margin-top: 40px; padding: 20px; background: #f0f0f0; border-radius: 8px;">
            <h3>Measurement Tools</h3>
            <button onclick="measureWidths()">Measure All Widths</button>
            <button onclick="highlightElements()">Highlight Elements</button>
            <div id="measurements" style="margin-top: 10px; font-family: monospace;"></div>
        </div>
    </div>

    <script>
        function measureWidths() {
            const measurements = document.getElementById('measurements');
            const elements = [
                { name: 'Widget Container', selector: '.wheel-fit-widget' },
                { name: 'Search Results', selector: '#search-results' },
                { name: 'Max-w-4xl Container', selector: '.max-w-4xl' },
                { name: 'Widget Preview', selector: '#widget-preview' },
                { name: 'Makes Grid', selector: '#wizard-makes-grid' }
            ];
            
            let html = '<h4>Width Measurements:</h4>';
            
            elements.forEach(element => {
                const el = document.querySelector(element.selector);
                if (el) {
                    const computedStyle = window.getComputedStyle(el);
                    const width = computedStyle.width;
                    const maxWidth = computedStyle.maxWidth;
                    html += `<div><strong>${element.name}:</strong> width: ${width}, max-width: ${maxWidth}</div>`;
                } else {
                    html += `<div><strong>${element.name}:</strong> Not found</div>`;
                }
            });
            
            measurements.innerHTML = html;
        }
        
        function highlightElements() {
            const elements = document.querySelectorAll('.wheel-fit-widget, #search-results, .max-w-4xl');
            elements.forEach((el, index) => {
                const colors = ['rgba(255,0,0,0.3)', 'rgba(0,255,0,0.3)', 'rgba(0,0,255,0.3)'];
                el.style.backgroundColor = colors[index % colors.length];
                el.style.border = '2px solid ' + colors[index % colors.length].replace('0.3', '1');
            });
        }
        
        // Auto-measure on load
        window.addEventListener('load', measureWidths);
    </script>
</body>
</html>
