<?php
/**
 * Theme Database Diagnostic Script
 * Run this via WordPress admin or WP-CLI to check theme data in database
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    die('This script must be run in WordPress context');
}

echo "🔍 === THEME DATABASE DIAGNOSTIC ===\n\n";

// Check if ThemeManager class exists
if (!class_exists('MyTyreFinder\Includes\ThemeManager')) {
    echo "❌ ThemeManager class not found\n";
    echo "Make sure the plugin is activated\n";
    exit;
}

echo "✅ ThemeManager class found\n\n";

// 1. Check raw database options
echo "1️⃣ Checking raw database options...\n";
$themes_option = get_option('wsf_theme_presets', null);
$active_theme_option = get_option('wsf_active_theme', null);

echo "wsf_theme_presets option: ";
if ($themes_option === null) {
    echo "❌ NOT SET\n";
} else {
    echo "✅ EXISTS\n";
    echo "Themes in database: " . implode(', ', array_keys($themes_option)) . "\n";
    
    if (isset($themes_option['dark'])) {
        echo "✅ Dark theme exists in database\n";
        echo "Dark theme data: " . json_encode($themes_option['dark'], JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "❌ Dark theme NOT found in database\n";
    }
}

echo "\nwsf_active_theme option: ";
if ($active_theme_option === null) {
    echo "❌ NOT SET\n";
} else {
    echo "✅ SET to: " . $active_theme_option . "\n";
}

echo "\n";

// 2. Check ThemeManager methods
echo "2️⃣ Testing ThemeManager methods...\n";

try {
    $all_themes = \MyTyreFinder\Includes\ThemeManager::get_themes();
    echo "✅ get_themes() successful\n";
    echo "Available themes: " . implode(', ', array_keys($all_themes)) . "\n";
    
    if (isset($all_themes['dark'])) {
        echo "✅ Dark theme found via ThemeManager\n";
    } else {
        echo "❌ Dark theme NOT found via ThemeManager\n";
    }
} catch (Exception $e) {
    echo "❌ get_themes() failed: " . $e->getMessage() . "\n";
}

try {
    $dark_theme = \MyTyreFinder\Includes\ThemeManager::get_theme('dark');
    if ($dark_theme) {
        echo "✅ get_theme('dark') successful\n";
        echo "Dark theme properties: " . json_encode($dark_theme, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "❌ get_theme('dark') returned null\n";
    }
} catch (Exception $e) {
    echo "❌ get_theme('dark') failed: " . $e->getMessage() . "\n";
}

try {
    $active_theme = \MyTyreFinder\Includes\ThemeManager::get_active_theme();
    echo "✅ get_active_theme() successful: " . $active_theme . "\n";
} catch (Exception $e) {
    echo "❌ get_active_theme() failed: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. Test theme activation
echo "3️⃣ Testing theme activation...\n";

try {
    $result = \MyTyreFinder\Includes\ThemeManager::set_active_theme('dark');
    if ($result) {
        echo "✅ set_active_theme('dark') successful\n";
        
        // Verify it was set
        $new_active = \MyTyreFinder\Includes\ThemeManager::get_active_theme();
        echo "New active theme: " . $new_active . "\n";
        
        if ($new_active === 'dark') {
            echo "✅ Dark theme successfully activated\n";
        } else {
            echo "❌ Dark theme activation failed - active theme is still: " . $new_active . "\n";
        }
    } else {
        echo "❌ set_active_theme('dark') returned false\n";
    }
} catch (Exception $e) {
    echo "❌ set_active_theme('dark') failed: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. Force theme initialization
echo "4️⃣ Force theme initialization...\n";

try {
    // Delete existing themes to force re-initialization
    delete_option('wsf_theme_presets');
    echo "✅ Deleted existing theme presets\n";
    
    // Force re-initialization
    $themes = \MyTyreFinder\Includes\ThemeManager::get_themes();
    echo "✅ Re-initialized themes\n";
    echo "New themes: " . implode(', ', array_keys($themes)) . "\n";
    
    if (isset($themes['dark'])) {
        echo "✅ Dark theme now exists after re-initialization\n";
    } else {
        echo "❌ Dark theme still missing after re-initialization\n";
    }
} catch (Exception $e) {
    echo "❌ Theme re-initialization failed: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. Check WordPress capabilities
echo "5️⃣ Checking WordPress capabilities...\n";

if (current_user_can('manage_options')) {
    echo "✅ Current user can manage_options\n";
} else {
    echo "❌ Current user cannot manage_options\n";
}

// 6. Check for any WordPress errors
echo "6️⃣ Checking for WordPress errors...\n";

if (defined('WP_DEBUG') && WP_DEBUG) {
    echo "✅ WP_DEBUG is enabled\n";
} else {
    echo "⚠️ WP_DEBUG is disabled - enable it to see detailed errors\n";
}

echo "\n=== DIAGNOSTIC COMPLETE ===\n";
?>
