<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Fix Test - TASK-BG-FIX</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            font-size: 20px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-top: 25px;
        }
        
        .comparison-item {
            padding: 20px;
            border: 2px solid #d1d5db;
            border-radius: 10px;
            background: white;
        }
        
        .comparison-item h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: #374151;
        }
        
        .before {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .demo-widget {
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border: 2px solid #e5e7eb;
        }
        
        /* Before: CSS background property (gets overridden by Tailwind) */
        .before-widget {
            background: var(--wsf-bg); /* This gets overridden by Tailwind's background-color: transparent */
            color: var(--wsf-text-primary);
        }
        
        /* After: Tailwind utility class (works correctly) */
        .after-widget {
            /* Uses bg-wsf-bg utility class which generates background-color: var(--wsf-bg) */
            color: var(--wsf-text-primary);
        }
        
        .demo-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .demo-field {
            display: flex;
            flex-direction: column;
        }
        
        .demo-label {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 5px;
            color: var(--wsf-text);
        }
        
        .demo-select {
            height: 44px;
            padding: 0 12px;
            border: 1px solid var(--wsf-input-border);
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            background: var(--wsf-input-bg);
            color: var(--wsf-input-text);
        }
        
        .demo-button {
            height: 44px;
            padding: 0 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--wsf-primary) !important;
            color: var(--wsf-text-inverse) !important;
        }
        
        .theme-switcher {
            margin: 20px 0;
            text-align: center;
        }
        
        .theme-switcher button {
            margin: 0 10px;
            padding: 10px 20px;
            border: 2px solid #d1d5db;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 600;
        }
        
        .theme-switcher button:hover {
            background: #f3f4f6;
        }
        
        .theme-switcher button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .explanation {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .explanation h4 {
            margin-top: 0;
            color: #0c4a6e;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .fix-list {
            list-style: none;
            padding: 0;
        }
        
        .fix-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .fix-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Background Fix Test - TASK-BG-FIX</h1>
        <p>Проверка исправления проблемы с Background токеном через utility-first подход</p>
        
        <div class="theme-switcher">
            <button onclick="setTheme('light')" class="active" id="light-btn">Light Theme</button>
            <button onclick="setTheme('dark')" id="dark-btn">Dark Theme</button>
        </div>
        
        <!-- Root Cause Analysis -->
        <div class="explanation">
            <h4>🔍 Root Cause Analysis (RCA)</h4>
            <p><strong>Проблема:</strong> Tailwind CSS загружается после нашего CSS и перебивает <code>background: var(--wsf-bg)</code> своим <code>background-color: transparent</code></p>
            <p><strong>Причина:</strong> Под-свойство (<code>background-color</code>) имеет приоритет над шортхендом (<code>background</code>) без <code>!important</code></p>
            <p><strong>Решение:</strong> Использовать Tailwind утилиту <code>bg-wsf-bg</code> вместо CSS правила</p>
        </div>
        
        <!-- Applied Fixes -->
        <div class="test-section">
            <h3>Применённые исправления</h3>
            <ul class="fix-list">
                <li><strong>Tailwind конфигурация:</strong> Подтверждено наличие 'wsf-bg': 'var(--wsf-bg)'</li>
                <li><strong>Шаблоны:</strong> Добавлен класс bg-wsf-bg во все корневые контейнеры виджета</li>
                <li><strong>CSS cleanup:</strong> Закомментировано конфликтующее правило background: var(--wsf-bg)</li>
                <li><strong>Сборка:</strong> Утилита .wsf-bg-wsf-bg присутствует в скомпилированном CSS</li>
            </ul>
        </div>
        
        <!-- Before vs After Comparison -->
        <div class="test-section">
            <h3>Сравнение "До" и "После"</h3>
            <div class="comparison-grid">
                <div class="comparison-item before">
                    <h4>❌ До исправления (CSS правило)</h4>
                    <div class="demo-widget before-widget">
                        <div class="demo-form">
                            <div class="demo-field">
                                <label class="demo-label">Make</label>
                                <select class="demo-select">
                                    <option>BMW</option>
                                    <option>Audi</option>
                                </select>
                            </div>
                            <button class="demo-button">Find Sizes</button>
                        </div>
                    </div>
                    <div class="code-block">
.wheel-fit-widget {
  background: var(--wsf-bg); /* Перебивается Tailwind */
}
                    </div>
                </div>
                
                <div class="comparison-item after">
                    <h4>✅ После исправления (Tailwind утилита)</h4>
                    <div class="demo-widget after-widget wsf-bg-wsf-bg" data-wsf-theme="light">
                        <div class="demo-form">
                            <div class="demo-field">
                                <label class="demo-label">Make</label>
                                <select class="demo-select">
                                    <option>BMW</option>
                                    <option>Audi</option>
                                </select>
                            </div>
                            <button class="demo-button">Find Sizes</button>
                        </div>
                    </div>
                    <div class="code-block">
&lt;div class="wheel-fit-widget bg-wsf-bg"&gt;
  /* Генерирует: background-color: var(--wsf-bg) */
&lt;/div&gt;
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Real Widget Test -->
        <div class="test-section">
            <h3>Тест с реальным виджетом</h3>
            <div class="wheel-fit-widget wsf-bg-wsf-bg wsf-max-w-4xl wsf-mx-auto wsf-p-4 wsf-font-sans" id="test-widget" data-wsf-theme="light">
                <h4 style="margin-top: 0; color: var(--wsf-text-primary); text-align: center;">Wheel Size Finder</h4>
                <div class="demo-form">
                    <div class="demo-field">
                        <label class="demo-label">Make</label>
                        <select class="demo-select">
                            <option value="">Select make...</option>
                            <option value="bmw">BMW</option>
                            <option value="audi">Audi</option>
                            <option value="mercedes">Mercedes-Benz</option>
                        </select>
                    </div>
                    <div class="demo-field">
                        <label class="demo-label">Model</label>
                        <select class="demo-select" disabled>
                            <option value="">Select make first</option>
                        </select>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="demo-button" disabled>Find Sizes</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>Технические детали</h3>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>📁 Обновленные файлы:</h4>
                    <ul>
                        <li><code>finder-form.twig</code></li>
                        <li><code>finder-form-inline.twig</code></li>
                        <li><code>finder-popup-horizontal.twig</code></li>
                        <li><code>finder-form-flow.twig</code></li>
                        <li><code>wheel-fit-shared.src.css</code></li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h4>🔧 CSS изменения:</h4>
                    <div class="code-block">
/* Закомментировано */
/* background: var(--wsf-bg); */

/* Добавлено в шаблоны */
class="wheel-fit-widget bg-wsf-bg ..."
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status success">
            ✅ TASK-BG-FIX выполнен! Background токен теперь правильно применяется через Tailwind утилиту bg-wsf-bg
        </div>
    </div>
    
    <script>
        function setTheme(theme) {
            const widgets = document.querySelectorAll('[data-wsf-theme]');
            const lightBtn = document.getElementById('light-btn');
            const darkBtn = document.getElementById('dark-btn');
            
            widgets.forEach(widget => {
                widget.setAttribute('data-wsf-theme', theme);
                
                if (theme === 'dark') {
                    widget.classList.add('wsf-theme-dark');
                } else {
                    widget.classList.remove('wsf-theme-dark');
                }
            });
            
            // Update button states
            lightBtn.classList.toggle('active', theme === 'light');
            darkBtn.classList.toggle('active', theme === 'dark');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Test button enabling
            const makeSelect = document.querySelector('#test-widget select');
            const button = document.querySelector('#test-widget button');
            
            if (makeSelect && button) {
                makeSelect.addEventListener('change', function() {
                    if (this.value) {
                        button.disabled = false;
                        button.textContent = 'Find Sizes (Enabled)';
                    } else {
                        button.disabled = true;
                        button.textContent = 'Find Sizes';
                    }
                });
            }
        });
    </script>
</body>
</html>
