<?php

declare(strict_types=1);

namespace MyTyreFinder\Ajax;

use MyTyreFinder\Services\WheelSizeApi;

/**
 * AJAX handlers for Wheel-Fit plugin
 */
final class AjaxController
{
    private WheelSizeApi $api;

    public function __construct()
    {
        $this->api = new WheelSizeApi();
    }

    public function register(): void
    {
        // Get vehicle makes
        add_action('wp_ajax_wf_get_makes', [$this, 'get_makes']);
        add_action('wp_ajax_nopriv_wf_get_makes', [$this, 'get_makes']);

        // Get models by make
        add_action('wp_ajax_wf_get_models', [$this, 'get_models']);
        add_action('wp_ajax_nopriv_wf_get_models', [$this, 'get_models']);

        // Get years by make and model
        add_action('wp_ajax_wf_get_years', [$this, 'get_years']);
        add_action('wp_ajax_nopriv_wf_get_years', [$this, 'get_years']);

        // Get generations by make and model
        add_action('wp_ajax_wf_get_generations', [$this, 'get_generations']);
        add_action('wp_ajax_nopriv_wf_get_generations', [$this, 'get_generations']);

        // Get modifications by make, model, year
        add_action('wp_ajax_wf_get_modifications', [$this, 'get_modifications']);
        add_action('wp_ajax_nopriv_wf_get_modifications', [$this, 'get_modifications']);

        // Get modifications by make, model, generation
        add_action('wp_ajax_wf_get_modifications_by_generation', [$this, 'get_modifications_by_generation']);
        add_action('wp_ajax_nopriv_wf_get_modifications_by_generation', [$this, 'get_modifications_by_generation']);

        // Search tire and wheel sizes
        add_action('wp_ajax_wf_search_sizes', [$this, 'search_sizes']);
        add_action('wp_ajax_nopriv_wf_search_sizes', [$this, 'search_sizes']);

        // Save to localStorage (for analytics)
        add_action('wp_ajax_wf_save_search', [$this, 'save_search']);
        add_action('wp_ajax_nopriv_wf_save_search', [$this, 'save_search']);

        // API test endpoint for admin panel
        add_action('wp_ajax_wheel_size_test_api', [$this, 'test_api']);

        // By year flow endpoints
        add_action('wp_ajax_wf_get_all_years', [$this, 'get_all_years']);
        add_action('wp_ajax_nopriv_wf_get_all_years', [$this, 'get_all_years']);
        add_action('wp_ajax_wf_get_makes_by_year', [$this, 'get_makes_by_year']);
        add_action('wp_ajax_nopriv_wf_get_makes_by_year', [$this, 'get_makes_by_year']);
        add_action('wp_ajax_wf_get_models_by_year', [$this, 'get_models_by_year']);
        add_action('wp_ajax_nopriv_wf_get_models_by_year', [$this, 'get_models_by_year']);
        add_action('wp_ajax_wf_get_modifications_by_year', [$this, 'get_modifications_by_year']);
        add_action('wp_ajax_nopriv_wf_get_modifications_by_year', [$this, 'get_modifications_by_year']);
    }

    /**
     * Get all vehicle makes
     */
    public function get_makes(): void
    {
        $this->verify_nonce();

        try {
            $makes = $this->api->getMakes();

            // Check if empty result is due to brand filters
            $filter_info = $this->checkBrandFilterStatus($makes);

            wp_send_json_success([
                'data' => $makes,
                'filter_info' => $filter_info
            ]);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_makes: ' . $e->getMessage());
            wp_send_json_error('Failed to load makes', 500);
        }
    }

    /**
     * Get models for specific make
     */
    public function get_models(): void
    {
        $this->verify_nonce();

        $make_slug = sanitize_text_field($_POST['make'] ?? '');
        
        if (empty($make_slug)) {
            wp_send_json_error('Make is required', 400);
        }

        try {
            $models = $this->api->getModels($make_slug);
            
            // If API returns empty, respond with empty list
            wp_send_json_success($models);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_models: ' . $e->getMessage());
            wp_send_json_error('Failed to load models', 500);
        }
    }

    /**
     * Get years for specific make and model
     */
    public function get_years(): void
    {
        $this->verify_nonce();

        $make_slug = sanitize_text_field($_POST['make'] ?? '');
        $model_slug = sanitize_text_field($_POST['model'] ?? '');

        if (empty($make_slug) || empty($model_slug)) {
            wp_send_json_error('Make and model are required', 400);
        }

        try {
            $years = $this->api->getYears($make_slug, $model_slug);

            // If API returns empty, respond with empty list
            wp_send_json_success($years);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_years: ' . $e->getMessage());
            wp_send_json_error('Failed to load years', 500);
        }
    }

    /**
     * Get generations for specific make and model
     */
    public function get_generations(): void
    {
        $this->verify_nonce();

        $make_slug = sanitize_text_field($_POST['make'] ?? '');
        $model_slug = sanitize_text_field($_POST['model'] ?? '');

        if (empty($make_slug) || empty($model_slug)) {
            wp_send_json_error('Make and model are required', 400);
        }

        try {
            $generations = $this->api->getGenerations($make_slug, $model_slug);

            // If API returns empty, respond with empty list
            wp_send_json_success($generations);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_generations: ' . $e->getMessage());
            wp_send_json_error('Failed to load generations', 500);
        }
    }

    /**
     * Get modifications for specific vehicle
     */
    public function get_modifications(): void
    {
        $this->verify_nonce();

        $make_slug = sanitize_text_field($_POST['make'] ?? '');
        $model_slug = sanitize_text_field($_POST['model'] ?? '');
        $year = (int)($_POST['year'] ?? 0);
        
        if (empty($make_slug) || empty($model_slug) || $year === 0) {
            wp_send_json_error('Make, model and year are required', 400);
        }

        try {
            $modifications = $this->api->getModifications($make_slug, $model_slug, $year);
            
            // If API returns empty, respond with generic empty array
            if (empty($modifications)) {
                $modifications = [];
            }

            wp_send_json_success($modifications);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_modifications: ' . $e->getMessage());
            wp_send_json_error('Failed to load modifications', 500);
        }
    }

    /**
     * Get modifications for specific make, model and generation
     */
    public function get_modifications_by_generation(): void
    {
        $this->verify_nonce();

        $make_slug = sanitize_text_field($_POST['make'] ?? '');
        $model_slug = sanitize_text_field($_POST['model'] ?? '');
        $generation_slug = sanitize_text_field($_POST['generation'] ?? '');

        if (empty($make_slug) || empty($model_slug) || empty($generation_slug)) {
            wp_send_json_error('Make, model and generation are required', 400);
        }

        try {
            $modifications = $this->api->getModificationsByGeneration($make_slug, $model_slug, $generation_slug);

            // If API returns empty, respond with generic empty array
            if (empty($modifications)) {
                $modifications = [];
            }

            wp_send_json_success($modifications);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_modifications_by_generation: ' . $e->getMessage());
            wp_send_json_error('Failed to load modifications', 500);
        }
    }

    /**
     * Search tire and wheel sizes
     */
    public function search_sizes(): void
    {
        $this->verify_nonce();

        $make_slug = sanitize_text_field($_POST['make'] ?? '');
        $model_slug = sanitize_text_field($_POST['model'] ?? '');
        $year = (int)($_POST['year'] ?? 0);
        $generation_slug = sanitize_text_field($_POST['generation'] ?? '');
        $modification_slug = sanitize_text_field($_POST['modification'] ?? '');

        // Support both year-based and generation-based search
        if (!empty($generation_slug)) {
            // Generation-based search: make, model, generation required
            if (empty($make_slug) || empty($model_slug) || empty($generation_slug)) {
                wp_send_json_error('Make, model and generation are required', 400);
            }
        } else {
            // Year-based search: make, model, year required
            if (empty($make_slug) || empty($model_slug) || $year === 0) {
                wp_send_json_error('Make, model and year are required', 400);
            }
        }

        try {
            $results = $this->api->searchByModel($make_slug, $model_slug, $year, $modification_slug, null, $generation_slug);

            // Log search for analytics
            if (!empty($generation_slug)) {
                $this->log_search($make_slug, $model_slug, null, $generation_slug);
            } else {
                $this->log_search($make_slug, $model_slug, $year);
            }

            wp_send_json_success([
                'factory_sizes' => $results['factory'],
                'optional_sizes' => $results['optional'],
                'total_count' => count($results['factory']) + count($results['optional'])
            ]);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in search_sizes: ' . $e->getMessage());
            wp_send_json_error('Failed to search sizes', 500);
        }
    }

    /**
     * Save search to localStorage (for analytics endpoint)
     */
    public function save_search(): void
    {
        $this->verify_nonce();

        $search_data = [
            'make' => sanitize_text_field($_POST['make'] ?? ''),
            'model' => sanitize_text_field($_POST['model'] ?? ''),
            'year' => (int)($_POST['year'] ?? 0),
            'timestamp' => current_time('timestamp'),
            'ip' => $this->get_client_ip()
        ];

        // Log to WordPress option for basic analytics
        $searches = get_option('wheel_size_searches', []);
        $searches[] = $search_data;
        
        // Keep only last 1000 searches
        if (count($searches) > 1000) {
            $searches = array_slice($searches, -1000);
        }
        
        update_option('wheel_size_searches', $searches);

        wp_send_json_success(['saved' => true]);
    }

    /**
     * Verify AJAX nonce for security
     */
    private function verify_nonce(string $nonce_name = 'wheel_fit_nonce'): void
    {
        if (!check_ajax_referer($nonce_name, 'nonce', false)) {
            wp_send_json_error('Security check failed', 403);
        }
    }

    /**
     * Log search for analytics
     */
    private function log_search(string $make, string $model, ?int $year = null, ?string $generation = null): void
    {
        $log_data = [
            'action' => 'search',
            'make' => $make,
            'model' => $model,
            'timestamp' => current_time('mysql'),
            'ip' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];

        // Add year or generation depending on search type
        if ($generation) {
            $log_data['generation'] = $generation;
        } elseif ($year) {
            $log_data['year'] = $year;
        }

        // Log to file or database
        error_log('Wheel-Size Search: ' . json_encode($log_data));
    }

    /**
     * Get client IP address
     */
    private function get_client_ip(): string
    {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim(explode(',', $_SERVER[$key])[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * Test API connection endpoint
     */
    public function test_api(): void
    {
        if (!check_ajax_referer('wheel_size_test_api', 'nonce', false)) {
            wp_send_json_error('Security check failed', 403);
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied', 403);
        }

        $api_key = sanitize_text_field($_POST['api_key'] ?? '');
        
        if (empty($api_key)) {
            wp_send_json_error('API ключ не указан');
        }

        try {
            $test_api = new WheelSizeApi($api_key);
            $makes = $test_api->getMakes(null, false); // Don't apply filters for API test

            if (!empty($makes)) {
                wp_send_json_success(['message' => 'API connection successful', 'makes_count' => count($makes)]);
            } else {
                wp_send_json_error('API connection failed - no data returned');
            }
        } catch (\Exception $e) {
            wp_send_json_error('API connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Get all available years (for by_year flow)
     */
    public function get_all_years(): void
    {
        $this->verify_nonce();

        try {
            $years = $this->api->getAllYears();
            wp_send_json_success($years);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_all_years: ' . $e->getMessage());
            wp_send_json_error('Failed to load years', 500);
        }
    }

    /**
     * Get makes for specific year (for by_year flow)
     */
    public function get_makes_by_year(): void
    {
        $this->verify_nonce();
        $year = (int)($_POST['year'] ?? 0);
        if ($year === 0) {
            wp_send_json_error('Year is required', 400);
        }
        try {
            $makes = $this->api->getMakesByYear($year);

            // Check if empty result is due to brand filters
            $filter_info = $this->checkBrandFilterStatus($makes, $year);

            wp_send_json_success([
                'data' => $makes,
                'filter_info' => $filter_info
            ]);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_makes_by_year: ' . $e->getMessage());
            wp_send_json_error('Failed to load makes', 500);
        }
    }
    public function get_models_by_year(): void
    {
        $this->verify_nonce();
        $year = (int)($_POST['year'] ?? 0);
        $make_slug = sanitize_text_field($_POST['make'] ?? '');
        if ($year === 0 || empty($make_slug)) {
            wp_send_json_error('Year and make are required', 400);
        }
        try {
            $models = $this->api->getModelsByYear($year, $make_slug);
            wp_send_json_success($models);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_models_by_year: ' . $e->getMessage());
            wp_send_json_error('Failed to load models', 500);
        }
    }
    public function get_modifications_by_year(): void
    {
        $this->verify_nonce();
        $year = (int)($_POST['year'] ?? 0);
        $make_slug = sanitize_text_field($_POST['make'] ?? '');
        $model_slug = sanitize_text_field($_POST['model'] ?? '');
        if ($year === 0 || empty($make_slug) || empty($model_slug)) {
            wp_send_json_error('Year, make and model are required', 400);
        }
        try {
            $modifications = $this->api->getModificationsByYear($year, $make_slug, $model_slug);
            wp_send_json_success($modifications);
        } catch (\Exception $e) {
            error_log('Wheel-Size Error in get_modifications_by_year: ' . $e->getMessage());
            wp_send_json_error('Failed to load modifications', 500);
        }
    }

    /**
     * Check if empty results are due to brand filters and provide user-friendly info
     */
    private function checkBrandFilterStatus(array $makes, ?int $year = null): array
    {
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        $include_brands = array_filter((array) get_option('wheel_size_include_makes', []));
        $exclude_brands = array_filter((array) get_option('wheel_size_exclude_makes', []));

        $info = [
            'has_filters' => !empty($selected_regions) || !empty($include_brands) || !empty($exclude_brands),
            'empty_due_to_filters' => false,
            'message' => '',
            'suggestions' => []
        ];

        // If no filters are active, empty result is not due to filters
        if (!$info['has_filters']) {
            return $info;
        }

        // If we have results, filters are working fine
        if (!empty($makes)) {
            return $info;
        }

        // Empty result with active filters - check what's causing it
        $region_names = $this->getRegionNames($selected_regions);
        $year_text = $year ? " for year {$year}" : "";

        if (!empty($include_brands) && !empty($selected_regions)) {
            // Most likely case: include brands not available in selected regions
            $brand_names = array_map('ucfirst', $include_brands);
            $info['empty_due_to_filters'] = true;
            $info['message'] = sprintf(
                __('Selected brands (%s) are not available in %s%s.', 'my-tyre-finder'),
                implode(', ', $brand_names),
                implode(', ', $region_names),
                $year_text
            );
            $info['suggestions'] = [
                __('Clear brand filters to see all available brands', 'my-tyre-finder'),
                __('Select different brands', 'my-tyre-finder'),
                __('Change region settings', 'my-tyre-finder')
            ];
        } elseif (!empty($include_brands)) {
            // Include brands set but no regions - brands might not exist
            $brand_names = array_map('ucfirst', $include_brands);
            $info['empty_due_to_filters'] = true;
            $info['message'] = sprintf(
                __('Selected brands (%s) are not available%s.', 'my-tyre-finder'),
                implode(', ', $brand_names),
                $year_text
            );
            $info['suggestions'] = [
                __('Clear brand filters to see all available brands', 'my-tyre-finder'),
                __('Select different brands', 'my-tyre-finder')
            ];
        } elseif (!empty($selected_regions)) {
            // Only regions set - region might not have any brands
            $info['empty_due_to_filters'] = true;
            $info['message'] = sprintf(
                __('No brands available in %s%s.', 'my-tyre-finder'),
                implode(', ', $region_names),
                $year_text
            );
            $info['suggestions'] = [
                __('Select different regions', 'my-tyre-finder'),
                __('Clear region filters', 'my-tyre-finder')
            ];
        }

        return $info;
    }

    /**
     * Get human-readable region names
     */
    private function getRegionNames(array $region_slugs): array
    {
        $region_map = [
            'usdm' => __('North America', 'my-tyre-finder'),
            'eudm' => __('Europe', 'my-tyre-finder'),
            'jdm' => __('Japan', 'my-tyre-finder'),
            'audm' => __('Australia', 'my-tyre-finder'),
            'skdm' => __('South Korea', 'my-tyre-finder'),
            'chdm' => __('China', 'my-tyre-finder'),
            'russia' => __('Russia', 'my-tyre-finder'),
            'sadm' => __('South America', 'my-tyre-finder'),
            'medm' => __('Middle East', 'my-tyre-finder'),
            'nadm' => __('North Africa', 'my-tyre-finder'),
            'ladm' => __('Latin America', 'my-tyre-finder'),
            'sam' => __('Southeast Asia', 'my-tyre-finder'),
            'cdm' => __('Canada', 'my-tyre-finder'),
            'mxndm' => __('Mexico', 'my-tyre-finder')
        ];

        return array_map(function($slug) use ($region_map) {
            return $region_map[$slug] ?? ucfirst($slug);
        }, $region_slugs);
    }
}