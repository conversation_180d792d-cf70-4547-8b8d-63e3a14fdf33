/**
 * Test script to verify tight spacing between search button and Garage button
 * in Step-by-Step layout after moving Garage into same container
 */

console.log('🧪 Testing Step-by-Step Garage tight spacing...');

function testGarageTightSpacing() {
    console.log('\n📍 Testing Garage button tight spacing in Step-by-Step layout...');
    
    // Find the stepper form
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm || !stepperForm.classList.contains('space-y-6')) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    console.log('✅ Found Step-by-Step form');
    
    // Find the search button container
    const searchContainer = stepperForm.querySelector('.text-center');
    if (!searchContainer) {
        console.log('❌ Search button container not found');
        return false;
    }
    
    console.log('✅ Found search button container');
    
    // Check if Garage button is inside the same container as search button
    const garageInSameContainer = searchContainer.querySelector('[data-garage-trigger]');
    const isInSameContainer = !!garageInSameContainer;
    console.log(`${isInSameContainer ? '✅' : '❌'} Garage button is in same container as search button`);
    
    if (isInSameContainer) {
        // Check spacing between search button and garage button
        const searchButton = searchContainer.querySelector('button[type="submit"]');
        const garageContainer = garageInSameContainer.parentElement;
        
        if (searchButton && garageContainer) {
            const searchRect = searchButton.getBoundingClientRect();
            const garageRect = garageContainer.getBoundingClientRect();
            
            const spacing = garageRect.top - searchRect.bottom;
            console.log(`✅ Spacing between buttons: ${spacing}px`);
            
            // Check if spacing is tight (should be around 8px for mt-2)
            const isTightSpacing = spacing <= 12; // Allow some tolerance
            console.log(`${isTightSpacing ? '✅' : '❌'} Spacing is tight (≤12px)`);
            
            // Check if garage button has mt-2 class
            const hasMt2 = garageContainer.classList.contains('mt-2');
            console.log(`${hasMt2 ? '✅' : '❌'} Garage container has mt-2 class`);
            
            return isInSameContainer && isTightSpacing && hasMt2;
        }
    }
    
    return isInSameContainer;
}

function testVisualConnection() {
    console.log('\n🔗 Testing visual connection between search and garage...');
    
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    // Find the main search container
    const searchContainer = stepperForm.querySelector('.text-center');
    if (!searchContainer) {
        console.log('❌ Search container not found');
        return false;
    }
    
    // Check if both buttons are visually grouped
    const searchButton = searchContainer.querySelector('button[type="submit"]');
    const garageButton = searchContainer.querySelector('[data-garage-trigger]');
    
    if (searchButton && garageButton) {
        console.log('✅ Both search and garage buttons found in same container');
        
        // Check if they're visually aligned (both should be within same container bounds)
        const containerRect = searchContainer.getBoundingClientRect();
        const searchRect = searchButton.getBoundingClientRect();
        const garageRect = garageButton.getBoundingClientRect();
        
        const searchInContainer = searchRect.left >= containerRect.left && searchRect.right <= containerRect.right;
        const garageInContainer = garageRect.left >= containerRect.left && garageRect.right <= containerRect.right;
        
        console.log(`${searchInContainer ? '✅' : '❌'} Search button within container bounds`);
        console.log(`${garageInContainer ? '✅' : '❌'} Garage button within container bounds`);
        
        return searchInContainer && garageInContainer;
    } else if (!garageButton) {
        console.log('ℹ️ Garage button not found (may be disabled)');
        return true; // Not an error if garage is disabled
    }
    
    return false;
}

function testFormIntegrity() {
    console.log('\n📝 Testing form integrity after container change...');
    
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    // Check if garage button is still inside the form
    const garageInForm = stepperForm.querySelector('[data-garage-trigger]');
    const isInForm = !!garageInForm;
    console.log(`${isInForm ? '✅' : '❌'} Garage button is inside the form`);
    
    // Check button type
    if (garageInForm) {
        const buttonType = garageInForm.getAttribute('type');
        const correctType = buttonType === 'button';
        console.log(`${correctType ? '✅' : '❌'} Garage button type is "button"`);
        
        // Check if it's properly positioned (right-aligned)
        const container = garageInForm.parentElement;
        const isRightAligned = container.classList.contains('justify-end');
        console.log(`${isRightAligned ? '✅' : '❌'} Garage button is right-aligned`);
        
        return isInForm && correctType && isRightAligned;
    }
    
    return isInForm;
}

function runStepperTightSpacingTests() {
    console.log('🚀 Starting Step-by-Step tight spacing tests...\n');
    
    const results = {
        tightSpacing: testGarageTightSpacing(),
        visualConnection: testVisualConnection(),
        formIntegrity: testFormIntegrity()
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Garage button spacing is now tight and visually connected.');
    } else {
        console.log('⚠️ Some tests failed. Check the details above.');
    }
    
    return results;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runStepperTightSpacingTests);
} else {
    runStepperTightSpacingTests();
}

// Export for manual testing
window.testStepperGarageTightSpacing = runStepperTightSpacingTests;
