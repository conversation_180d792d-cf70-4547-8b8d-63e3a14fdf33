<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accent Color Fix Test</title>
    <link rel="stylesheet" href="../assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .demo-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .color-controls {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .color-field {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .color-input {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .color-label {
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        .accent-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .accent-example {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
        }
        
        .accent-example h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #1f2937;
        }
        
        .demo-link {
            color: var(--wsf-accent, #3b82f6);
            text-decoration: underline;
            cursor: pointer;
        }
        
        .demo-link:hover {
            opacity: 0.8;
        }
        
        .demo-year-range {
            font-size: 12px;
            color: var(--wsf-accent, #3b82f6);
            font-weight: 500;
        }
        
        .demo-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            background: #dbeafe;
            color: var(--wsf-accent, #3b82f6);
        }
        
        .demo-model-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .demo-model-name {
            font-weight: 600;
            color: var(--wsf-text, #1f2937);
            margin-bottom: 4px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .before-after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Accent Color Fix Test</h1>
        <p>Тест проверяет, что цвет "Accent" теперь применяется к соответствующим элементам интерфейса.</p>
        
        <!-- Color Controls -->
        <div class="test-section">
            <h3>🎛️ Управление цветами</h3>
            <p>Измените цвет Accent и посмотрите, как он влияет на элементы ниже:</p>
            
            <div class="color-controls">
                <div class="color-field">
                    <input type="color" class="color-input" value="#3b82f6" data-token="--wsf-accent">
                    <label class="color-label">Accent (ссылки, year ranges, optional badges)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#2563eb" data-token="--wsf-primary">
                    <label class="color-label">Primary (кнопки, основные элементы)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#1f2937" data-token="--wsf-text">
                    <label class="color-label">Text (основной текст)</label>
                </div>
            </div>
        </div>
        
        <!-- Accent Examples -->
        <div class="test-section">
            <h3>✨ Элементы, использующие Accent цвет</h3>
            
            <div class="accent-examples">
                <div class="accent-example">
                    <h4>1. Ссылки "Show more"</h4>
                    <p>Обычный текст с <span class="demo-link">ссылкой "Show more"</span> внизу списка.</p>
                    <p style="font-size: 12px; color: #6b7280;">Класс: wsf-text-accent</p>
                </div>
                
                <div class="accent-example">
                    <h4>2. Year Ranges</h4>
                    <div class="demo-model-item">
                        <div class="demo-model-name">BMW 320i</div>
                        <div class="demo-year-range">2018-2023, 2020-2024</div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280;">Класс: wsf-text-accent</p>
                </div>
                
                <div class="accent-example">
                    <h4>3. Optional Badges</h4>
                    <div style="margin: 10px 0;">
                        <span class="demo-badge">Optional</span>
                    </div>
                    <p style="font-size: 12px; color: #6b7280;">Класс: wsf-text-accent</p>
                </div>
                
                <div class="accent-example">
                    <h4>4. Ссылки "Show less"</h4>
                    <p>Список элементов...</p>
                    <span class="demo-link">Show less</span>
                    <p style="font-size: 12px; color: #6b7280;">Класс: wsf-text-accent</p>
                </div>
            </div>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>📊 До и После исправления</h3>
            <div class="comparison-grid">
                <div class="before">
                    <h4>❌ ДО</h4>
                    <p><strong>Проблема:</strong> Accent цвет не использовался</p>
                    <ul>
                        <li>Ссылки: <code>text-blue-600</code> (жестко закодировано)</li>
                        <li>Year ranges: <code>text-indigo-700</code> (жестко закодировано)</li>
                        <li>Optional badges: <code>text-indigo-700</code> (жестко закодировано)</li>
                    </ul>
                    <p><strong>Результат:</strong> Accent в Theme Presets ничего не менял</p>
                </div>
                
                <div class="after">
                    <h4>✅ ПОСЛЕ</h4>
                    <p><strong>Решение:</strong> Accent цвет применяется к нужным элементам</p>
                    <ul>
                        <li>Ссылки: <code>wsf-text-accent</code></li>
                        <li>Year ranges: <code>wsf-text-accent</code></li>
                        <li>Optional badges: <code>wsf-text-accent</code></li>
                    </ul>
                    <p><strong>Результат:</strong> Accent в Theme Presets работает!</p>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Технические детали</h3>
            
            <h4>Добавлен CSS класс:</h4>
            <div class="code-block">
.wsf-text-accent { 
    color: var(--wsf-accent); 
}
            </div>
            
            <h4>Исправленные файлы:</h4>
            <ul>
                <li><code>assets/css/wheel-fit-shared.src.css</code> - добавлен .wsf-text-accent</li>
                <li><code>assets/js/finder.js</code> - ссылки и year ranges</li>
                <li><code>assets/js/wizard.js</code> - optional badges</li>
            </ul>
            
            <h4>Замены в коде:</h4>
            <div class="code-block">
// ДО
'text-blue-600'   → 'wsf-text-accent'
'text-indigo-700' → 'wsf-text-accent'

// CSS
.wsf-text-accent { color: var(--wsf-accent); }
            </div>
        </div>
        
        <!-- Demo Widget -->
        <div class="test-section">
            <h3>🎮 Демо виджета</h3>
            <div class="demo-widget" id="demo-widget">
                <h4>Результаты поиска:</h4>
                
                <div class="demo-model-item">
                    <div class="demo-model-name">BMW 320i</div>
                    <div class="demo-year-range">2018-2023, 2020-2024</div>
                </div>
                
                <div class="demo-model-item">
                    <div class="demo-model-name">BMW 330i</div>
                    <div class="demo-year-range">2019-2024</div>
                </div>
                
                <div style="text-align: center; margin-top: 15px;">
                    <span class="demo-link">Show more models</span>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>Размеры шин:</h4>
                    <div style="position: relative; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                        <span style="position: absolute; top: 6px; right: 6px; padding: 2px 6px; background: #dbeafe; border-radius: 4px; font-size: 10px; font-weight: 600; text-transform: uppercase;" class="wsf-text-accent">OPTIONAL</span>
                        <div style="font-weight: 600; margin-bottom: 5px;">225/45 R17</div>
                        <div style="font-size: 12px; color: #6b7280;">Load Index: 94, Speed Rating: W</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить исправление</h4>
            <ol>
                <li><strong>Очистите кэш</strong> WordPress и браузера</li>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>Создайте новую тему или отредактируйте существующую</li>
                <li>Измените цвет <strong>"Accent"</strong> на яркий (например, красный или зеленый)</li>
                <li>Сохраните и примените тему</li>
                <li>Перейдите на страницу с виджетом и выполните поиск</li>
                <li>Убедитесь, что изменились:
                    <ul>
                        <li>Ссылки "Show more" / "Show less"</li>
                        <li>Year ranges в результатах (например, "2018-2023")</li>
                        <li>Badges "OPTIONAL" на карточках размеров</li>
                    </ul>
                </li>
            </ol>
            
            <h4>🎯 Ожидаемый результат</h4>
            <ul>
                <li>✅ Accent цвет влияет на ссылки и вторичные элементы</li>
                <li>✅ Primary цвет влияет на кнопки и основные элементы</li>
                <li>✅ Четкое разделение между Primary и Accent</li>
                <li>✅ Все элементы интерфейса настраиваются через Theme Presets</li>
            </ul>
        </div>
    </div>

    <script>
        // Color picker functionality
        document.querySelectorAll('.color-input').forEach(input => {
            input.addEventListener('change', function() {
                const token = this.dataset.token;
                const value = this.value;
                const demoWidget = document.getElementById('demo-widget');
                
                demoWidget.style.setProperty(token, value);
                console.log(`Updated ${token} to ${value}`);
            });
        });
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            const demoWidget = document.getElementById('demo-widget');
            
            // Set default values
            demoWidget.style.setProperty('--wsf-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-border', '#e5e7eb');
            demoWidget.style.setProperty('--wsf-primary', '#2563eb');
            demoWidget.style.setProperty('--wsf-accent', '#3b82f6');
            
            console.log('Accent color fix demo initialized');
        });
    </script>
</body>
</html>
