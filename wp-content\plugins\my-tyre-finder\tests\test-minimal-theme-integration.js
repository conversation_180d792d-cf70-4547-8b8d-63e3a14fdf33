/**
 * Test script for minimal theme integration
 * Run in browser console on the admin theme panel page
 */

(function() {
    'use strict';

    console.log('🎨 === MINIMAL THEME INTEGRATION TEST ===');

    // Test 1: Check CSS Variable
    function testCSSVariable() {
        console.log('\n1️⃣ Testing CSS Variable...');
        
        const rootStyles = getComputedStyle(document.documentElement);
        const adminPanelBg = rootStyles.getPropertyValue('--wsf-admin-panel-bg').trim();
        
        console.log('CSS Variable --wsf-admin-panel-bg:', adminPanelBg || 'NOT FOUND');
        
        if (adminPanelBg) {
            console.log('✅ CSS variable is defined');
            return true;
        } else {
            console.log('❌ CSS variable is missing');
            return false;
        }
    }

    // Test 2: Check Live Preview Container
    function testLivePreviewContainer() {
        console.log('\n2️⃣ Testing Live Preview Container...');

        const previewContainer = document.getElementById('widget-preview');
        if (!previewContainer) {
            console.log('❌ Live preview container not found');
            return false;
        }

        console.log('✅ Live preview container found');
        console.log('Container type:', previewContainer.tagName.toLowerCase());

        // Check if it contains a widget
        const widgets = previewContainer.querySelectorAll('.wsf-finder-widget, .wheel-fit-widget');
        console.log(`Found ${widgets.length} widget(s) in preview`);

        if (widgets.length > 0) {
            const widget = widgets[0];
            const styles = getComputedStyle(widget);
            console.log('Widget background:', styles.backgroundColor);
            console.log('Widget text color:', styles.color);

            // Check for CSS variables
            const bgVar = widget.style.getPropertyValue('--wsf-bg');
            const textVar = widget.style.getPropertyValue('--wsf-text');
            console.log('CSS variables applied:', {
                '--wsf-bg': bgVar || 'not set',
                '--wsf-text': textVar || 'not set'
            });
        }

        return widgets.length > 0;
    }

    // Test 3: Check Available Themes
    function testAvailableThemes() {
        console.log('\n3️⃣ Testing Available Themes...');
        
        const themeCards = document.querySelectorAll('.wsf-theme-card');
        console.log(`Found ${themeCards.length} theme cards`);
        
        themeCards.forEach((card, index) => {
            const nameElement = card.querySelector('.wsf-theme-card__name');
            const isActive = card.classList.contains('wsf-theme-card--active');
            const name = nameElement ? nameElement.textContent.trim() : 'Unknown';
            
            console.log(`  ${index + 1}. ${name} ${isActive ? '(ACTIVE)' : ''}`);
        });
        
        return themeCards.length > 0;
    }

    // Test 4: Test Theme Application Function
    function testThemeApplicationFunction() {
        console.log('\n4️⃣ Testing Theme Application Function...');
        
        // Check if the theme panel instance exists
        if (typeof window.themePanel === 'undefined') {
            console.log('⚠️ Theme panel instance not found in window.themePanel');
            console.log('💡 This is normal - the function should still work when themes are activated');
            return true;
        }
        
        if (typeof window.themePanel.applyThemeToAdminPanel === 'function') {
            console.log('✅ applyThemeToAdminPanel function exists');
            return true;
        } else {
            console.log('❌ applyThemeToAdminPanel function not found');
            return false;
        }
    }

    // Test 5: Manual Theme Application Test
    function testManualThemeApplication() {
        console.log('\n5️⃣ Testing Manual Theme Application to Widget...');

        const previewContainer = document.getElementById('widget-preview');
        if (!previewContainer) {
            console.log('⚠️ No preview container found for testing');
            return false;
        }

        // Test with light theme colors
        const lightTheme = {
            '--wsf-bg': '#ffffff',
            '--wsf-text': '#1f2937',
            '--wsf-primary': '#2563eb'
        };

        // Test with dark theme colors
        const darkTheme = {
            '--wsf-bg': '#1e1e1e',
            '--wsf-text': '#f3f4f6',
            '--wsf-primary': '#7dd3fc'
        };

        console.log('Testing light theme on widget...');
        Object.entries(lightTheme).forEach(([prop, value]) => {
            previewContainer.style.setProperty(prop, value);
        });

        setTimeout(() => {
            console.log('Testing dark theme on widget...');
            Object.entries(darkTheme).forEach(([prop, value]) => {
                previewContainer.style.setProperty(prop, value);
            });

            setTimeout(() => {
                console.log('Resetting to default...');
                Object.entries(lightTheme).forEach(([prop, value]) => {
                    previewContainer.style.setProperty(prop, value);
                });
                console.log('✅ Manual widget theme application test completed');
            }, 1500);
        }, 1500);

        return true;
    }

    // Test 6: Create Visual Indicator
    function createVisualIndicator(results) {
        console.log('\n6️⃣ Creating Visual Indicator...');
        
        // Remove existing indicator
        const existing = document.getElementById('theme-integration-test');
        if (existing) existing.remove();
        
        const passedTests = Object.values(results).filter(Boolean).length;
        const totalTests = Object.keys(results).length;
        const percentage = Math.round((passedTests / totalTests) * 100);
        
        const indicator = document.createElement('div');
        indicator.id = 'theme-integration-test';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${percentage >= 80 ? '#28a745' : percentage >= 60 ? '#ffc107' : '#dc3545'};
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 13px;
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 250px;
        `;
        
        indicator.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 8px;">
                🎨 Theme Integration Test
            </div>
            <div style="font-size: 18px; font-weight: bold; margin-bottom: 4px;">
                ${percentage}%
            </div>
            <div style="font-size: 11px; opacity: 0.9;">
                ${passedTests}/${totalTests} tests passed
            </div>
            <div style="margin-top: 8px; font-size: 11px; opacity: 0.9;">
                ${percentage >= 80 ? '✅ Integration working!' : 
                  percentage >= 60 ? '⚠️ Partial integration' : 
                  '❌ Integration issues'}
            </div>
            <button onclick="this.parentElement.remove()" 
                    style="position: absolute; top: 4px; right: 6px; 
                           background: none; border: none; color: white; 
                           cursor: pointer; font-size: 16px;">×</button>
        `;
        
        document.body.appendChild(indicator);
        
        // Auto-remove after 15 seconds
        setTimeout(() => {
            if (document.getElementById('theme-integration-test')) {
                indicator.remove();
            }
        }, 15000);
        
        console.log('✅ Visual indicator created');
    }

    // Main test function
    function runTests() {
        console.log('🚀 Starting minimal theme integration tests...\n');
        
        const results = {
            'CSS Variable': testCSSVariable(),
            'Live Preview Container': testLivePreviewContainer(),
            'Available Themes': testAvailableThemes(),
            'Application Function': testThemeApplicationFunction()
        };
        
        // Run manual test (doesn't affect results)
        testManualThemeApplication();
        
        console.log('\n📊 === TEST RESULTS ===');
        Object.entries(results).forEach(([test, passed]) => {
            console.log(`${passed ? '✅' : '❌'} ${test}`);
        });
        
        const passedTests = Object.values(results).filter(Boolean).length;
        const totalTests = Object.keys(results).length;
        
        console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
        
        if (passedTests === totalTests) {
            console.log('🎉 All tests passed! Minimal theme integration is working.');
        } else if (passedTests >= totalTests * 0.75) {
            console.log('👍 Most tests passed. Minor issues may exist.');
        } else {
            console.log('⚠️ Several tests failed. Integration needs work.');
        }
        
        createVisualIndicator(results);
        
        console.log('\n💡 Next steps:');
        console.log('1. Try switching between themes in the admin panel');
        console.log('2. Watch the theme panel background color change');
        console.log('3. Check browser console for any errors');
        
        return results;
    }

    // Add global function for manual testing
    window.testThemeIntegration = runTests;
    
    // Run tests automatically
    runTests();

})();
