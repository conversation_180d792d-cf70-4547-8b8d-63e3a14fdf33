# 🔧 WordPress Admin Testing Instructions

## Цель: Проверить, что новые input-токены появились в Theme Presets

---

## 📋 Пошаговая инструкция

### 1. Очистите кэш (если используется)
- Очистите кэш WordPress (если установлены плагины кэширования)
- Очистите кэш браузера (Ctrl+F5 или Cmd+Shift+R)

### 2. Перейдите в WordPress Admin
- Откройте **WordPress Admin**
- Перейдите в **Wheel-Size → Appearance**

### 3. Откройте Developer Tools
- Нажмите **F12** (или Cmd+Option+I на Mac)
- Перейдите на вкладку **Console**

### 4. Проверьте передачу токенов в JavaScript
Выполните в консоли:
```javascript
console.log('wsfColorTokens:', window.wsfColorTokens);
```

**Ожидаемый результат:**
- Должен появиться объект с токенами
- В объекте `tokens` должны быть input-токены:
  - `--wsf-surface`
  - `--wsf-input-bg`
  - `--wsf-input-text`
  - `--wsf-input-border`
  - `--wsf-input-placeholder`
  - `--wsf-input-focus`

### 5. Проверьте Theme Presets панель
- В правой части экрана найдите панель **Theme Presets**
- Нажмите **"Add New Theme"** или **"Edit"** на существующей теме

### 6. Проверьте наличие новых полей
В редакторе тем должны появиться новые поля в секции **Colors**:

**Ожидаемые новые поля:**
- ✅ **Surface** - Background for cards, inputs & form elements
- ✅ **Input Background** - Background color for select elements & inputs  
- ✅ **Input Text** - Text color inside select elements & inputs
- ✅ **Input Border** - Border color for select elements & inputs
- ✅ **Input Placeholder** - Placeholder text color in select elements
- ✅ **Input Focus** - Focus ring color for select elements & inputs

---

## 🐛 Если поля не появились

### Проверка 1: JavaScript ошибки
- Откройте **Console** в Developer Tools
- Ищите красные ошибки JavaScript
- Если есть ошибки, скопируйте их для отладки

### Проверка 2: Проверьте wsfColorTokens
Выполните в консоли:
```javascript
if (window.wsfColorTokens && window.wsfColorTokens.tokens) {
    const tokens = window.wsfColorTokens.tokens;
    const inputTokens = [
        '--wsf-surface',
        '--wsf-input-bg',
        '--wsf-input-text', 
        '--wsf-input-border',
        '--wsf-input-placeholder',
        '--wsf-input-focus'
    ];
    
    console.log('=== Input Tokens Check ===');
    inputTokens.forEach(token => {
        if (tokens[token]) {
            console.log('✅', token, ':', tokens[token]);
        } else {
            console.log('❌', token, ': Missing');
        }
    });
} else {
    console.log('❌ wsfColorTokens not available');
}
```

### Проверка 3: Fallback labels
Выполните в консоли:
```javascript
// Проверьте, что fallback labels включают input токены
console.log('ThemePresetsPanel available:', typeof window.ThemePresetsPanel !== 'undefined');
```

---

## 🎯 Ожидаемый результат

После исправлений в Theme Presets должны появиться **6 новых полей** для настройки селекторов:

### До (что было):
```
Colors
├── Background
├── Text  
├── Border
├── Primary
├── Hover State
├── Accent
├── Secondary
├── Muted
├── Success
├── Warning
└── Error
```

### После (что должно быть):
```
Colors
├── Background
├── Text
├── Border  
├── Primary
├── Hover State
├── Accent
├── Secondary
├── Muted
├── Surface ⭐ НОВОЕ
├── Input Background ⭐ НОВОЕ
├── Input Text ⭐ НОВОЕ
├── Input Border ⭐ НОВОЕ
├── Input Placeholder ⭐ НОВОЕ
├── Input Focus ⭐ НОВОЕ
├── Success
├── Warning
└── Error
```

---

## 🧪 Тестирование функциональности

### 1. Создайте тестовую тему
- Нажмите **"Add New Theme"**
- Установите разные цвета для:
  - **Text** (цвет лейблов) - например, черный
  - **Input Text** (цвет текста в селекторах) - например, синий
  - **Input Background** (фон селекторов) - например, светло-серый

### 2. Примените тему
- Сохраните тему
- Активируйте её

### 3. Проверьте результат
- Перейдите на страницу с виджетом
- Убедитесь, что:
  - Лейблы ("Make", "Model", "Year") имеют цвет из **Text**
  - Текст внутри селекторов имеет цвет из **Input Text**
  - Фон селекторов имеет цвет из **Input Background**
  - Цвета лейблов и селекторов **независимы** друг от друга

---

## 📞 Если проблемы остаются

### Возможные причины:
1. **Кэширование** - очистите все кэши
2. **JavaScript ошибки** - проверьте консоль
3. **Файлы не обновились** - проверьте, что изменения в ColorTokens.php сохранились
4. **Конфликт плагинов** - временно отключите другие плагины

### Файлы для проверки:
- `src/includes/ColorTokens.php` - должен содержать input-токены
- `assets/js/admin-theme-panel.js` - должен содержать fallback labels
- `src/includes/ThemeManager.php` - должен содержать input-токены в дефолтных темах

---

**✅ После выполнения этих шагов новые поля для настройки селекторов должны появиться в Theme Presets интерфейсе!**
