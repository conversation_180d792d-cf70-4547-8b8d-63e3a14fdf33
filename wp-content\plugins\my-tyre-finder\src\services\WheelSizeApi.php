<?php

declare(strict_types=1);

namespace MyTyreFinder\Services;

use MyTyreFinder\Services\CsvLoader;
use MyTyreFinder\Services\ApiStats;
use MyTyreFinder\Admin\LogsPage;

/**
 * Wheel-Size API v2 integration service
 */
final class WheelSizeApi
{
    private string $api_base_url = 'https://api.wheel-size.com/v2';
    private string $user_key;
    private int $search_cache_timeout = 7200; // Search results TTL (default 2 hours)
    
    public function __construct(?string $user_key = null)
    {
        // Attempt to include wheel-fit-config.php from various parent directories
        $potentialPaths = [];
        $baseDir = dirname(__DIR__, 3); // plugin root (plugins/my-tyre-finder)
        // Same directory as plugin root
        $potentialPaths[] = $baseDir . '/wheel-fit-config.php';
        // One level up (wp-content/plugins/.. -> wp-content)
        $potentialPaths[] = dirname($baseDir) . '/wheel-fit-config.php';
        // Two levels up (wp-content -> my-wp-docker root)
        $potentialPaths[] = dirname($baseDir, 2) . '/wheel-fit-config.php';
        // Project root (workspace root)
        $potentialPaths[] = dirname($baseDir, 3) . '/wheel-fit-config.php';

        foreach ($potentialPaths as $path) {
            if (file_exists($path)) {
                require_once $path;
                break;
            }
        }

        // Try new option name first, then legacy names for backward compatibility
        $this->user_key = $user_key
            ?? get_option('wheel_size_api_key', '')
            ?? get_option('wheel_fit_api_key', '')
            ?? (defined('WHEEL_SIZE_API_KEY') ? WHEEL_SIZE_API_KEY : (defined('WHEEL_FIT_API_KEY') ? WHEEL_FIT_API_KEY : ''));

        // Update cache timeout from WordPress option or constant (new then legacy)
        $wp_cache_timeout = get_option('wheel_size_search_cache_ttl', null);
        if ($wp_cache_timeout !== null) {
            $this->search_cache_timeout = max(0, (int)$wp_cache_timeout);
        } elseif (defined('WHEEL_SIZE_SEARCH_CACHE_TTL')) {
            $this->search_cache_timeout = WHEEL_SIZE_SEARCH_CACHE_TTL;
        } elseif (defined('WHEEL_FIT_CACHE_TIMEOUT')) {
            $this->search_cache_timeout = WHEEL_FIT_CACHE_TIMEOUT;
        }

        // Fallback key (for development) if still empty
        if (empty($this->user_key)) {
            $this->user_key = '7174c9ea41a8b5edf19b31c40f9eb889';
            $this->log('Wheel-Size Warning: using fallback API key');
        }
    }

    /**
     * Get list of makes
     * @param string[]|null $slugs Optional array of make slugs to fetch.
     * @param bool $apply_brand_filters Whether to apply include/exclude brand filters (default: true)
     */
    public function getMakes(?array $slugs = null, bool $apply_brand_filters = true): array
    {
        // Append region filters if configured in admin
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        sort($selected_regions);

        // Admin-defined brand include/exclude lists
        $include = array_filter((array) get_option('wheel_size_include_makes', []));
        $exclude = array_filter((array) get_option('wheel_size_exclude_makes', []));

        // Build cache key - only include regions and specific slugs, NOT brand filters
        // Brand filters will be applied locally to avoid empty API results
        $cache_key_parts = ['wheel_fit_makes'];
        if (!empty($selected_regions)) {
            $cache_key_parts[] = 'regions_' . md5(implode('|', $selected_regions));
        }
        if (!empty($slugs)) {
            sort($slugs);
            $cache_key_parts[] = 'slugs_' . md5(implode('|', $slugs));
        }
        // NOTE: We do NOT include brand include/exclude in cache key
        // This allows caching the full regional result and applying filters locally
        $cache_key = implode('_', $cache_key_parts);

        $cached = get_transient($cache_key);
        if ($cached !== false) {
            $this->log("Returning cached response for Wheel-Size makes with key: {$cache_key}");
            // Apply brand filters locally if requested
            if ($apply_brand_filters) {
                return $this->applyBrandFilters($cached);
            }
            return $cached;
        }

        // Build endpoint with optional parameters
        $endpoint = '/makes/';
        $query_parts = ['ordering=slug'];

        if (!empty($selected_regions)) {
            foreach ($selected_regions as $r) {
                $query_parts[] = 'region=' . rawurlencode($r);
            }
        }

        /**
         * CRITICAL FIX: Do NOT add brands= parameter if regional filters are active
         * This prevents empty results when combining region + brands
         *
         * Logic:
         * - region exists => get ALL brands of region, filter locally
         * - no region => allow narrowing via brands= (old behavior)
         */
        if (empty($selected_regions) && !empty($slugs)) {
            // Only add brands parameter when NO regional filters are set
            $query_parts[] = 'brands=' . implode(',', array_map('rawurlencode', $slugs));
        }

        // Brand include/exclude filters are always applied locally via applyBrandFilters()
        
        $endpoint .= '?' . implode('&', $query_parts);

        $response = $this->makeRequest($endpoint);
        
        if ($response && isset($response['data'])) {
            if (!empty($response['data'])) {
                set_transient($cache_key, $response['data'], DAY_IN_SECONDS * 30); // 30 days
            }
            // Apply brand include/exclude filters from admin settings (if requested)
            if ($apply_brand_filters) {
                $filtered_data = $this->applyBrandFilters($response['data']);
                return $filtered_data;
            } else {
                return $response['data'];
            }
        }

        /*
         * Fallback: if regional query returns empty results OR when specific slugs are requested
         * with regional filters, try without region filters
         */
        if ((!empty($selected_regions) && empty($response['data'])) ||
            (!empty($selected_regions) && !empty($slugs))) {

            $endpoint2 = '/makes/?ordering=slug';
            if (!empty($slugs)) {
                // When specific slugs are requested, always include them in fallback
                $endpoint2 .= '&brands=' . implode(',', array_map('rawurlencode', $slugs));
            }

            $response2 = $this->makeRequest($endpoint2);

            if ($response2 && isset($response2['data']) && !empty($response2['data'])) {
                // Cache fallback result for shorter time
                set_transient($cache_key . '_fallback', $response2['data'], MINUTE_IN_SECONDS * 30);
                if ($apply_brand_filters) {
                    $filtered2 = $this->applyBrandFilters($response2['data']);
                    return $filtered2;
                } else {
                    return $response2['data'];
                }
            }
        }

        return [];
    }

    /**
     * Get models for specific make
     */
    public function getModels(string $make_slug): array
    {
        // Apply region filters if configured in admin
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        sort($selected_regions);

        // Build a cache key that accounts for region filters
        $cache_key_parts = ['wheel_fit_models', $make_slug];
        if (!empty($selected_regions)) {
            $cache_key_parts[] = 'regions_' . md5(implode('|', $selected_regions));
        }
        $cache_key = implode('_', $cache_key_parts);

        $cached = get_transient($cache_key);

        if ($cached !== false) {
            // Cache hit (models) – skip ApiStats to avoid polluting search analytics
            $this->log("Returning cached response for Wheel-Size models with key: {$cache_key}");
            return $cached;
        }

        // Build endpoint with optional parameters
        $endpoint = "/models/?make={$make_slug}";
        if (!empty($selected_regions)) {
            foreach ($selected_regions as $r) {
                $endpoint .= '&region=' . rawurlencode($r);
            }
        }

        $response = $this->makeRequest($endpoint);

        if ($response && isset($response['data'])) {
            // Cache only when список не пустой – иначе пробуем заново при следующем запросе
            if (!empty($response['data'])) {
                set_transient($cache_key, $response['data'], DAY_IN_SECONDS * 30); // 30 days
            }
            return $response['data'];
        }

        return [];
    }

    /**
     * Get years for specific make and model
     */
    public function getYears(string $make_slug, string $model_slug): array
    {
        $cache_key = "wheel_fit_years_{$make_slug}_{$model_slug}";
        $cached = get_transient($cache_key);

        if ($cached !== false) {
            // Cache hit (years) – skip ApiStats
            $this->log("Returning cached response for Wheel-Size years with key: {$cache_key}");
            return $cached;
        }

        $response = $this->makeRequest("/years/?make={$make_slug}&model={$model_slug}");

        if ($response && isset($response['data'])) {
            // Cache only when список не пустой – иначе пробуем заново при следующем запросе
            if (!empty($response['data'])) {
                set_transient($cache_key, $response['data'], DAY_IN_SECONDS * 30); // 30 days
            }
            return $response['data'];
        }

        return [];
    }

    /**
     * Get generations for specific make and model
     */
    public function getGenerations(string $make_slug, string $model_slug): array
    {
        $cache_key = "wheel_fit_generations_{$make_slug}_{$model_slug}";
        $cached = get_transient($cache_key);

        if ($cached !== false) {
            // Cache hit (generations) – skip ApiStats
            $this->log("Returning cached response for Wheel-Size generations with key: {$cache_key}");
            return $cached;
        }

        $response = $this->makeRequest("/generations/?make={$make_slug}&model={$model_slug}");

        if ($response && isset($response['data'])) {
            // Cache only when список не пустой – иначе пробуем заново при следующем запросе
            if (!empty($response['data'])) {
                set_transient($cache_key, $response['data'], DAY_IN_SECONDS * 30); // 30 days
            }
            return $response['data'];
        }

        return [];
    }

    /**
     * Get all available years (for by_year flow)
     */
    public function getAllYears(): array
    {
        $cache_key = "wheel_fit_all_years";
        $cached = get_transient($cache_key);
        
        if ($cached !== false) {
            // Cache hit (all years) – skip ApiStats
            $this->log("Returning cached response for Wheel-Size all years with key: {$cache_key}");
            return $cached;
        }

        $response = $this->makeRequest("/years/");
        
        if ($response && isset($response['data'])) {
            // Cache only when список не пустой – иначе пробуем заново при следующем запросе
            if (!empty($response['data'])) {
                set_transient($cache_key, $response['data'], DAY_IN_SECONDS * 30); // 30 days
            }
            return $response['data'];
        }

        return [];
    }

    /**
     * Get modifications for specific vehicle
     */
    public function getModifications(string $make_slug, string $model_slug, int $year): array
    {
        // Apply region filters if configured in admin
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        sort($selected_regions);

        // Build a cache key that accounts for region filters
        $cache_key_parts = ['wheel_fit_modifications', $make_slug, $model_slug, $year];
        if (!empty($selected_regions)) {
            $cache_key_parts[] = 'regions_' . md5(implode('|', $selected_regions));
        }
        $cache_key = implode('_', $cache_key_parts);

        $cached = get_transient($cache_key);

        if ($cached !== false) {
            // Cache hit (modifications) – skip ApiStats
            $this->log("Returning cached response for Wheel-Size modifications with key: {$cache_key}");
            return $cached;
        }

        // Build endpoint with optional parameters
        $endpoint = "/modifications/?make={$make_slug}&model={$model_slug}&year={$year}";
        if (!empty($selected_regions)) {
            foreach ($selected_regions as $r) {
                $endpoint .= '&region=' . rawurlencode($r);
            }
        }

        $response = $this->makeRequest($endpoint);

        if ($response && isset($response['data'])) {
            // Cache only when список не пустой – иначе пробуем заново при следующем запросе
            if (!empty($response['data'])) {
                set_transient($cache_key, $response['data'], DAY_IN_SECONDS * 30); // 30 days
            }
            return $response['data'];
        }

        return [];
    }

    /**
     * Get modifications for specific make, model and generation
     */
    public function getModificationsByGeneration(string $make_slug, string $model_slug, string $generation_slug): array
    {
        // Apply region filters if configured in admin
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        sort($selected_regions);

        // Build a cache key that accounts for region filters
        $cache_key_parts = ['wheel_fit_modifications_gen', $make_slug, $model_slug, $generation_slug];
        if (!empty($selected_regions)) {
            $cache_key_parts[] = 'regions_' . md5(implode('|', $selected_regions));
        }
        $cache_key = implode('_', $cache_key_parts);

        $cached = get_transient($cache_key);

        if ($cached !== false) {
            // Cache hit (modifications gen) – skip ApiStats
            $this->log("Returning cached response for Wheel-Size modifications by generation with key: {$cache_key}");
            return $cached;
        }

        // Build endpoint with optional parameters
        $endpoint = "/modifications/?make={$make_slug}&model={$model_slug}&generation={$generation_slug}";
        if (!empty($selected_regions)) {
            foreach ($selected_regions as $r) {
                $endpoint .= '&region=' . rawurlencode($r);
            }
        }

        $response = $this->makeRequest($endpoint);

        if ($response && isset($response['data'])) {
            // Cache only when список не пустой – иначе пробуем заново при следующем запросе
            if (!empty($response['data'])) {
                set_transient($cache_key, $response['data'], DAY_IN_SECONDS * 30); // 30 days
            }
            return $response['data'];
        }

        return [];
    }

    /**
     * Search tire/wheel sizes by vehicle model
     */
    public function searchByModel(string $make_slug, string $model_slug, int $year = null, ?string $modification_slug = null, ?string $region = null, ?string $generation_slug = null): array
    {
        $params = [
            'make' => $make_slug,
            'model' => $model_slug,
        ];

        // Support both year-based and generation-based search
        if ($generation_slug) {
            $params['generation'] = $generation_slug;
        } elseif ($year) {
            $params['year'] = $year;
        }

        if ($modification_slug) {
            $params['modification'] = $modification_slug;
        }

        if ($region) {
            $params['region'] = $region;
        }

        $query_string = http_build_query($params);
        $cache_key = "wheel_fit_search_" . md5($query_string);
        $cached = $this->search_cache_timeout > 0 ? get_transient($cache_key) : false;

        if ($cached !== false) {
            ApiStats::record(['is_cache' => true]);
            $this->log("Returning cached response for Wheel-Size search with key: {$cache_key}");
            // Log even when served from cache (no rim/width for by-model)
            $this->logSearchStat($make_slug, $model_slug, null, null);
            return $cached;
        }

        $response = $this->makeRequest("/search/by_model/?{$query_string}");

        if ($response && isset($response['data'])) {
            // Log successful search
            if (!empty($response['data'])) {
                // Record make/model without sizes
                $this->logSearchStat($make_slug, $model_slug, null, null);
            }
            $grouped_data = $this->groupResultsByStock($response['data']);
            if ($this->search_cache_timeout > 0) {
                set_transient($cache_key, $grouped_data, $this->search_cache_timeout);
            }
            return $grouped_data;
        }

        return ['factory' => [], 'optional' => []];
    }

    /**
     * Search vehicles by tire size.
     *
     * @param int   $width    Tire section width in mm (e.g., 225)
     * @param int   $profile  Tire aspect ratio (e.g., 55)
     * @param float $diameter Rim diameter in inches (e.g., 18)
     * @return array
     */
    public function searchByTire(int $width, int $profile, float $diameter): array
    {
        $params = [
            'section_width' => $width,
            'aspect_ratio'  => $profile,
            'rim_diameter'  => $diameter,
        ];

        $query_string = http_build_query($params);
        $cache_key    = 'wheel_fit_tire_search_' . md5($query_string);
        $cached       = $this->search_cache_timeout > 0 ? get_transient($cache_key) : false;

        if ($cached !== false) {
            ApiStats::record(['is_cache' => true]);
            $this->log("Returning cached response for Wheel-Size tire search with key: {$cache_key}");
            // Log cached tire search
            $this->logSearchStat(null, null, $diameter, $width);
            return $cached;
        }

        $response = $this->makeRequest("/by_tire/search/?{$query_string}");

        $data_to_return = [];
        if (isset($response['data']) && is_array($response['data'])) {
            $data_to_return = $response['data'];
        } elseif (is_array($response) && !isset($response['errors'])) { // Basic check to not cache errors
            $data_to_return = $response;
        }

        if ($this->search_cache_timeout > 0) {
            set_transient($cache_key, $data_to_return, $this->search_cache_timeout);
        }

        if (!empty($data_to_return)) {
            $this->logSearchStat(null, null, $diameter, $width);
        }

        return $data_to_return;
    }

    /**
     * Group results by stock/optional status
     */
    private function groupResultsByStock(array $data): array
    {
        $factory = [];
        $optional = [];

        // API v2 returns modifications with wheels array inside
        foreach ($data as $modification) {
            if (isset($modification['wheels']) && is_array($modification['wheels'])) {
                foreach ($modification['wheels'] as $wheel) {
                    // Extract wheel data with additional info
                    $wheelData = [
                        'tire_full' => $wheel['front']['tire'] ?? '',
                        'rim' => $wheel['front']['rim'] ?? '',
                        'rim_diameter' => $wheel['front']['rim_diameter'] ?? '',
                        'rim_width' => $wheel['front']['rim_width'] ?? '',
                        'rim_offset' => $wheel['front']['rim_offset'] ?? '',
                        'tire_pressure' => $wheel['front']['tire_pressure'] ?? null,
                        'is_stock' => $wheel['is_stock'] ?? false,
                        'showing_fp_only' => $wheel['showing_fp_only'] ?? true,
                        // Add rear wheel info if different
                        'rear_tire_full' => $wheel['rear']['tire'] ?? '',
                        'rear_rim' => $wheel['rear']['rim'] ?? '',
                        // Add technical info
                        'bolt_pattern' => $modification['technical']['bolt_pattern'] ?? '',
                        'pcd' => $modification['technical']['pcd'] ?? '',
                        'centre_bore' => $modification['technical']['centre_bore'] ?? '',
                        // Vehicle info
                        'make' => $modification['make']['name'] ?? '',
                        'model' => $modification['model']['name'] ?? '',
                        'generation' => $modification['generation']['name'] ?? '',
                        'modification' => $modification['name'] ?? ''
                    ];
                    
                    $isStock = $wheel['is_stock'] ?? false;
                    if ($isStock === true || $isStock === 1 || $isStock === '1') {
                        $factory[] = $wheelData;
                    } else {
                        $optional[] = $wheelData;
                    }
                }
            }
        }

        return [
            'factory' => $factory,
            'optional' => $optional
        ];
    }

    /**
     * Make HTTP request to API
     */
    private function makeRequest(string $endpoint): ?array
    {
        if (empty($this->user_key)) {
            $this->log('Wheel-Size API: user_key not configured');
            // Do not record stats for internal configuration errors
            return null;
        }

        $url = $this->api_base_url . $endpoint;
        $url .= (strpos($url, '?') !== false ? '&' : '?') . 'user_key=' . $this->user_key;

        // --- Logging (outgoing request) ------------------------------
        $masked_url = preg_replace('/user_key=[^&]+/i','user_key=***',$url);
        $this->log('Wheel-Size Request: GET ' . $masked_url);

        $response = wp_remote_get($url, [
            'timeout' => 30,
            'headers' => [
                'Accept' => 'application/json',
                'User-Agent' => 'WordPress-WheelFit-Plugin/1.0'
            ]
        ]);

        // Record API call statistics
        ApiStats::record(['response' => $response]);

        if (is_wp_error($response)) {
            $this->log('Wheel-Size API Error: ' . $response->get_error_message());
            return null;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code !== 200) {
            $this->log("Wheel-Size API HTTP Error: {$status_code} URL: {$masked_url}");
            return null;
        }

        // Log success with masked URL & status
        $this->log("Wheel-Size Response: {$status_code} {$masked_url}");

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log('Wheel-Size API JSON Error: ' . json_last_error_msg());
            return null;
        }

        return $data;
    }

    /**
     * Fallback to CSV data when API is unavailable
     */
    public function getFallbackData(string $make = '', string $model = '', int $year = 0): array
    {
        $csv_loader = new CsvLoader();
        $csv_data = $csv_loader->findByParams($make, $model, (string)$year);
        
        // Convert CSV format to API format
        $converted = [];
        foreach ($csv_data as $row) {
            $converted[] = [
                'tire_full' => $row['Tyre1'] ?? '',
                'rim' => $row['Wheel1'] ?? '',
                'pcd' => $row['PCD'] ?? '',
                'is_stock' => true, // Assume CSV data is stock
                'make' => ['name' => $row['Make'] ?? ''],
            ];
        }

        return $this->groupResultsByStock($converted);
    }

    /**
     * Apply brand include/exclude filters from admin settings
     */
    private function applyBrandFilters(array $makes): array
    {
        // Retrieve admin-defined include/exclude lists
        $include = array_filter((array) get_option('wheel_size_include_makes', []));
        $exclude = array_filter((array) get_option('wheel_size_exclude_makes', []));

        // If include list is specified, take intersection; otherwise use full list
        if (!empty($include)) {
            $makes = array_values(array_filter($makes, static fn($m) => in_array($m['slug'] ?? '', $include, true)));
        }

        // Always remove excluded slugs (they can't be in include per UI validation)
        if (!empty($exclude)) {
            $makes = array_values(array_filter($makes, static fn($m) => !in_array($m['slug'] ?? '', $exclude, true)));
        }

        return $makes;
    }

    public function getTireWidths(): array
    {
        return $this->getTireAttribute('/by_tire/sw/');
    }

    public function getTireAspectRatios(?int $width = null): array
    {
        $params = [];
        if ($width) {
            $params['section_width'] = $width;
        }
        return $this->getTireAttribute('/by_tire/ar/', $params);
    }

    public function getTireRimDiameters(?int $width = null, ?int $profile = null): array
    {
        $params = [];
        if ($width) {
            $params['section_width'] = $width;
        }
        if ($profile) {
            $params['aspect_ratio'] = $profile;
        }
        return $this->getTireAttribute('/by_tire/rd/', $params);
    }

    /**
     * Helper to fetch and format tire attribute lists (width, profile, diameter)
     */
    private function getTireAttribute(string $endpoint, array $params = []): array
    {
        // Append region filter if set
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        sort($selected_regions);

        $query_extra = [];
        if (!empty($params)) {
            $query_extra[] = http_build_query($params);
        }
        foreach ($selected_regions as $r) {
            $query_extra[] = 'region=' . rawurlencode($r);
        }

        $full_endpoint = $endpoint;
        if (!empty($query_extra)) {
            $full_endpoint .= '?' . implode('&', $query_extra);
        }
        
        $response = $this->makeRequest($full_endpoint);

        $list = [];
        // The API response can be a direct array or wrapped in a 'data' object.
        if (isset($response['data']) && is_array($response['data'])) {
            $list = $response['data'];
        } elseif (is_array($response)) {
            $list = $response;
        }

        if (empty($list)) {
            return [];
        }

        // API returns e.g. [{ "value": 185, "total": 12 }, ... ]
        // Frontend expects [{ "name": "185", "slug": "185" }, ... ]
        $mapped = array_map(static function ($item) {
            if (!isset($item['value'])) {
                return null;
            }
            return [
                'name' => (string) $item['value'],
                'slug' => (string) $item['value'],
            ];
        }, $list);

        // Remove any nulls from malformed items
        return array_values(array_filter($mapped));
    }

    /**
     * Get makes for a specific year (byYear flow)
     * @param bool $apply_brand_filters Whether to apply include/exclude brand filters (default: true)
     */
    public function getMakesByYear(int $year, bool $apply_brand_filters = true): array {
        // Apply region filters if configured in admin
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        sort($selected_regions);

        // Admin-defined brand include/exclude lists
        $include = array_filter((array) get_option('wheel_size_include_makes', []));
        $exclude = array_filter((array) get_option('wheel_size_exclude_makes', []));

        // Build cache key - only include regions and year, NOT brand filters
        $cache_key_parts = ['wheel_fit_makes_year', $year];
        if (!empty($selected_regions)) {
            $cache_key_parts[] = 'regions_' . md5(implode('|', $selected_regions));
        }
        // NOTE: We do NOT include brand include/exclude in cache key
        $cache_key = implode('_', $cache_key_parts);

        // Check cache first
        if ($cached = get_transient($cache_key)) {
            $this->log("Returning cached makes for year {$year}");
            // Apply brand filters locally if requested
            if ($apply_brand_filters) {
                return $this->applyBrandFilters($cached);
            }
            return $cached;
        }

        // Build endpoint with optional parameters
        $endpoint = "/makes/?year={$year}&ordering=slug";
        $query_parts = [];

        if (!empty($selected_regions)) {
            foreach ($selected_regions as $r) {
                $query_parts[] = 'region=' . rawurlencode($r);
            }
        }

        // Note: We don't add brand filters to the API query when regions are set
        // Instead, we fetch ALL brands for the region/year and apply filters locally
        // This allows users to see all available brands and add new ones to include list

        if (!empty($query_parts)) {
            $endpoint .= '&' . implode('&', $query_parts);
        }

        $response = $this->makeRequest($endpoint);

        if ($response && isset($response['data']) && !empty($response['data'])) {
            if (!empty($response['data'])) {
                set_transient($cache_key, $response['data'], DAY_IN_SECONDS * 30); // 30 days
            }
            // Apply brand include/exclude filters from admin settings (if requested)
            if ($apply_brand_filters) {
                $filtered_data = $this->applyBrandFilters($response['data']);
                return $filtered_data;
            } else {
                return $response['data'];
            }
        }

        /*
         * Fallback: if regional query returns empty results, try without region filters
         * This can happen when the selected regions don't have any brands for this year
         */
        if (!empty($selected_regions) && empty($response['data'])) {
            $endpoint2 = "/makes/?year={$year}&ordering=slug";

            $response2 = $this->makeRequest($endpoint2);

            if ($response2 && isset($response2['data']) && !empty($response2['data'])) {
                // Cache fallback result for shorter time
                set_transient($cache_key . '_fallback', $response2['data'], MINUTE_IN_SECONDS * 30);
                if ($apply_brand_filters) {
                    $filtered2 = $this->applyBrandFilters($response2['data']);
                    return $filtered2;
                } else {
                    return $response2['data'];
                }
            }
        }

        return [];
    }
    /**
     * Get models for a specific year and make (byYear flow)
     */
    public function getModelsByYear(int $year, string $make): array {
        // Apply region filters if configured in admin
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        sort($selected_regions);

        // Build a cache key that accounts for region filters
        $cache_key_parts = ['models', $year, $make];
        if (!empty($selected_regions)) {
            $cache_key_parts[] = 'regions_' . md5(implode('|', $selected_regions));
        }
        $cache_key = implode('_', $cache_key_parts);

        // Check cache first
        if ($data = get_transient($cache_key)) {
            $this->log("Returning cached models for {$make} year {$year} with filters");
            return $data;
        }

        // Build endpoint with optional parameters
        $endpoint = "/models/?make={$make}&year={$year}";
        if (!empty($selected_regions)) {
            foreach ($selected_regions as $r) {
                $endpoint .= '&region=' . rawurlencode($r);
            }
        }

        $resp = $this->makeRequest($endpoint);
        $data = $resp['data'] ?? [];
        if ($data) set_transient($cache_key, $data, DAY_IN_SECONDS*30);
        return $data;
    }
    /**
     * Get modifications for a specific year, make, and model (byYear flow)
     */
    public function getModificationsByYear(int $year, string $make, string $model): array {
        // Apply region filters if configured in admin
        $selected_regions = array_filter((array) get_option('wheel_size_regions', []));
        sort($selected_regions);

        // Build a cache key that accounts for region filters
        $cache_key_parts = ['mods', $year, $make, $model];
        if (!empty($selected_regions)) {
            $cache_key_parts[] = 'regions_' . md5(implode('|', $selected_regions));
        }
        $cache_key = implode('_', $cache_key_parts);

        // Check cache first
        if ($data = get_transient($cache_key)) {
            $this->log("Returning cached modifications for {$make} {$model} year {$year} with filters");
            return $data;
        }

        // Build endpoint with optional parameters
        $endpoint = "/modifications/?make={$make}&model={$model}&year={$year}";
        if (!empty($selected_regions)) {
            foreach ($selected_regions as $r) {
                $endpoint .= '&region=' . rawurlencode($r);
            }
        }

        $resp = $this->makeRequest($endpoint);
        $data = $resp['data'] ?? [];
        if ($data) set_transient($cache_key, $data, DAY_IN_SECONDS*30);
        return $data;
    }
    /**
     * Small utility to avoid code duplication for caching
     */
    private function cached(string $key, string $endpoint): array {
        if ($data = get_transient($key)) {
            $this->log("Returning cached {$key}");
            return $data;
        }
        $resp = $this->makeRequest($endpoint);
        $data = $resp['data'] ?? [];
        if ($data) set_transient($key, $data, DAY_IN_SECONDS*30);
        return $data;
    }

    /**
     * Insert search analytics row
     */
    private function logSearchStat(?string $make, ?string $model, ?float $rim, ?int $width): void
    {
        global $wpdb;
        $table = $wpdb->prefix . 'wsf_search_stats';
        $wpdb->insert(
            $table,
            [
                'searched_at' => current_time('mysql', 1),
                'make'  => $make ?? '',
                'model' => $model ?? '',
                'rim'   => $rim ? (int)$rim : 0,
                'width' => $width ?? 0,
            ],
            ['%s','%s','%s','%d','%d']
        );
    }

    /** Записывает строку в debug.log и в собственный лог-файл */
    private function log(string $message): void
    {
        // а) привычная отладка
        error_log($message);

        // б) файл под плагином
        $dir = LogsPage::LOG_DIR;                // 2) единая точка
        if (!is_dir($dir)) {
            @mkdir($dir, 0775, true);
        }
        $file = $dir . '/wheel-size-' . date('Y-m-d') . '.log';
        @file_put_contents($file, date('c') . ' — ' . $message . PHP_EOL, FILE_APPEND);
    }
} 