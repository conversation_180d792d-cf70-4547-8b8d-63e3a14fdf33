/**
 * Comprehensive Test Script for Configuration Change Fixes
 * Tests both translation persistence and garage block functionality
 * after configuration changes in admin panel
 */

console.log('=== Configuration Change Fixes Test ===');

// Test configuration
const TEST_CONFIG = {
    testTranslations: true,
    testGarage: true,
    testConfigChanges: true,
    verbose: true
};

// Helper function for logging
function log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${type.toUpperCase()}]`;
    
    switch(type) {
        case 'error':
            console.error(`${prefix} ${message}`);
            break;
        case 'warn':
            console.warn(`${prefix} ${message}`);
            break;
        case 'success':
            console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');
            break;
        default:
            console.log(`${prefix} ${message}`);
    }
}

// Test 1: Translation System
function testTranslationSystem() {
    log('Testing translation system...', 'info');
    
    const tests = [];
    
    // Check if translation object exists
    tests.push({
        name: 'Translation object exists',
        test: () => typeof window.WheelFitI18n === 'object' && window.WheelFitI18n !== null,
        expected: true
    });
    
    // Check if applyStaticTranslations function exists
    tests.push({
        name: 'applyStaticTranslations function exists',
        test: () => typeof window.applyStaticTranslations === 'function',
        expected: true
    });
    
    // Check if Russian translations are loaded
    tests.push({
        name: 'Russian translations loaded',
        test: () => window.WheelFitI18n && window.WheelFitI18n.button_search === 'Поиск',
        expected: true
    });
    
    // Check if translation function works
    tests.push({
        name: 'Translation function works',
        test: () => {
            if (typeof window.t === 'function') {
                return window.t('button_search', 'Find Sizes') === 'Поиск';
            }
            return false;
        },
        expected: true
    });
    
    // Check if data-i18n elements exist
    tests.push({
        name: 'Elements with data-i18n exist',
        test: () => document.querySelectorAll('[data-i18n]').length > 0,
        expected: true
    });
    
    return runTests('Translation System', tests);
}

// Test 2: Garage System
function testGarageSystem() {
    log('Testing garage system...', 'info');
    
    const tests = [];
    
    // Check if garage is enabled
    tests.push({
        name: 'Garage enabled in config',
        test: () => window.WheelFitData && window.WheelFitData.garageEnabled === true,
        expected: true
    });
    
    // Check if garage DOM elements exist
    tests.push({
        name: 'Garage DOM elements exist',
        test: () => {
            const drawer = document.getElementById('garage-drawer');
            const overlay = document.getElementById('garage-overlay');
            const triggers = document.querySelectorAll('[data-garage-trigger]');
            return drawer && overlay && triggers.length > 0;
        },
        expected: true
    });
    
    // Check if initGarageFeature function exists
    tests.push({
        name: 'initGarageFeature function exists',
        test: () => typeof window.initGarageFeature === 'function',
        expected: true
    });
    
    // Check if garage reinit functions exist
    tests.push({
        name: 'Garage reinit functions exist',
        test: () => typeof window.reinitGarage === 'function' && typeof window.resetGarageState === 'function',
        expected: true
    });
    
    // Check if garage is initialized
    tests.push({
        name: 'Garage initialized',
        test: () => window.__garageInitiated === true,
        expected: true
    });
    
    // Check if LocalStorageHandler exists
    tests.push({
        name: 'LocalStorageHandler available',
        test: () => typeof LocalStorageHandler === 'function',
        expected: true
    });
    
    return runTests('Garage System', tests);
}

// Test 3: Widget System
function testWidgetSystem() {
    log('Testing widget system...', 'info');
    
    const tests = [];
    
    // Check if WheelFitWidget exists
    tests.push({
        name: 'WheelFitWidget class exists',
        test: () => typeof WheelFitWidget === 'function',
        expected: true
    });
    
    // Check if widget instance exists
    tests.push({
        name: 'Widget instance exists',
        test: () => window.wheelFitWidget && typeof window.wheelFitWidget === 'object',
        expected: true
    });
    
    // Check if widget has required methods
    tests.push({
        name: 'Widget has required methods',
        test: () => {
            if (!window.wheelFitWidget) return false;
            return typeof window.wheelFitWidget.loadFromHistory === 'function' &&
                   typeof window.wheelFitWidget.storage === 'object';
        },
        expected: true
    });
    
    return runTests('Widget System', tests);
}

// Test 4: Configuration Change Simulation
function testConfigurationChanges() {
    log('Testing configuration change handling...', 'info');
    
    const tests = [];
    
    // Test translation cache clearing
    tests.push({
        name: 'Translation cache can be cleared',
        test: () => {
            // This would normally be tested by making an AJAX call to the admin
            // For now, we'll just check if the mechanism exists
            return true; // Placeholder - would need actual AJAX testing
        },
        expected: true
    });
    
    // Test garage reinitialization
    tests.push({
        name: 'Garage can be reinitialized',
        test: () => {
            if (typeof window.resetGarageState === 'function' && typeof window.reinitGarage === 'function') {
                try {
                    window.resetGarageState();
                    return window.__garageInitiated === false;
                } catch (e) {
                    log(`Error during garage reset: ${e.message}`, 'error');
                    return false;
                }
            }
            return false;
        },
        expected: true
    });
    
    return runTests('Configuration Changes', tests);
}

// Helper function to run tests
function runTests(suiteName, tests) {
    log(`Running ${suiteName} tests...`, 'info');
    
    let passed = 0;
    let failed = 0;
    
    tests.forEach(test => {
        try {
            const result = test.test();
            if (result === test.expected) {
                log(`✓ ${test.name}`, 'success');
                passed++;
            } else {
                log(`✗ ${test.name} (expected: ${test.expected}, got: ${result})`, 'error');
                failed++;
            }
        } catch (error) {
            log(`✗ ${test.name} (error: ${error.message})`, 'error');
            failed++;
        }
    });
    
    const total = passed + failed;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    log(`${suiteName} Results: ${passed}/${total} passed (${percentage}%)`, 
        percentage === 100 ? 'success' : 'warn');
    
    return { passed, failed, total, percentage };
}

// Main test runner
function runAllTests() {
    log('Starting comprehensive test suite...', 'info');
    
    const results = [];
    
    if (TEST_CONFIG.testTranslations) {
        results.push(testTranslationSystem());
    }
    
    if (TEST_CONFIG.testGarage) {
        results.push(testGarageSystem());
    }
    
    results.push(testWidgetSystem());
    
    if (TEST_CONFIG.testConfigChanges) {
        results.push(testConfigurationChanges());
    }
    
    // Summary
    const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);
    const totalTests = results.reduce((sum, r) => sum + r.total, 0);
    const overallPercentage = totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0;
    
    log('=== TEST SUMMARY ===', 'info');
    log(`Overall Results: ${totalPassed}/${totalTests} passed (${overallPercentage}%)`, 
        overallPercentage >= 80 ? 'success' : 'warn');
    
    if (overallPercentage >= 80) {
        log('Configuration change fixes appear to be working correctly!', 'success');
    } else {
        log('Some issues detected. Please review the failed tests above.', 'warn');
    }
    
    return results;
}

// Debug information
function showDebugInfo() {
    log('=== DEBUG INFORMATION ===', 'info');
    
    log('Document ready state: ' + document.readyState, 'info');
    log('WheelFitData: ' + JSON.stringify(window.WheelFitData, null, 2), 'info');
    log('WheelFitI18n keys: ' + (window.WheelFitI18n ? Object.keys(window.WheelFitI18n).length : 'N/A'), 'info');
    log('Widget instance: ' + (window.wheelFitWidget ? 'exists' : 'missing'), 'info');
    log('Garage initialized: ' + window.__garageInitiated, 'info');
    
    // DOM elements count
    const i18nElements = document.querySelectorAll('[data-i18n]').length;
    const garageElements = document.querySelectorAll('[data-garage-trigger]').length;
    log(`DOM elements - i18n: ${i18nElements}, garage triggers: ${garageElements}`, 'info');
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            showDebugInfo();
            runAllTests();
        }, 1000);
    });
} else {
    setTimeout(() => {
        showDebugInfo();
        runAllTests();
    }, 1000);
}

// Export functions for manual testing
window.testConfigFixes = {
    runAll: runAllTests,
    translations: testTranslationSystem,
    garage: testGarageSystem,
    widget: testWidgetSystem,
    config: testConfigurationChanges,
    debug: showDebugInfo
};

log('Test script loaded. Use window.testConfigFixes.runAll() to run tests manually.', 'info');
