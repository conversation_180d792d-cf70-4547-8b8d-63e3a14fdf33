/**
 * Тест исправления двойного перевода
 * Проверяет, что Translation Manager не перезаписывает уже правильно переведенный контент
 */

(function() {
    'use strict';

    console.log('[Double Translation Fix Test] Инициализация теста');

    // Тестовые переводы
    const testTranslations = {
        ru: {
            'widget_title': 'Подбор шин и дисков',
            'label_make': 'Марка',
            'label_model': 'Модель',
            'select_make_placeholder': 'Выберите марку',
            'select_model_placeholder': 'Выберите модель',
            'button_search': 'Подобрать размеры'
        },
        en: {
            'widget_title': 'Wheel & Tyre Finder',
            'label_make': 'Make',
            'label_model': 'Model',
            'select_make_placeholder': 'Choose a make',
            'select_model_placeholder': 'Choose a model',
            'button_search': 'Find Tire & Wheel Sizes'
        }
    };

    // Функция создания тестового виджета
    function createTestWidget() {
        const container = document.getElementById('test-widget-container') || document.createElement('div');
        container.id = 'test-widget-container';
        container.innerHTML = `
            <div class="wsf-finder-widget" data-wsf-theme="default">
                <h2 data-i18n="widget_title">Подбор шин и дисков</h2>
                <form>
                    <div class="form-group">
                        <label for="test-make" data-i18n="label_make">Марка</label>
                        <select id="test-make" name="make">
                            <option value="" data-i18n="select_make_placeholder">Выберите марку</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="test-model" data-i18n="label_model">Модель</label>
                        <select id="test-model" name="model">
                            <option value="" data-i18n="select_model_placeholder">Выберите модель</option>
                        </select>
                    </div>
                    <button type="submit" data-i18n="button_search">Подобрать размеры</button>
                </form>
            </div>
        `;
        
        if (!document.getElementById('test-widget-container')) {
            document.body.appendChild(container);
        }
        
        return container;
    }

    // Функция проверки состояния переводов
    function checkTranslationState(container, expectedLang = 'ru') {
        const results = {
            correct: 0,
            incorrect: 0,
            details: []
        };

        const expectedTranslations = testTranslations[expectedLang];
        const elementsToCheck = [
            { selector: '[data-i18n="widget_title"]', key: 'widget_title' },
            { selector: '[data-i18n="label_make"]', key: 'label_make' },
            { selector: '[data-i18n="label_model"]', key: 'label_model' },
            { selector: '[data-i18n="select_make_placeholder"]', key: 'select_make_placeholder' },
            { selector: '[data-i18n="select_model_placeholder"]', key: 'select_model_placeholder' },
            { selector: '[data-i18n="button_search"]', key: 'button_search' }
        ];

        elementsToCheck.forEach(({ selector, key }) => {
            const element = container.querySelector(selector);
            if (element) {
                const currentText = element.textContent.trim();
                const expectedText = expectedTranslations[key];
                const isCorrect = currentText === expectedText;
                
                if (isCorrect) {
                    results.correct++;
                } else {
                    results.incorrect++;
                }
                
                results.details.push({
                    selector,
                    key,
                    currentText,
                    expectedText,
                    isCorrect
                });
            }
        });

        return results;
    }

    // Основная функция тестирования
    function testDoubleTranslationFix() {
        console.log('[Double Translation Test] Начинаем тест...');
        
        // 1. Создаем виджет с русскими переводами (как будто сервер отдал)
        const container = createTestWidget();
        console.log('[Double Translation Test] Создан тестовый виджет с русскими переводами');
        
        // 2. Проверяем начальное состояние
        const initialState = checkTranslationState(container, 'ru');
        console.log('[Double Translation Test] Начальное состояние:', initialState);
        
        // 3. Устанавливаем русские переводы в глобальный объект
        window.WheelFitI18n = testTranslations.ru;
        console.log('[Double Translation Test] Установлены русские переводы в window.WheelFitI18n');
        
        // 4. Применяем переводы через Translation Manager
        if (window.translationManager && typeof window.translationManager.applyTranslations === 'function') {
            console.log('[Double Translation Test] Применяем переводы через Translation Manager...');
            const result = window.translationManager.applyTranslations(container);
            console.log('[Double Translation Test] Результат применения:', result);
        } else {
            console.warn('[Double Translation Test] Translation Manager недоступен');
        }
        
        // 5. Проверяем состояние после применения
        const afterState = checkTranslationState(container, 'ru');
        console.log('[Double Translation Test] Состояние после применения:', afterState);
        
        // 6. Анализируем результаты
        const wasOverwritten = initialState.correct > afterState.correct;
        const stayedCorrect = initialState.correct === afterState.correct && afterState.correct === afterState.details.length;
        
        console.log('[Double Translation Test] Анализ результатов:');
        console.log('- Было правильных переводов:', initialState.correct);
        console.log('- Стало правильных переводов:', afterState.correct);
        console.log('- Переводы были перезаписаны:', wasOverwritten);
        console.log('- Переводы остались корректными:', stayedCorrect);
        
        if (stayedCorrect) {
            console.log('✅ [Double Translation Test] ТЕСТ ПРОЙДЕН: Translation Manager не перезаписал правильные переводы');
        } else if (wasOverwritten) {
            console.error('❌ [Double Translation Test] ТЕСТ НЕ ПРОЙДЕН: Translation Manager перезаписал правильные переводы');
        } else {
            console.warn('⚠️ [Double Translation Test] ТЕСТ ЧАСТИЧНО ПРОЙДЕН: Некоторые переводы были исправлены');
        }
        
        // 7. Детальный отчет
        console.log('[Double Translation Test] Детальный отчет:');
        afterState.details.forEach(detail => {
            const status = detail.isCorrect ? '✅' : '❌';
            console.log(`${status} ${detail.key}: "${detail.currentText}" (ожидался: "${detail.expectedText}")`);
        });
        
        return {
            passed: stayedCorrect,
            initialState,
            afterState,
            wasOverwritten
        };
    }

    // Функция тестирования с английскими переводами
    function testWithEnglishOverwrite() {
        console.log('[Double Translation Test] Тестируем перезапись английскими переводами...');
        
        // 1. Создаем виджет с русскими переводами
        const container = createTestWidget();
        
        // 2. Устанавливаем английские переводы (симулируем проблему)
        window.WheelFitI18n = testTranslations.en;
        console.log('[Double Translation Test] Установлены английские переводы в window.WheelFitI18n');
        
        // 3. Применяем переводы
        if (window.translationManager && typeof window.translationManager.applyTranslations === 'function') {
            const result = window.translationManager.applyTranslations(container);
            console.log('[Double Translation Test] Результат применения английских переводов:', result);
        }
        
        // 4. Проверяем, что произошло
        const finalState = checkTranslationState(container, 'en');
        console.log('[Double Translation Test] Финальное состояние (английский):', finalState);
        
        return finalState;
    }

    // Глобальные функции для ручного тестирования
    window.testDoubleTranslationFix = testDoubleTranslationFix;
    window.testWithEnglishOverwrite = testWithEnglishOverwrite;
    window.createTestWidget = createTestWidget;
    window.checkTranslationState = checkTranslationState;

    // Автоматический запуск теста через 3 секунды
    setTimeout(() => {
        console.log('[Double Translation Test] Запуск автоматического теста...');
        testDoubleTranslationFix();
    }, 3000);

    console.log('[Double Translation Fix Test] Тест загружен. Доступные функции:');
    console.log('- testDoubleTranslationFix() - основной тест');
    console.log('- testWithEnglishOverwrite() - тест перезаписи английским');
    console.log('- createTestWidget() - создать тестовый виджет');
    console.log('- checkTranslationState(container, lang) - проверить состояние переводов');

})();
