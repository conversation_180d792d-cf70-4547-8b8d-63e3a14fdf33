<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Fix Test - TASK-FONT-FIX</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: 'Times New Roman', serif; /* Intentionally different to test isolation */
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            font-family: 'Times New Roman', serif; /* Page uses serif */
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            font-size: 20px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-top: 25px;
        }
        
        .comparison-item {
            padding: 20px;
            border: 2px solid #d1d5db;
            border-radius: 10px;
            background: white;
        }
        
        .comparison-item h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: #374151;
        }
        
        .before {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .demo-widget {
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border: 2px solid #e5e7eb;
        }
        
        /* Before: Mixed fonts (page serif + some Inter) */
        .before-widget {
            font-family: 'Times New Roman', serif; /* Page font leaks in */
            background: var(--wsf-bg);
            color: var(--wsf-text-primary);
        }
        
        .before-widget select {
            font-family: 'Inter', sans-serif; /* Only selects have Inter */
        }
        
        /* After: Consistent Inter font */
        .after-widget {
            /* Uses wsf-font utility class */
            background: var(--wsf-bg);
            color: var(--wsf-text-primary);
        }
        
        .demo-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .demo-field {
            display: flex;
            flex-direction: column;
        }
        
        .demo-label {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 5px;
            color: var(--wsf-text);
        }
        
        .demo-select {
            height: 44px;
            padding: 0 12px;
            border: 1px solid var(--wsf-input-border);
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            background: var(--wsf-input-bg);
            color: var(--wsf-input-text);
        }
        
        .demo-button {
            height: 44px;
            padding: 0 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--wsf-primary) !important;
            color: var(--wsf-text-inverse) !important;
        }
        
        .theme-switcher {
            margin: 20px 0;
            text-align: center;
        }
        
        .theme-switcher button {
            margin: 0 10px;
            padding: 10px 20px;
            border: 2px solid #d1d5db;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 600;
        }
        
        .theme-switcher button:hover {
            background: #f3f4f6;
        }
        
        .theme-switcher button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .explanation {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .explanation h4 {
            margin-top: 0;
            color: #0c4a6e;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .fix-list {
            list-style: none;
            padding: 0;
        }
        
        .fix-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .fix-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        
        .font-demo {
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: white;
        }
        
        .font-demo h5 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .font-demo p {
            margin: 5px 0;
            font-size: 13px;
        }
        
        .font-info {
            font-size: 11px;
            color: #6b7280;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔤 Font Fix Test - TASK-FONT-FIX</h1>
        <p>Проверка единого шрифта Inter для всего виджета (страница использует Times New Roman для контраста)</p>
        
        <div class="theme-switcher">
            <button onclick="setTheme('light')" class="active" id="light-btn">Light Theme</button>
            <button onclick="setTheme('dark')" id="dark-btn">Dark Theme</button>
        </div>
        
        <!-- Problem Description -->
        <div class="explanation">
            <h4>🔍 Проблема</h4>
            <p><strong>До исправления:</strong> Часть текста (лейблы, заголовки, результаты) использовала шрифт темы WordPress/Tailwind, а не Inter</p>
            <p><strong>Причина:</strong> CSS правила с font-family переопределялись утилитами/стилями темы</p>
            <p><strong>Решение:</strong> Создан токен --wsf-font-base и утилита .wsf-font с !important</p>
        </div>
        
        <!-- Applied Fixes -->
        <div class="test-section">
            <h3>Применённые исправления</h3>
            <ul class="fix-list">
                <li><strong>CSS переменные:</strong> Добавлены --wsf-font-base и --wsf-font-variable</li>
                <li><strong>Утилита wsf-font:</strong> Создан класс с !important для переопределения</li>
                <li><strong>Шаблоны:</strong> Добавлен класс wsf-font во все корневые контейнеры</li>
                <li><strong>Tailwind конфиг:</strong> Добавлены fontFamily.wsf и wsf-variable</li>
                <li><strong>Конфликты:</strong> Переопределены font-sans, has-global-padding, prose</li>
            </ul>
        </div>
        
        <!-- Before vs After Comparison -->
        <div class="test-section">
            <h3>Сравнение "До" и "После"</h3>
            <div class="comparison-grid">
                <div class="comparison-item before">
                    <h4>❌ До исправления (смешанные шрифты)</h4>
                    <div class="demo-widget before-widget">
                        <div class="demo-form">
                            <div class="demo-field">
                                <label class="demo-label">Make (Times New Roman)</label>
                                <select class="demo-select">
                                    <option>BMW (Inter)</option>
                                    <option>Audi (Inter)</option>
                                </select>
                            </div>
                            <h5 style="margin: 10px 0;">Results (Times New Roman)</h5>
                            <button class="demo-button">Find Sizes (Times New Roman)</button>
                        </div>
                    </div>
                    <div class="font-info">Лейблы и заголовки используют шрифт страницы, только селекты - Inter</div>
                </div>
                
                <div class="comparison-item after">
                    <h4>✅ После исправления (единый Inter)</h4>
                    <div class="demo-widget after-widget wsf-font" data-wsf-theme="light">
                        <div class="demo-form">
                            <div class="demo-field">
                                <label class="demo-label">Make (Inter)</label>
                                <select class="demo-select">
                                    <option>BMW (Inter)</option>
                                    <option>Audi (Inter)</option>
                                </select>
                            </div>
                            <h5 style="margin: 10px 0;">Results (Inter)</h5>
                            <button class="demo-button">Find Sizes (Inter)</button>
                        </div>
                    </div>
                    <div class="font-info">Все элементы используют Inter благодаря wsf-font классу</div>
                </div>
            </div>
        </div>
        
        <!-- Real Widget Test -->
        <div class="test-section">
            <h3>Тест с реальным виджетом</h3>
            <div class="wheel-fit-widget wsf-font bg-wsf-bg max-w-4xl mx-auto p-4" id="test-widget" data-wsf-theme="light">
                <h4 style="margin-top: 0; color: var(--wsf-text-primary); text-align: center;">Wheel Size Finder (Inter Font)</h4>
                <div class="demo-form">
                    <div class="demo-field">
                        <label class="demo-label">Make</label>
                        <select class="demo-select">
                            <option value="">Select make...</option>
                            <option value="bmw">BMW</option>
                            <option value="audi">Audi</option>
                            <option value="mercedes">Mercedes-Benz</option>
                        </select>
                    </div>
                    <div class="demo-field">
                        <label class="demo-label">Model</label>
                        <select class="demo-select" disabled>
                            <option value="">Select make first</option>
                        </select>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="demo-button" disabled>Find Sizes</button>
                    </div>
                </div>
                
                <!-- Results Demo -->
                <div style="margin-top: 20px; padding: 15px; background: var(--wsf-surface); border-radius: 8px;">
                    <h5 style="margin: 0 0 10px 0; color: var(--wsf-text-primary);">Search Results (Inter Font)</h5>
                    <div style="padding: 10px; background: var(--wsf-bg); border-radius: 4px; margin: 5px 0;">
                        <strong>225/45R17</strong> - Standard size
                    </div>
                    <div style="padding: 10px; background: var(--wsf-bg); border-radius: 4px; margin: 5px 0;">
                        <strong>235/40R18</strong> - Plus size
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Font Demonstration -->
        <div class="test-section">
            <h3>Демонстрация шрифтов</h3>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>🌐 Шрифт страницы (Times New Roman)</h4>
                    <div class="font-demo">
                        <h5>Заголовок страницы</h5>
                        <p>Этот текст использует Times New Roman - шрифт страницы по умолчанию.</p>
                        <p class="font-info">font-family: 'Times New Roman', serif</p>
                    </div>
                </div>
                <div class="comparison-item">
                    <h4>🎯 Шрифт виджета (Inter)</h4>
                    <div class="font-demo wsf-font">
                        <h5>Заголовок виджета</h5>
                        <p>Этот текст использует Inter - единый шрифт виджета.</p>
                        <p class="font-info">font-family: var(--wsf-font-base) !important</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status success">
            ✅ TASK-FONT-FIX выполнен! Весь контент виджета теперь использует единый шрифт Inter
        </div>
    </div>
    
    <script>
        function setTheme(theme) {
            const widgets = document.querySelectorAll('[data-wsf-theme]');
            const lightBtn = document.getElementById('light-btn');
            const darkBtn = document.getElementById('dark-btn');
            
            widgets.forEach(widget => {
                widget.setAttribute('data-wsf-theme', theme);
                
                if (theme === 'dark') {
                    widget.classList.add('wsf-theme-dark');
                } else {
                    widget.classList.remove('wsf-theme-dark');
                }
            });
            
            // Update button states
            lightBtn.classList.toggle('active', theme === 'light');
            darkBtn.classList.toggle('active', theme === 'dark');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Test button enabling
            const makeSelect = document.querySelector('#test-widget select');
            const button = document.querySelector('#test-widget button');
            
            if (makeSelect && button) {
                makeSelect.addEventListener('change', function() {
                    if (this.value) {
                        button.disabled = false;
                        button.textContent = 'Find Sizes (Enabled)';
                    } else {
                        button.disabled = true;
                        button.textContent = 'Find Sizes';
                    }
                });
            }
        });
    </script>
</body>
</html>
