/**
 * Быстрая проверка границ 2px
 * Запустите в консоли браузера для мгновенной проверки
 */

(function() {
    'use strict';

    console.log('⚡ === БЫСТРАЯ ПРОВЕРКА ГРАНИЦ 2PX ===');

    // Проверяем CSS переменные
    const rootStyles = getComputedStyle(document.documentElement);
    const borderWidth = rootStyles.getPropertyValue('--wsf-card-border-width').trim();
    const borderColor = rootStyles.getPropertyValue('--wsf-card-border-color').trim();
    
    console.log(`📏 Переменная ширины: ${borderWidth || 'не найдена'}`);
    console.log(`🎨 Переменная цвета: ${borderColor || 'не найдена'}`);

    const cards = document.querySelectorAll('.wsf-theme-card');
    
    if (cards.length === 0) {
        console.log('❌ Карточки тем не найдены');
        return;
    }

    console.log(`📊 Найдено ${cards.length} карточек`);

    let correct2px = 0;
    let correctColor = 0;
    let activeCards = 0;

    cards.forEach((card, index) => {
        const styles = window.getComputedStyle(card);
        const cardBorderWidth = styles.borderWidth;
        const cardBorderColor = styles.borderColor;
        const isActive = card.classList.contains('wsf-theme-card--active');
        
        // Проверяем ширину 2px
        if (cardBorderWidth === '2px') {
            correct2px++;
        }
        
        // Проверяем цвет slate-300
        if (cardBorderColor.includes('203, 213, 225') || cardBorderColor.includes('#cbd5e1')) {
            correctColor++;
        }
        
        if (isActive) {
            activeCards++;
            const boxShadow = styles.boxShadow;
            const hasRing = boxShadow && (boxShadow.includes('4px') || boxShadow.includes('var(--wsf-ring)'));
            
            console.log(`🎯 Активная карточка ${index + 1}: ${hasRing ? '✅ Кольцо есть' : '❌ Кольца нет'}`);
        }
    });

    console.log('\n📈 РЕЗУЛЬТАТ:');
    console.log(`✅ Ширина 2px: ${correct2px}/${cards.length}`);
    console.log(`✅ Правильный цвет: ${correctColor}/${cards.length}`);
    console.log(`🎯 Активные карточки: ${activeCards}`);

    const allCorrect = correct2px === cards.length && correctColor === cards.length;
    
    if (allCorrect) {
        console.log('🎉 ВСЕ ГРАНИЦЫ 2PX НАСТРОЕНЫ ПРАВИЛЬНО!');
        console.log('💪 Карточки теперь четко отделяются от фона');
    } else {
        console.log('⚠️ Некоторые границы требуют настройки');
        if (correct2px < cards.length) {
            console.log(`  • ${cards.length - correct2px} карточек не имеют ширину 2px`);
        }
        if (correctColor < cards.length) {
            console.log(`  • ${cards.length - correctColor} карточек имеют неправильный цвет`);
        }
    }

    // Создаем мини-индикатор
    const indicator = document.createElement('div');
    indicator.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: ${allCorrect ? '#28a745' : '#ffc107'};
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
        font-family: monospace;
        border: 2px solid rgba(255,255,255,0.3);
    `;
    indicator.innerHTML = `
        <div>Границы 2px: ${correct2px}/${cards.length}</div>
        <div style="font-size: 10px; opacity: 0.8;">Цвет: ${correctColor}/${cards.length}</div>
    `;
    document.body.appendChild(indicator);

    setTimeout(() => indicator.remove(), 4000);

    // Добавляем функции для экспериментов
    window.testBorder = (width, color) => {
        document.documentElement.style.setProperty('--wsf-card-border-width', width || '2px');
        if (color) document.documentElement.style.setProperty('--wsf-card-border-color', color);
        console.log(`🔧 Границы изменены: ${width || '2px'} ${color || 'текущий цвет'}`);
    };

    console.log('\n💡 Для экспериментов: testBorder("3px", "#ff6b6b")');

})();
