<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Styling Fix Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .color-picker {
            margin: 10px 0;
        }
        .color-picker input {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 Button Styling Fix Test</h1>
    
    <div class="test-container">
        <h2>Theme Color Controls</h2>
        <div class="color-picker">
            <label for="primary-color">Primary Color:</label>
            <input type="color" id="primary-color" value="#2563eb">
            <span id="primary-value">#2563eb</span>
        </div>
        <div class="color-picker">
            <label for="hover-color">Hover Color:</label>
            <input type="color" id="hover-color" value="#1d4ed8">
            <span id="hover-value">#1d4ed8</span>
        </div>
    </div>

    <div class="test-container">
        <h2>Button Tests</h2>
        
        <!-- Mock widget container with CSS variables -->
        <div class="wsf-finder-widget" id="test-widget" style="
            --wsf-primary: #2563eb;
            --wsf-hover: #1d4ed8;
            --wsf-bg: #ffffff;
            --wsf-text: #1f2937;
            --wsf-border: #e5e7eb;
            --wsf-muted: #9ca3af;
        ">
            <h3>1. btn-primary Class Test</h3>
            <button class="btn-primary">Search Button</button>
            <button class="btn-primary" disabled>Disabled Search</button>
            
            <h3>2. Submit Button in ws-submit Container</h3>
            <div class="ws-submit">
                <button type="submit" class="btn-primary">Find Tire Sizes</button>
            </div>
            
            <h3>3. Various Button Sizes</h3>
            <button class="btn-primary text-sm px-4 py-2">Small Button</button>
            <button class="btn-primary">Normal Button</button>
            <button class="btn-primary py-3 px-10">Large Button</button>
            
            <h3>4. Wizard Button Test</h3>
            <button class="btn-primary w-full md:w-auto py-3 px-10">Show Results</button>
        </div>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="test-results">
            <p>Click "Run Tests" to check button styling...</p>
        </div>
        <button onclick="runTests()" style="background: #10b981; color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer;">
            Run Tests
        </button>
    </div>

    <script>
        // Update CSS variables when color inputs change
        document.getElementById('primary-color').addEventListener('input', function(e) {
            const color = e.target.value;
            document.getElementById('primary-value').textContent = color;
            document.getElementById('test-widget').style.setProperty('--wsf-primary', color);
        });

        document.getElementById('hover-color').addEventListener('input', function(e) {
            const color = e.target.value;
            document.getElementById('hover-value').textContent = color;
            document.getElementById('test-widget').style.setProperty('--wsf-hover', color);
        });

        function runTests() {
            const results = [];
            const widget = document.getElementById('test-widget');
            const buttons = widget.querySelectorAll('.btn-primary');
            
            console.log('🧪 Running button styling tests...');
            
            // Test 1: Check if all buttons have the same background color (when not disabled)
            const enabledButtons = Array.from(buttons).filter(btn => !btn.disabled);
            const backgroundColors = enabledButtons.map(btn => {
                const style = window.getComputedStyle(btn);
                return style.backgroundColor;
            });
            
            const allSameColor = backgroundColors.every(color => color === backgroundColors[0]);
            results.push({
                test: 'All enabled buttons have same background color',
                passed: allSameColor,
                details: allSameColor ? `All buttons: ${backgroundColors[0]}` : `Colors: ${backgroundColors.join(', ')}`
            });
            
            // Test 2: Check if background color matches CSS variable
            const primaryColor = widget.style.getPropertyValue('--wsf-primary');
            const expectedRgb = hexToRgb(primaryColor);
            const actualRgb = backgroundColors[0];
            
            results.push({
                test: 'Background color matches --wsf-primary variable',
                passed: actualRgb.includes(expectedRgb.r) && actualRgb.includes(expectedRgb.g) && actualRgb.includes(expectedRgb.b),
                details: `Expected: rgb(${expectedRgb.r}, ${expectedRgb.g}, ${expectedRgb.b}), Actual: ${actualRgb}`
            });
            
            // Test 3: Check disabled button opacity
            const disabledButtons = Array.from(buttons).filter(btn => btn.disabled);
            if (disabledButtons.length > 0) {
                const disabledOpacity = window.getComputedStyle(disabledButtons[0]).opacity;
                results.push({
                    test: 'Disabled buttons have reduced opacity',
                    passed: parseFloat(disabledOpacity) < 1,
                    details: `Opacity: ${disabledOpacity}`
                });
            }
            
            // Display results
            const resultsDiv = document.getElementById('test-results');
            let html = '<h4>Test Results:</h4>';
            
            results.forEach(result => {
                const icon = result.passed ? '✅' : '❌';
                html += `<p>${icon} <strong>${result.test}:</strong> ${result.details}</p>`;
            });
            
            const allPassed = results.every(r => r.passed);
            html += `<p><strong>Overall Result:</strong> ${allPassed ? '✅ All tests passed!' : '❌ Some tests failed'}</p>`;
            
            resultsDiv.innerHTML = html;
            
            console.log('Test results:', results);
        }
        
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
