/**
 * Диагностика проблемы с таймингом переводов селекторов
 * Отслеживает ВСЕ изменения и находит источник перезаписи
 */

(function() {
    'use strict';

    console.log('[Timing Debug] 🔍 Запуск диагностики проблемы с таймингом...');

    const changeLog = [];
    let isMonitoring = false;

    // Функция логирования изменений с полной информацией
    function logSelectorChange(selectId, oldText, newText, source, stackTrace) {
        const timestamp = performance.now();
        const change = {
            timestamp,
            selectId,
            oldText,
            newText,
            source,
            stackTrace: stackTrace || new Error().stack,
            isReversion: oldText.includes('Выберите') && newText.includes('Choose')
        };
        
        changeLog.push(change);
        
        // Определяем тип изменения
        let changeType = '📝';
        if (change.isReversion) {
            changeType = '🚨'; // КРИТИЧЕСКАЯ ПРОБЛЕМА - реверсия с русского на английский
        } else if (newText.includes('Choose') || newText.includes('Select')) {
            changeType = '❌'; // Английский
        } else if (newText.includes('Выберите') || newText.includes('Загрузка')) {
            changeType = '✅'; // Русский
        }
        
        console.log(`[Timing Debug] ${changeType} ${selectId}: "${oldText}" → "${newText}"`);
        console.log(`[Timing Debug]   Источник: ${source}`);
        console.log(`[Timing Debug]   Время: ${timestamp.toFixed(2)}ms`);
        
        if (change.isReversion) {
            console.error(`[Timing Debug] 🚨 НАЙДЕНА ПРОБЛЕМА! Реверсия с русского на английский!`);
            console.error(`[Timing Debug] Stack trace:`, stackTrace);
            console.error(`[Timing Debug] Это то, что мы ищем!`);
        }
    }

    // Перехват всех способов изменения текста в селекторах
    function interceptAllChanges() {
        console.log('[Timing Debug] Перехват всех способов изменения текста...');

        // 1. Перехват innerHTML
        const originalInnerHTMLSetter = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML').set;
        Object.defineProperty(Element.prototype, 'innerHTML', {
            set: function(value) {
                if (this.tagName === 'SELECT' && this.id && this.id.startsWith('wf-')) {
                    const oldOption = this.querySelector('option[value=""]');
                    const oldText = oldOption ? oldOption.textContent.trim() : '';
                    
                    const result = originalInnerHTMLSetter.call(this, value);
                    
                    const newOption = this.querySelector('option[value=""]');
                    const newText = newOption ? newOption.textContent.trim() : '';
                    
                    if (oldText !== newText) {
                        logSelectorChange(this.id, oldText, newText, 'innerHTML setter', new Error().stack);
                    }
                    
                    return result;
                }
                return originalInnerHTMLSetter.call(this, value);
            }
        });

        // 2. Перехват textContent
        const originalTextContentSetter = Object.getOwnPropertyDescriptor(Node.prototype, 'textContent').set;
        Object.defineProperty(Node.prototype, 'textContent', {
            set: function(value) {
                if (this.parentElement && 
                    this.parentElement.tagName === 'OPTION' && 
                    this.parentElement.value === '' &&
                    this.parentElement.parentElement &&
                    this.parentElement.parentElement.id &&
                    this.parentElement.parentElement.id.startsWith('wf-')) {
                    
                    const oldText = this.textContent;
                    const selectId = this.parentElement.parentElement.id;
                    
                    const result = originalTextContentSetter.call(this, value);
                    
                    if (oldText !== value) {
                        logSelectorChange(selectId, oldText, value, 'textContent setter', new Error().stack);
                    }
                    
                    return result;
                }
                return originalTextContentSetter.call(this, value);
            }
        });

        // 3. Перехват appendChild/insertBefore для новых option элементов
        const originalAppendChild = Element.prototype.appendChild;
        Element.prototype.appendChild = function(child) {
            const result = originalAppendChild.call(this, child);
            
            if (this.tagName === 'SELECT' && 
                this.id && 
                this.id.startsWith('wf-') && 
                child.tagName === 'OPTION' && 
                child.value === '') {
                
                const text = child.textContent.trim();
                logSelectorChange(this.id, '', text, 'appendChild new option', new Error().stack);
            }
            
            return result;
        };

        console.log('[Timing Debug] ✅ Все способы изменения перехвачены');
    }

    // MutationObserver для отслеживания изменений DOM
    function setupMutationObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE && 
                            node.tagName === 'OPTION' && 
                            node.value === '' &&
                            node.parentElement &&
                            node.parentElement.id &&
                            node.parentElement.id.startsWith('wf-')) {
                            
                            const text = node.textContent.trim();
                            logSelectorChange(node.parentElement.id, '', text, 'DOM mutation - added option', new Error().stack);
                        }
                    });
                } else if (mutation.type === 'characterData') {
                    const node = mutation.target;
                    if (node.parentElement && 
                        node.parentElement.tagName === 'OPTION' && 
                        node.parentElement.value === '' &&
                        node.parentElement.parentElement &&
                        node.parentElement.parentElement.id &&
                        node.parentElement.parentElement.id.startsWith('wf-')) {
                        
                        const selectId = node.parentElement.parentElement.id;
                        const newText = node.textContent.trim();
                        const oldText = mutation.oldValue || '';
                        
                        if (oldText !== newText) {
                            logSelectorChange(selectId, oldText, newText, 'DOM mutation - text change', new Error().stack);
                        }
                    }
                }
            });
        });

        const container = document.getElementById('widget-preview') || document.body;
        observer.observe(container, {
            childList: true,
            subtree: true,
            characterData: true,
            characterDataOldValue: true
        });

        console.log('[Timing Debug] ✅ MutationObserver настроен');
        return observer;
    }

    // Функция анализа логов для поиска проблемы
    function analyzeReversions() {
        console.log('[Timing Debug] === АНАЛИЗ РЕВЕРСИЙ ===');
        
        const reversions = changeLog.filter(change => change.isReversion);
        
        if (reversions.length === 0) {
            console.log('[Timing Debug] ✅ Реверсий не найдено');
            return;
        }
        
        console.error(`[Timing Debug] ❌ НАЙДЕНО ${reversions.length} РЕВЕРСИЙ:`);
        
        reversions.forEach((reversion, index) => {
            console.error(`\n[Timing Debug] Реверсия #${index + 1}:`);
            console.error(`  Селектор: ${reversion.selectId}`);
            console.error(`  Изменение: "${reversion.oldText}" → "${reversion.newText}"`);
            console.error(`  Время: ${reversion.timestamp.toFixed(2)}ms`);
            console.error(`  Источник: ${reversion.source}`);
            console.error(`  Stack trace:`, reversion.stackTrace);
        });
        
        // Анализируем паттерны
        const sources = reversions.map(r => r.source);
        const uniqueSources = [...new Set(sources)];
        
        console.error(`[Timing Debug] Источники реверсий: ${uniqueSources.join(', ')}`);
        
        return reversions;
    }

    // Функция мониторинга изменений настроек
    function monitorSettingsChanges() {
        console.log('[Timing Debug] === Мониторинг изменений настроек ===');
        
        document.addEventListener('change', function(e) {
            if (e.target && (e.target.id === 'search_flow' || e.target.id === 'form_layout')) {
                console.log(`[Timing Debug] 🔄 Изменение ${e.target.id}: ${e.target.value}`);
                
                // Очищаем лог для нового теста
                changeLog.length = 0;
                console.log('[Timing Debug] Лог очищен, начинаем отслеживание...');
                
                // Анализируем через 5 секунд
                setTimeout(() => {
                    console.log('[Timing Debug] Анализ результатов...');
                    analyzeReversions();
                    
                    // Показываем весь лог изменений
                    console.log(`[Timing Debug] Всего изменений: ${changeLog.length}`);
                    changeLog.forEach((change, index) => {
                        const type = change.isReversion ? '🚨' : 
                                   change.newText.includes('Choose') ? '❌' : 
                                   change.newText.includes('Выберите') ? '✅' : '📝';
                        console.log(`${index + 1}. ${type} ${change.selectId}: "${change.oldText}" → "${change.newText}" (${change.source})`);
                    });
                }, 5000);
            }
        });
        
        console.log('[Timing Debug] ✅ Мониторинг настроен');
    }

    // Основная функция диагностики
    function runTimingDiagnostic() {
        if (isMonitoring) {
            console.log('[Timing Debug] Диагностика уже запущена');
            return;
        }
        
        isMonitoring = true;
        console.log('[Timing Debug] 🚀 ЗАПУСК ДИАГНОСТИКИ ПРОБЛЕМЫ С ТАЙМИНГОМ');
        
        // 1. Перехватываем все способы изменения
        interceptAllChanges();
        
        // 2. Настраиваем MutationObserver
        const observer = setupMutationObserver();
        
        // 3. Мониторим изменения настроек
        monitorSettingsChanges();
        
        console.log('[Timing Debug] ✅ Диагностика запущена');
        console.log('[Timing Debug] Измените Search Flow или Form Layout для начала отслеживания');
        
        return { observer, stop: () => { isMonitoring = false; observer.disconnect(); } };
    }

    // Глобальные функции
    window.timingDebug = {
        run: runTimingDiagnostic,
        analyze: analyzeReversions,
        getLog: () => changeLog,
        clear: () => { changeLog.length = 0; }
    };

    // Автоматический запуск
    setTimeout(() => {
        console.log('[Timing Debug] Автоматический запуск диагностики...');
        runTimingDiagnostic();
    }, 1000);

    console.log('[Timing Debug] Диагностика загружена. Доступные функции:');
    console.log('- timingDebug.run() - запуск диагностики');
    console.log('- timingDebug.analyze() - анализ реверсий');
    console.log('- timingDebug.getLog() - получить лог');

})();
