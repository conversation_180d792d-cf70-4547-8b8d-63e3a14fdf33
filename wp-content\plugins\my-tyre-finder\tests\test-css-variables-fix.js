/**
 * Тест исправления CSS переменных
 * Проверяет, что все жёсткие цвета заменены на CSS переменные
 */

(function() {
    'use strict';

    console.log('🎨 === ТЕСТ ИСПРАВЛЕНИЯ CSS ПЕРЕМЕННЫХ ===');

    // Найти виджет
    const widget = document.querySelector('.wheel-fit-widget, .wsf-finder-widget');
    if (!widget) {
        console.error('❌ Виджет не найден');
        return;
    }

    console.log('✅ Виджет найден:', widget.className);

    // Проверить кнопку поиска
    function checkSearchButton() {
        console.log('\n1️⃣ Проверка кнопки поиска...');
        
        const searchButton = widget.querySelector('button[type="submit"]');
        if (!searchButton) {
            console.log('⚠️ Кнопка поиска не найдена');
            return false;
        }

        const computedStyle = window.getComputedStyle(searchButton);
        const backgroundColor = computedStyle.backgroundColor;
        
        console.log('Фон кнопки:', backgroundColor);
        
        // Проверить, что используется CSS переменная
        const hasVariableBackground = searchButton.classList.contains('bg-wsf-primary');
        
        if (hasVariableBackground) {
            console.log('✅ Кнопка использует CSS переменную bg-wsf-primary');
        } else {
            console.log('❌ Кнопка НЕ использует CSS переменную');
            console.log('Классы кнопки:', searchButton.className);
        }
        
        return hasVariableBackground;
    }

    // Проверить счётчик гаража
    function checkGarageCounter() {
        console.log('\n2️⃣ Проверка счётчика гаража...');
        
        const garageCount = widget.querySelector('#garage-count');
        if (!garageCount) {
            console.log('⚠️ Счётчик гаража не найден');
            return true; // Не критично
        }

        const hasVariableBackground = garageCount.classList.contains('bg-wsf-primary');
        
        if (hasVariableBackground) {
            console.log('✅ Счётчик гаража использует CSS переменную');
        } else {
            console.log('❌ Счётчик гаража НЕ использует CSS переменную');
            console.log('Классы счётчика:', garageCount.className);
        }
        
        return hasVariableBackground;
    }

    // Проверить лоадеры
    function checkLoaders() {
        console.log('\n3️⃣ Проверка лоадеров...');
        
        const loaders = widget.querySelectorAll('[id$="-loader"] div');
        let allGood = true;
        
        loaders.forEach((loader, index) => {
            const hasBorderVariable = loader.classList.contains('border-wsf-primary');
            
            if (hasBorderVariable) {
                console.log(`✅ Лоадер ${index + 1} использует CSS переменную`);
            } else {
                console.log(`❌ Лоадер ${index + 1} НЕ использует CSS переменную`);
                console.log('Классы лоадера:', loader.className);
                allGood = false;
            }
        });
        
        if (loaders.length === 0) {
            console.log('⚠️ Лоадеры не найдены');
        }
        
        return allGood;
    }

    // Проверить жёсткие цвета
    function checkHardcodedColors() {
        console.log('\n4️⃣ Проверка жёстких цветов...');
        
        const problematicClasses = [
            'bg-blue-600',
            'hover:bg-blue-700', 
            'focus:ring-blue-500',
            'border-slate-300',
            'text-slate-900',
            'text-slate-600'
        ];
        
        let foundProblems = 0;
        
        problematicClasses.forEach(className => {
            const elements = widget.querySelectorAll(`.${className}`);
            if (elements.length > 0) {
                console.log(`❌ Найдено ${elements.length} элементов с классом .${className}`);
                foundProblems += elements.length;
                
                elements.forEach(el => {
                    console.log('  - Элемент:', el.tagName, el.className);
                });
            }
        });
        
        if (foundProblems === 0) {
            console.log('✅ Жёсткие цвета не найдены');
        }
        
        return foundProblems === 0;
    }

    // Проверить CSS переменные
    function checkCSSVariables() {
        console.log('\n5️⃣ Проверка CSS переменных...');
        
        const computedStyle = window.getComputedStyle(widget);
        const requiredVariables = [
            '--wsf-bg',
            '--wsf-text',
            '--wsf-primary',
            '--wsf-hover',
            '--wsf-border',
            '--wsf-muted'
        ];
        
        let allVariablesPresent = true;
        
        requiredVariables.forEach(variable => {
            const value = computedStyle.getPropertyValue(variable);
            if (value) {
                console.log(`✅ ${variable}: ${value.trim()}`);
            } else {
                console.log(`❌ ${variable}: не найдена`);
                allVariablesPresent = false;
            }
        });
        
        return allVariablesPresent;
    }

    // Запуск всех тестов
    const results = {
        searchButton: checkSearchButton(),
        garageCounter: checkGarageCounter(),
        loaders: checkLoaders(),
        hardcodedColors: checkHardcodedColors(),
        cssVariables: checkCSSVariables()
    };

    // Итоговый результат
    console.log('\n📊 === РЕЗУЛЬТАТЫ ===');
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`Пройдено тестов: ${passedTests}/${totalTests}`);
    
    if (passedTests === totalTests) {
        console.log('🎉 Все тесты пройдены! Кнопка должна быть видимой.');
    } else {
        console.log('⚠️ Есть проблемы, требующие исправления.');
        
        // Показать, что нужно исправить
        Object.entries(results).forEach(([test, passed]) => {
            if (!passed) {
                console.log(`❌ ${test}: требует исправления`);
            }
        });
    }

})();
