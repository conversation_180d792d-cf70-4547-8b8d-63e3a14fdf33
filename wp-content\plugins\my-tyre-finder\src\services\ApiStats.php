<?php

declare(strict_types=1);

namespace MyTyreFinder\Services;

/**
 * Helper class for tracking Wheel-Size API usage statistics.
 */
final class ApiStats
{
    private const OPTION = 'wheel_size_api_stats';

    /**
     * Records a single API request event.
     *
     * @param array{is_cache?: bool, response?: array|\WP_Error|null} $args {
     *     Optional. An array of arguments.
     *
     *     @type bool $is_cache Whether the result was served from cache.
     *     @type array|\WP_Error|null $response The response from wp_remote_get or a WP_Error object.
     * }
     */
    public static function record(array $args): void
    {
        $stats = get_option(self::OPTION, [
            'total'   => 0,
            'success' => 0,
            'failed'  => 0,
            'cache'   => 0,
            'last_ts' => null,
        ]);

        $stats['total']++;
        $stats['last_ts'] = time();

        if (!empty($args['is_cache'])) {
            $stats['cache']++;
            // A cached request is implicitly a successful one.
            $stats['success']++;
        } elseif (isset($args['response'])) {
            if (is_wp_error($args['response'])) {
                $stats['failed']++;
            } else {
                $code = wp_remote_retrieve_response_code($args['response']);
                if ($code >= 200 && $code < 300) {
                    $stats['success']++;
                } else {
                    $stats['failed']++;
                }
            }
        }

        update_option(self::OPTION, $stats, false);
    }
} 