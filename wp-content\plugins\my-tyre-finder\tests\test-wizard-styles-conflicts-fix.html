<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wizard Styles Conflicts Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            height: 800px;
            overflow-y: auto;
        }
        
        .before-section {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after-section {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .widget-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* Wizard styles simulation */
        .wheel-fit-widget {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }
        
        .wsf-widget__header {
            margin-bottom: 24px;
        }
        
        .wsf-widget__title {
            text-align: center;
            font-size: 24px;
            font-weight: 800;
            color: #2563eb;
            margin: 0;
        }
        
        .wsf-widget__title.broken {
            word-break: break-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 200px;
        }
        
        #wizard-header {
            margin-bottom: 24px;
        }
        
        .wizard-steps {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            font-weight: 600;
            color: #9ca3af;
            margin-bottom: 8px;
        }
        
        .wizard-step-name {
            color: #2563eb;
        }
        
        .wizard-step-name.active {
            color: #2563eb;
        }
        
        .progress-bar {
            background: #f3f4f6;
            border-radius: 9999px;
            height: 6px;
            margin-top: 8px;
        }
        
        .progress-fill {
            background: #2563eb;
            height: 6px;
            border-radius: 9999px;
            width: 75%;
            transition: width 0.5s ease;
        }
        
        .wsf-form-wrapper {
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .wizard-content {
            text-align: left;
            color: #6b7280;
        }
        
        .wizard-content.improved {
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .wizard-content h2 {
            color: #2563eb;
            margin-bottom: 16px;
            font-size: 24px;
            font-weight: 700;
        }
        
        .wizard-content h2.broken {
            word-break: break-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 150px;
            line-height: 1;
        }
        
        .wizard-content nav {
            margin-bottom: 16px;
            color: #6b7280;
            font-size: 14px;
        }
        
        .search-input {
            width: 100%;
            max-width: 400px;
            height: 48px;
            padding: 12px 48px 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 24px;
            transition: all 0.2s ease;
        }
        
        .search-input:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }
        
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .model-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 16px;
            text-align: center;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            word-break: break-word;
            line-height: 1.3;
        }
        
        .model-item.broken {
            word-break: break-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-height: 32px;
            padding: 8px;
            font-size: 12px;
        }
        
        .model-item.uneven {
            min-height: auto;
            height: auto;
            padding: 6px 8px;
        }
        
        .model-item.uneven:nth-child(odd) {
            height: 40px;
            padding: 8px 12px;
        }
        
        .model-item.uneven:nth-child(even) {
            height: 60px;
            padding: 16px 8px;
        }
        
        .model-item:hover {
            background: #e5e7eb;
            border-color: #2563eb;
        }
        
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        .nav-button {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-height: 44px;
        }
        
        .nav-button.broken {
            word-break: break-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 60px;
            padding: 8px;
            font-size: 12px;
        }
        
        .nav-button.primary {
            background: #2563eb;
            color: white;
        }
        
        .nav-button.secondary {
            background: #e5e7eb;
            color: #374151;
        }
        
        .garage-button {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border-radius: 6px;
            background: #f3f4f6;
            color: #6b7280;
            font-size: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .garage-badge {
            background: #2563eb;
            color: white;
            font-size: 11px;
            font-weight: 700;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 6px;
        }
        
        .scroll-indicator {
            background: #fbbf24;
            color: #92400e;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .scroll-indicator.fixed {
            background: #10b981;
            color: white;
        }
        
        .issue-highlight {
            background: #fef2f2;
            border: 2px dashed #ef4444;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
        }
        
        .fix-highlight {
            background: #f0fdf4;
            border: 2px dashed #10b981;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">🔧 Wizard Styles Conflicts Fix Test</h1>
            <p style="color: #6b7280; font-size: 18px;">Исправление конфликтов CSS правил</p>
        </header>

        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h3 style="margin-top: 0; color: #92400e;">🐛 Анализ конфликтов стилей</h3>
            <p><strong>Выявленные проблемы:</strong></p>
            <ul>
                <li>❌ <strong>Слово "Aion" сломано:</strong> Разбито на строки ("A\nc\nt\ni\no\nn")</li>
                <li>❌ <strong>Кнопки модификаций "прыгают":</strong> Разная ширина и высота</li>
                <li>❌ <strong>Конфликтующие CSS правила:</strong> white-space, word-break, overflow</li>
                <li>❌ <strong>Неправильная специфичность:</strong> Глобальные правила перебивают специфичные</li>
            </ul>
            
            <p><strong>Источники конфликтов:</strong></p>
            <ul>
                <li>🔍 <code>live-preview-width-fix.css</code> - агрессивные правила</li>
                <li>🔍 <code>wheel-fit-shared.css</code> - Tailwind утилиты</li>
                <li>🔍 JavaScript - разные классы для похожих элементов</li>
            </ul>
        </div>

        <div class="comparison-grid">
            <!-- Before -->
            <div class="comparison-section before-section">
                <h2 style="color: #dc2626; margin-top: 0;">❌ До исправления</h2>
                <div class="scroll-indicator">⚠️ Конфликты CSS правил</div>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <!-- Заголовок вверху -->
                        <div class="wsf-widget__header">
                            <h1 class="wsf-widget__title broken">Wheel & Tyre Finder</h1>
                        </div>
                        
                        <!-- Стадии под заголовком -->
                        <div id="wizard-header">
                            <div class="wizard-steps">
                                <div class="wizard-step-name">Make</div>
                                <div class="wizard-step-name">Model</div>
                                <div class="wizard-step-name">Year</div>
                                <div class="wizard-step-name active">Modification</div>
                                <div class="wizard-step-name">Wheel Options</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                        
                        <div class="wsf-form-wrapper">
                            <div class="wizard-content">
                                <div class="issue-highlight">
                                    <h2 class="broken">Select Modification</h2>
                                </div>
                                <nav>BMW > 3 Series > 2020</nav>
                                <input type="text" class="search-input" placeholder="Search modifications...">
                                <div class="models-grid">
                                    <div class="model-item broken issue-highlight">Aion</div>
                                    <div class="model-item uneven">Hyper HT</div>
                                    <div class="model-item uneven">Hyper SSR</div>
                                    <div class="model-item uneven">S Plus</div>
                                    <div class="model-item uneven">Y Plus</div>
                                    <div class="model-item uneven">Pro</div>
                                    <div class="model-item uneven">Sport</div>
                                    <div class="model-item uneven">Comfort</div>
                                </div>
                                <div class="navigation-buttons">
                                    <button class="garage-button">
                                        🚗 Garage
                                        <span class="garage-badge">3</span>
                                    </button>
                                    <button class="nav-button primary broken issue-highlight">Next</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #7f1d1d; margin-top: 15px; font-size: 14px;">
                    <strong>Проблемы:</strong> CSS конфликты ломают отображение текста и выравнивание
                </p>
            </div>

            <!-- After -->
            <div class="comparison-section after-section">
                <h2 style="color: #059669; margin-top: 0;">✅ После исправления</h2>
                <div class="scroll-indicator fixed">✅ Конфликты устранены</div>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <!-- Заголовок вверху -->
                        <div class="wsf-widget__header">
                            <h1 class="wsf-widget__title">Wheel & Tyre Finder</h1>
                        </div>
                        
                        <!-- Стадии под заголовком -->
                        <div id="wizard-header">
                            <div class="wizard-steps">
                                <div class="wizard-step-name">Make</div>
                                <div class="wizard-step-name">Model</div>
                                <div class="wizard-step-name">Year</div>
                                <div class="wizard-step-name active">Modification</div>
                                <div class="wizard-step-name">Wheel Options</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                        
                        <div class="wsf-form-wrapper">
                            <div class="wizard-content improved">
                                <div class="fix-highlight">
                                    <h2>Select Modification</h2>
                                </div>
                                <nav>BMW > 3 Series > 2020</nav>
                                <input type="text" class="search-input" placeholder="Search modifications...">
                                <div class="models-grid">
                                    <div class="model-item fix-highlight">Aion</div>
                                    <div class="model-item">Hyper HT</div>
                                    <div class="model-item">Hyper SSR</div>
                                    <div class="model-item">S Plus</div>
                                    <div class="model-item">Y Plus</div>
                                    <div class="model-item">Pro</div>
                                    <div class="model-item">Sport</div>
                                    <div class="model-item">Comfort</div>
                                </div>
                                <div class="navigation-buttons">
                                    <button class="garage-button">
                                        🚗 Garage
                                        <span class="garage-badge">3</span>
                                    </button>
                                    <button class="nav-button primary fix-highlight">Next</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #065f46; margin-top: 15px; font-size: 14px;">
                    <strong>Решение:</strong> Специфичные CSS правила с правильной иерархией
                </p>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f0fdf4; border: 1px solid #16a34a; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #15803d;">✅ Выполненные исправления</h3>
            
            <p><strong>1. Критические CSS правила с высокой специфичностью:</strong></p>
            <ul>
                <li>✅ <code>#widget-preview .wizard-step h2</code> - защита заголовков</li>
                <li>✅ <code>#widget-preview .list-item</code> - унифицированные кнопки</li>
                <li>✅ <code>#widget-preview #wizard-next-btn</code> - защита навигации</li>
                <li>✅ Переопределение Tailwind утилит</li>
            </ul>
            
            <p><strong>2. Унифицированные стили для всех кнопок:</strong></p>
            <ul>
                <li>✅ Одинаковые классы в JavaScript</li>
                <li>✅ Консистентная высота (48px min-height)</li>
                <li>✅ Единообразное выравнивание (flex center)</li>
                <li>✅ Правильная обработка текста</li>
            </ul>
            
            <p><strong>3. Исправление текстовых правил:</strong></p>
            <ul>
                <li>✅ <code>white-space: normal</code> для читаемости</li>
                <li>✅ <code>word-break: normal</code> для заголовков</li>
                <li>✅ <code>overflow: visible</code> для полного отображения</li>
                <li>✅ <code>text-overflow: initial</code> без обрезания</li>
            </ul>
            
            <p><strong>4. Защита от конфликтов:</strong></p>
            <ul>
                <li>✅ Переопределение <code>.wsf-truncate</code></li>
                <li>✅ Переопределение <code>.wsf-whitespace-nowrap</code></li>
                <li>✅ Переопределение <code>.wsf-break-all</code></li>
                <li>✅ Переопределение <code>.wsf-overflow-hidden</code></li>
            </ul>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #eff6ff; border: 1px solid #3b82f6; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1d4ed8;">🔧 Для тестирования</h3>
            <p>Чтобы проверить исправления:</p>
            <ol>
                <li>Перейдите в админку WordPress → Wheel-Size → Appearance</li>
                <li>Установите <strong>Form Layout: Wizard</strong></li>
                <li>В Live Preview пройдите до шага "Select Modification"</li>
                <li>Проверьте, что слово "Aion" отображается целиком</li>
                <li>Убедитесь, что все кнопки модификаций одинаковой высоты</li>
                <li>Проверьте, что кнопка "Next" не разбита</li>
                <li>Запустите диагностический скрипт в консоли</li>
            </ol>
            
            <p><strong>Диагностический скрипт:</strong></p>
            <code style="background: #f3f4f6; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                debug-wizard-styles-conflicts.js
            </code>
            
            <p><strong>Ожидаемый результат:</strong></p>
            <ul>
                <li>✅ Все заголовки отображаются корректно</li>
                <li>✅ Кнопки модификаций имеют одинаковую высоту</li>
                <li>✅ Текст в кнопках читаемый и не разбитый</li>
                <li>✅ Навигационные кнопки работают правильно</li>
                <li>✅ Нет визуальных "прыжков" элементов</li>
            </ul>
        </div>
    </div>
</body>
</html>
