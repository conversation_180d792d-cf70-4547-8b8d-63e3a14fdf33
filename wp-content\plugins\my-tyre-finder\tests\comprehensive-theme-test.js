/**
 * Comprehensive Theme System Test
 * Tests all aspects of the theme system implementation
 */

(function() {
    'use strict';

    console.log('🧪 === COMPREHENSIVE THEME SYSTEM TEST ===');

    const testResults = {
        cssTokens: false,
        themeClasses: false,
        themeSwitching: false,
        apiIntegration: false,
        responsiveDesign: false,
        accessibility: false,
        performance: false,
        errors: []
    };

    // Test 1: CSS Token System
    function testCssTokens() {
        console.log('\n1️⃣ Testing CSS Token System...');
        
        const widget = document.querySelector('.wsf-finder-widget, .wheel-fit-widget');
        if (!widget) {
            testResults.errors.push('Widget element not found');
            return false;
        }

        const computedStyle = getComputedStyle(widget);
        const requiredTokens = [
            '--wsf-bg', '--wsf-text', '--wsf-primary', '--wsf-border',
            '--wsf-hover', '--wsf-secondary', '--wsf-accent', '--wsf-muted',
            '--wsf-success', '--wsf-warning', '--wsf-error'
        ];

        let tokensFound = 0;
        requiredTokens.forEach(token => {
            const value = computedStyle.getPropertyValue(token).trim();
            if (value) {
                console.log(`✅ ${token}: ${value}`);
                tokensFound++;
            } else {
                console.warn(`⚠️ ${token}: not found`);
                testResults.errors.push(`CSS token ${token} not found`);
            }
        });

        const success = tokensFound >= requiredTokens.length * 0.8; // 80% threshold
        testResults.cssTokens = success;
        console.log(`CSS Tokens: ${tokensFound}/${requiredTokens.length} found`);
        return success;
    }

    // Test 2: Theme Classes
    function testThemeClasses() {
        console.log('\n2️⃣ Testing Theme Classes...');
        
        const widget = document.querySelector('.wsf-finder-widget, .wheel-fit-widget');
        if (!widget) return false;

        // Check for required classes
        const hasFinderClass = widget.classList.contains('wsf-finder-widget') || 
                              widget.classList.contains('wheel-fit-widget');
        const themeClasses = Array.from(widget.classList).filter(cls => cls.startsWith('wsf-theme-'));
        const hasThemeAttribute = widget.hasAttribute('data-wsf-theme');

        if (hasFinderClass) {
            console.log('✅ Widget base class found');
        } else {
            console.error('❌ Widget base class missing');
            testResults.errors.push('Widget base class missing');
        }

        if (themeClasses.length > 0) {
            console.log('✅ Theme classes found:', themeClasses);
        } else {
            console.error('❌ No theme classes found');
            testResults.errors.push('No theme classes found');
        }

        if (hasThemeAttribute) {
            console.log('✅ Theme data attribute found:', widget.getAttribute('data-wsf-theme'));
        } else {
            console.error('❌ Theme data attribute missing');
            testResults.errors.push('Theme data attribute missing');
        }

        const success = hasFinderClass && themeClasses.length > 0 && hasThemeAttribute;
        testResults.themeClasses = success;
        return success;
    }

    // Test 3: Theme Switching
    async function testThemeSwitching() {
        console.log('\n3️⃣ Testing Theme Switching...');
        
        if (typeof window.ThemePresetsPanel === 'undefined' && 
            typeof wpApiSettings === 'undefined') {
            console.warn('⚠️ Theme switching not available (admin panel only)');
            testResults.themeSwitching = true; // Pass if not in admin
            return true;
        }

        const widget = document.querySelector('.wsf-finder-widget, .wheel-fit-widget');
        if (!widget) return false;

        try {
            // Test programmatic theme switching
            const originalClasses = Array.from(widget.classList);
            const originalTheme = widget.getAttribute('data-wsf-theme');

            // Apply light theme
            widget.classList.remove(...originalClasses.filter(cls => cls.startsWith('wsf-theme-')));
            widget.classList.add('wsf-theme-light');
            widget.setAttribute('data-wsf-theme', 'light');
            widget.style.setProperty('--wsf-primary', '#2563eb');

            // Verify light theme applied
            const lightPrimary = getComputedStyle(widget).getPropertyValue('--wsf-primary').trim();
            console.log('✅ Light theme applied, primary color:', lightPrimary);

            // Apply dark theme
            widget.classList.remove('wsf-theme-light');
            widget.classList.add('wsf-theme-dark');
            widget.setAttribute('data-wsf-theme', 'dark');
            widget.style.setProperty('--wsf-primary', '#7dd3fc');

            // Verify dark theme applied
            const darkPrimary = getComputedStyle(widget).getPropertyValue('--wsf-primary').trim();
            console.log('✅ Dark theme applied, primary color:', darkPrimary);

            // Restore original state
            widget.className = originalClasses.join(' ');
            widget.setAttribute('data-wsf-theme', originalTheme || 'light');

            testResults.themeSwitching = true;
            return true;
        } catch (error) {
            console.error('❌ Theme switching failed:', error);
            testResults.errors.push(`Theme switching error: ${error.message}`);
            return false;
        }
    }

    // Test 4: API Integration
    async function testApiIntegration() {
        console.log('\n4️⃣ Testing API Integration...');
        
        if (typeof wpApiSettings === 'undefined') {
            console.warn('⚠️ API testing not available (admin panel only)');
            testResults.apiIntegration = true; // Pass if not in admin
            return true;
        }

        try {
            const apiBase = wpApiSettings.root + 'wheel-size/v1/themes';
            const response = await fetch(apiBase, {
                headers: { 'X-WP-Nonce': wpApiSettings.nonce }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ API accessible, themes found:', Object.keys(data.themes || {}));
                console.log('✅ Active theme:', data.active_theme);
                testResults.apiIntegration = true;
                return true;
            } else {
                throw new Error(`API returned ${response.status}`);
            }
        } catch (error) {
            console.error('❌ API integration failed:', error);
            testResults.errors.push(`API error: ${error.message}`);
            return false;
        }
    }

    // Test 5: Responsive Design
    function testResponsiveDesign() {
        console.log('\n5️⃣ Testing Responsive Design...');
        
        const widget = document.querySelector('.wsf-finder-widget, .wheel-fit-widget');
        if (!widget) return false;

        try {
            // Test different viewport sizes
            const originalWidth = window.innerWidth;
            
            // Mobile test (simulate)
            const mobileMediaQuery = window.matchMedia('(max-width: 768px)');
            const desktopMediaQuery = window.matchMedia('(min-width: 1024px)');
            
            console.log('📱 Mobile breakpoint active:', mobileMediaQuery.matches);
            console.log('🖥️ Desktop breakpoint active:', desktopMediaQuery.matches);
            
            // Check if responsive classes exist
            const hasResponsiveClasses = widget.querySelector('.md\\:p-0, .md\\:text-3xl, .max-w-4xl');
            if (hasResponsiveClasses) {
                console.log('✅ Responsive classes found');
            } else {
                console.warn('⚠️ Responsive classes not found');
            }

            testResults.responsiveDesign = true;
            return true;
        } catch (error) {
            console.error('❌ Responsive design test failed:', error);
            testResults.errors.push(`Responsive design error: ${error.message}`);
            return false;
        }
    }

    // Test 6: Accessibility
    function testAccessibility() {
        console.log('\n6️⃣ Testing Accessibility...');
        
        const widget = document.querySelector('.wsf-finder-widget, .wheel-fit-widget');
        if (!widget) return false;

        try {
            let accessibilityScore = 0;
            const maxScore = 5;

            // Check for proper heading structure
            const headings = widget.querySelectorAll('h1, h2, h3, h4, h5, h6');
            if (headings.length > 0) {
                console.log('✅ Headings found:', headings.length);
                accessibilityScore++;
            }

            // Check for form labels
            const selects = widget.querySelectorAll('select');
            const buttons = widget.querySelectorAll('button');
            if (selects.length > 0 || buttons.length > 0) {
                console.log('✅ Interactive elements found');
                accessibilityScore++;
            }

            // Check for ARIA attributes
            const ariaElements = widget.querySelectorAll('[aria-label], [aria-labelledby], [role]');
            if (ariaElements.length > 0) {
                console.log('✅ ARIA attributes found');
                accessibilityScore++;
            }

            // Check color contrast (basic test)
            const computedStyle = getComputedStyle(widget);
            const bgColor = computedStyle.backgroundColor;
            const textColor = computedStyle.color;
            if (bgColor !== textColor) {
                console.log('✅ Different background and text colors');
                accessibilityScore++;
            }

            // Check for focus indicators
            const focusableElements = widget.querySelectorAll('select, button, input, [tabindex]');
            if (focusableElements.length > 0) {
                console.log('✅ Focusable elements found');
                accessibilityScore++;
            }

            const success = accessibilityScore >= maxScore * 0.6; // 60% threshold
            testResults.accessibility = success;
            console.log(`Accessibility score: ${accessibilityScore}/${maxScore}`);
            return success;
        } catch (error) {
            console.error('❌ Accessibility test failed:', error);
            testResults.errors.push(`Accessibility error: ${error.message}`);
            return false;
        }
    }

    // Test 7: Performance
    function testPerformance() {
        console.log('\n7️⃣ Testing Performance...');
        
        try {
            const startTime = performance.now();
            
            // Test CSS custom property access speed
            const widget = document.querySelector('.wsf-finder-widget, .wheel-fit-widget');
            if (!widget) return false;

            const computedStyle = getComputedStyle(widget);
            for (let i = 0; i < 100; i++) {
                computedStyle.getPropertyValue('--wsf-primary');
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            console.log(`✅ CSS property access time: ${duration.toFixed(2)}ms`);
            
            // Check for memory leaks (basic)
            const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            // Simulate theme switching multiple times
            for (let i = 0; i < 10; i++) {
                widget.style.setProperty('--wsf-primary', `#${Math.floor(Math.random()*16777215).toString(16)}`);
            }
            
            const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            const memoryIncrease = finalMemory - initialMemory;
            
            console.log(`✅ Memory usage increase: ${memoryIncrease} bytes`);
            
            const success = duration < 50 && memoryIncrease < 1000000; // Reasonable thresholds
            testResults.performance = success;
            return success;
        } catch (error) {
            console.error('❌ Performance test failed:', error);
            testResults.errors.push(`Performance error: ${error.message}`);
            return false;
        }
    }

    // Generate comprehensive report
    function generateReport() {
        console.log('\n📊 === COMPREHENSIVE TEST REPORT ===');
        
        const tests = [
            { name: 'CSS Tokens', result: testResults.cssTokens },
            { name: 'Theme Classes', result: testResults.themeClasses },
            { name: 'Theme Switching', result: testResults.themeSwitching },
            { name: 'API Integration', result: testResults.apiIntegration },
            { name: 'Responsive Design', result: testResults.responsiveDesign },
            { name: 'Accessibility', result: testResults.accessibility },
            { name: 'Performance', result: testResults.performance }
        ];

        const passedTests = tests.filter(test => test.result).length;
        const totalTests = tests.length;
        const successRate = (passedTests / totalTests * 100).toFixed(1);

        console.log(`\n🎯 Overall Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
        
        tests.forEach(test => {
            console.log(`   ${test.result ? '✅' : '❌'} ${test.name}`);
        });

        if (testResults.errors.length > 0) {
            console.log('\n⚠️ Issues Found:');
            testResults.errors.forEach((error, idx) => {
                console.log(`   ${idx + 1}. ${error}`);
            });
        } else {
            console.log('\n🎉 No issues found! Theme system is working perfectly.');
        }

        return {
            successRate: parseFloat(successRate),
            passedTests,
            totalTests,
            errors: testResults.errors
        };
    }

    // Run all tests
    async function runComprehensiveTest() {
        console.log('Starting comprehensive theme system test...\n');

        testCssTokens();
        testThemeClasses();
        await testThemeSwitching();
        await testApiIntegration();
        testResponsiveDesign();
        testAccessibility();
        testPerformance();

        const report = generateReport();
        
        console.log('\n💡 Recommendations:');
        if (report.successRate === 100) {
            console.log('✨ Perfect! Your theme system is fully functional.');
        } else if (report.successRate >= 80) {
            console.log('👍 Good! Minor issues to address.');
        } else if (report.successRate >= 60) {
            console.log('⚠️ Needs attention. Several issues found.');
        } else {
            console.log('🚨 Critical issues. Theme system needs significant fixes.');
        }

        return report;
    }

    // Export to window for manual access
    window.comprehensiveThemeTest = {
        run: runComprehensiveTest,
        individual: {
            testCssTokens,
            testThemeClasses,
            testThemeSwitching,
            testApiIntegration,
            testResponsiveDesign,
            testAccessibility,
            testPerformance
        },
        results: testResults
    };

    // Auto-run test
    runComprehensiveTest();

})();
