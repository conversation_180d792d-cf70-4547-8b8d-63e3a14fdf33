/**
 * Quick Selector Alignment Check
 * 
 * Copy and paste this entire script into browser console on:
 * - Frontend: actual page with Step-by-Step widget
 * - Admin: /wp-admin/admin.php?page=wheel-size-appearance (Live Preview)
 * 
 * Make sure to select "Step-by-Step" layout first!
 */

console.log('📐 Quick Selector Alignment Check...');

// Detect context
const isAdmin = window.location.href.includes('/wp-admin/');
const context = isAdmin ? 'ADMIN (Live Preview)' : 'FRONTEND';

console.log(`🔍 Testing in: ${context}`);

// Find widget and selectors
const widgetContainer = document.querySelector('.wheel-fit-widget, .wsf-finder-widget');
const selectors = widgetContainer ? widgetContainer.querySelectorAll('.step-container select, select.wsf-input') : [];

if (!widgetContainer || selectors.length === 0) {
    console.error('❌ Step-by-Step layout or selectors not detected');
    console.log('💡 Make sure to:');
    console.log('   1. Select "Step-by-Step" in Form Layout dropdown');
    console.log('   2. Refresh the Live Preview (if in admin)');
    console.log('   3. Run this test again');
    console.log('');
    console.log('Found elements:');
    console.log('   Widget container:', !!widgetContainer);
    console.log('   Selectors:', selectors.length);
} else {
    console.log('✅ Step-by-Step layout detected');
    console.log(`   Found ${selectors.length} selectors`);
    
    console.log('\n📊 SELECTOR ALIGNMENT CHECK:');
    
    // Expected values
    const expected = {
        height: 44,
        fontSize: 14,
        fontWeight: 500,
        lineHeight: 1.3,
        paddingLeft: 12,
        borderRadius: 8
    };
    
    let allGood = true;
    const issues = [];
    
    selectors.forEach((select, index) => {
        const style = window.getComputedStyle(select);
        const rect = select.getBoundingClientRect();
        
        // Get actual values
        const actual = {
            height: rect.height,
            fontSize: parseFloat(style.fontSize),
            fontWeight: parseInt(style.fontWeight) || (style.fontWeight === 'normal' ? 400 : 500),
            lineHeight: parseFloat(style.lineHeight),
            paddingLeft: parseFloat(style.paddingLeft),
            borderRadius: parseFloat(style.borderRadius),
            fontFamily: style.fontFamily,
            display: style.display,
            alignItems: style.alignItems
        };
        
        console.log(`\n📋 Selector ${index + 1} analysis:`);
        
        // Check height
        const heightOK = Math.abs(actual.height - expected.height) <= 2;
        console.log(`Height: ${actual.height.toFixed(1)}px ${heightOK ? '✅' : '❌'} (expected ${expected.height}px)`);
        if (!heightOK) {
            issues.push(`Selector ${index + 1}: Height ${actual.height.toFixed(1)}px (should be ${expected.height}px)`);
            allGood = false;
        }
        
        // Check font family
        const hasInter = actual.fontFamily.toLowerCase().includes('inter');
        console.log(`Font family: ${actual.fontFamily.split(',')[0]} ${hasInter ? '✅' : '❌'} (should include Inter)`);
        if (!hasInter) {
            issues.push(`Selector ${index + 1}: Font family doesn't include Inter`);
            allGood = false;
        }
        
        // Check font size
        const fontSizeOK = Math.abs(actual.fontSize - expected.fontSize) <= 1;
        console.log(`Font size: ${actual.fontSize}px ${fontSizeOK ? '✅' : '❌'} (expected ${expected.fontSize}px)`);
        if (!fontSizeOK) {
            issues.push(`Selector ${index + 1}: Font size ${actual.fontSize}px (should be ${expected.fontSize}px)`);
            allGood = false;
        }
        
        // Check font weight
        const fontWeightOK = Math.abs(actual.fontWeight - expected.fontWeight) <= 100;
        console.log(`Font weight: ${actual.fontWeight} ${fontWeightOK ? '✅' : '❌'} (expected ${expected.fontWeight})`);
        if (!fontWeightOK) {
            issues.push(`Selector ${index + 1}: Font weight ${actual.fontWeight} (should be ${expected.fontWeight})`);
            allGood = false;
        }
        
        // Check line height
        const lineHeightOK = Math.abs(actual.lineHeight - expected.lineHeight) <= 0.2;
        console.log(`Line height: ${actual.lineHeight} ${lineHeightOK ? '✅' : '❌'} (expected ${expected.lineHeight})`);
        if (!lineHeightOK) {
            issues.push(`Selector ${index + 1}: Line height ${actual.lineHeight} (should be ${expected.lineHeight})`);
            allGood = false;
        }
        
        // Check vertical alignment
        const hasFlexDisplay = actual.display === 'flex';
        const hasCenterAlign = actual.alignItems === 'center';
        const verticalAlignOK = hasFlexDisplay && hasCenterAlign;
        console.log(`Vertical align: display=${actual.display}, align-items=${actual.alignItems} ${verticalAlignOK ? '✅' : '❌'}`);
        if (!verticalAlignOK) {
            issues.push(`Selector ${index + 1}: Not properly centered (should be display:flex, align-items:center)`);
            allGood = false;
        }
        
        // Check padding
        const paddingOK = Math.abs(actual.paddingLeft - expected.paddingLeft) <= 2;
        console.log(`Padding left: ${actual.paddingLeft}px ${paddingOK ? '✅' : '❌'} (expected ${expected.paddingLeft}px)`);
        if (!paddingOK) {
            issues.push(`Selector ${index + 1}: Padding left ${actual.paddingLeft}px (should be ${expected.paddingLeft}px)`);
            allGood = false;
        }
        
        // Check border radius
        const radiusOK = Math.abs(actual.borderRadius - expected.borderRadius) <= 2;
        console.log(`Border radius: ${actual.borderRadius}px ${radiusOK ? '✅' : '❌'} (expected ${expected.borderRadius}px)`);
        if (!radiusOK) {
            issues.push(`Selector ${index + 1}: Border radius ${actual.borderRadius}px (should be ${expected.borderRadius}px)`);
            allGood = false;
        }
    });
    
    // Overall status
    console.log(`\n🎯 OVERALL STATUS: ${allGood ? '✅ PERFECT ALIGNMENT' : '❌ NEEDS FIXING'}`);
    
    if (!allGood) {
        console.log('\n💡 ISSUES TO FIX:');
        issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue}`);
        });
        
        console.log('\n🔧 POSSIBLE SOLUTIONS:');
        if (context === 'FRONTEND') {
            console.log('   - Check if CSS files are properly loaded');
            console.log('   - Verify theme is not overriding selector styles');
            console.log('   - Ensure .wsf-input class is applied to selectors');
            console.log('   - Check for CSS specificity conflicts');
        } else {
            console.log('   - Admin preview should work correctly');
            console.log('   - If issues persist, check CSS compilation');
        }
    }
    
    // Font smoothing check
    console.log('\n🔤 FONT RENDERING CHECK:');
    const firstSelector = selectors[0];
    if (firstSelector) {
        const style = window.getComputedStyle(firstSelector);
        const fontSmoothing = style.webkitFontSmoothing || style.fontSmooth;
        const textRendering = style.textRendering;
        
        console.log(`Font smoothing: ${fontSmoothing || 'default'}`);
        console.log(`Text rendering: ${textRendering || 'default'}`);
        
        const hasAntialiasing = fontSmoothing === 'antialiased';
        const hasOptimizedRendering = textRendering === 'optimizeLegibility';
        
        console.log(`Antialiasing: ${hasAntialiasing ? '✅' : '⚠️'}`);
        console.log(`Optimized rendering: ${hasOptimizedRendering ? '✅' : '⚠️'}`);
    }
    
    // Theme override check
    console.log('\n🎨 THEME OVERRIDE CHECK:');
    selectors.forEach((select, index) => {
        const style = window.getComputedStyle(select);
        
        const appearance = style.appearance || style.webkitAppearance;
        const hasCustomAppearance = appearance === 'none';
        
        const backgroundColor = style.backgroundColor;
        const hasCustomBackground = backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent';
        
        console.log(`Selector ${index + 1}: appearance=${appearance}, background=${backgroundColor}`);
        console.log(`  Custom styling: ${hasCustomAppearance && hasCustomBackground ? '✅' : '⚠️'}`);
    });
    
    // Visual highlight
    console.log('\n🎨 Highlighting selectors for 5 seconds...');
    
    selectors.forEach((select, index) => {
        const style = window.getComputedStyle(select);
        const rect = select.getBoundingClientRect();
        
        // Highlight with different colors based on status
        const height = rect.height;
        const fontSize = parseFloat(style.fontSize);
        const hasInter = style.fontFamily.toLowerCase().includes('inter');
        
        const heightOK = Math.abs(height - 44) <= 2;
        const fontOK = Math.abs(fontSize - 14) <= 1 && hasInter;
        
        let color = 'red'; // Default: has issues
        if (heightOK && fontOK) {
            color = 'green'; // Perfect
        } else if (heightOK || fontOK) {
            color = 'orange'; // Partial
        }
        
        select.style.outline = `3px solid ${color}`;
        select.style.backgroundColor = `rgba(${color === 'green' ? '0,255,0' : color === 'orange' ? '255,165,0' : '255,0,0'}, 0.1)`;
        
        // Add tooltip
        select.title = `Selector ${index + 1}: ${height.toFixed(1)}px, ${fontSize}px, ${style.fontFamily.split(',')[0]}`;
    });
    
    setTimeout(() => {
        selectors.forEach(select => {
            select.style.outline = '';
            select.style.backgroundColor = '';
            select.title = '';
        });
        console.log('🧹 Highlights removed');
    }, 5000);
    
    // Summary
    console.log('\n📋 SUMMARY:');
    console.log('Green outline = Perfect alignment');
    console.log('Orange outline = Partial issues');
    console.log('Red outline = Needs fixing');
    console.log(`Context: ${context}`);
    
    if (allGood) {
        console.log('🎉 Perfect! Selectors are properly aligned and styled');
    } else {
        console.log('🔧 Needs adjustment - check CSS rules and theme conflicts');
        
        if (context === 'FRONTEND') {
            console.log('💡 Compare with Admin Live Preview to see differences');
        }
    }
}
