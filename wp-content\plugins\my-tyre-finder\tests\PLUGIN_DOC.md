# Wheel-Size Tyre & Wheel Fitment Finder Plugin

> **Version:** 1.0.0  |  **Requires:** PHP 8.1 + WordPress 5.8 +

---

## 📘 Plugin Overview

**Wheel-Size** is an all-in-one tyre & wheel fitment finder for WordPress / WooCommerce.  
Powered by the public [Wheel-Size API](https://developer.wheel-size.com/), it lets visitors search OEM & optional wheel / tyre sizes either **by vehicle** (make → model → year → modification) or **by raw tyre size** (section width × aspect ratio × rim diameter).

Key capabilities:

* Modern Tailwind-based, responsive UI with **Stepper** or **Inline** layouts
* Real-time AJAX data loading & caching
* Built-in **Garage** to save favourite vehicles (opt-in)
* Brand / region filters & feature flags managed from an admin UI
* Google Analytics / GTM event tracking
* Multilingual JSON translations (PO-less)
* Classic **Widget** + shortcodes `[tyre_finder]`, `[wheel_fit]`
* REST & AJAX endpoints for 3-rd-party integrations

---

## ⚙️ Features

| # | Feature | Description | Example / Reference |
|---|---------|-------------|---------------------|
| 1 | By-Vehicle search | Guided 4-step form: Make → Model → Year → Modification. Results grouped into _Factory_ / _Optional_. | `templates/finder-form.twig` lines 1-150 |
| 2 | By-Tyre-Size search | Query OEM vehicles that fit a given tyre size. | `src/services/WheelSizeApi.php::searchByTire()` |
| 3 | Stepper vs Inline layouts | Choose between classic wizard or compact inline selects. | `Appearance → Form Layout` setting |
| 4 | Saved **Garage** | LocalStorage powered drawer to store vehicles across visits. | `assets/js/garage.js`, Garage markup inside Twig template |
| 5 | Dynamic primary colour | CSS variables & inline style compiled from admin colour-picker. | `src/public/Frontend.php::output_dynamic_styles()` |
| 6 | Auto-search | Optionally fire search automatically on final selection. | `auto_search_on_last_input` option handled in JS |
| 7 | Brand include/exclude | Whitelist or blacklist OEM brands globally. | `BrandFilters.php`, admin **Features** tab |
| 8 | Region filter | Limit makes to specific sales regions. | `wheel_size_regions` option, used in `WheelSizeApi::getMakes()` |
| 9 | Analytics | Send custom events to Google Analytics 4 / GTM. | **Analytics** settings page |
|10 | API usage stats & Logs | Dashboard for total / success / cache hits, plus JSON logs & reset tools. | `Admin::display_api_stats()` + **Logs** tab |
|11 | Multilingual UI | JSON-based locale packs, automatic fallback. | `src/Translations/TranslationManager.php` |
|12 | Classic Widget | `TyreFinderWidget` for legacy sidebars & page builders. | `src/widgets/TyreFinderWidget.php` |
|13 | WooCommerce stub | Placeholder class to auto-link results to product catalog. | `src/services/WooIntegration.php` |

> **Note:** A full REST controller (`src/rest/FitmentController.php`) is scaffolded for future extensions.

---

## 🖥️ UI Elements & Interactions

### 1. Finder Widget

* **Stepper progress bar** – highlights current selection step
* **Dynamic selects** – each subsequent select is populated via AJAX (loading spinners included)
* **Search button** – disabled until all required fields are chosen (or auto-search fires)
* **Results panel** – collapsible section listing Factory / Optional sizes in responsive grid
* **Garage icon / counter** – toggles garage drawer; counter shows saved vehicles
* **Garage drawer** – slide-in panel with list, _Load_ & _Clear all_ actions
* Empty-state messages, animated toasts & skeleton loaders included

### 2. Admin Screens

Menu: `Wheel-Size` → sub pages **API**, **Appearance**, **Features**, **Analytics**, **Logs**

* **API** – API key (masked), cache TTL, live connection tester, usage statistics
* **Appearance** – colour-picker, form layout, tab visibility, auto-search toggle with **Live Preview** powered by Twig + Tailwind in-admin
* **Features** – enable Garage / OE-filter, include / exclude brands
* **Analytics** – GA4 ID, GTM ID, enable/disable tracking, reset analytics
* **Logs** – AJAX-powered viewer & _Clear logs_ / _Reset stats_ buttons

All admin pages use native WP components, Tailwind utilities & lucide icons for a consistent look-and-feel.

### States & Feedback

* Hover / focus rings on interactive elements (`focus:ring-blue-500`)
* Disabled selects & buttons greyed out until prerequisites met
* Loading spinners (CSS animate-spin)
* Toast notifications for Garage save events

---

## 🛠️ Installation

1. Copy the plugin folder **`my-tyre-finder`** into `wp-content/plugins/` (or `git clone ...`).
2. Run

   ```bash
   composer install --no-dev --optimize-autoloader
   ```
3. Activate **Wheel-Size** via **Plugins → Installed Plugins** in WP admin.

Optional for asset development:

```bash
npm install          # install JS deps
npm run dev          # compile + watch assets (Laravel Mix)
```

> The distributed `/assets` folder is already compiled – no build step required for production.

---

## 🔧 Configuration

| Setting | Location | Default | Description |
|---------|----------|---------|-------------|
| **API Key** | API tab | – | Your Wheel-Size API key (required) |
| **Search cache TTL** | API tab | `7200 s` | Cache vehicle/tyre search results for X seconds |
| **Primary colour** | Appearance | `#2563eb` | Brand colour applied across widget |
| **Form layout** | Appearance | `stepper` | `stepper` or `inline` |
| **Show "By Vehicle"** | Appearance | `true` | Toggle Vehicle tab |
| **Show "By Size"** | Appearance | `true` | Toggle Tyre-size tab |
| **Auto-search on last input** | Appearance | `false` | Skip explicit submit |
| **Include brands** | Features | – | Only show selected OEM brands |
| **Exclude brands** | Features | – | Hide selected OEM brands |
| **Allowed regions** | Features | – | Filter makes by sales region codes (e.g. `eu`, `us`) |
| **Enable Garage** | Features | `false` | Activates saved-vehicles drawer |
| **Enable OE filter** | Features | `false` | Hide _Optional_ sizes in results |
| **Enable tracking** | Analytics | `false` | Send events to GA / GTM |
| **GA4 Measurement ID** | Analytics | – | `G-XXXXXXXXXX` |
| **GTM ID** | Analytics | – | `GTM-XXXX` |

All settings are persisted as WordPress options; you can also define them as constants (`wp-config.php`) for CI-driven deployments.

---

## 🚀 Usage

### Shortcodes

```wordpress
[tyre_finder]
[wheel_fit] <!-- alias -->
```

Attributes are not required – all styling & behaviour come from the admin settings.

### Widget

`Appearance → Widgets → Tyre Finder` – drag into any sidebar / footer.

### PHP API

```php
use MyTyreFinder\Services\WheelSizeApi;
$api = new WheelSizeApi();
$factory = $api->searchByModel('audi', 'a4', 2020);
```

### CLI Helpers

If you use WP-CLI, clear cache via:

```bash
wp transient delete --all --search=wheel_fit_*
```

---

## 🔌 Integrations & Extensibility

* **WooCommerce** – `WooIntegration` stub ready for hooking search results to product filters.
* **REST API** – scaffolded namespace `tyre-finder/v1`; register your own routes via `FitmentController` or external plugins.
* **DI Container** – Service classes are registered in a PSR-11 container (League\Container) allowing you to swap implementations.
* **Twig templates** – Copy any `.twig` file to your theme and filter `wheel_size_template_path` to override.
* **JS events** – Frontend fires `wheel-fit:loaded` and `wheel-fit:searchCompleted` custom events for further scripting.

---

## 🧪 Examples / Demos

```html
<div style="max-width:420px;margin:auto;">
  [tyre_finder]
</div>
```

![Finder widget](https://user-images.githubusercontent.com/000000/placeholder.png)

![Admin Appearance](https://user-images.githubusercontent.com/000000/placeholder2.png)

> Replace screenshots with real captures from your installation.

---

## ⚠️ Limitations / Known Issues

* **API key quota** – Free Wheel-Size plan is limited; heavy traffic may hit the limit.
* **CSV fallback** – A local CSV backup exists but mapping is minimal and may lack full data.
* **By-Size search UI** – Currently implemented in JS only; needs dedicated Twig view.
* **WooCommerce linking** – Product sync is not implemented yet.
* **Accessibility (a11y)** – ARIA labels & keyboard navigation need further audit.
* **REST endpoints** – Provided as stubs; not production-ready.

---

## 🗒️ Changelog

### 1.0.0 – Initial release

* Core finder widget (by vehicle & by tyre size)
* Admin settings (API, Appearance, Features, Analytics, Logs)
* Multilingual support & Garage feature

---

## 🧾 License & Author

* **License:** GPL-2.0-or-later (see `LICENSE` file)
* **Author:** Your Name – <https://example.com>
* **Plugin URI:** <https://example.com/wheel-size>

Feel free to open issues or pull requests on GitHub! 