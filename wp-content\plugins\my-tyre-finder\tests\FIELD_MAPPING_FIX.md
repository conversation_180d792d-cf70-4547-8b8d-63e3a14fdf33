# 🐞 Fix: Empty Options Due to Wrong Field Names

## Problem Description
When API returns data with different field names (like `title`, `range`, `year_range` instead of `name`), the populate methods create empty options because they only check for `generation.name`.

**Example:**
- API returns: `{ title: "EV 570 HP", slug: "ev-570-hp" }`
- Code expects: `generation.name` (undefined)
- Result: Empty option with working value but no visible text

**Root Issue:**
```javascript
// ❌ BEFORE: Only checks 'name' field
select.add(new Option(generation.name, generation.slug));
// If generation.name is undefined → empty option text
```

## Root Cause Analysis
API responses use different field names for the same data:
- **Generations**: `title`, `range`, `year_range`, `gen` instead of `name`
- **Models**: `title`, `model` instead of `name`  
- **Years**: `year`, `title` instead of `name`
- **Modifications**: `title`, `modification` instead of `name`

## Solution Applied
Implemented fallback field mapping with priority order:

```javascript
// ✅ AFTER: Smart field mapping with fallbacks
populateGenerations(generations) {
    generations.forEach(g => {
        // Priority order: name → title → range → year_range → gen → slug → fallback
        const label = g.name || g.title || g.range || g.year_range || g.gen || g.slug || 'Unknown Generation';
        const value = g.slug || g.name || g.title || g.range || label;
        select.add(new Option(label, value));
    });
    
    // Auto-select if single item
    if (generations.length === 1) {
        select.value = select.options[1].value; // first after placeholder
        this.onGenerationSelect(select.value); // trigger next step
    }
}
```

## Field Mapping Priority

### Generations
```javascript
const label = g.name || g.title || g.range || g.year_range || g.gen || g.slug || 'Unknown Generation';
```

### Models  
```javascript
const label = m.name || m.title || m.model || m.slug || 'Unknown Model';
```

### Years
```javascript
const label = y.name || y.year || y.title || y.slug || 'Unknown Year';
```

### Modifications
```javascript
const label = mod.name || mod.title || mod.modification || mod.slug || 'Unknown Modification';
```

## Auto-Selection Feature
Added auto-selection for single items to improve UX:

```javascript
// If only one option, auto-select and trigger next step
if (items.length === 1) {
    select.value = select.options[1].value; // skip placeholder
    this.onItemSelect(select.value); // trigger progression
}
```

## Benefits
1. **No Empty Options** - Always finds a displayable field name
2. **API Flexibility** - Works with any field naming convention
3. **Auto-Progression** - Single items auto-select for faster UX
4. **Fallback Safety** - Always has a fallback value
5. **Consistent Behavior** - All populate methods work the same way

## Testing
Run test script to verify all field mappings:
```javascript
// Copy test-field-mapping-fix.js content to browser console
```

## Expected Results
- **Any field name**: Displays correctly (title, range, year_range, etc.)
- **Single item**: Auto-selected, triggers next step
- **Multiple items**: Shows placeholder, manual selection required
- **No empty options**: Always has visible text

## Files Modified
- `wp-content/plugins/my-tyre-finder/assets/js/finder.js`
  - `populateModels()` - Lines 662-683 (added field mapping + auto-select)
  - `populateYears()` - Lines 745-763 (added field mapping + auto-select)
  - `populateGenerations()` - Lines 764-784 (added field mapping + auto-select)
  - `populateModifications()` - Lines 866-889 (added field mapping + auto-select)

## Real-World Impact
This fix resolves the "empty but working selector" issue where:
- ✅ API data loads correctly
- ✅ Values are set properly  
- ✅ Search functionality works
- ❌ **BUT** user sees empty options (FIXED!)

Now users see proper text labels regardless of API field naming! 🎉
