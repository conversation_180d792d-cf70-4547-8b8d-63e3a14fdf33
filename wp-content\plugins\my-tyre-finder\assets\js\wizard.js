// OE Only Mode setting from WordPress admin
const WIZARD_OE_ONLY_MODE = !!(window.WheelFitData && window.WheelFitData.enableOeFilter);

class WheelWizard {
    constructor() {
        console.log('[WheelWizard] Constructor called');
        console.log('[WheelWizard] OE Only Mode:', WIZARD_OE_ONLY_MODE);

        this.currentStep = 1;
        this.selection = {
            make: null,
            model: null,
            year: null,
            modification: null,
        };
        this.cache = new Map();
        this.totalSteps = 5;
        this.elements = {
            wizard: document.getElementById('wheel-fit-wizard'),
            steps: document.querySelectorAll('.wizard-step'),
            backBtn: document.getElementById('wizard-back-btn'),
            nextBtn: document.getElementById('wizard-next-btn'),
            progressBar: document.getElementById('wizard-progress-bar'),
            stepNames: document.querySelectorAll('.wizard-step-name'),
            makesGrid: document.getElementById('wizard-makes-grid'),
            modelsList: document.getElementById('wizard-models-list'),
            yearsList: document.getElementById('wizard-years-list'),
            modificationsList: document.getElementById('wizard-modifications-list'),
            resultsContainer: document.getElementById('wizard-results'),
            resultsContent: document.querySelector('#wizard-results #search-results'),
            wizardHeader: document.getElementById('wizard-header'),
            wizardNav: document.getElementById('wizard-nav'),
            selectionSummary: document.getElementById('wizard-selection-summary'),
            modelsCount: document.getElementById('wizard-models-count'),
            yearsCount: document.getElementById('wizard-years-count'),
            modificationsCount: document.getElementById('wizard-modifications-count'),
            selectedMake: document.getElementById('wizard-selected-make'),
            selectedModel: document.getElementById('wizard-selected-model'),
            selectedYear: document.getElementById('wizard-selected-year'),
        };

        console.log('[WheelWizard] Elements found:', {
            wizard: !!this.elements.wizard,
            steps: this.elements.steps.length,
            backBtn: !!this.elements.backBtn,
            nextBtn: !!this.elements.nextBtn,
            makesGrid: !!this.elements.makesGrid
        });

        this.init();

        /* ---------- Apply i18n to everything inside wizard ---------- */
        if (typeof window.t === 'function') {
            this.translateDom(this.elements.wizard);
        }
    }

    /* Helper: translate every [data-i18n] node inside container */
    translateDom(container){
        if (!container) return;
        console.log('[WheelWizard] Translating DOM in container:', container);

        // Use the improved applyStaticTranslations function if available
        if (typeof window.applyStaticTranslations === 'function') {
            console.log('[WheelWizard] Using improved translation system');
            window.applyStaticTranslations(container);
        } else {
            // Fallback to basic translation
            console.log('[WheelWizard] Using basic translation system');
            const elements = container.querySelectorAll('[data-i18n]');
            console.log(`[WheelWizard] Found ${elements.length} elements with data-i18n`, elements);
            elements.forEach((el, index) => {
                const key = el.dataset.i18n;
                const originalText = el.textContent.trim();
                const translation = t(key, originalText);
                console.log(`[WheelWizard] Element ${index + 1}: "${key}" -> "${translation}"`);
                el.textContent = translation;
            });
        }
    }

    init() {
        console.log('[WheelWizard] Initializing...');
        console.log('[WheelWizard] Wizard container:', this.elements.wizard);

        if (!this.elements.wizard) {
            console.error('[WheelWizard] Wizard container not found!');
            return;
        }

        console.log('[WheelWizard] Found wizard container, proceeding with initialization');
        this.bindEvents();
        this.loadMakes();
        this.updateNav();
        console.log('[WheelWizard] Initialization complete');
    }
    
    ajax(action, data = {}) {
        const body = new URLSearchParams({ action, ...data });
        if (window.WheelFitData && window.WheelFitData.nonce) {
            body.append('nonce', window.WheelFitData.nonce);
        }
        return fetch(window.WheelFitData.ajaxurl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body,
        }).then(res => res.json());
    }

    bindEvents() {
        console.log('[WheelWizard] Binding events...');
        console.log('[WheelWizard] Next button:', this.elements.nextBtn);
        console.log('[WheelWizard] Back button:', this.elements.backBtn);

        if (this.elements.nextBtn) {
            this.elements.nextBtn.addEventListener('click', () => this.nextStep());
        } else {
            console.error('[WheelWizard] Next button not found!');
        }

        if (this.elements.backBtn) {
            this.elements.backBtn.addEventListener('click', () => this.prevStep());
            this.elements.backBtn.className = 'px-6 py-2 text-sm font-semibold text-wsf-muted bg-wsf-bg border border-wsf-border rounded-md hover:bg-wsf-surface transition hidden';
        } else {
            console.error('[WheelWizard] Back button not found!');
        }
    }
    
    // Step 1: Makes
    async loadMakes() {
        const makes = await this.ajax('wizard_get_makes');
        if (makes.success) {
            this.elements.makesGrid.innerHTML = '';
            makes.data.forEach(make => {
                const item = this.createGridItem(make);
                item.addEventListener('click', () => {
                    this.selection.make = make;
                    this.selection.model = null;
                    this.selection.year = null;
                    this.selection.modification = null;

                    this.setActiveListItem(this.elements.makesGrid, item);
                    this.updateNav();

                    // Automatically go to the next step
                    this.nextStep();
                });
                this.elements.makesGrid.appendChild(item);
            });

            // Re-apply translations to the newly loaded content
            this.translateDom(this.elements.makesGrid);
        }
    }
    
    // ... Other steps and methods will be added here
    async nextStep() {
        if (this.currentStep === 1 && this.selection.make) await this.loadModels();
        else if (this.currentStep === 2 && this.selection.model) await this.loadYears();
        else if (this.currentStep === 3 && this.selection.year) await this.loadModifications();
        else if (this.currentStep === 4 && this.selection.modification) {
            await this.search();
            return; 
        }
        
        if (this.currentStep < 4) {
            this.goToStep(this.currentStep + 1);
        }
    }
    prevStep() {
        if (this.currentStep > 1) {
            this.goToStep(this.currentStep - 1);
        }
    }
    goToStep(step) {
        this.currentStep = step;

        this.elements.steps.forEach(s => {
            s.classList.add('hidden', 'opacity-0');
        });

        const currentStepEl = document.getElementById(`wizard-step-${this.currentStep}`);
        if (currentStepEl) {
            currentStepEl.classList.remove('hidden');
            setTimeout(() => currentStepEl.classList.remove('opacity-0'), 50);
        }

        // Scroll to top of wizard container to ensure content is visible
        if (this.elements.wizard) {
            this.elements.wizard.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        this.renderBreadcrumbs();
        this.updateNav();
    }
    updateNav() {
        // Back button
        this.elements.backBtn.classList.toggle('hidden', this.currentStep === 1);
        
        // Progress bar and labels
        let progress;
        if (this.currentStep === this.totalSteps) {
            progress = 100; // full width when on last step
        } else {
            const offset = 100 / (this.totalSteps * 2); // balanced padding on both sides
            const usable = 100 - offset * 2;
            progress = ((this.currentStep - 1) / (this.totalSteps - 1)) * usable + offset;
        }
        this.elements.progressBar.style.width = `${progress}%`;
        
        this.elements.stepNames.forEach((name, i) => {
            name.classList.toggle('text-wsf-primary', i < this.currentStep);
            name.classList.toggle('text-wsf-muted', i >= this.currentStep);
        });

        // Next button
        const isFinal = this.currentStep === 4;
        this.elements.nextBtn.disabled = true;
        const labelKey = isFinal ? 'button_search' : 'button_next';
        this.elements.nextBtn.textContent = t(labelKey, isFinal ? 'Show Results' : 'Next');

        // Apply style identical to classic search button on final step
        if (isFinal) {
            this.elements.nextBtn.className = 'btn-primary w-full md:w-auto py-3 px-10';
        } else {
            this.elements.nextBtn.className = 'btn-primary px-6 py-2 text-sm rounded-md';
        }

        const selection = this.getSelectionForStep(this.currentStep);
        if (selection) {
            this.elements.nextBtn.disabled = false;
        }
        // Remove summary from bottom
        this.elements.selectionSummary.innerHTML = '';
        this.elements.selectionSummary.classList.add('hidden');

        // Hide Next button completely on results step
        this.elements.nextBtn.classList.toggle('hidden', this.currentStep === 5);
    }
    updateSummary() {
        const summary = [
            this.selection.make?.name,
            this.selection.model?.name,
            this.selection.year,
        ].filter(Boolean).join(' <span class="text-wsf-muted mx-1">/</span> ');
        this.elements.selectionSummary.innerHTML = summary;
        this.elements.selectionSummary.classList.toggle('hidden', !summary);
    }
    getSelectionForStep(step) {
        if (step === 1) return this.selection.make;
        if (step === 2) return this.selection.model;
        if (step === 3) return this.selection.year;
        if (step === 4) return this.selection.modification;
        return null;
    }
    
    async loadModels() {
        this.updateBreadcrumbs('model');

        const stepContainer = this.elements.modelsList.parentElement;
        
        // Ensure search input is only created once
        if (!stepContainer.querySelector('.wizard-search-wrapper')) {
            const searchWrapper = this.createSearchInput('model', (term) => {
                this.filterList(this.elements.modelsList, term);
            });
            stepContainer.insertBefore(searchWrapper, this.elements.modelsList);
        }

        this.elements.modelsList.innerHTML = this.getLoaderHtml();
        const models = await this.ajax('wizard_get_models', { make: this.selection.make.slug });
        this.elements.modelsList.innerHTML = '';
        if (models.success && models.data.length > 0) {
            this.elements.modelsCount.textContent = `${t('wizard_models_count_label','Models count')}: ${models.data.length}`;
            this.elements.modelsCount.className = 'text-sm text-wsf-muted';
            this.elements.modelsCount.classList.remove('hidden');

            models.data.forEach(model => {
                const item = this.createListItem(model.name, model);
                item.addEventListener('click', () => {
                    this.selection.model = model;
                    this.selection.year = null;
                    this.selection.modification = null;
                    this.setActiveListItem(this.elements.modelsList, item);
                    this.updateNav();
                    this.nextStep(); // Auto-advance
                });
                this.elements.modelsList.appendChild(item);
            });

            // Re-apply translations to the newly loaded content
            this.translateDom(this.elements.modelsList.parentElement);
        }
    }
    async loadYears() {
        this.updateBreadcrumbs('year');
        this.elements.yearsList.innerHTML = this.getLoaderHtml();
        const years = await this.ajax('wizard_get_years', { make: this.selection.make.slug, model: this.selection.model.slug });
        this.elements.yearsList.innerHTML = '';
        if (years.success && years.data.length > 0) {
            this.elements.yearsCount.textContent = `${t('wizard_years_count_label','Production years')}: ${years.data.length}`;
            this.elements.yearsCount.className = 'text-sm text-wsf-muted';
            this.elements.yearsCount.classList.remove('hidden');
            years.data.forEach(year => {
                const item = this.createListItem(year.name, year);
                item.addEventListener('click', () => {
                    this.selection.year = year.name;
                    this.selection.modification = null;
                    this.setActiveListItem(this.elements.yearsList, item);
                    this.updateNav();
                    this.nextStep(); // Auto-advance
                });
                this.elements.yearsList.appendChild(item);
            });

            // Re-apply translations to the newly loaded content
            this.translateDom(this.elements.yearsList.parentElement);
        }
    }
    async loadModifications() {
        this.updateBreadcrumbs('modification');
        this.elements.modificationsList.innerHTML = this.getLoaderHtml();
        const mods = await this.ajax('wizard_get_modifications', { make: this.selection.make.slug, model: this.selection.model.slug, year: this.selection.year });
        this.elements.modificationsList.innerHTML = '';
        if (mods.success && mods.data.length > 0) {
            this.elements.modificationsCount.textContent = `${t('wizard_mods_count_label','Modifications count')}: ${mods.data.length}`;
            this.elements.modificationsCount.className = 'text-sm text-wsf-muted';
            this.elements.modificationsCount.classList.remove('hidden');
            mods.data.forEach(mod => {
                const item = this.createListItem(mod.name, mod);
                // Use unified styling - no special overrides needed
                item.addEventListener('click', () => {
                    this.selection.modification = mod;
                    this.setActiveListItem(this.elements.modificationsList, item);
                    this.updateNav();
                    // Auto-run search immediately after selecting modification
                    this.search();
                });
                this.elements.modificationsList.appendChild(item);
            });

            // Re-apply translations to the newly loaded content
            this.translateDom(this.elements.modificationsList.parentElement);
        }
    }
    async search() {
        // Show the results step inside the wizard (do not hide the wizard itself)
        const modStep = document.getElementById('wizard-step-4');
        if (modStep) modStep.classList.add('hidden', 'opacity-0');

        this.elements.resultsContainer.classList.remove('hidden');
        this.elements.resultsContainer.classList.add('opacity-0');
        setTimeout(() => this.elements.resultsContainer.classList.remove('opacity-0'), 50);

        if (!this.elements.resultsContent) {
            this.elements.resultsContent = this.elements.resultsContainer.querySelector('#search-results');
        }

        this.elements.resultsContent.classList.add('hidden'); // Hide content until data is ready
        this.elements.resultsContainer.insertAdjacentHTML('afterbegin', this.getLoaderHtml());

        const params = {
            make: this.selection.make.slug,
            model: this.selection.model.slug,
            year: this.selection.year,
            modification: this.selection.modification.slug,
        };
        const results = await this.ajax('wizard_search', params);
        
        console.log("Wizard search results:", results);

        const loader = this.elements.resultsContainer.querySelector('.wizard-loader');
        if (loader) loader.remove();
        
        if (results.success) {
             this.displayResults(results.data);
        } else {
            this.elements.resultsContainer.innerHTML = `<p class="text-red-500 text-center">Sorry, an error occurred while fetching results.</p>`;
            console.error("Wizard search failed:", results);
        }

        // Update nav/steps to fifth step
        this.currentStep = 5;
        this.updateNav();
    }

    displayResults(data) {
        const container = this.elements.resultsContainer.querySelector('#search-results');
        if (!container) return;

        container.classList.remove('hidden'); // Show the results section
        
        const modNameLabel = this.selection.modification?.name ? ` — ${this.selection.modification.name}` : '';

        // Build vehicle label based on available data
        let vehicleLabel;
        if (this.selection.generation) {
            // Generation flow: show generation instead of year
            let generationName;
            if (this.selection.generation.name) {
                generationName = this.selection.generation.name;
            } else if (typeof this.selection.generation === 'string') {
                // If generation is just a string (ID/slug), try to get the display name
                generationName = this.getGenerationDisplayName(this.selection.generation);
            } else {
                generationName = this.selection.generation.title || this.selection.generation.range || 'Generation';
            }
            vehicleLabel = `${this.selection.make.name} ${this.selection.model.name} ${generationName}`;
        } else if (this.selection.year) {
            // Year flow: show year in parentheses
            vehicleLabel = `${this.selection.make.name} ${this.selection.model.name} (${this.selection.year})`;
        } else {
            // Fallback
            vehicleLabel = `${this.selection.make.name} ${this.selection.model.name}`;
        }

        container.querySelector('#vehicle-label').textContent = `${vehicleLabel}${modNameLabel}`;

        const modInfoContainer = container.querySelector('#selected-modification-info');
        modInfoContainer.innerHTML = ''; // Clear previous
        const modInfo = this.createModInfoBar(this.selection.modification);
        modInfoContainer.appendChild(modInfo);

        const factoryGrid = container.querySelector('#factory-grid');
        const optionalGrid = container.querySelector('#optional-grid');
        const factorySection = container.querySelector('#factory-section');
        const optionalSection = container.querySelector('#optional-section');
        const noResults = container.querySelector('#no-results');

        factoryGrid.innerHTML = '';
        optionalGrid.innerHTML = '';
        factorySection.classList.add('hidden');
        optionalSection.classList.add('hidden');
        noResults.classList.add('hidden');

        const hasFactoryResults = data && data.factory && data.factory.length > 0;
        const hasOptionalResults = data && data.optional && data.optional.length > 0;

        // Show factory results
        if (hasFactoryResults) {
            data.factory.forEach(size => factoryGrid.appendChild(this.createSizeCard(size, 'factory')));
            factorySection.classList.remove('hidden');
        }

        // Show optional results only if OE only mode is disabled
        if (hasOptionalResults && !WIZARD_OE_ONLY_MODE) {
            data.optional.forEach(size => optionalGrid.appendChild(this.createSizeCard(size, 'optional')));
            optionalSection.classList.remove('hidden');
        }

        // Hide optional section if OE only mode is enabled
        if (WIZARD_OE_ONLY_MODE) {
            optionalSection.classList.add('hidden');
            console.log('[WheelWizard] Optional section hidden due to OE only mode');
        }

        // Calculate hasResults - in OE only mode, only factory sizes matter
        const hasResults = WIZARD_OE_ONLY_MODE ? hasFactoryResults : (hasFactoryResults || hasOptionalResults);

        if (!hasResults) {
            noResults.classList.remove('hidden');
        }

        console.log('[WheelWizard] Results display:', {
            factoryCount: data?.factory?.length || 0,
            optionalCount: data?.optional?.length || 0,
            oeOnlyMode: WIZARD_OE_ONLY_MODE,
            hasResults: hasResults
        });

        // Re-apply translations to the results container
        this.translateDom(container);
    }

    createModInfoBar(mod) {
        const container = document.createElement('div');
        container.className = 'flex items-center flex-wrap gap-2 text-xs font-semibold';

        // Normalise data fields to match classic widget
        const fuel   = mod.engine?.fuel ?? mod.fuel;
        const hp     = mod.engine?.power?.hp ?? mod.power_hp;
        const engine = mod.engine?.type ?? mod.engine_type;

        if (fuel) {
            container.insertAdjacentHTML('beforeend', `<span class="px-2 py-1 bg-gray-100 text-gray-700 rounded">${fuel.toUpperCase()}</span>`);
        }
        if (hp) {
            container.insertAdjacentHTML('beforeend', `<span class="px-2 py-1 bg-green-100 text-green-800 rounded">${hp} HP</span>`);
        }
        if (engine) {
            container.insertAdjacentHTML('beforeend', `<span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">${engine}</span>`);
        }

        return container;
    }

    /* ---------------- Size Card (Clone of classic forms) ------------------ */
    translate(key, fallback = null) {
        if (typeof window.t === 'function') {
            return window.t(key, fallback);
        }
        return fallback ?? key;
    }

    getDiameters(data) {
        const extract = (s) => ((s || '').match(/R(\d+)/) || [, ''])[1];
        const front = extract(data.tire_full);
        const rear  = extract(data.rear_tire_full);
        const arr   = (front && rear && front !== rear) ? [front, rear] : [front];
        return arr.filter(Boolean);
    }

    createSizeCard(sizeData, type) {
        const card = document.createElement('div');
        card.className = 'size-card relative group bg-wsf-surface border border-wsf-border rounded-xl p-4 pt-6 shadow-sm transition hover:ring hover:ring-wsf-primary/20 animate-fadeIn';

        // Badge (Factory / Optional)
        const badgeTypeClass = type === 'factory' ? 'bg-orange-100 text-orange-700' : 'bg-blue-100 wsf-text-accent';
        const badgeLabel = this.translate(`badge_${type}`, type === 'factory' ? 'Factory' : 'Optional');
        const badgeHTML = `<span class="absolute top-1.5 right-1.5 z-10 pointer-events-none px-2 py-[2px] text-[10px] font-semibold rounded uppercase tracking-wider ${badgeTypeClass}">${badgeLabel}</span>`;

        // Diameter block (single or dual)
        const diams = this.getDiameters(sizeData);
        const diameterHTML = diams.length === 1
            ? `<span class="text-[clamp(1.75rem,5vw,2.5rem)] font-black text-black leading-none">${diams[0]}"</span>`
            : `<div class="flex items-baseline gap-1">
                 <span class="text-[clamp(1.75rem,5vw,2.5rem)] font-black text-black leading-none">${diams[0]}"</span>
                 <span class="text-xl font-semibold text-gray-500">/</span>
                 <span class="text-[clamp(1.75rem,5vw,2.5rem)] font-black text-black leading-none">${diams[1]}"</span>
               </div>`;

        // Tire sizes (front / rear)
        const isDual = !sizeData.showing_fp_only && sizeData.rear_tire_full;
        const sizesHTML = isDual
            ? `<div class="mt-2 space-y-0.5 text-sm text-gray-700"><p><span class="font-semibold text-gray-500">${this.translate('position_front','Front')}:</span> ${sizeData.tire_full}</p><p><span class="font-semibold text-gray-500">${this.translate('position_rear','Rear')}:&nbsp;</span> ${sizeData.rear_tire_full}</p></div>`
            : `<div class="text-sm md:text-base text-gray-700 mt-1 font-medium tracking-tight">${sizeData.tire_full}</div>`;

        card.innerHTML = `${badgeHTML}${diameterHTML}${sizesHTML}`;
        return card;
    }

    fuelIcon(fuel, className) {
        switch ((fuel || '').toLowerCase()) {
            case 'diesel': return `<svg class="${className}" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.707 2.293a1 1 0 010 1.414L9.414 7.5H11a1 1 0 110 2H9.414l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>`;
            case 'petrol': return `<svg class="${className}" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.707 2.293a1 1 0 010 1.414L9.414 7.5H11a1 1 0 110 2H9.414l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>`;
            default: return `<svg class="${className}" viewBox="0 0 20 20" fill="currentColor"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" /></svg>`;
        }
    }

    // UI Helpers
    createGridItem(item) {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'group flex flex-col items-center justify-center bg-transparent border border-wsf-border rounded-xl p-3 h-24 shadow-sm hover:shadow-md hover:border-wsf-primary transition-all duration-200 text-center';
        button.dataset.slug = item.slug;
        
        const logoHtml = `<div class="w-10 h-10 mb-2 flex items-center justify-center">
            ${item.logo ? `<img src="${item.logo}" alt="${item.name}" class="max-w-full max-h-full object-contain">` : ''}
        </div>`;

        button.innerHTML = `
            ${logoHtml}
            <span class="text-xs font-medium text-wsf-primary leading-tight break-words group-hover:text-wsf-primary">
                ${item.name}
            </span>
        `;
        return button;
    }
    createListItem(text, item) {
        const button = document.createElement('button');
        button.type = 'button';
        // Unified classes for all list items - consistent styling
        button.className = 'list-item px-4 py-3 bg-wsf-bg rounded-lg shadow-sm border border-wsf-border hover:border-wsf-primary hover:shadow-md transition-all duration-200 text-center min-h-[48px] flex items-center justify-center';

        const span = document.createElement('span');
        span.className = 'text-sm font-semibold text-wsf-primary block';
        span.textContent = text;
        span.title = text;

        button.appendChild(span);
        button.dataset.slug = item.slug;
        return button;
    }
    setActiveListItem(container, item) {
        container.querySelectorAll('button').forEach(el => {
            el.classList.remove('border-wsf-primary', 'bg-wsf-primary', 'text-white', 'shadow-md');
            el.classList.add('border-wsf-border', 'bg-wsf-bg', 'text-wsf-primary');
            el.querySelector('span').classList.remove('text-white');
            el.querySelector('span').classList.add('text-wsf-primary');
        });
        item.classList.remove('border-wsf-border', 'bg-wsf-bg', 'text-wsf-primary');
        item.classList.add('border-wsf-primary', 'bg-wsf-primary', 'text-white', 'shadow-md');
        item.querySelector('span').classList.remove('text-wsf-primary');
        item.querySelector('span').classList.add('text-white');
    }
    getLoaderHtml() {
        return `<div class="wizard-loader text-center p-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-wsf-primary mx-auto"></div></div>`;
    }
    createSearchInput(type, onInputCallback) {
        const searchWrapper = document.createElement('div');
        searchWrapper.className = 'relative mb-6 wizard-search-wrapper w-full';

        const searchIcon = `<div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg class="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" /></svg>
        </div>`;

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = t('wizard_search_placeholder', 'Search models...');
        searchInput.className = 'w-full pr-12 pl-4 py-3 h-12 border border-gray-300 rounded-lg shadow-sm focus:border-wsf-primary focus:ring focus:ring-blue-200 focus:ring-opacity-50 outline-none transition placeholder-gray-400 text-base bg-white';

        searchInput.addEventListener('input', (e) => onInputCallback(e.target.value));

        searchWrapper.innerHTML = searchIcon;
        searchWrapper.appendChild(searchInput);
        return searchWrapper;
    }

    filterList(listElement, searchTerm) {
        const term = searchTerm.toLowerCase();
        listElement.querySelectorAll('.list-item').forEach(item => {
            const itemName = item.textContent.toLowerCase();
            item.classList.toggle('hidden', !itemName.includes(term));
        });
    }

    // Breadcrumb logic
    updateBreadcrumbs(step) {
        let breadcrumbHtml = '';
        if (this.selection.make) {
            breadcrumbHtml += `<span class="font-medium text-wsf-primary">${this.selection.make.name}</span>`;
        }
        if (this.selection.model && (step === 'year' || step === 'modification')) {
            breadcrumbHtml += ` <span class="text-wsf-muted mx-1">/</span> <span>${this.selection.model.name}</span>`;
        }
        if (this.selection.year && step === 'modification') {
            breadcrumbHtml += ` <span class="text-wsf-muted mx-1">/</span> <span>${this.selection.year}</span>`;
        }

        let container = document.getElementById(`wizard-breadcrumbs-${this.currentStep}`);
        if (!container) {
            container = document.getElementById(`wizard-breadcrumbs-4`); // fallback
        }
        
        if (container) {
            container.innerHTML = breadcrumbHtml;
            container.classList.toggle('hidden', !breadcrumbHtml);
        }
    }

    renderBreadcrumbs() {
        let container = document.getElementById(`wizard-breadcrumbs-${this.currentStep}`);
        if (!container) {
            container = document.getElementById(`wizard-breadcrumbs-4`); // fallback
        }
        if (!container) return;

        const crumbs = [];
        // Step 1: Make
        if (this.selection.make) {
            if (this.currentStep > 1) {
                crumbs.push(`<li><button class="hover:underline text-wsf-secondary" data-step="1">${this.selection.make.name}</button></li>`);
            } else {
                crumbs.push(`<li><span class="font-medium text-wsf-primary">${this.selection.make.name}</span></li>`);
            }
        }
        // Step 2: Model
        if (this.selection.model) {
            const label = this.currentStep === 2
                ? t('select_model_placeholder', 'Select Model')
                : this.selection.model.name;
            if (this.currentStep > 2) {
                crumbs.push(`<li>/</li><li><button class="hover:underline text-wsf-secondary" data-step="2">${label}</button></li>`);
            } else {
                crumbs.push(`<li>/</li><li><span class="font-medium text-wsf-primary">${label}</span></li>`);
            }
        }
        // Step 3: Year
        if (this.selection.year) {
            const label = this.currentStep === 3
                ? t('select_year_placeholder', 'Select Year')
                : this.selection.year;
            if (this.currentStep > 3) {
                crumbs.push(`<li>/</li><li><button class="hover:underline text-wsf-secondary" data-step="3">${label}</button></li>`);
            } else {
                crumbs.push(`<li>/</li><li><span class="font-medium text-wsf-primary">${label}</span></li>`);
            }
        }
        // Step 4: Modification
        if (this.currentStep === 4) {
            crumbs.push(`<li>/</li><li><span class="font-medium text-wsf-primary">${t('select_mods_placeholder','Select Modification')}</span></li>`);
        }
        // Step 5: Results
        if (this.currentStep === 5) {
            crumbs.push(`<li>/</li><li><span class="font-medium text-wsf-primary">${t('label_wheel_options','Wheel Options')}</span></li>`);
        }

        container.innerHTML = `<ul class="flex space-x-1">${crumbs.join('')}</ul>`;

        // Add event listeners to breadcrumb buttons
        container.querySelectorAll('button[data-step]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const step = parseInt(e.target.dataset.step, 10);
                this.goToStep(step);
            });
        });
    }

    /**
     * Get display name for generation, handling both object and string formats
     */
    getGenerationDisplayName(generation) {
        if (!generation) return 'Generation';

        // If it's already an object with name
        if (typeof generation === 'object' && generation.name) {
            return generation.name;
        }

        // If it's a string (ID/slug), check if it looks like an internal ID
        if (typeof generation === 'string') {
            // Helper function to detect internal IDs
            const isInternalId = (str) => {
                return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
            };

            // Even if it looks like an internal ID, prefer showing the actual data
            // over a generic placeholder. Users can at least see something specific.
            if (isInternalId(generation)) {
                // Return the ID as-is for internal IDs (don't capitalize)
                return generation;
            }

            // Otherwise, capitalize and return the string
            return generation.charAt(0).toUpperCase() + generation.slice(1);
        }

        // Fallback for object without name
        if (typeof generation === 'object') {
            return generation.title || generation.range || generation.year_range || generation.gen || 'Generation';
        }

        return 'Generation';
    }
}

// Auto-init wizard on page load
document.addEventListener('DOMContentLoaded', () => {
    console.log('[WheelWizard] DOM loaded, checking for wizard container...');
    const wizardContainer = document.getElementById('wheel-fit-wizard');
    console.log('[WheelWizard] Wizard container found:', !!wizardContainer);

    if (wizardContainer) {
        console.log('[WheelWizard] Creating wizard instance...');
        try {
            window.wheelFitWizard = new WheelWizard();
            console.log('[WheelWizard] Instance created successfully');
        } catch (error) {
            console.error('[WheelWizard] Error creating instance:', error);
        }
    } else {
        console.log('[WheelWizard] No wizard container found, skipping initialization');
    }
});