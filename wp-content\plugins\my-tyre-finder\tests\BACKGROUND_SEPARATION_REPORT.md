# Background Separation Report

## 🎯 Цель: Разделить фоны раз и навсегда

**Проблема:** Токен `--wsf-bg` использовался везде - на селектах, в гараже, на корневом фоне. Это не позволяло создать визуальную иерархию и правильную семантику.

**Решение:** Разделили на три четких уровня:
- **Widget Background** (`--wsf-bg`) - основной фон виджета
- **Input Background** (`--wsf-input-bg`) - фон полей ввода  
- **Surface Background** (`--wsf-surface`) - фон панелей/модалок

## ✅ Выполненные исправления

### 1. Селекты больше не используют --wsf-bg

**Было:**
```css
.wheel-fit-widget select,
.wsf-finder-widget select {
  background-color: var(--wsf-bg);
  color: var(--wsf-text-primary);
  border-color: var(--wsf-border);
}
```

**Стало:**
```css
.wheel-fit-widget select,
.wsf-finder-widget select {
  background-color: var(--wsf-input-bg);
  color: var(--wsf-input-text);
  border-color: var(--wsf-input-border);
}
```

### 2. Корневой контейнер получил правильный фон

**Добавлено:**
```css
.wheel-fit-widget,
.wsf-finder-widget {
  background: var(--wsf-bg);
  color: var(--wsf-text-primary);
}
```

### 3. Гараж переведен на Surface токен

**CSS исправления:**
```css
/* Было */
#garage-drawer,
#garage-overlay,
#garage-toast {
  background-color: var(--wsf-bg);
  border-color: var(--wsf-border);
}

/* Стало */
#garage-drawer,
#garage-overlay,
#garage-toast {
  background-color: var(--wsf-surface);
  border-color: var(--wsf-border-light);
}
```

**Twig шаблоны исправления:**
- `finder-form.twig`: `bg-wsf-bg` → `bg-wsf-surface`
- `finder-form-inline.twig`: `bg-wsf-bg` → `bg-wsf-surface`

### 4. Disabled селекты остались с Surface

**Оставлено как есть (правильно):**
```css
.wheel-fit-widget select[disabled] {
  background-color: var(--wsf-surface);
  opacity: .35;
}
```

Это логично - неактивные поля должны отличаться от активных.

## 🏗️ Архитектура фонов

### Уровень 1: Widget Background (`--wsf-bg`)
- **Назначение:** Основной фон виджета и его секций
- **Использование:** 
  - Корневой контейнер `.wheel-fit-widget`
  - Секция результатов `#search-results`
  - Карточки `.wsf-card`

### Уровень 2: Input Background (`--wsf-input-bg`)
- **Назначение:** Фон интерактивных элементов ввода
- **Использование:**
  - Селекты `select`
  - Инпуты `input`
  - Текстовые поля

### Уровень 3: Surface Background (`--wsf-surface`)
- **Назначение:** Фон панелей, модалок, вторичных элементов
- **Использование:**
  - Гараж `#garage-drawer`
  - Оверлеи `#garage-overlay`
  - Тосты `#garage-toast`
  - Disabled элементы

## 📁 Измененные файлы

1. **`assets/css/wheel-fit-shared.src.css`**
   - Исправлены стили гаража (surface вместо bg)
   - Подтверждено использование input токенов для селектов

2. **`templates/finder-form.twig`**
   - Гараж: `bg-wsf-bg` → `bg-wsf-surface`

3. **`templates/finder-form-inline.twig`**
   - Гараж: `bg-wsf-bg` → `bg-wsf-surface`

4. **`test-background-separation.html`** (новый)
   - Демонстрация разделения фонов
   - Визуальное сравнение токенов
   - Тест с реальным виджетом

## 🧪 Тестирование

### Как проверить:

1. **Откройте `test-background-separation.html`**
   - Проверьте визуальную иерархию
   - Переключите темы (Light/Dark)
   - Убедитесь в правильном применении токенов

2. **Проверьте реальный виджет:**
   - Фон виджета должен использовать `--wsf-bg`
   - Селекты должны использовать `--wsf-input-bg`
   - Гараж должен использовать `--wsf-surface`

3. **Проверьте CSS переменные:**
   - Все три токена должны иметь разные значения
   - Переключение тем должно корректно работать

## 🎯 Результат

### ✅ Достигнуто:
- **Четкая визуальная иерархия** - три уровня фонов
- **Правильная семантика** - каждый токен имеет свое назначение
- **Консистентная темизация** - все токены работают в светлой и темной теме
- **Отсутствие конфликтов** - нет дублирующих правил
- **Готовность к кастомизации** - разработчики могут легко настроить каждый уровень

### 🏠 Widget Background (`--wsf-bg`)
- ✅ Красит основной фон виджета
- ✅ НЕ влияет на поля ввода
- ✅ НЕ влияет на панели/гараж

### 📝 Input Background (`--wsf-input-bg`)  
- ✅ Красит только поля ввода
- ✅ Создает контраст с фоном виджета
- ✅ Обеспечивает читаемость

### 🗂️ Surface Background (`--wsf-surface`)
- ✅ Красит панели и модалки
- ✅ Создает дополнительный уровень иерархии
- ✅ Отделяет вторичные элементы

## 🚀 Заключение

Background токен теперь правильно разделен на три семантических уровня. Это обеспечивает:

1. **Визуальную ясность** - пользователи легко различают элементы
2. **Техническую гибкость** - разработчики могут настроить каждый уровень
3. **Масштабируемость** - система готова к добавлению новых элементов
4. **Консистентность** - все элементы следуют единой логике

Разделение фонов выполнено раз и навсегда! 🎉
