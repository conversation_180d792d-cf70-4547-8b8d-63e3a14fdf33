/**
 * Упрощенный тест CSS переменных для тестового виджета
 */

(function() {
    'use strict';

    console.log('🎨 === ТЕСТ CSS ПЕРЕМЕННЫХ ВИДЖЕТА ===');

    // Найти виджет
    const widget = document.querySelector('.wsf-finder-widget, #test-widget');
    if (!widget) {
        console.error('❌ Виджет не найден');
        return;
    }

    console.log('✅ Виджет найден:', widget.id || widget.className);

    // Проверить CSS переменные
    function checkCSSVariables() {
        console.log('\n1️⃣ Проверка CSS переменных...');
        
        const computedStyle = window.getComputedStyle(widget);
        const requiredVariables = [
            '--wsf-bg',
            '--wsf-text',
            '--wsf-primary',
            '--wsf-hover',
            '--wsf-border',
            '--wsf-muted'
        ];
        
        let allVariablesPresent = true;
        
        requiredVariables.forEach(variable => {
            const value = computedStyle.getPropertyValue(variable);
            if (value && value.trim()) {
                console.log(`✅ ${variable}: ${value.trim()}`);
            } else {
                console.log(`❌ ${variable}: не найдена`);
                allVariablesPresent = false;
            }
        });
        
        return allVariablesPresent;
    }

    // Проверить кнопку поиска
    function checkSearchButton() {
        console.log('\n2️⃣ Проверка кнопки поиска...');
        
        const searchButton = widget.querySelector('button[type="submit"]');
        if (!searchButton) {
            console.log('⚠️ Кнопка поиска не найдена');
            return false;
        }

        const computedStyle = window.getComputedStyle(searchButton);
        const backgroundColor = computedStyle.backgroundColor;
        
        console.log(`Фон кнопки: ${backgroundColor}`);
        
        // Проверяем, что кнопка использует CSS переменную
        const hasCorrectClasses = searchButton.classList.contains('wsf-bg-wsf-primary');
        console.log(`Использует wsf-bg-wsf-primary: ${hasCorrectClasses ? '✅' : '❌'}`);
        
        return hasCorrectClasses;
    }

    // Проверить спиннеры
    function checkSpinners() {
        console.log('\n3️⃣ Проверка спиннеров...');
        
        const spinners = widget.querySelectorAll('.wsf-border-wsf-primary, [class*="border-wsf-primary"]');
        console.log(`Найдено спиннеров с правильными классами: ${spinners.length}`);
        
        let allCorrect = true;
        spinners.forEach((spinner, index) => {
            const hasCorrectClass = spinner.classList.contains('wsf-border-wsf-primary') || 
                                  Array.from(spinner.classList).some(cls => cls.includes('border-wsf-primary'));
            console.log(`Спиннер ${index + 1}: ${hasCorrectClass ? '✅' : '❌'}`);
            if (!hasCorrectClass) allCorrect = false;
        });
        
        return allCorrect;
    }

    // Проверить disabled состояния
    function checkDisabledStates() {
        console.log('\n4️⃣ Проверка disabled состояний...');
        
        const disabledButtons = widget.querySelectorAll('button:disabled');
        console.log(`Найдено disabled кнопок: ${disabledButtons.length}`);
        
        let allCorrect = true;
        disabledButtons.forEach((button, index) => {
            const computedStyle = window.getComputedStyle(button);
            const opacity = computedStyle.opacity;
            const cursor = computedStyle.cursor;
            
            console.log(`Disabled кнопка ${index + 1}:`);
            console.log(`  Opacity: ${opacity} ${parseFloat(opacity) < 1 ? '✅' : '❌'}`);
            console.log(`  Cursor: ${cursor} ${cursor === 'not-allowed' ? '✅' : '❌'}`);
            
            if (parseFloat(opacity) >= 1 || cursor !== 'not-allowed') {
                allCorrect = false;
            }
        });
        
        return allCorrect;
    }

    // Тест переключения тем
    function testThemeSwitching() {
        console.log('\n5️⃣ Тест переключения тем...');
        
        const originalPrimary = window.getComputedStyle(widget).getPropertyValue('--wsf-primary').trim();
        console.log(`Исходный primary цвет: ${originalPrimary}`);
        
        // Применить темную тему
        widget.style.setProperty('--wsf-primary', '#7dd3fc');
        
        setTimeout(() => {
            const newPrimary = window.getComputedStyle(widget).getPropertyValue('--wsf-primary').trim();
            console.log(`Новый primary цвет: ${newPrimary}`);
            
            const changed = newPrimary !== originalPrimary;
            console.log(`Тема изменилась: ${changed ? '✅' : '❌'}`);
            
            // Вернуть исходную тему
            widget.style.setProperty('--wsf-primary', originalPrimary);
            
            return changed;
        }, 100);
    }

    // Проверить жёсткие цвета
    function checkHardcodedColors() {
        console.log('\n6️⃣ Проверка жёстких цветов...');
        
        const problematicClasses = [
            'bg-blue-600',
            'hover:bg-blue-700', 
            'border-blue-600',
            'text-blue-700'
        ];
        
        let foundProblems = 0;
        
        problematicClasses.forEach(className => {
            const elements = widget.querySelectorAll(`.${className}`);
            if (elements.length > 0) {
                console.log(`❌ Найдено ${elements.length} элементов с классом .${className}`);
                foundProblems += elements.length;
            }
        });
        
        if (foundProblems === 0) {
            console.log('✅ Жёсткие цвета не найдены');
        }
        
        return foundProblems === 0;
    }

    // Основная функция тестирования
    function runAllTests() {
        const results = {
            'CSS переменные установлены': checkCSSVariables(),
            'Кнопка поиска использует переменные': checkSearchButton(),
            'Спиннеры используют переменные': checkSpinners(),
            'Disabled состояния работают': checkDisabledStates(),
            'Нет жёстких цветов': checkHardcodedColors()
        };
        
        console.log('\n📊 === РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ ===');
        let allPassed = true;
        
        Object.entries(results).forEach(([test, passed]) => {
            console.log(`${passed ? '✅' : '❌'} ${test}`);
            if (!passed) allPassed = false;
        });
        
        console.log(`\n🎯 Общий результат: ${allPassed ? '✅ ВСЕ ТЕСТЫ ПРОШЛИ' : '❌ ЕСТЬ ПРОБЛЕМЫ'}`);
        
        // Запустить тест переключения тем
        testThemeSwitching();
        
        return results;
    }

    // Запустить тесты
    const testResults = runAllTests();
    
    // Экспортировать результаты для внешнего использования
    window.widgetTestResults = testResults;
    
    console.log('\n💡 Рекомендации:');
    if (!testResults['CSS переменные установлены']) {
        console.log('1. Убедитесь, что CSS переменные правильно определены');
    }
    if (!testResults['Кнопка поиска использует переменные']) {
        console.log('2. Замените bg-blue-600 на bg-wsf-primary в кнопках');
    }
    if (!testResults['Спиннеры используют переменные']) {
        console.log('3. Замените border-blue-600 на border-wsf-primary в спиннерах');
    }
    if (!testResults['Disabled состояния работают']) {
        console.log('4. Добавьте стили для disabled состояний кнопок');
    }
    if (!testResults['Нет жёстких цветов']) {
        console.log('5. Удалите оставшиеся жёсткие цвета из шаблонов');
    }

})();
