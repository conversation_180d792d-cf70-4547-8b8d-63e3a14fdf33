// Критический тест исправления фильтров
// Запустите в консоли: window.testCriticalFix()

window.testCriticalFix = async function() {
    console.log('🚨 [Critical Fix Test] Тестируем критическое исправление фильтров...');
    console.log('📋 Проблема: Пустой список брендов при комбинации региональных + брендовых фильтров');
    console.log('🔧 Решение: Убрали брендовые фильтры из API запроса, применяем локально');
    
    // Функция для AJAX запроса
    async function makeRequest(action, data = {}) {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('nonce', window.WheelFitData?.nonce || 'test');
        
        Object.keys(data).forEach(key => {
            formData.append(key, data[key]);
        });

        const response = await fetch(window.WheelFitData?.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }
    
    console.log('\n--- Тест 1: Загрузка брендов (by vehicle) ---');
    const result1 = await makeRequest('wf_get_makes');
    console.log('Результат wf_get_makes:', result1);
    
    const success1 = result1.success && Array.isArray(result1.data) && result1.data.length > 0;
    const count1 = success1 ? result1.data.length : 0;
    
    if (success1) {
        console.log(`✅ УСПЕХ: Получено ${count1} брендов через wf_get_makes`);
        console.log('Первые 5 брендов:', result1.data.slice(0, 5).map(m => m.name || m.slug));
    } else {
        console.error(`❌ ПРОВАЛ: wf_get_makes вернул ${count1} брендов`);
        console.error('Детали ошибки:', result1);
    }
    
    console.log('\n--- Тест 2: Загрузка брендов (by year) ---');
    const result2 = await makeRequest('wf_get_makes_by_year', { year: 2020 });
    console.log('Результат wf_get_makes_by_year:', result2);

    const success2 = result2.success && Array.isArray(result2.data) && result2.data.length > 0;
    const count2 = success2 ? result2.data.length : 0;

    if (success2) {
        console.log(`✅ УСПЕХ: Получено ${count2} брендов через wf_get_makes_by_year`);
        console.log('Первые 5 брендов:', result2.data.slice(0, 5).map(m => m.name || m.slug));
    } else {
        console.error(`❌ ПРОВАЛ: wf_get_makes_by_year вернул ${count2} брендов`);
        console.error('Детали ошибки:', result2);
    }

    console.log('\n--- Тест 3: Загрузка конкретных брендов (для логотипов) ---');
    const testSlugs = ['bmw', 'audi', 'mercedes-benz'];
    const result3 = await makeRequest('wf_get_makes_details', { slugs: JSON.stringify(testSlugs) });
    console.log('Результат wf_get_makes_details:', result3);

    const success3 = result3.success && typeof result3.data === 'object' && Object.keys(result3.data).length > 0;
    const count3 = success3 ? Object.keys(result3.data).length : 0;

    if (success3) {
        console.log(`✅ УСПЕХ: Получено ${count3} брендов через wf_get_makes_details`);
        console.log('Полученные бренды:', Object.keys(result3.data));
    } else {
        console.error(`❌ ПРОВАЛ: wf_get_makes_details вернул ${count3} брендов`);
        console.error('Детали ошибки:', result3);
    }
    
    console.log('\n--- Анализ результатов ---');

    if (success1 && success2 && success3) {
        console.log('🎉 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ УСПЕШНО!');
        console.log('✅ Все три метода возвращают бренды');
        console.log('✅ Фильтры больше не блокируют результаты');
        console.log('✅ Конкретные бренды загружаются через fallback');

        if (count1 >= 20 && count2 >= 20) {
            console.log('✅ Количество брендов достаточное - показываются все региональные бренды');
            console.log('✅ Брендовые фильтры теперь применяются локально');
        } else {
            console.warn(`⚠️ Количество брендов: ${count1}/${count2}. Возможно API ограничивает результаты.`);
        }

        console.log('\n🔍 Что изменилось:');
        console.log('• API запрос: /makes/?region=eudm (БЕЗ brands параметра)');
        console.log('• Брендовые фильтры применяются в PHP после получения результата');
        console.log('• Конкретные бренды загружаются через fallback без региональных ограничений');
        console.log('• Кэш не зависит от брендовых фильтров');

    } else if (!success1 && !success2 && !success3) {
        console.error('❌ КРИТИЧЕСКАЯ ПРОБЛЕМА: Оба метода не работают');
        console.log('\n🔧 Возможные причины:');
        console.log('1. API ключ не настроен или неверный');
        console.log('2. Проблема с сетью или API сервером');
        console.log('3. Кэш содержит старые пустые результаты');
        
        console.log('\n💡 Рекомендации:');
        console.log('1. Очистите кэш: запустите clear-cache.php');
        console.log('2. Проверьте API ключ в админке');
        console.log('3. Временно отключите ВСЕ фильтры и проверьте снова');
        
    } else {
        console.warn('⚠️ ЧАСТИЧНАЯ ПРОБЛЕМА: Один или несколько методов не работают');
        console.log(`wf_get_makes: ${success1 ? 'OK' : 'FAIL'}`);
        console.log(`wf_get_makes_by_year: ${success2 ? 'OK' : 'FAIL'}`);
        console.log(`wf_get_makes_details: ${success3 ? 'OK' : 'FAIL'}`);
    }

    console.log('\n--- Следующие шаги ---');
    if (success1 && success2 && success3) {
        console.log('1. ✅ Протестируйте виджет на фронтенде');
        console.log('2. ✅ Проверьте что можно выбирать бренды');
        console.log('3. ✅ Убедитесь что фильтры работают корректно');
        console.log('4. ✅ Проверьте что логотипы брендов загружаются');
    } else {
        console.log('1. 🔧 Очистите кэш плагина');
        console.log('2. 🔧 Проверьте настройки API');
        console.log('3. 🔧 Отключите фильтры для тестирования');
    }

    return {
        success1,
        success2,
        success3,
        count1,
        count2,
        count3,
        overall_success: success1 && success2 && success3,
        result1,
        result2,
        result3
    };
};

console.log('🚨 [Critical Fix Test] Скрипт загружен.');
console.log('📋 Этот тест проверяет критическое исправление пустых результатов при комбинации фильтров.');
console.log('🚀 Запустите: window.testCriticalFix()');
