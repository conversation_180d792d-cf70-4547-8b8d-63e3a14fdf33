<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automatic Search Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-header {
            background: #2563eb;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
        }
        .status-good { color: #059669; }
        .status-bad { color: #dc2626; }
        .status-warning { color: #d97706; }
        .button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #1d4ed8;
        }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🔍 Automatic Search Functionality Test</h1>
        <p>This page helps test and validate the automatic search functionality fixes for the my-tyre-finder plugin.</p>
    </div>

    <div class="instructions">
        <h3>📋 Instructions</h3>
        <ol>
            <li><strong>Enable Auto-search:</strong> Go to WordPress Admin → Wheel-Size → Appearance → Check "Automatically search on last input" → Save</li>
            <li><strong>Load this page</strong> on a page that has the wheel-size widget</li>
            <li><strong>Run the tests</strong> using the buttons below</li>
            <li><strong>Check the console output</strong> for detailed results</li>
        </ol>
    </div>

    <div class="test-container">
        <div class="test-section">
            <h3>🧪 Available Tests</h3>
            <button class="button" onclick="runDiagnosisTest()">Run Diagnosis Test</button>
            <button class="button" onclick="runFixValidation()">Run Fix Validation</button>
            <button class="button" onclick="runComprehensiveTest()">Run Comprehensive Test</button>
            <button class="button" onclick="clearConsole()">Clear Console</button>
        </div>

        <div class="test-section">
            <h3>📊 Quick Status Check</h3>
            <div id="status-check">
                <p>Click "Run Quick Check" to see current status...</p>
                <button class="button" onclick="runQuickCheck()">Run Quick Check</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🖥️ Console Output</h3>
            <div id="console-output" class="console-output">
                Console output will appear here...
            </div>
        </div>
    </div>

    <script>
        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToPage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            logToPage(args.join(' '), 'log');
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            logToPage(args.join(' '), 'error');
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            logToPage(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function runQuickCheck() {
            console.log('=== Quick Status Check ===');
            
            const statusDiv = document.getElementById('status-check');
            let statusHTML = '<h4>Status:</h4>';
            
            // Check WheelFitData
            if (window.WheelFitData) {
                statusHTML += '<p class="status-good">✅ WheelFitData available</p>';
                statusHTML += `<p>Auto-search enabled: <span class="${window.WheelFitData.autoSearch ? 'status-good' : 'status-warning'}">${window.WheelFitData.autoSearch ? '✅ Yes' : '⚠️ No'}</span></p>`;
            } else {
                statusHTML += '<p class="status-bad">❌ WheelFitData not available</p>';
            }
            
            // Check widget
            if (window.wheelFitWidget) {
                statusHTML += '<p class="status-good">✅ Widget available</p>';
            } else {
                statusHTML += '<p class="status-bad">❌ Widget not available</p>';
            }
            
            // Check submit buttons
            const submitButtons = document.querySelectorAll('.wheel-fit-widget button[type="submit"]');
            if (submitButtons.length > 0) {
                const hiddenCount = Array.from(submitButtons).filter(btn => btn.classList.contains('hidden')).length;
                if (window.WheelFitData?.autoSearch && hiddenCount === submitButtons.length) {
                    statusHTML += '<p class="status-good">✅ Submit buttons correctly hidden</p>';
                } else if (!window.WheelFitData?.autoSearch && hiddenCount === 0) {
                    statusHTML += '<p class="status-good">✅ Submit buttons correctly visible</p>';
                } else {
                    statusHTML += '<p class="status-warning">⚠️ Submit button visibility issue</p>';
                }
            } else {
                statusHTML += '<p class="status-warning">⚠️ No submit buttons found</p>';
            }
            
            statusHTML += '<button class="button" onclick="runQuickCheck()">Refresh Status</button>';
            statusDiv.innerHTML = statusHTML;
            
            console.log('Quick check completed');
        }

        function runDiagnosisTest() {
            console.log('Loading diagnosis test...');
            loadAndRunScript('test-auto-search-diagnosis.js');
        }

        function runFixValidation() {
            console.log('Loading fix validation test...');
            loadAndRunScript('test-auto-search-validation.js');
        }

        function runComprehensiveTest() {
            console.log('Loading comprehensive test...');
            loadAndRunScript('test-search-comprehensive.js');
        }

        function loadAndRunScript(scriptName) {
            const script = document.createElement('script');
            script.src = scriptName;
            script.onload = function() {
                console.log(`✅ ${scriptName} loaded and executed`);
            };
            script.onerror = function() {
                console.error(`❌ Failed to load ${scriptName}`);
            };
            document.head.appendChild(script);
        }

        // Initialize page
        window.addEventListener('load', function() {
            console.log('🚀 Auto-search test page loaded');
            console.log('Ready to run tests...');
            runQuickCheck();
        });
    </script>
</body>
</html>
