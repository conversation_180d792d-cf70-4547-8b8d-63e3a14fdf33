<?php
declare(strict_types=1);

namespace MyTyreFinder\Admin;

/**
 * Экран «Wheel-Size → Features»
 */
final class FeaturesPage
{
    public const SLUG = 'wheel-size-features';

    public function register(): void
    {
        add_action('admin_menu',              [$this, 'add_menu']);
        add_action('admin_init',              [$this, 'register_settings']);
        add_action('admin_enqueue_scripts',   [$this, 'enqueue_assets']);
        add_action('admin_post_wheel_size_save_features', [$this, 'save_settings']);
    }

    /* ---------- Settings API ---------- */
    public function register_settings(): void
    {
        // флаги-фич
        register_setting('wheel_size_feature_options', 'wheel_size_features', [
            'default' => [
                'enable_garage'   => false,
                'enable_oe_filter'=> false,
            ],
        ]);

        // региональный фильтр
        register_setting('wheel_size_filter_settings', 'wheel_size_regions');

        // (бренд-фильтры регистрируются в BrandFilters)
    }

    /* ---------- Меню ---------- */
    public function add_menu(): void
    {
        add_submenu_page(
            'wheel-size',
            'Wheel-Size - Features',
            'Features',
            'manage_options',
            self::SLUG,
            [$this, 'render_page']
        );
    }

    /* ---------- Assets ---------- */
    public function enqueue_assets(string $hook): void
    {
        if ($hook !== 'wheel-size_page_' . self::SLUG) {
            return;
        }

        // Tailwind для утилит (preflight отключён в CDN параметрами ниже)
        wp_enqueue_script(
            'tailwind-cdn',
            'https://cdn.tailwindcss.com?plugins=forms,typography&preflight=false',
            [],
            '3.4.1',
            false
        );

        $plugin = dirname(__DIR__, 2) . '/my-tyre-finder.php';

        wp_enqueue_style(
            'wheel-size-admin-brands',
            plugins_url('assets/css/admin-brands.css', $plugin),
            [],
            file_exists(plugin_dir_path($plugin).'assets/css/admin-brands.css')
                ? filemtime(plugin_dir_path($plugin).'assets/css/admin-brands.css')
                : '1.0.0'
        );

        wp_enqueue_script(
            'wheel-size-admin-brands',
            plugins_url('assets/js/admin-brands.js', $plugin),
            ['jquery'],
            file_exists(plugin_dir_path($plugin).'assets/js/admin-brands.js')
                ? filemtime(plugin_dir_path($plugin).'assets/js/admin-brands.js')
                : '1.0.0',
            true
        );

        wp_enqueue_style(
            'wheel-size-admin-filters',
            plugins_url('assets/css/admin-filters.css', $plugin),
            [],
            file_exists(plugin_dir_path($plugin).'assets/css/admin-filters.css')
                ? filemtime(plugin_dir_path($plugin).'assets/css/admin-filters.css')
                : '1.0.0'
        );
    }

    /* ---------- Страница ---------- */
    public function render_page(): void
    {
        // 1) вытягиваем текущие значения
        $features = get_option('wheel_size_features', [
            'enable_garage'    => false,
            'enable_oe_filter' => false,
        ]);
        $garage_enabled = (bool) ($features['enable_garage']    ?? false);
        $oe_enabled     = (bool) ($features['enable_oe_filter'] ?? false);

        // 2) регионы
        $all_regions = ['audm','cdm','chdm','eudm','jdm','ladm','medm','mxndm','nadm','russia','sadm','sam','skdm','usdm'];
        $region_names = [
            'audm' => 'Australia',
            'cdm'  => 'Canada',
            'chdm' => 'China',
            'eudm' => 'Europe',
            'jdm'  => 'Japan',
            'ladm' => 'Latin America',
            'medm' => 'Middle East',
            'mxndm'=> 'Mexico',
            'nadm' => 'Northern Africa',
            'russia'=> 'Russia',
            'sadm' => 'Southern Africa',
            'sam'  => 'Southeast Asia',
            'skdm' => 'South Korea',
            'usdm' => 'United States',
        ];
        $selected    = array_map('sanitize_key', (array) get_option('wheel_size_regions', []));

        // 3) выводим форму (код тот же, просто перенесён)
        ?>
        <div class="wrap">
            <style>
                /* Fix spacing and remove tooltip for region filter */
                .region-filter__wrapper {
                    margin-top: 15px !important;
                }

                /* Remove tooltip from region tags */
                .region-tag::after {
                    display: none !important;
                }

                /* Features page layout improvements */
                .features-section {
                    background: #fff;
                    border: 1px solid #c3c4c7;
                    border-radius: 4px;
                    margin-bottom: 20px;
                    padding: 20px;
                    box-shadow: 0 1px 1px rgba(0,0,0,.04);
                }

                .features-section h2 {
                    margin-top: 0;
                    margin-bottom: 15px;
                    font-size: 18px;
                    font-weight: 600;
                    color: #1d2327;
                    border-bottom: 1px solid #e0e0e0;
                    padding-bottom: 10px;
                }

                .features-section .description {
                    margin-bottom: 20px;
                    color: #646970;
                }

                /* Scroll indicator */
                .scroll-indicator {
                    position: fixed;
                    right: 20px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: #2271b1;
                    color: white;
                    padding: 10px;
                    border-radius: 4px;
                    font-size: 12px;
                    z-index: 1000;
                    animation: pulse 2s infinite;
                    cursor: pointer;
                }

                .scroll-indicator:hover {
                    background: #135e96;
                }

                @keyframes pulse {
                    0% { opacity: 1; }
                    50% { opacity: 0.7; }
                    100% { opacity: 1; }
                }

                /* Hide scroll indicator when brand section is visible */
                .scroll-indicator.hidden {
                    display: none;
                }

                /* Consistent button styling for both save buttons - WordPress standard size */
                .features-section .button-primary {
                    background: #2271b1;
                    border-color: #2271b1;
                    color: #fff;
                    font-weight: 400;
                    padding: 0 12px;
                    font-size: 13px;
                    line-height: 2.15384615;
                    min-height: 30px;
                    border-radius: 3px;
                }

                .features-section .button-primary:hover {
                    background: #135e96;
                    border-color: #135e96;
                }
            </style>

            <h1><?php esc_html_e('Wheel-Size – Features', 'wheel-size'); ?></h1>

            <!-- Scroll indicator -->
            <div class="scroll-indicator" id="scroll-indicator" onclick="scrollToBrandFilters()">
                ↓ <?php esc_html_e('Brand Filters Below', 'wheel-size'); ?>
            </div>

            <!-- Main Features Section -->
            <div class="features-section">
                <h2><?php esc_html_e('Main Features & Regional Settings', 'wheel-size'); ?></h2>
                <div class="description">
                    <?php esc_html_e('Configure core functionality and regional filtering options. Each setting below affects how vehicle data is shown in the front-end vehicle selector.', 'wheel-size'); ?>
                </div>

                <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
                    <?php
                        wp_nonce_field('wheel_size_features_action', 'wheel_size_features_nonce');
                        echo '<input type="hidden" name="action" value="wheel_size_save_features">';
                    ?>

                    <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th><?php esc_html_e('Garage Module', 'wheel-size'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wheel_size_features[enable_garage]" value="1" <?php checked($garage_enabled); ?>>
                                    <?php esc_html_e('Enable "My Garage" drawer', 'wheel-size'); ?>
                                </label>
                                <p class="description">
                                    <?php esc_html_e('Allows your site visitors to save and quickly reload their favorite vehicle configurations. Once enabled, a side panel becomes available on the front-end. It stores selected vehicles locally in the browser so users can load or remove them later.', 'wheel-size'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th><?php esc_html_e('OE only mode', 'wheel-size'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wheel_size_features[enable_oe_filter]" value="1" <?php checked($oe_enabled); ?>>
                                    <?php esc_html_e('Show factory sizes only', 'wheel-size'); ?>
                                </label>
                                <p class="description">
                                    <?php esc_html_e('When this option is on, visitors will see only original equipment (OE) wheel sizes in the results — no aftermarket alternatives.', 'wheel-size'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th><?php esc_html_e('Region Filter', 'wheel-size'); ?></th>
                            <td>
                                <p class="description">
                                    <?php esc_html_e('Use this filter to see only vehicle data officially available in the selected sales regions. For example, choosing "United States" will display vehicles and modifications that were sold on the US market. You can select multiple regions. Leave empty to show all regions.', 'wheel-size'); ?>
                                </p>
                                <div class="region-filter__wrapper">
                                    <?php foreach ($all_regions as $slug): ?>
                                        <?php
                                            $on   = in_array($slug, $selected, true);
                                            $name = $region_names[$slug] ?? strtoupper($slug);
                                        ?>
                                        <button type="button" role="checkbox"
                                            class="brand-tag region-tag <?php echo $on ? 'is-included' : 'is-neutral'; ?>"
                                            data-slug="<?php echo esc_attr($slug); ?>"
                                            aria-checked="<?php echo $on ? 'true' : 'false'; ?>">
                                            <?php echo esc_html($name); ?>
                                        </button>
                                    <?php endforeach; ?>
                                </div>
                                <div id="region-filter-hidden-inputs"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                    <?php submit_button(__('Save Main Features & Regional Settings', 'wheel-size'), 'primary', 'submit', false); ?>
                </form>
            </div>

            <?php BrandFilters::render_form(); /* рендер секции брендов */ ?>
        </div>

        <script>
        /* JS перенесён 1-в-1 из старой страницы */
        (function(){
            const cont = document.querySelector('.region-filter__wrapper');
            if(!cont) return;
            const hidden = document.getElementById('region-filter-hidden-inputs');
            const tags = [...cont.querySelectorAll('.region-tag')];

            function sync(){
                hidden.innerHTML='';
                tags.forEach(t=>{
                    if(t.classList.contains('is-included')){
                        const i=document.createElement('input');
                        i.type='hidden'; i.name='wheel_size_regions[]'; i.value=t.dataset.slug;
                        hidden.appendChild(i);
                    }
                });
            }
            cont.addEventListener('click',e=>{
                const t=e.target.closest('.region-tag'); if(!t) return;
                t.classList.toggle('is-included'); t.classList.toggle('is-neutral');
                sync();
            });
            sync();
        })();

        /* Scroll indicator management */
        function scrollToBrandFilters() {
            const brandSection = document.querySelector('.brand-filters__section');
            if (brandSection) {
                brandSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // Hide scroll indicator when brand filters section is visible
        function checkScrollIndicator() {
            const indicator = document.getElementById('scroll-indicator');
            const brandSection = document.querySelector('.brand-filters__section');

            if (!indicator || !brandSection) return;

            const rect = brandSection.getBoundingClientRect();
            const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

            if (isVisible) {
                indicator.classList.add('hidden');
            } else {
                indicator.classList.remove('hidden');
            }
        }

        // Check on scroll and load
        window.addEventListener('scroll', checkScrollIndicator);
        window.addEventListener('load', checkScrollIndicator);
        document.addEventListener('DOMContentLoaded', checkScrollIndicator);
        </script>
        <?php
    }

    /* ---------- Сохранение ---------- */
    public function save_settings(): void
    {
        if (
            !isset($_POST['wheel_size_features_nonce']) ||
            !wp_verify_nonce($_POST['wheel_size_features_nonce'], 'wheel_size_features_action')
        ) {
            wp_die(__('Permission check failed', 'wheel-size'), 403);
        }

        $features = [
            'enable_garage'   => !empty($_POST['wheel_size_features']['enable_garage']),
            'enable_oe_filter'=> !empty($_POST['wheel_size_features']['enable_oe_filter']),
        ];
        update_option('wheel_size_features', $features);

        // регионы
        $reg = array_map('sanitize_key', (array) ($_POST['wheel_size_regions'] ?? []));
        update_option('wheel_size_regions', $reg);

        // Clear API cache when region filters change
        \MyTyreFinder\Admin\ApiValidator::clear_api_cache();

        wp_safe_redirect(wp_get_referer());
        exit;
    }
} 