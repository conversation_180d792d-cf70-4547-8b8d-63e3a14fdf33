<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-weight: bold;
            font-size: 14px;
        }
        .control-group select, .control-group button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .test-results h3 {
            margin-top: 0;
            color: #495057;
        }
        .result-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .result-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .widget-preview {
            border: 2px solid #007cba;
            border-radius: 8px;
            padding: 20px;
            background: white;
            min-height: 300px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Translation Persistence Test</h1>
        <p>This page tests whether translations persist when Search Flow or Form Layout settings change.</p>
        
        <div class="test-controls">
            <div class="control-group">
                <label for="test-locale">Test Locale:</label>
                <select id="test-locale">
                    <option value="en">English</option>
                    <option value="de">German</option>
                    <option value="fr">French</option>
                    <option value="es">Spanish</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="test-search-flow">Search Flow:</label>
                <select id="test-search-flow">
                    <option value="by_vehicle">By Vehicle</option>
                    <option value="by_year">By Year</option>
                    <option value="by_generation">By Generation</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="test-form-layout">Form Layout:</label>
                <select id="test-form-layout">
                    <option value="popup-horizontal">Popup Horizontal</option>
                    <option value="inline">Inline</option>
                    <option value="stepper">Stepper</option>
                    <option value="wizard">Wizard</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>&nbsp;</label>
                <button id="run-test" type="button">Run Test</button>
                <button id="run-all-tests" type="button">Run All Tests</button>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>Widget Preview</h2>
        <div id="widget-preview" class="widget-preview">
            <p>Widget will be loaded here...</p>
        </div>
    </div>

    <div class="test-container">
        <div class="test-results" id="test-results">
            <h3>Test Results</h3>
            <p>No tests run yet. Click "Run Test" or "Run All Tests" to begin.</p>
        </div>
    </div>

    <script>
        // Mock translation data for testing
        window.WheelFitI18n = {
            'widget_title': 'Wheel & Tyre Finder',
            'label_make': 'Make',
            'label_model': 'Model',
            'label_year': 'Year',
            'label_modification': 'Modification',
            'placeholder_make': 'Choose a make',
            'placeholder_model': 'Choose a model',
            'placeholder_year': 'Choose a year',
            'placeholder_mod': 'Choose a modification',
            'button_search': 'Find Tire & Wheel Sizes',
            'section_results': 'Search Results'
        };

        // Mock German translations
        const germanTranslations = {
            'widget_title': 'Rad & Reifen Finder',
            'label_make': 'Marke',
            'label_model': 'Modell',
            'label_year': 'Jahr',
            'label_modification': 'Modifikation',
            'placeholder_make': 'Marke wählen',
            'placeholder_model': 'Modell wählen',
            'placeholder_year': 'Jahr wählen',
            'placeholder_mod': 'Modifikation wählen',
            'button_search': 'Reifen- & Radgrößen finden',
            'section_results': 'Suchergebnisse'
        };

        // Mock French translations
        const frenchTranslations = {
            'widget_title': 'Recherche de Roues et Pneus',
            'label_make': 'Marque',
            'label_model': 'Modèle',
            'label_year': 'Année',
            'label_modification': 'Modification',
            'placeholder_make': 'Choisir une marque',
            'placeholder_model': 'Choisir un modèle',
            'placeholder_year': 'Choisir une année',
            'placeholder_mod': 'Choisir une modification',
            'button_search': 'Trouver les tailles de pneus et roues',
            'section_results': 'Résultats de recherche'
        };

        // Mock widget HTML for testing
        function generateMockWidget(searchFlow, formLayout) {
            return `
                <div class="wsf-finder-widget" data-wsf-theme="default">
                    <h2 data-i18n="widget_title">Wheel & Tyre Finder</h2>
                    <form id="wheel-fit-form" class="space-y-6">
                        <div class="form-group">
                            <label data-i18n="label_make">Make</label>
                            <select id="wf-make">
                                <option value="" data-i18n-placeholder="placeholder_make">Choose a make</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label data-i18n="label_model">Model</label>
                            <select id="wf-model">
                                <option value="" data-i18n-placeholder="placeholder_model">Choose a model</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label data-i18n="label_year">Year</label>
                            <select id="wf-year">
                                <option value="" data-i18n-placeholder="placeholder_year">Choose a year</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label data-i18n="label_modification">Modification</label>
                            <select id="wf-modification">
                                <option value="" data-i18n-placeholder="placeholder_mod">Choose a modification</option>
                            </select>
                        </div>
                        <button type="submit" data-i18n="button_search">Find Tire & Wheel Sizes</button>
                    </form>
                    <div id="search-results" class="hidden">
                        <h3 data-i18n="section_results">Search Results</h3>
                    </div>
                </div>
            `;
        }

        // Test functions
        function updateTranslations(locale) {
            switch(locale) {
                case 'de':
                    window.WheelFitI18n = germanTranslations;
                    break;
                case 'fr':
                    window.WheelFitI18n = frenchTranslations;
                    break;
                default:
                    window.WheelFitI18n = {
                        'widget_title': 'Wheel & Tyre Finder',
                        'label_make': 'Make',
                        'label_model': 'Model',
                        'label_year': 'Year',
                        'label_modification': 'Modification',
                        'placeholder_make': 'Choose a make',
                        'placeholder_model': 'Choose a model',
                        'placeholder_year': 'Choose a year',
                        'placeholder_mod': 'Choose a modification',
                        'button_search': 'Find Tire & Wheel Sizes',
                        'section_results': 'Search Results'
                    };
            }
        }

        function runSingleTest() {
            const locale = document.getElementById('test-locale').value;
            const searchFlow = document.getElementById('test-search-flow').value;
            const formLayout = document.getElementById('test-form-layout').value;
            
            console.log(`Running test: ${locale} + ${searchFlow} + ${formLayout}`);
            
            // Update translations
            updateTranslations(locale);
            
            // Generate mock widget
            const widgetHtml = generateMockWidget(searchFlow, formLayout);
            document.getElementById('widget-preview').innerHTML = widgetHtml;
            
            // Apply translations (simulate the fixed functionality)
            if (typeof applyStaticTranslations === 'function') {
                applyStaticTranslations(document.getElementById('widget-preview'));
            } else {
                // Fallback manual translation application
                applyMockTranslations(document.getElementById('widget-preview'));
            }
            
            // Check results
            const results = checkTranslationResults(document.getElementById('widget-preview'));
            displayResults(results, `${locale} + ${searchFlow} + ${formLayout}`);
        }

        function applyMockTranslations(container) {
            // Apply text translations
            const textElements = container.querySelectorAll('[data-i18n]');
            textElements.forEach(el => {
                const key = el.dataset.i18n;
                if (window.WheelFitI18n[key]) {
                    el.textContent = window.WheelFitI18n[key];
                }
            });
            
            // Apply placeholder translations
            const placeholderElements = container.querySelectorAll('[data-i18n-placeholder]');
            placeholderElements.forEach(el => {
                const key = el.dataset.i18nPlaceholder;
                if (window.WheelFitI18n[key]) {
                    el.textContent = window.WheelFitI18n[key];
                }
            });
        }

        function checkTranslationResults(container) {
            const results = { passed: 0, failed: 0, details: [] };
            
            // Check text elements
            const textElements = container.querySelectorAll('[data-i18n]');
            textElements.forEach(el => {
                const key = el.dataset.i18n;
                const expected = window.WheelFitI18n[key];
                const actual = el.textContent.trim();
                
                if (expected && actual === expected) {
                    results.passed++;
                    results.details.push({ key, expected, actual, status: 'PASS', type: 'text' });
                } else {
                    results.failed++;
                    results.details.push({ key, expected, actual, status: 'FAIL', type: 'text' });
                }
            });
            
            return results;
        }

        function displayResults(results, testName) {
            const resultsContainer = document.getElementById('test-results');
            const successRate = ((results.passed / (results.passed + results.failed)) * 100).toFixed(1);
            
            let html = `
                <h3>Test Results for: ${testName}</h3>
                <p><strong>Summary:</strong> ${results.passed} passed, ${results.failed} failed (${successRate}% success rate)</p>
                <div class="results-details">
            `;
            
            results.details.forEach(detail => {
                const statusClass = detail.status === 'PASS' ? 'result-pass' : 'result-fail';
                const statusIcon = detail.status === 'PASS' ? '✓' : '✗';
                html += `
                    <div class="result-item ${statusClass}">
                        ${statusIcon} ${detail.type} "${detail.key}": expected "${detail.expected}", got "${detail.actual}"
                    </div>
                `;
            });
            
            html += '</div>';
            resultsContainer.innerHTML = html;
        }

        // Event listeners
        document.getElementById('run-test').addEventListener('click', runSingleTest);
        
        document.getElementById('run-all-tests').addEventListener('click', function() {
            const locales = ['en', 'de', 'fr'];
            const flows = ['by_vehicle', 'by_year', 'by_generation'];
            const layouts = ['popup-horizontal', 'inline', 'stepper'];
            
            let allResults = [];
            let currentTest = 0;
            const totalTests = locales.length * flows.length * layouts.length;
            
            function runNextTest() {
                if (currentTest >= totalTests) {
                    // All tests complete
                    const summary = allResults.reduce((acc, result) => {
                        acc.passed += result.passed;
                        acc.failed += result.failed;
                        return acc;
                    }, { passed: 0, failed: 0 });
                    
                    const overallRate = ((summary.passed / (summary.passed + summary.failed)) * 100).toFixed(1);
                    
                    document.getElementById('test-results').innerHTML = `
                        <h3>All Tests Complete</h3>
                        <p><strong>Overall Summary:</strong> ${summary.passed} passed, ${summary.failed} failed (${overallRate}% success rate)</p>
                        <p>Ran ${totalTests} test configurations.</p>
                    `;
                    return;
                }
                
                const localeIndex = Math.floor(currentTest / (flows.length * layouts.length));
                const flowIndex = Math.floor((currentTest % (flows.length * layouts.length)) / layouts.length);
                const layoutIndex = currentTest % layouts.length;
                
                const locale = locales[localeIndex];
                const flow = flows[flowIndex];
                const layout = layouts[layoutIndex];
                
                // Skip wizard for incompatible flows
                if (layout === 'wizard' && (flow === 'by_year' || flow === 'by_generation')) {
                    currentTest++;
                    setTimeout(runNextTest, 100);
                    return;
                }
                
                document.getElementById('test-locale').value = locale;
                document.getElementById('test-search-flow').value = flow;
                document.getElementById('test-form-layout').value = layout;
                
                runSingleTest();
                
                const results = checkTranslationResults(document.getElementById('widget-preview'));
                allResults.push(results);
                
                currentTest++;
                setTimeout(runNextTest, 500);
            }
            
            runNextTest();
        });

        // Initialize
        runSingleTest();
    </script>
</body>
</html>
