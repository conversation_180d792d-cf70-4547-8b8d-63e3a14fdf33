<?php
declare(strict_types=1);

namespace MyTyreFinder\Admin;

use MyTyreFinder\Services\WheelSizeApi;

final class ApiPage
{
    /** menu-slug of the main plugin page (already saved for users) */
    public const SLUG = 'wheel-size';

    public function register(): void
    {
        /* page + menu item */
        add_action('admin_menu', [$this, 'add_menu']);
        add_action('admin_init', [$this, 'register_api_settings']);

        /* save form before rendering page,
           so that notice «Settings saved!» is displayed */
        add_action('admin_post_wheel_size_save_api', [$this, 'save_settings']);

        // AJAX for API test
        add_action('wp_ajax_wheel_size_test_api', [$this, 'ajax_test_api']);
    }

    public function register_api_settings(): void
    {
        register_setting('wheel_size_api_settings', 'wheel_size_api_key');
        register_setting('wheel_size_api_settings', 'wheel_size_api_region');
        register_setting('wheel_size_api_settings', 'wheel_size_search_cache_ttl');
    }

    /* ---------- Menu ---------- */
    public function add_menu(): void
    {
        add_menu_page(
            'Wheel-Size',
            'Wheel-Size',
            'manage_options',
            self::SLUG,
            [$this, 'render_page'],
            'dashicons-admin-tools',
            30
        );

        add_submenu_page(
            self::SLUG,
            'Wheel-Size - API Settings',
            'API',
            'manage_options',
            self::SLUG,
            [$this, 'render_page']
        );
    }

    /* ---------- Screen Render ---------- */
    public function render_page(): void
    {
        $api_key       = get_option('wheel_size_api_key', '');
        $cache_timeout = get_option('wheel_size_search_cache_ttl', 7200);
        $saved         = isset($_GET['saved']);  // flag after redirect
        $api_validated = isset($_GET['api_validated']);
        $api_error     = isset($_GET['api_error']) ? urldecode($_GET['api_error']) : '';
        $is_configured = ApiValidator::is_api_configured();

        ?>
        <div class="wrap">
            <style>
                .shortcode-box {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    border: 1px solid #cbd5e1;
                    padding: 18px;
                    border-radius: 12px;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                    max-width: 600px;
                    font-size: 14px;
                    position: relative;
                    border-left: 4px solid #10b981;
                }
                .shortcode-box::before {
                    content: "🚀";
                    position: absolute;
                    top: -10px;
                    left: 20px;
                    background: #10b981;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 600;
                    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
                }
                .shortcode-box .shortcode-title {
                    color: #059669;
                    font-size: 16px;
                    font-weight: 700;
                    display: block;
                    margin-bottom: 12px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                .shortcode-box .shortcode-description {
                    color: #475569;
                    margin: 0 0 14px 0;
                    line-height: 1.5;
                    font-size: 13px;
                }
                .shortcode-box .shortcode-steps {
                    background: #ffffff;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    padding: 12px;
                    margin: 12px 0;
                    font-size: 12px;
                    color: #64748b;
                    line-height: 1.4;
                }
                .shortcode-box .shortcode-steps ol {
                    margin: 0;
                    padding-left: 16px;
                }
                .shortcode-box .shortcode-steps li {
                    margin: 4px 0;
                }
                .shortcode-box .shortcode-code {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    background: #1e293b;
                    border: 1px solid #334155;
                    border-radius: 8px;
                    padding: 12px 16px;
                    margin-top: 12px;
                }
                .shortcode-box code {
                    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
                    background: transparent;
                    color: #10b981;
                    font-size: 15px;
                    font-weight: 600;
                    letter-spacing: 0.5px;
                    flex: 1;
                }
                .shortcode-copy-btn {
                    background: #10b981;
                    color: white;
                    border: none;
                    padding: 8px 14px;
                    border-radius: 6px;
                    font-size: 11px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    white-space: nowrap;
                }
                .shortcode-copy-btn:hover {
                    background: #059669;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
                }
                .api-notices-container {
                    margin-bottom: 20px;
                }
                .api-notices-container .notice {
                    margin-bottom: 10px;
                }
                /* Custom warning alert - independent from WordPress notice system */
                .ws-alert {
                    border-left: 4px solid #ffb900;
                    background: #fffbe5;
                    padding: 12px 16px;
                    margin: 5px 0 15px 0;
                    border-radius: 0 4px 4px 0;
                    box-shadow: 0 1px 1px rgba(0,0,0,.04);
                }
                .ws-alert__title {
                    display: block;
                    font-size: 16px;
                    font-weight: 600;
                    margin: 0 0 6px 0;
                    color: #8a6914;
                }
                @media (max-width: 768px) {
                    .shortcode-box {
                        max-width: none;
                        padding: 16px;
                    }
                    .shortcode-box .shortcode-title {
                        font-size: 14px;
                    }
                    .shortcode-box .shortcode-description {
                        font-size: 12px;
                    }
                    .shortcode-box .shortcode-steps {
                        font-size: 11px;
                    }
                    .shortcode-box .shortcode-code {
                        flex-direction: column;
                        gap: 8px;
                        align-items: stretch;
                    }
                    .shortcode-copy-btn {
                        width: 100%;
                        padding: 10px 12px;
                    }
                }
                
                /* Two-column layout styles */
                .grid {
                    display: grid;
                }
                .grid-cols-1 {
                    grid-template-columns: repeat(1, minmax(0, 1fr));
                }
                .lg\:grid-cols-2 {
                    grid-template-columns: repeat(2, minmax(0, 1fr));
                }
                .gap-8 {
                    gap: 2rem;
                }
                .flex-1 {
                    flex: 1 1 0%;
                }
                .flex-shrink-0 {
                    flex-shrink: 0;
                }
                
                /* Responsive adjustments */
                @media (max-width: 1024px) {
                    .lg\:grid-cols-2 {
                        grid-template-columns: repeat(1, minmax(0, 1fr));
                    }
                    .flex-shrink-0 {
                        order: -1;
                    }
                }
            </style>

            <h1>Wheel-Size – API Settings</h1>

            <div class="api-notices-container">
                <?php if ($saved): ?>
                    <div class="notice notice-success is-dismissible"><p>Settings saved!</p></div>
                <?php endif; ?>

                <?php if ($api_validated): ?>
                    <div class="notice notice-success is-dismissible">
                        <p><strong>✅ API key validated successfully!</strong> All plugin functionality is now enabled.</p>
                    </div>
                <?php endif; ?>

                <?php if ($api_error): ?>
                    <div class="notice notice-error is-dismissible">
                        <p><strong>❌ API validation failed:</strong> <?php echo esc_html($api_error); ?></p>
                    </div>
                <?php endif; ?>

                <?php if (!$is_configured): ?>
                    <div class="ws-alert">
                        <strong class="ws-alert__title">⚠️ Plugin Inactive</strong>
                        <p>The Wheel-Size plugin is currently inactive because a valid API key has not been provided or has not been validated.</p>

                        <p><strong>To activate the plugin, follow these steps:</strong></p>
                        <ol>
                            <li>Visit <a href="https://developer.wheel-size.com" target="_blank" rel="noopener">developer.wheel-size.com</a></li>
                            <li>Register for a developer account (if you don't have one)</li>
                            <li>Fill out the application form with your project details</li>
                            <li>Wait for manual approval (typically 10–30 minutes during business hours)</li>
                            <li>Once approved, you will receive your working API key</li>
                            <li>Paste the key into the field below and click "Test Connection"</li>
                        </ol>

                        <p><strong>ℹ️ Note:</strong><br>
                        After receiving your key, activation may take a few minutes. You can use the "Test Connection" button to check its status.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Two-column layout -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column: API Settings Form -->
                <div class="flex-1">
                    <form method="post" action="<?php echo esc_url( admin_url('admin-post.php') ); ?>">
                        <?php
                            wp_nonce_field('wheel_size_api_settings', 'wheel_size_nonce');
                            /* admin-post требует указать действие */
                            echo '<input type="hidden" name="action" value="wheel_size_save_api">';
                        ?>

                        <table class="form-table" role="presentation">
                            <tr>
                                <th scope="row"><label for="api_key">API Key Wheel-Size</label></th>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                        <input type="password" id="api_key" name="api_key"
                                               value="<?php echo esc_attr($api_key); ?>" class="regular-text" />
                                        <button type="button" id="test-api-key" class="button button-secondary">Test Connection</button>
                                        <span id="api-test-status" style="font-weight: bold;"></span>
                                    </div>
                                    <div id="api-test-result" style="margin-top: 8px;"></div>
                                    <p class="description">
                                        Get your API key at <a href="https://developer.wheel-size.com/"
                                        target="_blank">developer.wheel-size.com</a>
                                    </p>
                                    <p id="api-status-text" style="font-weight: bold; <?php echo $is_configured ? 'color: #46b450;' : 'color: #dc3232;'; ?>">
                                        <?php if ($is_configured): ?>
                                            ✅ API key is validated and active
                                        <?php else: ?>
                                            ❌ API key not validated - plugin functionality disabled
                                        <?php endif; ?>
                                    </p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><label for="search_cache_ttl">Search Results Cache TTL (sec)</label></th>
                                <td>
                                    <input type="number" id="search_cache_ttl" name="search_cache_ttl"
                                           value="<?php echo esc_attr($cache_timeout); ?>"
                                           class="small-text" min="0" max="14400" />
                                    <p class="description">Cache duration for search results only. 0 — disable caching. Allowed: 3600–14400 sec. Other data (makes, models, years, modifications) are cached for 30 days.</p>
                                </td>
                            </tr>
                        </table>

                        <!-- API test functionality is now integrated with the API key field above -->

                        <?php submit_button('Save Changes'); ?>
                    </form>
                </div>

                <!-- Right Column: Widget Ready Box -->
                <?php if ($is_configured): ?>
                <div class="flex-shrink-0">
                    <div class="shortcode-box">
                        <span class="shortcode-title">Widget Ready!</span>
                        <p class="shortcode-description">
                            Your tire finder widget is configured and ready to be placed on your website.
                        </p>
                        <div class="shortcode-steps">
                            <ol>
                                <li>Copy the shortcode below</li>
                                <li>Paste it into any page or post</li>
                                <li>Publish the page</li>
                            </ol>
                        </div>
                        <div class="shortcode-code">
                            <code id="shortcode-text">[wheel_fit]</code>
                            <button type="button" class="shortcode-copy-btn" onclick="copyShortcode()">Copy</button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <h2>Usage Statistics</h2>
            <?php $this->render_stats(); ?>
        </div>
        <script>
        document.getElementById('test-api-key').addEventListener('click', function() {
            const button = this;
            const statusSpan = document.getElementById('api-test-status');
            const resultDiv = document.getElementById('api-test-result');
            const apiKeyInput = document.getElementById('api_key');

            const apiKey = apiKeyInput.value.trim();
            if (!apiKey) {
                statusSpan.textContent = '❌ Error';
                statusSpan.style.color = '#dc3232';
                resultDiv.innerHTML = '<div class="notice notice-error inline"><p>Please enter an API key first.</p></div>';
                return;
            }

            button.disabled = true;
            button.textContent = 'Testing...';
            statusSpan.textContent = '⏳ Testing...';
            statusSpan.style.color = '#0073aa';
            resultDiv.innerHTML = '<div class="spinner is-active" style="float: none; margin: 5px 0;"></div>';

            fetch(ajaxurl, {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: new URLSearchParams({
                    action: 'wheel_size_test_api',
                    nonce: '<?php echo wp_create_nonce('wheel_size_test_api'); ?>',
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('API Test Response:', data); // Debug logging

                // Get references to status elements
                const apiStatusText = document.getElementById('api-status-text');
                const existingNotices = document.querySelectorAll('.notice:not(.inline)');

                if (data.success) {
                    // Update inline status
                    statusSpan.textContent = '✅ Valid';
                    statusSpan.style.color = '#46b450';

                    // Update static status text
                    if (apiStatusText) {
                        apiStatusText.style.color = '#46b450';
                        apiStatusText.textContent = '✅ API key is validated and active';
                    }

                    // Hide existing error notices
                    existingNotices.forEach(notice => {
                        if (notice.classList.contains('notice-error') || notice.classList.contains('notice-warning')) {
                            notice.style.display = 'none';
                        }
                    });

                    // Show shortcode box if it was hidden
                    const gridContainer = document.querySelector('.grid');
                    const existingShortcodeBox = document.querySelector('.shortcode-box');
                    if (!existingShortcodeBox && gridContainer) {
                        // Create right column container
                        const rightColumn = document.createElement('div');
                        rightColumn.className = 'flex-shrink-0';
                        
                        const shortcodeBox = document.createElement('div');
                        shortcodeBox.className = 'shortcode-box';
                        shortcodeBox.innerHTML = `
                            <span class="shortcode-title">Widget Ready!</span>
                            <p class="shortcode-description">
                                Your tire finder widget is configured and ready to be placed on your website.
                            </p>
                            <div class="shortcode-steps">
                                <ol>
                                    <li>Copy the shortcode below</li>
                                    <li>Paste it into any page or post</li>
                                    <li>Publish the page</li>
                                </ol>
                            </div>
                            <div class="shortcode-code">
                                <code id="shortcode-text">[wheel_fit]</code>
                                <button type="button" class="shortcode-copy-btn" onclick="copyShortcode()">Copy</button>
                            </div>
                        `;
                        
                        rightColumn.appendChild(shortcodeBox);
                        gridContainer.appendChild(rightColumn);
                    }

                    let successMessage = '<div class="notice notice-success inline"><p><strong>API key validated successfully!</strong> ';
                    if (data.data && data.data.makes_count) {
                        successMessage += 'Found ' + data.data.makes_count + ' vehicle makes. ';
                    }
                    if (data.data && data.data.debug_info) {
                        successMessage += '<br><small>Debug: Flag saved=' + data.data.debug_info.flag_saved + ', Time=' + data.data.debug_info.timestamp + '</small>';
                    }
                    successMessage += '<br><strong>Plugin is now active!</strong> Reloading page...</p></div>';

                    resultDiv.innerHTML = successMessage;

                    // Reload page after successful validation to show updated menu and remove notices
                    console.log('Reloading page in 2 seconds...');
                    setTimeout(() => {
                        const currentUrl = window.location.href;
                        const separator = currentUrl.includes('?') ? '&' : '?';
                        window.location.href = currentUrl + separator + 'api_validated=1&t=' + Date.now();
                    }, 2000);
                } else {
                    // Update inline status
                    statusSpan.textContent = '❌ Invalid';
                    statusSpan.style.color = '#dc3232';

                    // Update static status text
                    if (apiStatusText) {
                        apiStatusText.style.color = '#dc3232';
                        apiStatusText.textContent = '❌ API key not validated - plugin functionality disabled';
                    }

                    // Hide existing success notices
                    existingNotices.forEach(notice => {
                        if (notice.classList.contains('notice-success')) {
                            notice.style.display = 'none';
                        }
                    });

                    // Hide shortcode box on validation failure
                    const rightColumn = document.querySelector('.flex-shrink-0');
                    if (rightColumn) {
                        rightColumn.remove();
                    }

                    resultDiv.innerHTML = '<div class="notice notice-error inline"><p><strong>Validation failed:</strong> ' + (data.data || 'Unknown error') + '</p></div>';
                }
            })
            .catch(error => {
                statusSpan.textContent = '❌ Error';
                statusSpan.style.color = '#dc3232';
                resultDiv.innerHTML = '<div class="notice notice-error inline"><p><strong>Connection error:</strong> ' + error.message + '</p></div>';
            })
            .finally(() => {
                button.disabled = false;
                button.textContent = 'Test Connection';
            });
        });

        // Copy shortcode function
        function copyShortcode() {
            const shortcodeText = document.getElementById('shortcode-text');
            const copyBtn = document.querySelector('.shortcode-copy-btn');

            if (shortcodeText) {
                // Create a temporary textarea to copy text
                const tempTextarea = document.createElement('textarea');
                tempTextarea.value = shortcodeText.textContent;
                document.body.appendChild(tempTextarea);
                tempTextarea.select();

                try {
                    document.execCommand('copy');

                    // Visual feedback
                    const originalText = copyBtn.textContent;
                    copyBtn.textContent = 'Copied!';
                    copyBtn.style.background = '#059669';

                    setTimeout(() => {
                        copyBtn.textContent = originalText;
                        copyBtn.style.background = '#10b981';
                    }, 2000);

                } catch (err) {
                    console.error('Failed to copy shortcode:', err);
                    copyBtn.textContent = 'Error';
                    setTimeout(() => {
                        copyBtn.textContent = 'Copy';
                    }, 2000);
                } finally {
                    document.body.removeChild(tempTextarea);
                }
            }
        }
        </script>
        <?php
    }

    /* ---------- Save Settings ---------- */
    public function save_settings(): void
    {
        /* nonce + permissions check */
        if (
            !current_user_can('manage_options') ||
            !isset($_POST['wheel_size_nonce']) ||
            !wp_verify_nonce($_POST['wheel_size_nonce'], 'wheel_size_api_settings')
        ) {
            wp_die('Permission check failed', 403);
        }

        $api_key = sanitize_text_field($_POST['api_key'] ?? '');
        $current_api_key = get_option('wheel_size_api_key', '');
        
        // Only validate API key if it has changed
        $validation_result = null;
        if ($api_key !== $current_api_key) {
            $validation_result = ApiValidator::validate_and_save_api_key($api_key);
        } else {
            // API key hasn't changed, just save it without validation
            update_option('wheel_size_api_key', $api_key);
        }

        // Save other settings
        update_option(
            'wheel_size_search_cache_ttl',
            max(0, (int)($_POST['search_cache_ttl'] ?? 7200))
        );

        $this->clear_dynamic_cache();

        // Prepare redirect with validation result only if validation was performed
        $redirect_args = ['saved' => '1'];
        if ($validation_result !== null) {
            if ($validation_result['success']) {
                $redirect_args['api_validated'] = '1';
            } else {
                $redirect_args['api_error'] = urlencode($validation_result['message']);
            }
        }

        /* redirect back with validation result flag */
        wp_safe_redirect( add_query_arg($redirect_args, wp_get_referer() ?: admin_url('admin.php?page=' . self::SLUG)) );
        exit;
    }

    /* ---------- Stats Block (unchanged) ---------- */
    private function render_stats(): void
    {
        $stats = get_option('wheel_size_api_stats', [
            'total'    => 0,
            'success'  => 0,
            'failed'   => 0,
            'cache'    => 0,
            'last_ts'  => null,
        ]);

        $total = (int) $stats['total'];
        $success = (int) $stats['success'];
        $failed = (int) $stats['failed'];
        $cache = (int) $stats['cache'];

        $success_rate = $total > 0 ? ($success / $total) * 100 : 0;
        $failed_rate = $total > 0 ? ($failed / $total) * 100 : 0;
        $cache_rate = $total > 0 ? ($cache / $total) * 100 : 0;

        $last_request_time = $stats['last_ts']
            ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $stats['last_ts'])
            : 'Never';
        ?>
        <style>
            .stats-container { background: #fff; border: 1px solid #ccd0d4; border-radius: 4px; padding: 1rem; }
            .stat-item { display: grid; grid-template-columns: 160px 1fr; align-items: center; gap: 1rem; padding: 0.75rem 0; border-bottom: 1px solid #f0f0f1; }
            .stat-item:last-child { border-bottom: none; }
            .stat-label { font-weight: 500; color: #2c3338; }
            .stat-label span { font-size: 1.5em; margin-right: 8px; vertical-align: middle; }
            .stat-value-container { display: flex; flex-direction: column; gap: 4px; }
            .stat-value { font-weight: 600; font-size: 1.1em; }
            .stat-bar { height: 6px; background-color: #e5e7eb; border-radius: 3px; width: 100%; overflow: hidden; }
            .stat-bar-fill { height: 100%; border-radius: 3px; transition: width 0.3s ease-in-out; }
            .stat-bar-fill.bg-success { background-color: #22c55e; }
            .stat-bar-fill.bg-danger { background-color: #ef4444; }
            .stat-bar-fill.bg-info { background-color: #3b82f6; }
            .stat-footer { margin-top: 1rem; text-align: right; }
        </style>
        <div id="api-stats-wrapper">
            <div class="stats-container">
                <div class="stat-item"><div class="stat-label"><span>⏱️</span> Total Requests</div><div class="stat-value-container"><strong class="stat-value" data-stat="total"><?php echo $total; ?></strong></div></div>
                <div class="stat-item"><div class="stat-label"><span>✅</span> Successful</div><div class="stat-value-container"><strong class="stat-value"><span data-stat="success"><?php echo $success; ?></span> <small data-stat-rate="success">(<?php echo round($success_rate, 1); ?>%)</small></strong><div class="stat-bar"><div class="stat-bar-fill bg-success" style="width: <?php echo $success_rate; ?>%;" data-stat-bar="success"></div></div></div></div>
                <div class="stat-item"><div class="stat-label"><span>❌</span> Failed</div><div class="stat-value-container"><strong class="stat-value"><span data-stat="failed"><?php echo $failed; ?></span> <small data-stat-rate="failed">(<?php echo round($failed_rate, 1); ?>%)</small></strong><div class="stat-bar"><div class="stat-bar-fill bg-danger" style="width: <?php echo $failed_rate; ?>%;" data-stat-bar="failed"></div></div></div></div>
                <div class="stat-item"><div class="stat-label"><span>📦</span> Cache Hits</div><div class="stat-value-container"><strong class="stat-value"><span data-stat="cache"><?php echo $cache; ?></span> <small data-stat-rate="cache">(<?php echo round($cache_rate, 1); ?>%)</small></strong><div class="stat-bar"><div class="stat-bar-fill bg-info" style="width: <?php echo $cache_rate; ?>%;" data-stat-bar="cache"></div></div></div></div>
                <div class="stat-item"><div class="stat-label"><span>🗓️</span> Last Request</div><div class="stat-value-container"><strong class="stat-value" data-stat="last_ts"><?php echo esc_html($last_request_time); ?></strong></div></div>
            </div>
            <div class="stat-footer">
                <button type="button" id="reset-stats-btn" class="button button-secondary">Reset Statistics</button>
            </div>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetButton = document.getElementById('reset-stats-btn');
            if (!resetButton) return;

            resetButton.addEventListener('click', function() {
                if (!confirm('Reset API statistics? This action is irreversible.')) return;

                const button = this;
                button.disabled = true;
                const originalText = button.textContent;
                button.textContent = 'Resetting...';

                fetch(ajaxurl, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: new URLSearchParams({
                        action: 'wheel_size_reset_stats',
                        nonce: '<?php echo wp_create_nonce('wheel_size_reset_stats_nonce'); ?>'
                    })
                })
                .then(response => response.ok ? response.json() : Promise.reject('Network response was not ok.'))
                .then(data => {
                    if (data.success) {
                        const wrapper = document.getElementById('api-stats-wrapper');
                        ['total', 'success', 'failed', 'cache'].forEach(key => {
                            wrapper.querySelector(`[data-stat="${key}"]`).textContent = '0';
                        });
                        wrapper.querySelector(`[data-stat="last_ts"]`).textContent = 'Never';
                        ['success', 'failed', 'cache'].forEach(key => {
                            wrapper.querySelector(`[data-stat-rate="${key}"]`).textContent = '(0%)';
                            wrapper.querySelector(`[data-stat-bar="${key}"]`).style.width = '0%';
                        });
                        wrapper.style.transition = 'opacity 0.2s';
                        wrapper.style.opacity = '0.5';
                        setTimeout(() => { wrapper.style.opacity = '1'; }, 200);
                    } else {
                        alert('Error: ' + (data.data || 'Could not reset statistics.'));
                    }
                })
                .catch(error => alert('An error occurred. Please try again.'))
                .finally(() => {
                    button.disabled = false;
                    button.textContent = originalText;
                });
            });
        });
        </script>
        <?php
    }

    private function clear_dynamic_cache(): void
    {
        global $wpdb;
        $like_patterns = [
            '_transient_wheel_fit_modifications_%',
            '_transient_timeout_wheel_fit_modifications_%',
            '_transient_wheel_fit_search_%',
            '_transient_timeout_wheel_fit_search_%'
        ];
        $where = implode(' OR ', array_map(fn($p) => "option_name LIKE '{$p}'", $like_patterns));
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE {$where}");
    }

    public function ajax_test_api(): void
    {
        if (
            !current_user_can('manage_options') ||
            !isset($_POST['nonce']) ||
            !wp_verify_nonce($_POST['nonce'] ?? '', 'wheel_size_test_api')
        ) {
            wp_die('Permission check failed', 403);
        }

        $api_key = sanitize_text_field($_POST['api_key'] ?? '');

        // Use the validation system that also saves the configuration
        $validation_result = ApiValidator::validate_and_save_api_key($api_key);

        if ($validation_result['success']) {
            // Double-check that the flag was actually saved
            $is_now_configured = ApiValidator::is_api_configured();

            // Return success with additional status information
            wp_send_json_success([
                'message' => $validation_result['message'],
                'data' => $validation_result['data'] ?? [],
                'api_configured' => $is_now_configured,
                'reload_required' => true,
                'debug_info' => [
                    'flag_saved' => $is_now_configured,
                    'api_key_length' => strlen($api_key),
                    'timestamp' => current_time('mysql')
                ]
            ]);
        } else {
            wp_send_json_error($validation_result['message']);
        }
    }
} 