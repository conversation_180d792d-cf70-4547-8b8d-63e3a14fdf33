<?php
/**
 * Web interface for debugging and updating themes
 */

// Ensure WordPress is loaded
require_once dirname(__DIR__, 3) . '/wp-load.php';

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

use MyTyreFinder\Includes\ThemeManager;

$output = '';
$error = '';

if (isset($_POST['update_themes'])) {
    try {
        $output .= "🔍 Theme Debug and Force Update\n";
        $output .= "===============================\n\n";
        
        // 1. Check current themes
        $output .= "1️⃣ Current themes in database:\n";
        $current_themes = get_option('wsf_theme_presets', []);
        
        if (empty($current_themes)) {
            $output .= "   ❌ No themes found in database\n";
        } else {
            foreach ($current_themes as $slug => $theme) {
                $name = $theme['name'] ?? $slug;
                $accent = $theme['--wsf-accent'] ?? 'not set';
                $output .= "   • {$name} ({$slug}): accent = {$accent}\n";
            }
        }
        
        $output .= "\n2️⃣ Active theme: " . get_option('wsf_active_theme', 'not set') . "\n\n";
        
        // 2. Force clear and regenerate
        $output .= "3️⃣ Force clearing themes...\n";
        delete_option('wsf_theme_presets');
        $output .= "   ✅ Cleared database\n";
        
        // 3. Get fresh themes (this will regenerate them)
        $output .= "4️⃣ Regenerating themes...\n";
        $new_themes = ThemeManager::get_themes();
        $output .= "   ✅ Regenerated themes\n";
        
        // 4. Verify new themes
        $output .= "5️⃣ New themes:\n";
        foreach ($new_themes as $slug => $theme) {
            $name = $theme['name'] ?? $slug;
            $accent = $theme['--wsf-accent'] ?? 'not set';
            $output .= "   • {$name} ({$slug}): accent = {$accent}\n";
            
            if ($slug === 'light' && $accent === '#1E40AF') {
                $output .= "     ✅ Light theme has correct dark blue accent\n";
            } elseif ($slug === 'light') {
                $output .= "     ❌ Light theme accent should be #1E40AF\n";
            }
            
            if ($slug === 'dark' && $accent === '#E5E7EB') {
                $output .= "     ✅ Dark theme has correct light gray accent\n";
            } elseif ($slug === 'dark') {
                $output .= "     ❌ Dark theme accent should be #E5E7EB\n";
            }
        }
        
        // 5. Set light theme as active
        $output .= "\n6️⃣ Setting light theme as active...\n";
        $result = ThemeManager::set_active_theme('light');
        $output .= "   " . ($result ? '✅' : '❌') . " Result: " . ($result ? 'success' : 'failed') . "\n";
        
        $output .= "\n🎉 Update completed! Refresh your admin panel now.\n";
        
    } catch (Exception $e) {
        $error = "❌ Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Theme Debug Tool</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .output { background: #f0f0f0; padding: 15px; white-space: pre-line; margin: 10px 0; }
        .error { background: #ffebee; color: #c62828; padding: 15px; margin: 10px 0; }
        button { padding: 10px 20px; font-size: 16px; }
    </style>
</head>
<body>
    <h1>🎨 Theme Debug Tool</h1>
    
    <form method="post">
        <button type="submit" name="update_themes">🔄 Force Update Themes</button>
    </form>
    
    <?php if ($error): ?>
        <div class="error"><?php echo esc_html($error); ?></div>
    <?php endif; ?>
    
    <?php if ($output): ?>
        <div class="output"><?php echo esc_html($output); ?></div>
    <?php endif; ?>
    
    <p><a href="<?php echo admin_url('admin.php?page=wheel-fit-appearance'); ?>">← Back to Appearance Settings</a></p>
</body>
</html>
