/**
 * Test script to verify garage functionality with auto-search enabled
 * Run in browser console on admin appearance page
 */

console.log('🚗 === GARAGE + AUTO-SEARCH TEST ===');

// Check elements
const checkbox = document.getElementById('auto_search_on_last_input');
const previewContainer = document.getElementById('widget-preview');

console.log('\n1. Elements Check:');
console.log('   Auto-search checkbox found:', !!checkbox);
console.log('   Preview container found:', !!previewContainer);

if (!checkbox || !previewContainer) {
    console.log('❌ Required elements not found');
    return;
}

console.log('\n2. Current State:');
console.log('   Auto-search enabled:', checkbox.checked);
console.log('   WheelFitData.garageEnabled:', window.WheelFitData?.garageEnabled);

// Test garage buttons in preview
function testGarageButtons(autoSearchEnabled) {
    const previewWidget = previewContainer.querySelector('.wheel-fit-widget');
    if (!previewWidget) {
        console.log('   ❌ Preview widget not found');
        return;
    }

    const garageButtons = previewWidget.querySelectorAll('[data-garage-trigger]');
    const submitButtons = previewWidget.querySelectorAll('button[type="submit"]');
    
    console.log(`\n${autoSearchEnabled ? '3a' : '3b'}. Garage Buttons Test (auto-search ${autoSearchEnabled ? 'ON' : 'OFF'}):`);
    console.log('   Garage buttons found:', garageButtons.length);
    console.log('   Submit buttons found:', submitButtons.length);
    
    if (garageButtons.length > 0) {
        console.log('   ✅ Garage buttons present');
        
        // Check if garage buttons are visible
        const visibleGarageButtons = Array.from(garageButtons).filter(btn => {
            const style = window.getComputedStyle(btn);
            return style.display !== 'none' && style.visibility !== 'hidden';
        });
        
        console.log('   Visible garage buttons:', visibleGarageButtons.length);
        
        if (visibleGarageButtons.length > 0) {
            console.log('   ✅ Garage buttons are visible');
            
            // Test garage button click
            const firstGarageButton = visibleGarageButtons[0];
            console.log('   Testing garage button click...');
            
            // Mock click event
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            firstGarageButton.dispatchEvent(clickEvent);
            
            // Check if garage drawer opened
            setTimeout(() => {
                const garageDrawer = document.getElementById('garage-drawer');
                if (garageDrawer) {
                    const isOpen = !garageDrawer.classList.contains('translate-x-full');
                    console.log('   Garage drawer opened:', isOpen ? '✅' : '❌');
                    
                    // Close drawer if opened
                    if (isOpen) {
                        const closeBtn = document.getElementById('garage-close-btn');
                        if (closeBtn) {
                            closeBtn.click();
                        }
                    }
                } else {
                    console.log('   ❌ Garage drawer not found');
                }
            }, 100);
            
        } else {
            console.log('   ❌ Garage buttons are hidden');
        }
    } else {
        console.log('   ❌ No garage buttons found');
    }
    
    // Check submit buttons visibility
    if (autoSearchEnabled) {
        const hiddenSubmitButtons = Array.from(submitButtons).filter(btn => btn.classList.contains('hidden'));
        console.log('   Hidden submit buttons:', hiddenSubmitButtons.length, '(should equal total)');
        console.log('   Submit buttons correctly hidden:', hiddenSubmitButtons.length === submitButtons.length ? '✅' : '❌');
    } else {
        const visibleSubmitButtons = Array.from(submitButtons).filter(btn => !btn.classList.contains('hidden'));
        console.log('   Visible submit buttons:', visibleSubmitButtons.length, '(should equal total)');
        console.log('   Submit buttons correctly visible:', visibleSubmitButtons.length === submitButtons.length ? '✅' : '❌');
    }
}

// Test current state
testGarageButtons(checkbox.checked);

// Test toggle
console.log('\n🔄 Testing Auto-Search Toggle...');
const originalState = checkbox.checked;

// Toggle checkbox
checkbox.checked = !originalState;
checkbox.dispatchEvent(new Event('change', { bubbles: true }));

// Wait for preview update and test again
setTimeout(() => {
    testGarageButtons(checkbox.checked);
    
    // Restore original state
    checkbox.checked = originalState;
    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
    
    setTimeout(() => {
        console.log('\n4. Final Test (restored state):');
        testGarageButtons(originalState);
        
        console.log('\n✅ Test Summary:');
        console.log('   - Garage buttons should be visible in both auto-search modes');
        console.log('   - Submit buttons should be hidden when auto-search is ON');
        console.log('   - Submit buttons should be visible when auto-search is OFF');
        console.log('   - Garage functionality should work regardless of auto-search setting');
        
    }, 2000);
    
}, 2000);

console.log('\n⏳ Running toggle test, please wait...');
