/**
 * Test script for translation persistence during dynamic form updates
 * This script tests that translations are properly re-applied when:
 * 1. Search Flow changes (by_vehicle, by_year, by_generation)
 * 2. Form Layout changes (popup-horizontal, inline, stepper, wizard)
 * 3. Dynamic content is loaded (dropdowns, results)
 */

(function() {
    'use strict';

    console.log('[Translation Test] Starting translation persistence tests');

    // Test configuration
    const TEST_CONFIG = {
        // Test different language configurations
        locales: ['en', 'de', 'fr', 'es'],
        
        // Test different search flows
        searchFlows: ['by_vehicle', 'by_year', 'by_generation'],
        
        // Test different form layouts
        formLayouts: ['popup-horizontal', 'inline', 'stepper', 'wizard'],
        
        // Translation keys to test
        testKeys: [
            'widget_title',
            'label_make',
            'label_model', 
            'label_year',
            'label_modification',
            'placeholder_make',
            'placeholder_model',
            'placeholder_year',
            'placeholder_mod',
            'button_search',
            'section_results'
        ]
    };

    // Test utilities
    const TestUtils = {
        /**
         * Wait for element to appear in DOM
         */
        waitForElement: function(selector, timeout = 5000) {
            return new Promise((resolve, reject) => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }

                const observer = new MutationObserver((mutations, obs) => {
                    const element = document.querySelector(selector);
                    if (element) {
                        obs.disconnect();
                        resolve(element);
                    }
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                setTimeout(() => {
                    observer.disconnect();
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                }, timeout);
            });
        },

        /**
         * Simulate form configuration change
         */
        simulateConfigChange: function(searchFlow, formLayout) {
            console.log(`[Translation Test] Simulating config change: ${searchFlow} + ${formLayout}`);
            
            // Trigger preview update if we're in admin
            if (typeof updatePreview === 'function') {
                // Set form values
                const searchFlowSelect = document.getElementById('search_flow');
                const layoutSelect = document.getElementById('form_layout');
                
                if (searchFlowSelect) searchFlowSelect.value = searchFlow;
                if (layoutSelect) layoutSelect.value = formLayout;
                
                // Trigger update
                updatePreview(false);
            }
        },

        /**
         * Check if translations are applied correctly
         */
        checkTranslations: function(container = document) {
            const results = {
                passed: 0,
                failed: 0,
                details: []
            };

            // Check data-i18n elements
            const i18nElements = container.querySelectorAll('[data-i18n]');
            i18nElements.forEach(el => {
                const key = el.dataset.i18n;
                const currentText = el.textContent.trim();
                const expectedTranslation = window.WheelFitI18n && window.WheelFitI18n[key];
                
                if (expectedTranslation && currentText === expectedTranslation) {
                    results.passed++;
                    results.details.push({
                        type: 'text',
                        key: key,
                        status: 'PASS',
                        element: el.tagName,
                        expected: expectedTranslation,
                        actual: currentText
                    });
                } else {
                    results.failed++;
                    results.details.push({
                        type: 'text',
                        key: key,
                        status: 'FAIL',
                        element: el.tagName,
                        expected: expectedTranslation || key,
                        actual: currentText
                    });
                }
            });

            // Check data-i18n-placeholder elements
            const placeholderElements = container.querySelectorAll('[data-i18n-placeholder]');
            placeholderElements.forEach(el => {
                const key = el.dataset.i18nPlaceholder;
                const currentPlaceholder = el.placeholder || '';
                const expectedTranslation = window.WheelFitI18n && window.WheelFitI18n[key];
                
                if (expectedTranslation && currentPlaceholder === expectedTranslation) {
                    results.passed++;
                    results.details.push({
                        type: 'placeholder',
                        key: key,
                        status: 'PASS',
                        element: el.tagName,
                        expected: expectedTranslation,
                        actual: currentPlaceholder
                    });
                } else {
                    results.failed++;
                    results.details.push({
                        type: 'placeholder',
                        key: key,
                        status: 'FAIL',
                        element: el.tagName,
                        expected: expectedTranslation || key,
                        actual: currentPlaceholder
                    });
                }
            });

            return results;
        },

        /**
         * Generate test report
         */
        generateReport: function(testResults) {
            console.group('[Translation Test] Test Results Summary');
            
            let totalPassed = 0;
            let totalFailed = 0;
            
            testResults.forEach(result => {
                totalPassed += result.results.passed;
                totalFailed += result.results.failed;
                
                console.log(`${result.config}: ${result.results.passed} passed, ${result.results.failed} failed`);
                
                if (result.results.failed > 0) {
                    console.group(`Failed tests for ${result.config}:`);
                    result.results.details.filter(d => d.status === 'FAIL').forEach(detail => {
                        console.warn(`${detail.type} "${detail.key}" on ${detail.element}: expected "${detail.expected}", got "${detail.actual}"`);
                    });
                    console.groupEnd();
                }
            });
            
            console.log(`\nOverall: ${totalPassed} passed, ${totalFailed} failed`);
            console.log(`Success rate: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);
            
            console.groupEnd();
            
            return {
                totalPassed,
                totalFailed,
                successRate: (totalPassed / (totalPassed + totalFailed)) * 100
            };
        }
    };

    // Main test runner
    const TranslationPersistenceTest = {
        async runTests() {
            console.log('[Translation Test] Starting comprehensive translation persistence tests');
            
            const testResults = [];
            
            // Test different configurations
            for (const searchFlow of TEST_CONFIG.searchFlows) {
                for (const formLayout of TEST_CONFIG.formLayouts) {
                    // Skip wizard for flows that don't support it
                    if (formLayout === 'wizard' && (searchFlow === 'by_year' || searchFlow === 'by_generation')) {
                        continue;
                    }
                    
                    try {
                        console.log(`[Translation Test] Testing ${searchFlow} + ${formLayout}`);
                        
                        // Simulate configuration change
                        TestUtils.simulateConfigChange(searchFlow, formLayout);
                        
                        // Wait for form to update
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                        // Wait for preview container to be updated
                        const previewContainer = await TestUtils.waitForElement('#widget-preview', 3000);
                        
                        // Check translations
                        const results = TestUtils.checkTranslations(previewContainer);
                        
                        testResults.push({
                            config: `${searchFlow} + ${formLayout}`,
                            searchFlow,
                            formLayout,
                            results
                        });
                        
                    } catch (error) {
                        console.error(`[Translation Test] Error testing ${searchFlow} + ${formLayout}:`, error);
                        testResults.push({
                            config: `${searchFlow} + ${formLayout}`,
                            searchFlow,
                            formLayout,
                            results: { passed: 0, failed: 1, details: [{ error: error.message }] }
                        });
                    }
                }
            }
            
            // Generate and display report
            const summary = TestUtils.generateReport(testResults);
            
            // Return results for external use
            return {
                summary,
                details: testResults
            };
        }
    };

    // Make test runner globally available
    window.TranslationPersistenceTest = TranslationPersistenceTest;
    
    // Auto-run tests if we're in admin and have the necessary elements
    if (document.getElementById('widget-preview') && document.getElementById('search_flow')) {
        // Wait for page to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => TranslationPersistenceTest.runTests(), 2000);
            });
        } else {
            setTimeout(() => TranslationPersistenceTest.runTests(), 2000);
        }
    }

    console.log('[Translation Test] Translation persistence test script loaded');
})();
