<?php
// file: src/admin/BrandFilters.php

declare(strict_types=1);

namespace MyTyreFinder\Admin;

/**
 * Handles the Brand Include/Exclude feature in the admin panel.
 */
final class BrandFilters
{
    private const MAKES_TRANSIENT_KEY = 'wheel_size_all_makes';
    private const NONCE_ACTION = 'wheel_size_features_brands_action';
    private const NONCE_NAME = 'wheel_size_features_brands_nonce';

    /**
     * Registers the necessary hooks.
     */
    public static function register(): void
    {
        add_action('admin_post_wheel_size_save_brands', [__CLASS__, 'save_features']);
    }

    /**
     * Renders the brand filter form.
     * This method should be called from within the `features_page` of the main Admin class.
     */
    public static function render_form(): void
    {
        $all_makes = self::get_makes();
        $included_makes = get_option('wheel_size_include_makes', []);
        $excluded_makes = get_option('wheel_size_exclude_makes', []);

        if (is_wp_error($all_makes)) {
            echo '<div class="notice notice-error"><p>' . esc_html($all_makes->get_error_message()) . '</p></div>';
            return;
        }
        ?>
        <style>
            /* Brand Filters section styling to match main features */
            .brand-filters__section {
                background: #fff;
                border: 1px solid #c3c4c7;
                border-radius: 4px;
                margin-bottom: 20px;
                padding: 20px;
                box-shadow: 0 1px 1px rgba(0,0,0,.04);
            }

            .brand-filters__section h2 {
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 18px;
                font-weight: 600;
                color: #1d2327;
                border-bottom: 1px solid #e0e0e0;
                padding-bottom: 10px;
            }

            .brand-filters__section .description {
                margin-bottom: 20px;
                color: #646970;
            }

            /* Consistent button styling for both save buttons - WordPress standard size */
            .features-section .button-primary,
            .brand-filters__section .button-primary,
            .sticky-controls .button-primary {
                background: #2271b1;
                border-color: #2271b1;
                color: #fff;
                font-weight: 400;
                padding: 0 12px;
                font-size: 13px;
                line-height: 2.15384615;
                min-height: 30px;
                border-radius: 3px;
            }

            .features-section .button-primary:hover,
            .brand-filters__section .button-primary:hover,
            .sticky-controls .button-primary:hover {
                background: #135e96;
                border-color: #135e96;
            }

            /* Enhanced brand tag styling for better UX */
            .brand-tag {
                cursor: pointer !important;
                position: relative !important;
            }

            /* Hover and tooltip effects removed as per UI cleanup */

            /* Brand search input in sticky panel */
            .sticky-controls .brand-search-input {
                /* Visually consistent, rounded & with search icon */
                flex: 1 1 300px;      /* Grow but keep reasonable base */
                max-width: 350px;
                min-width: 250px;
                height: 32px;          /* Minimum height */
                padding: 6px 10px 6px 30px; /* room for icon */
                border: 1px solid #8c8f94;
                border-radius: 6px;
                font-size: 13px;
                margin-left: 10px;     /* ~8-12 px spacing from Neutral */

                /* Search icon */
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%238c8f94' viewBox='0 0 24 24'%3E%3Cpath d='M10 2a8 8 0 015.292 13.707l4 4a1 1 0 01-1.414 1.414l-4-4A8 8 0 1110 2zm0 2a6 6 0 100 12 6 6 0 000-12z'/%3E%3C/svg%3E") no-repeat 8px center;
                background-size: 16px 16px;
            }

            .sticky-controls .brand-search-input:focus {
                border-color: #2271b1;
                outline: none;
                box-shadow: 0 0 0 1px #2271b1;
            }

            /* Mobile: search goes beneath legend */
            @media (max-width: 600px) {
                .sticky-controls .brand-search-input {
                    flex: 1 1 100%;
                    margin-left: 0;
                    margin-top: 8px;
                }
            }

            /* Collapsible instructions */
            .instructions-toggle {
                background: none;
                border: none;
                color: #2271b1;
                cursor: pointer;
                font-size: 14px;
                text-decoration: underline;
                padding: 0;
                margin-bottom: 10px;
            }

            .instructions-toggle:hover {
                color: #135e96;
            }

            .instructions-content {
                display: none;
                margin-bottom: 15px;
            }

            .instructions-content.show {
                display: block;
            }

            /* Sticky controls panel */
            .sticky-controls {
                position: sticky;
                top: 32px; /* WordPress admin bar height */
                background: #fff;
                border: 1px solid #c3c4c7;
                border-radius: 4px;
                padding: 12px;
                margin-bottom: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                z-index: 100;
            }

            .sticky-controls .controls-row {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
            }

            /* Push the save button to the far right while keeping legend + search together */
            .sticky-controls .controls-row .button-primary {
                margin-left: auto;
            }

            .sticky-controls .legend-compact {
                display: flex;
                gap: 15px;
                flex-wrap: wrap;
            }

            .sticky-controls .legend-item {
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 12px;
            }

            .sticky-controls .color-dot {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                display: inline-block;
            }

            .sticky-controls .count-badge {
                background: #8c8f94;
                color: white;
                padding: 1px 6px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11px;
                min-width: 16px;
                text-align: center;
            }

            .sticky-controls .count-badge.included { background: #00a32a; }
            .sticky-controls .count-badge.excluded { background: #d63638; }
            .sticky-controls .count-badge.neutral { background: #8c8f94; }
        </style>
        <div class="brand-filters__section">
        <h2><?php esc_html_e('Brand Filters', 'my-tyre-finder'); ?></h2>

        <!-- Collapsible Instructions -->
        <button type="button" class="instructions-toggle" onclick="toggleInstructions()">
            ℹ️ <?php esc_html_e('How to use Brand Filters (click to show/hide)', 'my-tyre-finder'); ?>
        </button>

        <div class="instructions-content" id="brand-instructions">
            <div style="max-width: 700px;">
                <p><strong><?php esc_html_e('How to use Brand Filters:', 'my-tyre-finder'); ?></strong></p>
                <ul style="margin-left: 20px; margin-bottom: 15px;">
                    <li><?php esc_html_e('Click on any brand name to change its state', 'my-tyre-finder'); ?></li>
                    <li><?php esc_html_e('Each click cycles through: Neutral (gray) → Included (green) → Excluded (red) → back to Neutral', 'my-tyre-finder'); ?></li>
                    <li><strong><?php esc_html_e('Included (green):', 'my-tyre-finder'); ?></strong> <?php esc_html_e('Only these brands will appear in the finder', 'my-tyre-finder'); ?></li>
                    <li><strong><?php esc_html_e('Excluded (red):', 'my-tyre-finder'); ?></strong> <?php esc_html_e('These brands will be hidden from the finder', 'my-tyre-finder'); ?></li>
                    <li><strong><?php esc_html_e('Neutral (gray):', 'my-tyre-finder'); ?></strong> <?php esc_html_e('Default state - no special filtering applied', 'my-tyre-finder'); ?></li>
                </ul>
                <p style="background: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                    <strong><?php esc_html_e('💡 Tip:', 'my-tyre-finder'); ?></strong>
                    <?php esc_html_e('If you select any brands as "Included", only those brands will show up. If you only use "Excluded", all brands except the excluded ones will appear.', 'my-tyre-finder'); ?>
                </p>
            </div>
        </div>

        <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" id="brand-filters-form">
            <input type="hidden" name="action" value="wheel_size_save_brands">
            <?php wp_nonce_field(self::NONCE_ACTION, self::NONCE_NAME); ?>

            <div class="brand-filters__priority-note" style="background: #e7f3ff; padding: 12px; border-left: 4px solid #2271b1; margin-bottom: 15px;">
                <strong>⚡ Priority Rule:</strong><br>
                When at least one brand is Included, Excluded brands are ignored
            </div>

            <div class="brand-filters__combined-note" style="background: #f0f6ff; padding: 12px; border-left: 4px solid #0073aa; margin-bottom: 15px;">
                <strong>🔗 Combined with Regional Filters:</strong><br>
                When both filters are active, only brands matching your selection AND available in chosen regions will be shown.
            </div>

            <!-- Sticky Controls Panel -->
            <div class="sticky-controls" id="sticky-controls">
                <div class="controls-row">
                    <div class="legend-compact">
                        <div class="legend-item">
                            <span class="color-dot" style="background: #00a32a;"></span>
                            <?php esc_html_e('Included:', 'my-tyre-finder'); ?>
                            <span id="include-count-sticky" class="count-badge included">0</span>
                        </div>
                        <div class="legend-item">
                            <span class="color-dot" style="background: #d63638;"></span>
                            <?php esc_html_e('Excluded:', 'my-tyre-finder'); ?>
                            <span id="exclude-count-sticky" class="count-badge excluded">0</span>
                        </div>
                        <div class="legend-item">
                            <span class="color-dot" style="background: #8c8f94;"></span>
                            <?php esc_html_e('Neutral:', 'my-tyre-finder'); ?>
                            <span id="neutral-count-sticky" class="count-badge neutral">0</span>
                        </div>
                    </div>

                    <!-- Brand Search Field (moved outside legend-compact but same row) -->
                    <input type="search"
                           id="brand-search"
                           class="brand-search-input"
                           placeholder="<?php esc_attr_e('Search brands...', 'my-tyre-finder'); ?>"
                           onkeyup="filterBrands(this.value)">

                    <button type="submit" class="button-primary" form="brand-filters-form">
                        <?php esc_html_e('Save Brand Filters', 'my-tyre-finder'); ?>
                    </button>
                </div>
            </div>

            <div class="brand-filters__container">
                <?php foreach ($all_makes as $make) : ?>
                    <?php
                    $slug = $make['slug'];
                    $name = $make['name'];
                    $state = 'neutral';
                    if (in_array($slug, $included_makes, true)) {
                        $state = 'included';
                    } elseif (in_array($slug, $excluded_makes, true)) {
                        $state = 'excluded';
                    }
                    ?>
                    <button type="button"
                            role="checkbox"
                            class="brand-tag is-<?php echo esc_attr($state); ?>"
                            data-slug="<?php echo esc_attr($slug); ?>"
                            aria-checked="<?php echo 'neutral' !== $state ? 'true' : 'false'; ?>">
                        <?php echo esc_html($name); ?>
                    </button>
                <?php endforeach; ?>
            </div>

            <div id="brand-filters-hidden-inputs">
                <!-- JS will populate these -->
            </div>
        </form>

        <script>
        // Toggle instructions visibility
        function toggleInstructions() {
            const content = document.getElementById('brand-instructions');
            content.classList.toggle('show');
        }

        // Filter brands by search term
        function filterBrands(searchTerm) {
            const brands = document.querySelectorAll('.brand-tag');
            const term = searchTerm.toLowerCase();

            brands.forEach(brand => {
                const brandName = brand.textContent.toLowerCase();
                if (brandName.includes(term)) {
                    brand.style.display = '';
                } else {
                    brand.style.display = 'none';
                }
            });
        }

        // Update sticky counters (called from existing admin-brands.js)
        function updateStickyCounters() {
            const includedCount = document.querySelectorAll('.brand-tag.is-included').length;
            const excludedCount = document.querySelectorAll('.brand-tag.is-excluded').length;
            const neutralCount = document.querySelectorAll('.brand-tag.is-neutral').length;

            // Update sticky counters
            const includedSticky = document.getElementById('include-count-sticky');
            const excludedSticky = document.getElementById('exclude-count-sticky');
            const neutralSticky = document.getElementById('neutral-count-sticky');

            if (includedSticky) includedSticky.textContent = includedCount;
            if (excludedSticky) excludedSticky.textContent = excludedCount;
            if (neutralSticky) neutralSticky.textContent = neutralCount;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateStickyCounters();

            // Prevent Enter key from submitting form inside search field
            const searchInput = document.getElementById('brand-search');
            if (searchInput) {
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        filterBrands(this.value);
                    }
                });
            }

            // Hook into existing brand tag click handler to update sticky counters
            const container = document.querySelector('.brand-filters__container');
            if (container) {
                container.addEventListener('click', function() {
                    // Small delay to let the state change first
                    setTimeout(updateStickyCounters, 10);
                });
            }
        });
        </script>

        </div> <!-- /.brand-filters__section -->
        <?php
    }

    /**
     * Handles the saving of brand filter settings.
     */
    public static function save_features(): void
    {
        if (!isset($_POST[self::NONCE_NAME]) || !wp_verify_nonce(sanitize_key($_POST[self::NONCE_NAME]), self::NONCE_ACTION)) {
            wp_die(__('Invalid nonce specified', 'my-tyre-finder'), __('Error', 'my-tyre-finder'), 403);
        }

        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'my-tyre-finder'), __('Error', 'my-tyre-finder'), 403);
        }

        $included = isset($_POST['wheel_size_include_makes']) ? (array)$_POST['wheel_size_include_makes'] : [];
        $excluded = isset($_POST['wheel_size_exclude_makes']) ? (array)$_POST['wheel_size_exclude_makes'] : [];

        $validate_slug = static fn($slug) => preg_match('/^[a-z0-9-]+$/', $slug) ? $slug : null;

        $sanitized_included = array_filter(array_map($validate_slug, array_map('sanitize_text_field', $included)));
        $sanitized_excluded = array_filter(array_map($validate_slug, array_map('sanitize_text_field', $excluded)));

        update_option('wheel_size_include_makes', $sanitized_included);
        update_option('wheel_size_exclude_makes', $sanitized_excluded);

        // Clear API cache when brand filters change
        \MyTyreFinder\Admin\ApiValidator::clear_api_cache();

        // Log the new state for debugging/analytics
        error_log('Wheel-Size Brand Filters updated: include=[' . implode(', ', $sanitized_included) . '] exclude=[' . implode(', ', $sanitized_excluded) . ']');

        // Add a success notice
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . esc_html__('Brand filter settings saved successfully.', 'my-tyre-finder') . '</p></div>';
        });

        wp_safe_redirect(wp_get_referer());
        exit;
    }

    /**
     * Fetches makes from the API or transient cache.
     * For admin use - shows ALL makes without brand filters applied.
     *
     * @return array|\WP_Error
     */
    public static function get_makes()
    {
        $cached_makes = get_transient(self::MAKES_TRANSIENT_KEY);
        if (false !== $cached_makes) {
            return $cached_makes;
        }

        // Use WheelSizeApi with brand filters disabled for admin
        try {
            $api = new \MyTyreFinder\Services\WheelSizeApi();
            $makes = $api->getMakes(null, false); // false = don't apply brand filters

            if (empty($makes)) {
                return new \WP_Error('no_makes', __('No makes returned from API.', 'my-tyre-finder'));
            }

            // Cache the results
            set_transient(self::MAKES_TRANSIENT_KEY, $makes, DAY_IN_SECONDS);
            return $makes;

        } catch (\Exception $e) {
            return new \WP_Error('api_error', $e->getMessage());
        }
    }

    /**
     * Legacy method - kept for backward compatibility
     * Fetches makes directly from API without using WheelSizeApi class
     */
    public static function get_makes_legacy()
    {
        $api_key = get_option('wheel_size_api_key', '');
        if (empty($api_key)) {
            return new \WP_Error('api_key_missing', __('API Key is not configured.', 'my-tyre-finder'));
        }

        $url = "https://api.wheel-size.com/v2/makes/?ordering=slug&user_key=" . rawurlencode($api_key);
        $response = wp_remote_get($url, [
            'timeout' => 20,
            'headers' => [
                'Accept' => 'application/json',
            ],
        ]);

        if (is_wp_error($response)) {
            return $response;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code < 200 || $status_code >= 300) {
            return new \WP_Error(
                'http_error',
                sprintf(/* translators: 1: HTTP status code */ __('Wheel-Size API returned HTTP %d.', 'my-tyre-finder'), $status_code)
            );
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return new \WP_Error('invalid_json', __('Failed to decode Wheel-Size API response.', 'my-tyre-finder'));
        }

        // API may return either an object with "data" key or a plain array.
        $makes_raw = [];
        if (isset($data['data']) && is_array($data['data'])) {
            $makes_raw = $data['data'];
        } elseif (is_array($data) && isset($data[0]['slug'])) {
            $makes_raw = $data;
        }

        if (empty($makes_raw)) {
            return new \WP_Error('invalid_response', __('Unexpected API response format.', 'my-tyre-finder'));
        }

        $makes = array_map(
            static fn($make) => [
                'slug' => sanitize_key($make['slug'] ?? ''),
                'name' => sanitize_text_field($make['name'] ?? ''),
            ],
            $makes_raw
        );

        // Remove empty rows just in case
        $makes = array_values(array_filter($makes, static fn($m) => !empty($m['slug']) && !empty($m['name'])));

        set_transient(self::MAKES_TRANSIENT_KEY, $makes, DAY_IN_SECONDS);

        return $makes;
    }
} 