/**
 * Quick Hierarchy Check
 * 
 * Copy and paste this entire script into browser console on:
 * /wp-admin/admin.php?page=wheel-size-appearance
 */

console.log('📋 Quick Header Below Title Hierarchy Check...');

// Find elements
const title = document.querySelector('.wsf-widget__title, h1');
const header = document.querySelector('#wizard-header');
const form = document.querySelector('.wsf-form-wrapper');

if (!title || !header || !form) {
    console.error('❌ Required elements not found');
    console.log('Title:', !!title, 'Header:', !!header, 'Form:', !!form);
} else {
    console.log('✅ All elements found');
    
    console.log('\n📊 HIERARCHY CHECK:');
    
    // Check 1: Are both elements inside form?
    const titleInForm = form.contains(title);
    const headerInForm = form.contains(header);
    
    console.log(`1. Title inside form: ${titleInForm ? '✅' : '❌'}`);
    console.log(`2. Header inside form: ${headerInForm ? '✅' : '❌'}`);
    
    if (titleInForm && headerInForm) {
        // Check 3: DOM order
        const formChildren = Array.from(form.children);
        const titleIndex = formChildren.findIndex(child => child.contains(title));
        const headerIndex = formChildren.findIndex(child => child === header || child.contains(header));
        
        const correctOrder = titleIndex < headerIndex;
        
        console.log(`3. DOM order: ${correctOrder ? '✅' : '❌'}`);
        console.log(`   Title position: ${titleIndex + 1} of ${formChildren.length}`);
        console.log(`   Header position: ${headerIndex + 1} of ${formChildren.length}`);
        
        // Check 4: Visual positioning
        const titleRect = title.getBoundingClientRect();
        const headerRect = header.getBoundingClientRect();
        
        const headerBelowTitle = headerRect.top > titleRect.bottom;
        const gap = headerRect.top - titleRect.bottom;
        
        console.log(`4. Visual position: ${headerBelowTitle ? '✅' : '❌'}`);
        console.log(`   Gap between title and header: ${gap.toFixed(1)}px`);
        
        // Check 5: Spacing
        const reasonableSpacing = gap >= 15 && gap <= 60;
        console.log(`5. Spacing: ${reasonableSpacing ? '✅' : '⚠️'}`);
        
        if (!reasonableSpacing) {
            if (gap < 15) {
                console.log('   → Too close together');
            } else {
                console.log('   → Too far apart');
            }
        }
        
        // Check 6: Centering
        const titleCenter = titleRect.left + (titleRect.width / 2);
        const headerCenter = headerRect.left + (headerRect.width / 2);
        const centerDiff = Math.abs(titleCenter - headerCenter);
        const wellCentered = centerDiff < 10;
        
        console.log(`6. Horizontal alignment: ${wellCentered ? '✅' : '⚠️'}`);
        console.log(`   Center difference: ${centerDiff.toFixed(1)}px`);
        
        // Check 7: Header styling
        const headerStyle = window.getComputedStyle(header);
        const headerMaxWidth = parseFloat(headerStyle.maxWidth);
        const correctMaxWidth = Math.abs(headerMaxWidth - 896) < 10; // 56rem = 896px
        
        console.log(`7. Header max-width: ${correctMaxWidth ? '✅' : '❌'}`);
        console.log(`   Max-width: ${headerStyle.maxWidth}`);
        
        // Overall status
        const allGood = titleInForm && headerInForm && correctOrder && 
                       headerBelowTitle && reasonableSpacing && wellCentered && correctMaxWidth;
        
        console.log(`\n🎯 OVERALL STATUS: ${allGood ? '✅ PERFECT HIERARCHY' : '❌ NEEDS ATTENTION'}`);
        
        if (!allGood) {
            console.log('\n💡 ISSUES TO FIX:');
            if (!titleInForm) console.log('   - Move title inside .wsf-form-wrapper');
            if (!headerInForm) console.log('   - Move header inside .wsf-form-wrapper');
            if (!correctOrder) console.log('   - Ensure title comes before header in DOM');
            if (!headerBelowTitle) console.log('   - Fix visual positioning');
            if (!reasonableSpacing) console.log('   - Adjust spacing between title and header');
            if (!wellCentered) console.log('   - Fix horizontal alignment');
            if (!correctMaxWidth) console.log('   - Set header max-width to 56rem');
        }
        
        // Show expected hierarchy
        console.log('\n🏗️ EXPECTED HIERARCHY:');
        console.log('1. Widget Title ("Поиск дисков и шин") ← Should be first');
        console.log('2. Wizard Header (step indicators) ← Should be second');
        console.log('3. Form content ← Should be third');
        
        // Show actual hierarchy
        console.log('\n🔍 ACTUAL HIERARCHY:');
        formChildren.forEach((child, index) => {
            let description = 'Unknown element';
            
            if (child.contains(title)) {
                description = 'Widget Title ✅';
            } else if (child === header || child.contains(header)) {
                description = 'Wizard Header ✅';
            } else if (child.classList.contains('relative') && child.classList.contains('min-h-[300px]')) {
                description = 'Form content area';
            } else {
                const classes = Array.from(child.classList).slice(0, 2).join('.');
                description = `${child.tagName.toLowerCase()}.${classes}`;
            }
            
            console.log(`${index + 1}. ${description}`);
        });
        
    } else {
        console.log('\n❌ Elements are not properly placed inside form wrapper');
    }
    
    // Visual highlight
    console.log('\n🎨 Highlighting elements for 5 seconds...');
    
    // Title in green (should be first)
    title.style.outline = '3px solid green';
    title.style.backgroundColor = 'rgba(0, 255, 0, 0.1)';
    
    // Header in red (should be second)
    header.style.outline = '3px solid red';
    header.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
    
    // Form in blue (container)
    form.style.outline = '3px solid blue';
    
    setTimeout(() => {
        title.style.outline = '';
        title.style.backgroundColor = '';
        header.style.outline = '';
        header.style.backgroundColor = '';
        form.style.outline = '';
        console.log('🧹 Highlights removed');
    }, 5000);
}
