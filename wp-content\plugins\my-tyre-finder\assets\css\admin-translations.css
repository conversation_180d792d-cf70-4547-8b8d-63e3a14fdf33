/* file: assets/css/admin-translations.css */
#wheel-size-translations-page .translations-toolbar {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #fff;
    border: 1px solid #c3c4c7;
    flex-wrap: wrap;
}
#wheel-size-translations-page .locale-selector-form {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
#wheel-size-translations-page .search-box {
    margin-left: auto;
}
.table-container {
    max-height: 65vh;
    overflow-y: auto;
    border-bottom: 1px solid #c3c4c7;
}
.sticky-header th {
    position: sticky;
    top: 0;
    background: #f0f0f1;
    z-index: 10;
}
.key-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}
.key-cell .key-input {
    background-color: #f6f7f7;
    border: 1px solid #ddd;
    font-family: monospace;
}
.key-cell .dashicons-info-outline {
    color: #2271b1;
    cursor: help;
}
.actions-cell {
    width: 40px;
    text-align: center;
}
.delete-row {
    cursor: pointer;
    color: #b32d2e;
}
.delete-row:hover {
    color: #8b2121;
}
.value-input.is-changed {
    background-color: #fffbe5;
    border-color: #ffb900;
}
.key-input.is-duplicate,
.key-input.is-empty {
    border-color: #d63638;
    background-color: #fbeaea;
}
.form-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
}
.hidden-row {
    display: none;
}
.wp-list-table td, .wp-list-table th {padding:6px 8px;}
.key-input {width:140px;} 