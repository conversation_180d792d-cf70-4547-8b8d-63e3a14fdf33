<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Editor Labels & Cheat Sheet Test</title>
    <link rel="stylesheet" href="assets/css/admin-theme-panel.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f1f1f1;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        
        .demo-button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 10px 10px 0;
        }
        
        .demo-button:hover {
            background: #005a87;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .comparison-item {
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .comparison-item h4 {
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .old-style {
            background: #fff5f5;
            border-color: #fed7d7;
        }
        
        .new-style {
            background: #f0fff4;
            border-color: #c6f6d5;
        }
        
        .status {
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .mock-color-field {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            padding: 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        
        .mock-color-input {
            width: 40px;
            height: 30px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .mock-label-wrapper {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .mock-help-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
            color: #999;
            cursor: help;
        }
        
        .mock-help-icon:hover {
            color: #666;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }

        /* Additional styles for new features */
        .wsf-contrast-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            margin-left: 8px;
        }

        .wsf-contrast-indicator--good {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .wsf-contrast-indicator--poor {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .wsf-cheat-sheet__var-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .wsf-cheat-sheet__var-item:last-child {
            margin-bottom: 0;
        }

        .wsf-cheat-sheet__table-wrapper {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 0 0 6px 6px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Theme Editor Labels & Cheat Sheet Test</h1>
        <p>Test page for the enhanced theme editor modal with English labels and color cheat sheet</p>
        
        <!-- Demo Buttons -->
        <div class="test-section">
            <h3>Demo Controls</h3>
            <button class="demo-button" onclick="openMockEditor()">Open Mock Theme Editor</button>
            <button class="demo-button" onclick="testColorTokens()">Test Color Tokens Data</button>
            <button class="demo-button" onclick="testCheatSheet()">Test Cheat Sheet</button>
        </div>
        
        <!-- Before vs After Comparison -->
        <div class="test-section">
            <h3>Before vs After Comparison</h3>
            <div class="comparison-grid">
                <div class="comparison-item old-style">
                    <h4>❌ Before (Russian labels, no help)</h4>
                    <div class="mock-color-field">
                        <input type="color" class="mock-color-input" value="#2563eb">
                        <label>Основной цвет</label>
                    </div>
                    <div class="mock-color-field">
                        <input type="color" class="mock-color-input" value="#ffffff">
                        <label>Фон</label>
                    </div>
                    <div class="mock-color-field">
                        <input type="color" class="mock-color-input" value="#1f2937">
                        <label>Цвет текста</label>
                    </div>
                    <p><small>No help text, Russian labels, no reference guide</small></p>
                </div>
                
                <div class="comparison-item new-style">
                    <h4>✅ After (English labels + help + cheat sheet)</h4>
                    <div class="mock-color-field">
                        <input type="color" class="mock-color-input" value="#2563eb">
                        <div class="mock-label-wrapper">
                            <label>Primary</label>
                            <span class="mock-help-icon" title="Main action color for primary buttons & focus rings">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <div class="mock-color-field">
                        <input type="color" class="mock-color-input" value="#ffffff">
                        <div class="mock-label-wrapper">
                            <label>Background</label>
                            <span class="mock-help-icon" title="Global widget/page background">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <div class="mock-color-field">
                        <input type="color" class="mock-color-input" value="#1f2937">
                        <div class="mock-label-wrapper">
                            <label>Text Color</label>
                            <span class="mock-help-icon" title="Default body text color">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <p><small>English labels, help tooltips, integrated cheat sheet</small></p>
                </div>
            </div>
        </div>
        
        <!-- Features List -->
        <div class="test-section">
            <h3>New Features</h3>
            <ul class="feature-list">
                <li>English labels for all 11 color tokens in logical order</li>
                <li>Help tooltips with improved descriptions</li>
                <li>Collapsible color tokens reference table with localStorage</li>
                <li>Individual copy buttons for each CSS variable</li>
                <li>WCAG contrast ratio indicator for Primary/Background</li>
                <li>Fixed height table with internal scrolling</li>
                <li>Mobile-responsive design with adaptive columns</li>
                <li>Single source of truth for token metadata</li>
                <li>Preserved theme saving functionality</li>
            </ul>
        </div>
        
        <!-- Color Tokens Data -->
        <div class="test-section">
            <h3>Color Tokens Metadata</h3>
            <div class="code-block" id="tokens-data">
                Loading color tokens data...
            </div>
        </div>
        
        <!-- Mock Cheat Sheet -->
        <div class="test-section">
            <h3>Mock Cheat Sheet Preview</h3>
            <div class="wsf-cheat-sheet">
                <div class="wsf-cheat-sheet__header">
                    <h4 class="wsf-cheat-sheet__title">Color Tokens Reference</h4>
                    <button type="button" class="wsf-cheat-sheet__toggle" onclick="toggleMockCheatSheet()">
                        <span class="wsf-cheat-sheet__toggle-text">Show Details</span>
                        <svg class="wsf-cheat-sheet__toggle-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                </div>
                <div class="wsf-cheat-sheet__content" id="mock-cheat-content" style="display: none;">
                    <table class="wsf-cheat-sheet__table">
                        <thead>
                            <tr>
                                <th>Token</th>
                                <th>Used for</th>
                                <th>CSS Variables</th>
                                <th>Example Elements</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="wsf-cheat-sheet__row">
                                <td class="wsf-cheat-sheet__token">Background</td>
                                <td class="wsf-cheat-sheet__description">Global widget/page background</td>
                                <td class="wsf-cheat-sheet__vars">
                                    <div class="wsf-cheat-sheet__var-item">
                                        <code class="wsf-cheat-sheet__code">--wsf-bg</code>
                                        <button type="button" class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-bg')" title="Copy --wsf-bg">
                                            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                                <td class="wsf-cheat-sheet__example">Widget wrapper</td>
                            </tr>
                            <tr class="wsf-cheat-sheet__row">
                                <td class="wsf-cheat-sheet__token">Text</td>
                                <td class="wsf-cheat-sheet__description">Default body text</td>
                                <td class="wsf-cheat-sheet__vars">
                                    <div class="wsf-cheat-sheet__var-item">
                                        <code class="wsf-cheat-sheet__code">--wsf-text</code>
                                        <button type="button" class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-text')" title="Copy --wsf-text">
                                            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="wsf-cheat-sheet__var-item">
                                        <code class="wsf-cheat-sheet__code">--wsf-text-primary</code>
                                        <button type="button" class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-text-primary')" title="Copy --wsf-text-primary">
                                            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                                <td class="wsf-cheat-sheet__example">Body text, headings</td>
                            </tr>
                            <tr class="wsf-cheat-sheet__row">
                                <td class="wsf-cheat-sheet__token">Primary</td>
                                <td class="wsf-cheat-sheet__description">Main action color (buttons & focus)</td>
                                <td class="wsf-cheat-sheet__vars">
                                    <div class="wsf-cheat-sheet__var-item">
                                        <code class="wsf-cheat-sheet__code">--wsf-primary</code>
                                        <button type="button" class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-primary')" title="Copy --wsf-primary">
                                            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                                <td class="wsf-cheat-sheet__example">Primary buttons, focus rings</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="status success">
            ✅ Theme Editor enhancement completed! English labels, help tooltips, and color cheat sheet are ready.
        </div>
    </div>
    
    <!-- Mock Theme Editor Modal -->
    <div class="wsf-theme-editor__overlay" id="mock-overlay" style="display: none;">
        <div class="wsf-theme-editor" id="mock-editor">
            <div class="wsf-theme-editor__header">
                <h3 class="wsf-theme-editor__title">Edit Theme</h3>
                <button class="wsf-theme-editor__close" onclick="closeMockEditor()">×</button>
            </div>
            <div class="wsf-theme-editor__content">
                <div class="wsf-theme-editor__field">
                    <label class="wsf-theme-editor__label">Theme Name</label>
                    <input type="text" class="wsf-theme-editor__input" value="Test Theme" placeholder="Enter theme name">
                </div>
                <div class="wsf-theme-editor__field">
                    <label class="wsf-theme-editor__label">Colors</label>
                    <div class="wsf-theme-editor__color-grid" id="mock-color-grid">
                        <!-- Color fields will be populated by JavaScript -->
                    </div>
                </div>
                <div class="wsf-theme-editor__field">
                    <div class="wsf-cheat-sheet">
                        <div class="wsf-cheat-sheet__header">
                            <h4 class="wsf-cheat-sheet__title">Color Tokens Reference</h4>
                            <button type="button" class="wsf-cheat-sheet__toggle" onclick="toggleMockEditorCheatSheet()">
                                <span class="wsf-cheat-sheet__toggle-text">Show Details</span>
                                <svg class="wsf-cheat-sheet__toggle-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="6,9 12,15 18,9"></polyline>
                                </svg>
                            </button>
                        </div>
                        <div class="wsf-cheat-sheet__content" id="mock-editor-cheat-content" style="display: none;">
                            <table class="wsf-cheat-sheet__table">
                                <thead>
                                    <tr>
                                        <th>Token</th>
                                        <th>Used for</th>
                                        <th>CSS Variables</th>
                                        <th>Example Elements</th>
                                    </tr>
                                </thead>
                                <tbody id="mock-editor-cheat-body">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="wsf-theme-editor__footer">
                <button class="wsf-theme-editor__button wsf-theme-editor__button--cancel" onclick="closeMockEditor()">Cancel</button>
                <button class="wsf-theme-editor__button wsf-theme-editor__button--save">Save Theme</button>
            </div>
        </div>
    </div>
    
    <script>
        // Mock color tokens data (simulating PHP data) - Updated order and texts
        const mockColorTokens = {
            '--wsf-bg': {
                key: 'background',
                label: 'Background',
                help: 'Global widget/page background',
                vars: ['--wsf-bg'],
                property: '--wsf-bg',
                order: 1
            },
            '--wsf-text': {
                key: 'text',
                label: 'Text',
                help: 'Default body text',
                vars: ['--wsf-text', '--wsf-text-primary'],
                property: '--wsf-text',
                order: 2
            },
            '--wsf-border': {
                key: 'border',
                label: 'Border',
                help: 'Default borders for inputs & cards',
                vars: ['--wsf-border'],
                property: '--wsf-border',
                order: 3
            },
            '--wsf-primary': {
                key: 'primary',
                label: 'Primary',
                help: 'Main action color (buttons & focus)',
                vars: ['--wsf-primary'],
                property: '--wsf-primary',
                order: 4
            },
            '--wsf-hover': {
                key: 'hover',
                label: 'Hover State',
                help: 'Hover color for primary elements',
                vars: ['--wsf-hover'],
                property: '--wsf-hover',
                order: 5
            },
            '--wsf-accent': {
                key: 'accent',
                label: 'Accent',
                help: 'Links, icons, accent highlights',
                vars: ['--wsf-accent'],
                property: '--wsf-accent',
                order: 6
            },
            '--wsf-secondary': {
                key: 'secondary',
                label: 'Secondary',
                help: 'Secondary text & UI elements',
                vars: ['--wsf-text-secondary', '--wsf-secondary'],
                property: '--wsf-secondary',
                order: 7
            },
            '--wsf-muted': {
                key: 'muted',
                label: 'Muted',
                help: 'Placeholders & subtle/disabled UI',
                vars: ['--wsf-muted', '--wsf-text-muted'],
                property: '--wsf-muted',
                order: 8
            },
            '--wsf-success': {
                key: 'success',
                label: 'Success',
                help: 'Positive/confirmed states',
                vars: ['--wsf-success'],
                property: '--wsf-success',
                order: 9
            },
            '--wsf-warning': {
                key: 'warning',
                label: 'Warning',
                help: 'Attention-needed states',
                vars: ['--wsf-warning'],
                property: '--wsf-warning',
                order: 10
            },
            '--wsf-error': {
                key: 'error',
                label: 'Error',
                help: 'Errors & invalid input states',
                vars: ['--wsf-error'],
                property: '--wsf-error',
                order: 11
            }
        };

        const mockExamples = {
            background: 'Widget wrapper',
            text: 'Body text, headings',
            border: 'Input borders, cards',
            primary: 'Primary buttons, focus rings',
            hover: 'Button hovers, active links',
            accent: 'Links, small badges',
            secondary: 'Subtitles, labels',
            muted: 'Placeholders, disabled fields',
            success: 'Success messages',
            warning: 'Warnings, notices',
            error: 'Validation errors'
        };

        function openMockEditor() {
            document.getElementById('mock-overlay').style.display = 'flex';
            populateMockColorFields();
            populateMockCheatSheet();
        }

        function closeMockEditor() {
            document.getElementById('mock-overlay').style.display = 'none';
        }

        // Color contrast calculation functions
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        function getLuminance(r, g, b) {
            const [rs, gs, bs] = [r, g, b].map(c => {
                c = c / 255;
                return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
            });
            return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
        }

        function getContrastRatio(color1, color2) {
            const rgb1 = hexToRgb(color1);
            const rgb2 = hexToRgb(color2);

            if (!rgb1 || !rgb2) return 1;

            const lum1 = getLuminance(rgb1.r, rgb1.g, rgb1.b);
            const lum2 = getLuminance(rgb2.r, rgb2.g, rgb2.b);

            const brightest = Math.max(lum1, lum2);
            const darkest = Math.min(lum1, lum2);

            return (brightest + 0.05) / (darkest + 0.05);
        }

        function createContrastIndicator(primaryColor, backgroundColor) {
            const ratio = getContrastRatio(primaryColor, backgroundColor);
            const isGood = ratio >= 4.5;
            const level = ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : 'Fail';

            return `
                <div class="wsf-contrast-indicator ${isGood ? 'wsf-contrast-indicator--good' : 'wsf-contrast-indicator--poor'}"
                     title="Contrast ratio: ${ratio.toFixed(2)}:1 (WCAG ${level})">
                    <span class="wsf-contrast-indicator__icon">${isGood ? '✓' : '⚠'}</span>
                    <span class="wsf-contrast-indicator__text">${level}</span>
                </div>
            `;
        }

        function populateMockColorFields() {
            const grid = document.getElementById('mock-color-grid');
            const defaultColors = {
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#1f2937',
                '--wsf-border': '#e5e7eb',
                '--wsf-primary': '#2563eb',
                '--wsf-hover': '#1d4ed8',
                '--wsf-accent': '#7c3aed',
                '--wsf-secondary': '#6b7280',
                '--wsf-muted': '#9ca3af',
                '--wsf-success': '#10b981',
                '--wsf-warning': '#f59e0b',
                '--wsf-error': '#ef4444'
            };

            // Sort tokens by order
            const sortedTokens = Object.entries(mockColorTokens).sort((a, b) =>
                (a[1].order || 999) - (b[1].order || 999)
            );

            grid.innerHTML = sortedTokens.map(([property, tokenData]) => {
                const currentValue = defaultColors[property] || '#000000';

                // Add contrast indicator for Primary color
                let contrastIndicator = '';
                if (property === '--wsf-primary' && defaultColors['--wsf-bg']) {
                    contrastIndicator = createContrastIndicator(currentValue, defaultColors['--wsf-bg']);
                }

                return `
                    <div class="wsf-theme-editor__color-field">
                        <input type="color"
                               class="wsf-theme-editor__color-input"
                               data-property="${property}"
                               value="${currentValue}"
                               onchange="updateContrastIndicator('${property}', this.value)">
                        <div class="wsf-theme-editor__color-label-wrapper">
                            <label class="wsf-theme-editor__color-label">${tokenData.label}</label>
                            <span class="wsf-theme-editor__help-icon" title="${tokenData.help}">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                </svg>
                            </span>
                            ${contrastIndicator}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function populateMockCheatSheet() {
            const tbody = document.getElementById('mock-editor-cheat-body');

            // Sort tokens by order
            const sortedTokens = Object.entries(mockColorTokens).sort((a, b) =>
                (a[1].order || 999) - (b[1].order || 999)
            );

            tbody.innerHTML = sortedTokens.map(([property, tokenData]) => {
                const vars = tokenData.vars || [property];
                const example = mockExamples[tokenData.key] || 'Various elements';

                // Create individual copy buttons for each variable
                const varButtons = vars.map(varName => `
                    <div class="wsf-cheat-sheet__var-item">
                        <code class="wsf-cheat-sheet__code">${varName}</code>
                        <button type="button" class="wsf-cheat-sheet__copy" onclick="copyToClipboard('${varName}')" title="Copy ${varName}">
                            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                        </button>
                    </div>
                `).join('');

                return `
                    <tr class="wsf-cheat-sheet__row">
                        <td class="wsf-cheat-sheet__token">${tokenData.label}</td>
                        <td class="wsf-cheat-sheet__description">${tokenData.help}</td>
                        <td class="wsf-cheat-sheet__vars">
                            ${varButtons}
                        </td>
                        <td class="wsf-cheat-sheet__example">${example}</td>
                    </tr>
                `;
            }).join('');
        }

        function testColorTokens() {
            const tokensData = document.getElementById('tokens-data');
            tokensData.innerHTML = JSON.stringify(mockColorTokens, null, 2);
        }

        function testCheatSheet() {
            const content = document.getElementById('mock-cheat-content');
            if (content.style.display === 'none') {
                content.style.display = 'block';
                document.querySelector('.wsf-cheat-sheet__toggle-text').textContent = 'Hide Details';
                document.querySelector('.wsf-cheat-sheet__toggle-icon').style.transform = 'rotate(180deg)';
            }
        }

        function toggleMockCheatSheet() {
            const content = document.getElementById('mock-cheat-content');
            const toggleText = document.querySelector('.wsf-cheat-sheet__toggle-text');
            const toggleIcon = document.querySelector('.wsf-cheat-sheet__toggle-icon');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggleText.textContent = 'Hide Details';
                toggleIcon.style.transform = 'rotate(180deg)';
                setCheatSheetState(true);
            } else {
                content.style.display = 'none';
                toggleText.textContent = 'Show Details';
                toggleIcon.style.transform = 'rotate(0deg)';
                setCheatSheetState(false);
            }
        }

        function toggleMockEditorCheatSheet() {
            const content = document.getElementById('mock-editor-cheat-content');
            const toggleText = document.querySelector('#mock-editor .wsf-cheat-sheet__toggle-text');
            const toggleIcon = document.querySelector('#mock-editor .wsf-cheat-sheet__toggle-icon');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggleText.textContent = 'Hide Details';
                toggleIcon.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                toggleText.textContent = 'Show Details';
                toggleIcon.style.transform = 'rotate(0deg)';
            }
        }

        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopyFeedback(event.target);
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showCopyFeedback(event.target);
            }
        }

        function updateContrastIndicator(changedProperty, newValue) {
            const primaryInput = document.querySelector('[data-property="--wsf-primary"]');
            const backgroundInput = document.querySelector('[data-property="--wsf-bg"]');

            if (!primaryInput || !backgroundInput) return;

            const primaryColor = changedProperty === '--wsf-primary' ? newValue : primaryInput.value;
            const backgroundColor = changedProperty === '--wsf-bg' ? newValue : backgroundInput.value;

            // Find and update the contrast indicator
            const primaryField = primaryInput.closest('.wsf-theme-editor__color-field');
            const existingIndicator = primaryField.querySelector('.wsf-contrast-indicator');

            if (existingIndicator) {
                const newIndicator = createContrastIndicator(primaryColor, backgroundColor);
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newIndicator;
                existingIndicator.replaceWith(tempDiv.firstElementChild);
            }
        }

        function showCopyFeedback(button) {
            const originalTitle = button.title;
            button.title = 'Copied!';
            button.classList.add('wsf-cheat-sheet__copy--copied');

            setTimeout(() => {
                button.title = originalTitle;
                button.classList.remove('wsf-cheat-sheet__copy--copied');
            }, 1500);
        }

        function getCheatSheetState() {
            try {
                return localStorage.getItem('wsf-cheat-sheet-expanded') === 'true';
            } catch (e) {
                return false; // Default to collapsed
            }
        }

        function setCheatSheetState(expanded) {
            try {
                localStorage.setItem('wsf-cheat-sheet-expanded', expanded.toString());
            } catch (e) {
                // Ignore localStorage errors
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            testColorTokens();

            // Restore cheat sheet state
            const isExpanded = getCheatSheetState();
            if (isExpanded) {
                const content = document.getElementById('mock-cheat-content');
                const toggleText = document.querySelector('.wsf-cheat-sheet__toggle-text');
                const toggleIcon = document.querySelector('.wsf-cheat-sheet__toggle-icon');

                if (content && toggleText && toggleIcon) {
                    content.style.display = 'block';
                    toggleText.textContent = 'Hide Details';
                    toggleIcon.style.transform = 'rotate(180deg)';
                }
            }
        });
    </script>
</body>
</html>
