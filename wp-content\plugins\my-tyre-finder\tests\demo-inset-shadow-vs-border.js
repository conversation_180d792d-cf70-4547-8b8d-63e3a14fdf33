/**
 * Демонстрация преимуществ inset box-shadow над обычным border
 * Запустите в консоли браузера для визуального сравнения
 */

(function() {
    'use strict';

    console.log('🔬 === ДЕМОНСТРАЦИЯ: INSET SHADOW vs BORDER ===');

    // Создаем демо-контейнер
    function createDemo() {
        console.log('\n🎨 Создание демонстрации...');
        
        const demoContainer = document.createElement('div');
        demoContainer.id = 'border-vs-shadow-demo';
        demoContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 3px solid #007bff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 600px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        const testColors = [
            { name: 'Чисто белый', color: '#ffffff' },
            { name: 'Очень светлый', color: '#f8f9fa' },
            { name: 'Светло-серый', color: '#e9ecef' },
            { name: 'Средний серый', color: '#adb5bd' },
            { name: 'Темный', color: '#343a40' },
            { name: 'Синий', color: '#007bff' }
        ];
        
        demoContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 style="margin: 0; font-size: 18px; color: #333;">
                    🔬 Border vs Inset Shadow
                </h2>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="border: none; background: #dc3545; color: white; 
                               border-radius: 6px; padding: 6px 12px; cursor: pointer; font-size: 14px;">
                    Закрыть ✕
                </button>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 16px;">
                <div>
                    <h3 style="margin: 0 0 12px 0; font-size: 14px; color: #666;">
                        ❌ Обычный Border
                    </h3>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                        ${testColors.map(({name, color}) => `
                            <div style="text-align: center;">
                                <div style="
                                    width: 40px; 
                                    height: 40px; 
                                    background-color: ${color}; 
                                    border: 1px solid rgba(0,0,0,0.12);
                                    border-radius: 6px;
                                    margin: 0 auto 4px;
                                " title="${name}: ${color}"></div>
                                <div style="font-size: 10px; color: #666; max-width: 40px; word-wrap: break-word;">
                                    ${name}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div>
                    <h3 style="margin: 0 0 12px 0; font-size: 14px; color: #666;">
                        ✅ Inset Box-Shadow
                    </h3>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                        ${testColors.map(({name, color}) => `
                            <div style="text-align: center;">
                                <div style="
                                    width: 40px; 
                                    height: 40px; 
                                    background-color: ${color}; 
                                    border: none;
                                    border-radius: 6px;
                                    position: relative;
                                    margin: 0 auto 4px;
                                " title="${name}: ${color}">
                                    <div style="
                                        position: absolute;
                                        inset: 0;
                                        border-radius: inherit;
                                        pointer-events: none;
                                        box-shadow: 0 0 0 1px rgba(0,0,0,0.12) inset;
                                    "></div>
                                </div>
                                <div style="font-size: 10px; color: #666; max-width: 40px; word-wrap: break-word;">
                                    ${name}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
            
            <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; font-size: 13px; line-height: 1.5;">
                <strong>🔍 Обратите внимание:</strong><br>
                • <strong>Border</strong> рисуется ПОД заливкой — на белом фоне почти не виден<br>
                • <strong>Inset shadow</strong> рисуется ПОВЕРХ заливки — всегда виден<br>
                • Оба подхода имеют одинаковую толщину и цвет рамки<br>
                • Inset shadow не влияет на размеры элемента
            </div>
            
            <div style="margin-top: 16px; text-align: center;">
                <button onclick="toggleAnimation()" 
                        style="border: 1px solid #007bff; background: white; color: #007bff; 
                               border-radius: 6px; padding: 8px 16px; cursor: pointer; font-size: 13px;">
                    🎬 Анимация сравнения
                </button>
            </div>
        `;
        
        document.body.appendChild(demoContainer);
        
        // Добавляем функцию анимации
        window.toggleAnimation = function() {
            const swatches = demoContainer.querySelectorAll('[style*="background-color"]');
            let isAnimating = false;
            
            function animate() {
                if (isAnimating) return;
                isAnimating = true;
                
                const colors = ['#ffffff', '#f8f9fa', '#e9ecef', '#adb5bd', '#343a40', '#007bff'];
                let colorIndex = 0;
                
                const interval = setInterval(() => {
                    swatches.forEach(swatch => {
                        swatch.style.backgroundColor = colors[colorIndex];
                    });
                    colorIndex = (colorIndex + 1) % colors.length;
                }, 800);
                
                setTimeout(() => {
                    clearInterval(interval);
                    isAnimating = false;
                    // Возвращаем исходные цвета
                    const originalColors = ['#ffffff', '#f8f9fa', '#e9ecef', '#adb5bd', '#343a40', '#007bff'];
                    swatches.forEach((swatch, index) => {
                        if (index < 6) {
                            swatch.style.backgroundColor = originalColors[index];
                        } else {
                            swatch.style.backgroundColor = originalColors[index - 6];
                        }
                    });
                }, 5000);
            }
            
            animate();
        };
        
        console.log('✅ Демонстрация создана в центре экрана');
        console.log('💡 Сравните видимость рамок на белом и светлых цветах');
    }

    // Проверяем текущие стили свотчей
    function checkCurrentImplementation() {
        console.log('\n🔍 Проверка текущей реализации...');
        
        const swatches = document.querySelectorAll('.wsf-theme-card__swatch');
        if (swatches.length === 0) {
            console.warn('⚠️ Свотчи не найдены на странице');
            return;
        }
        
        const firstSwatch = swatches[0];
        const styles = window.getComputedStyle(firstSwatch);
        const beforeStyles = window.getComputedStyle(firstSwatch, '::before');
        
        console.log('Текущие стили свотча:', {
            border: styles.border,
            position: styles.position,
            beforeContent: beforeStyles.content,
            beforeBoxShadow: beforeStyles.boxShadow
        });
        
        if (beforeStyles.content && beforeStyles.content !== 'none' && 
            beforeStyles.boxShadow && beforeStyles.boxShadow !== 'none') {
            console.log('✅ Inset shadow реализован через ::before');
        } else if (styles.border && styles.border !== 'none') {
            console.log('⚠️ Используется обычный border');
        } else {
            console.log('❌ Рамка не найдена');
        }
    }

    // Запускаем демонстрацию
    console.log('🚀 Запуск демонстрации...\n');
    checkCurrentImplementation();
    createDemo();
    
    console.log('\n💡 Советы:');
    console.log('1. Обратите внимание на белый и светлые цвета');
    console.log('2. Border почти не виден на светлом фоне');
    console.log('3. Inset shadow всегда четко виден');
    console.log('4. Попробуйте анимацию для лучшего сравнения');

})();
