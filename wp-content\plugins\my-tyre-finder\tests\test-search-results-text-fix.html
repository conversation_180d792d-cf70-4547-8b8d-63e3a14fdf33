<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results Text Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .problem-demo {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .solution-demo {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .demo-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .color-controls {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .color-field {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .color-input {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .color-label {
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        .demo-label {
            color: var(--wsf-text, #1f2937);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        
        .demo-results-header {
            color: var(--wsf-text, #1f2937);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .demo-vehicle-label-old {
            color: var(--wsf-text, #1f2937);
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        .demo-vehicle-label-new {
            color: #111827; /* text-gray-900 - фиксированный цвет */
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .highlight-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Search Results Text Fix</h1>
        <p>Исправление проблемы, когда настройка "Text" в Theme Presets влияла на текст результатов поиска.</p>
        
        <!-- Problem Description -->
        <div class="test-section">
            <h3>❌ Проблема</h3>
            <div class="problem-demo">
                <h4>ДО исправления:</h4>
                <p>При изменении цвета <strong>"Text"</strong> в Theme Presets менялся цвет текста результатов поиска:</p>
                <div class="highlight-box">
                    <strong>Пример:</strong> "Audi A3-allstreet (2026) — 1.5 TFSi I4 PETROL 148 HP"
                </div>
                
                <p><strong>Причина:</strong> Элемент <code>vehicle-label</code> использовал класс <code>text-wsf-text</code>, который зависит от CSS переменной <code>--wsf-text</code>.</p>
                
                <div class="code-block">
&lt;!-- ДО (проблемный код): --&gt;
&lt;p id="vehicle-label" class="text-base font-medium text-wsf-text"&gt;&lt;/p&gt;

/* CSS: */
.text-wsf-text { color: var(--wsf-text); }
                </div>
            </div>
        </div>
        
        <!-- Solution Description -->
        <div class="test-section">
            <h3>✅ Решение</h3>
            <div class="solution-demo">
                <h4>ПОСЛЕ исправления:</h4>
                <p>Текст результатов поиска теперь использует фиксированный цвет и не зависит от настройки "Text".</p>
                
                <div class="code-block">
&lt;!-- ПОСЛЕ (исправленный код): --&gt;
&lt;p id="vehicle-label" class="text-base font-medium text-gray-900"&gt;&lt;/p&gt;

/* Фиксированный Tailwind класс, не зависит от CSS переменных */
                </div>
                
                <h4>Исправленные файлы:</h4>
                <ul>
                    <li><code>templates/finder-form.twig</code></li>
                    <li><code>templates/finder-form-flow.twig</code></li>
                    <li><code>templates/finder-form-inline.twig</code></li>
                    <li><code>templates/finder-popup-horizontal.twig</code></li>
                    <li><code>templates/finder-wizard.twig</code> (2 места)</li>
                </ul>
            </div>
        </div>
        
        <!-- Live Demo -->
        <div class="test-section">
            <h3>🎨 Демонстрация исправления</h3>
            <p>Измените цвет "Text" ниже и убедитесь, что текст результатов НЕ меняется:</p>
            
            <div class="color-controls">
                <div class="color-field">
                    <input type="color" class="color-input" value="#1f2937" data-token="--wsf-text">
                    <label class="color-label">Text (для лейблов и заголовков)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#ffffff" data-token="--wsf-bg">
                    <label class="color-label">Background (фон виджета)</label>
                </div>
            </div>
            
            <div class="demo-widget" id="demo-widget">
                <div class="demo-results-header">Search Results</div>
                
                <div style="border-bottom: 1px solid #e5e7eb; margin: 15px 0;"></div>
                
                <div class="demo-vehicle-label-new">
                    Audi A3-allstreet (2026) — 1.5 TFSi I4 PETROL 148 HP
                </div>
                
                <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin: 15px 0;">
                    <h4 style="margin-top: 0; color: var(--wsf-text);">Factory Sizes</h4>
                    <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px;">
                        <div style="font-weight: 600; margin-bottom: 5px;">225/45 R17</div>
                        <div style="font-size: 12px; color: #6b7280;">Load Index: 94, Speed Rating: W</div>
                    </div>
                </div>
                
                <p style="font-size: 12px; color: #6b7280; margin-top: 15px;">
                    ↑ Текст результатов "Audi A3-allstreet..." теперь НЕ меняется при изменении "Text"
                </p>
            </div>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>📊 Сравнение До/После</h3>
            <div class="comparison-grid">
                <div class="problem-demo">
                    <h4>❌ ДО</h4>
                    <p><strong>Проблема:</strong></p>
                    <ul>
                        <li>Text влияет на результаты поиска</li>
                        <li>Нет контроля над цветом результатов</li>
                        <li>Неожиданное поведение для пользователей</li>
                    </ul>
                    
                    <div style="margin-top: 15px;">
                        <div class="demo-vehicle-label-old">Audi A3-allstreet (2026) — 1.5 TFSi I4 PETROL 148 HP</div>
                        <p style="font-size: 11px; margin-top: 5px;">Меняется при изменении Text</p>
                    </div>
                </div>
                
                <div class="solution-demo">
                    <h4>✅ ПОСЛЕ</h4>
                    <p><strong>Решение:</strong></p>
                    <ul>
                        <li>Text влияет только на лейблы и заголовки</li>
                        <li>Результаты поиска имеют стабильный цвет</li>
                        <li>Предсказуемое поведение</li>
                    </ul>
                    
                    <div style="margin-top: 15px;">
                        <div class="demo-vehicle-label-new">Audi A3-allstreet (2026) — 1.5 TFSi I4 PETROL 148 HP</div>
                        <p style="font-size: 11px; margin-top: 5px;">Всегда стабильный цвет</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Технические детали</h3>
            
            <h4>Что изменилось:</h4>
            <div class="code-block">
/* ДО */
&lt;p id="vehicle-label" class="text-wsf-text"&gt;  /* Зависел от --wsf-text */

/* ПОСЛЕ */
&lt;p id="vehicle-label" class="text-gray-900"&gt; /* Фиксированный цвет */
            </div>
            
            <h4>Разделение ответственности:</h4>
            <ul>
                <li><strong>--wsf-text</strong> - только для лейблов полей и заголовков секций</li>
                <li><strong>text-gray-900</strong> - для текста результатов поиска (фиксированный)</li>
                <li><strong>--wsf-accent</strong> - для ссылок и вторичных элементов</li>
                <li><strong>--wsf-primary</strong> - для кнопок и основных действий</li>
            </ul>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить исправление</h4>
            <ol>
                <li><strong>Очистите кэш</strong> WordPress и браузера</li>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>Создайте новую тему или отредактируйте существующую</li>
                <li>Измените цвет <strong>"Text"</strong> на яркий (например, красный)</li>
                <li>Сохраните и примените тему</li>
                <li>Перейдите на страницу с виджетом и выполните поиск</li>
                <li>Убедитесь, что:
                    <ul>
                        <li>Лейблы полей ("Make", "Model", "Year") изменили цвет</li>
                        <li>Заголовки секций ("Search Results", "Factory Sizes") изменили цвет</li>
                        <li>Текст результатов поиска <strong>НЕ изменил цвет</strong></li>
                    </ul>
                </li>
            </ol>
            
            <h4>🎯 Ожидаемый результат</h4>
            <ul>
                <li>✅ Text влияет только на лейблы и заголовки</li>
                <li>✅ Результаты поиска всегда читаемы</li>
                <li>✅ Предсказуемое поведение Theme Presets</li>
                <li>✅ Четкое разделение элементов интерфейса</li>
            </ul>
        </div>
    </div>

    <script>
        // Color picker functionality
        document.querySelectorAll('.color-input').forEach(input => {
            input.addEventListener('change', function() {
                const token = this.dataset.token;
                const value = this.value;
                const demoWidget = document.getElementById('demo-widget');
                
                demoWidget.style.setProperty(token, value);
                console.log(`Updated ${token} to ${value}`);
            });
        });
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            const demoWidget = document.getElementById('demo-widget');
            
            // Set default values
            demoWidget.style.setProperty('--wsf-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-border', '#e5e7eb');
            
            console.log('Search results text fix demo initialized');
        });
    </script>
</body>
</html>
