/**
 * Test script to verify Garage button positioning in Inline (1x4) layout
 * after moving it below the search button within the same form block
 */

console.log('🧪 Testing Inline (1x4) Garage button positioning...');

function testInlineGaragePositioning() {
    console.log('\n📍 Testing Garage button position in Inline layout...');
    
    // Check for inline form (popup-horizontal layout)
    const inlineForm = document.getElementById('tab-by-car');
    if (!inlineForm || !inlineForm.classList.contains('grid')) {
        console.log('❌ Inline form not found or not in grid layout');
        return false;
    }
    
    console.log('✅ Found inline form with grid layout');
    
    // Test for non-auto-search mode
    const submitContainer = inlineForm.querySelector('.ws-submit');
    if (submitContainer) {
        console.log('✅ Found search button container (.ws-submit)');
        
        // Check if Garage button is inside the submit container
        const garageInSubmit = submitContainer.querySelector('[data-garage-trigger]');
        if (garageInSubmit) {
            console.log('✅ Garage button found inside search button container');
            
            // Check if it's positioned below the search button
            const searchButton = submitContainer.querySelector('button[type="submit"]');
            const garageContainer = garageInSubmit.parentElement;
            
            if (searchButton && garageContainer) {
                const searchRect = searchButton.getBoundingClientRect();
                const garageRect = garageContainer.getBoundingClientRect();
                
                const isBelow = garageRect.top > searchRect.bottom;
                console.log(`${isBelow ? '✅' : '❌'} Garage button positioned below search button`);
                console.log(`   Search button bottom: ${searchRect.bottom}px`);
                console.log(`   Garage container top: ${garageRect.top}px`);
                
                // Check spacing (should be mt-2 = 8px)
                const spacing = garageRect.top - searchRect.bottom;
                const expectedSpacing = 8; // mt-2 in Tailwind
                const spacingOk = Math.abs(spacing - expectedSpacing) <= 4; // Allow 4px tolerance
                console.log(`${spacingOk ? '✅' : '❌'} Spacing between buttons: ${spacing}px (expected ~${expectedSpacing}px)`);
                
                return isBelow && spacingOk;
            }
        } else {
            console.log('❌ Garage button not found inside search button container');
        }
    }
    
    // Test for auto-search mode (if applicable)
    const autoSearchGarage = inlineForm.querySelector('[data-garage-trigger]');
    if (autoSearchGarage && !submitContainer) {
        console.log('✅ Found Garage button in auto-search mode');
        
        // Check if it spans the correct columns
        const garageContainer = autoSearchGarage.parentElement;
        const hasCorrectSpan = garageContainer.classList.contains('md:col-span-4');
        console.log(`${hasCorrectSpan ? '✅' : '❌'} Garage container has correct column span for auto-search`);
        
        return hasCorrectSpan;
    }
    
    return false;
}

function testGarageAlignment() {
    console.log('\n📐 Testing Garage button alignment...');
    
    const garageButtons = document.querySelectorAll('[data-garage-trigger]');
    let allAligned = true;
    
    garageButtons.forEach((button, index) => {
        const container = button.parentElement;
        const hasRightAlign = container.classList.contains('justify-end');
        console.log(`${hasRightAlign ? '✅' : '❌'} Garage button ${index + 1} right-aligned`);
        if (!hasRightAlign) allAligned = false;
    });
    
    return allAligned;
}

function testFormIntegrity() {
    console.log('\n📝 Testing form integrity...');
    
    const inlineForm = document.getElementById('tab-by-car');
    if (!inlineForm) {
        console.log('❌ Inline form not found');
        return false;
    }
    
    // Check if Garage button is inside the form
    const garageInForm = inlineForm.querySelector('[data-garage-trigger]');
    const isInForm = !!garageInForm;
    console.log(`${isInForm ? '✅' : '❌'} Garage button is inside the form`);
    
    // Check button type
    if (garageInForm) {
        const buttonType = garageInForm.getAttribute('type');
        const correctType = buttonType === 'button';
        console.log(`${correctType ? '✅' : '❌'} Garage button type is "button" (prevents form submission)`);
        return isInForm && correctType;
    }
    
    return isInForm;
}

function runInlinePositioningTests() {
    console.log('🚀 Starting Inline (1x4) Garage positioning tests...\n');
    
    const results = {
        positioning: testInlineGaragePositioning(),
        alignment: testGarageAlignment(),
        formIntegrity: testFormIntegrity()
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Garage button positioning is correct.');
    } else {
        console.log('⚠️ Some tests failed. Check the details above.');
    }
    
    return results;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runInlinePositioningTests);
} else {
    runInlinePositioningTests();
}

// Export for manual testing
window.testInlineGaragePositioning = runInlinePositioningTests;
