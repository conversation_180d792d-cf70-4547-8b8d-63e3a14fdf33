// Debug script for search functionality issue
console.log('=== Debugging Search "Sizes not found" Issue ===');

// Test 1: Check widget state and selected data
console.log('1. Widget state check:');
if (window.wheelFitWidget) {
    console.log('   ✅ Widget exists');
    console.log('   Selected data:', window.wheelFitWidget.selectedData);
    console.log('   Mode:', window.wheelFitWidget.mode);
    console.log('   Flow order:', window.wheelFitWidget.flowOrder);
} else {
    console.log('   ❌ Widget not found');
}

// Test 2: Check AJAX configuration
console.log('2. AJAX configuration check:');
if (window.WheelFitData) {
    console.log('   ✅ WheelFitData available');
    console.log('   AJAX URL:', window.WheelFitData.ajaxurl);
    console.log('   Nonce:', window.WheelFitData.nonce ? 'Present' : 'Missing');
} else {
    console.log('   ❌ WheelFitData not available');
}

// Test 3: Intercept AJAX requests to see what's being sent
console.log('3. Setting up AJAX request interception...');

// Store original fetch
const originalFetch = window.fetch;

// Override fetch to intercept requests
window.fetch = async function(url, options) {
    // Check if this is our search request
    if (url === window.WheelFitData?.ajaxurl && options?.method === 'POST') {
        const formData = options.body;
        
        // Extract form data
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // Check if this is a search sizes request
        if (data.action === 'wf_search_sizes') {
            console.log('🔍 INTERCEPTED SEARCH REQUEST:');
            console.log('   Action:', data.action);
            console.log('   Make:', data.make);
            console.log('   Model:', data.model);
            console.log('   Year:', data.year);
            console.log('   Generation:', data.generation);
            console.log('   Modification:', data.modification);
            console.log('   Nonce:', data.nonce ? 'Present' : 'Missing');
            console.log('   Full data:', data);
        }
    }
    
    // Call original fetch and intercept response
    const response = await originalFetch.apply(this, arguments);
    
    // If this is our search request, log the response
    if (url === window.WheelFitData?.ajaxurl && options?.method === 'POST') {
        const formData = options.body;
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        if (data.action === 'wf_search_sizes') {
            // Clone response to read it without consuming the original
            const responseClone = response.clone();
            try {
                const responseData = await responseClone.json();
                console.log('📥 INTERCEPTED SEARCH RESPONSE:');
                console.log('   Status:', response.status);
                console.log('   Success:', responseData.success);
                console.log('   Data:', responseData.data);
                
                if (responseData.success && responseData.data) {
                    const factoryCount = responseData.data.factory_sizes?.length || 0;
                    const optionalCount = responseData.data.optional_sizes?.length || 0;
                    console.log('   Factory sizes count:', factoryCount);
                    console.log('   Optional sizes count:', optionalCount);
                    console.log('   Total count:', responseData.data.total_count);
                    
                    if (factoryCount === 0 && optionalCount === 0) {
                        console.log('   ❌ PROBLEM: API returned empty results');
                        console.log('   This explains why "Sizes not found" is shown');
                    } else {
                        console.log('   ✅ API returned results, issue might be in displayResults()');
                    }
                } else {
                    console.log('   ❌ PROBLEM: API request failed');
                    console.log('   Error:', responseData.data || 'Unknown error');
                }
            } catch (error) {
                console.log('   ❌ Error parsing response:', error);
            }
        }
    }
    
    return response;
};

// Test 4: Test manual search if widget is available
console.log('4. Manual search test:');
if (window.wheelFitWidget && window.wheelFitWidget.selectedData) {
    const selectedData = window.wheelFitWidget.selectedData;
    
    // Check if we have enough data for a search
    const hasRequiredData = selectedData.make && selectedData.model && 
                           (selectedData.year || selectedData.generation) && 
                           selectedData.modification;
    
    console.log('   Has required data:', hasRequiredData);
    
    if (hasRequiredData) {
        console.log('   Triggering manual search...');
        
        // Trigger search and monitor results
        window.wheelFitWidget.searchSizes()
            .then(() => {
                console.log('   ✅ Search completed');
                
                // Check if results are displayed
                setTimeout(() => {
                    const resultsContainer = document.getElementById('search-results');
                    const noResultsElement = document.getElementById('no-results');
                    
                    console.log('   Results container visible:', resultsContainer && !resultsContainer.classList.contains('hidden'));
                    console.log('   No-results element visible:', noResultsElement && !noResultsElement.classList.contains('hidden'));
                    
                    // Check for actual size cards
                    const factoryGrid = document.getElementById('factory-grid');
                    const optionalGrid = document.getElementById('optional-grid');
                    
                    const factoryCards = factoryGrid ? factoryGrid.children.length : 0;
                    const optionalCards = optionalGrid ? optionalGrid.children.length : 0;
                    
                    console.log('   Factory size cards:', factoryCards);
                    console.log('   Optional size cards:', optionalCards);
                    
                    if (factoryCards === 0 && optionalCards === 0) {
                        console.log('   ❌ CONFIRMED: No size cards generated');
                    } else {
                        console.log('   ✅ Size cards found - issue might be elsewhere');
                    }
                }, 1000);
            })
            .catch(error => {
                console.log('   ❌ Search failed:', error);
            });
    } else {
        console.log('   ❌ Insufficient data for search');
        console.log('   Missing fields:', {
            make: !selectedData.make,
            model: !selectedData.model,
            year_or_generation: !selectedData.year && !selectedData.generation,
            modification: !selectedData.modification
        });
    }
} else {
    console.log('   ❌ Widget or selected data not available');
}

// Test 5: Check for common issues
console.log('5. Common issues check:');

// Check if DOM elements exist
const elements = [
    'search-results',
    'no-results', 
    'factory-grid',
    'optional-grid',
    'vehicle-label'
];

elements.forEach(id => {
    const element = document.getElementById(id);
    console.log(`   ${id}: ${element ? '✅ found' : '❌ missing'}`);
});

// Test 6: Check garage load vs manual search difference
console.log('6. Garage vs manual search comparison:');
if (typeof LocalStorageHandler !== 'undefined') {
    const storage = new LocalStorageHandler();
    const garage = storage.getGarage();
    
    if (garage.length > 0) {
        const garageItem = garage[0];
        console.log('   Sample garage item:', garageItem);
        console.log('   Garage item has sizes:', !!(garageItem.tire_full || garageItem.rear_tire_full));
        
        if (window.wheelFitWidget?.selectedData) {
            const currentData = window.wheelFitWidget.selectedData;
            console.log('   Current selected data:', currentData);
            
            // Compare data structures
            console.log('   Data structure comparison:');
            console.log('     Garage item keys:', Object.keys(garageItem));
            console.log('     Selected data keys:', Object.keys(currentData));
        }
    } else {
        console.log('   No garage items to compare');
    }
} else {
    console.log('   LocalStorageHandler not available');
}

// Restore original fetch after 30 seconds
setTimeout(() => {
    window.fetch = originalFetch;
    console.log('🔄 Restored original fetch function');
}, 30000);

console.log('\n=== Debug Summary ===');
console.log('This script will:');
console.log('1. Monitor AJAX requests to see what data is being sent');
console.log('2. Monitor AJAX responses to see what the API returns');
console.log('3. Test manual search functionality');
console.log('4. Compare garage data vs current selected data');
console.log('');
console.log('Watch the console for intercepted requests and responses');
console.log('Try performing a search to see the detailed logs');

console.log('\n=== Debug script active ===');
