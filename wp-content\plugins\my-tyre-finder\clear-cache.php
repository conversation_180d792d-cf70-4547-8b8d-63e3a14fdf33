<?php
/**
 * Скрипт для очистки кэша после исправления фильтров
 * Запустите этот файл один раз после применения исправлений
 */

// Проверяем что мы в WordPress
if (!defined('ABSPATH')) {
    // Пытаемся загрузить WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php'
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists(__DIR__ . '/' . $path)) {
            require_once __DIR__ . '/' . $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress не найден. Запустите этот скрипт из админки WordPress.');
    }
}

// Проверяем права доступа
if (!current_user_can('manage_options')) {
    die('У вас нет прав для выполнения этого действия.');
}

echo "<h2>Очистка кэша Wheel-Size фильтров v5.0 - КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ + UX УЛУЧШЕНИЯ</h2>\n";
echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>\n";
echo "<h3 style='color: #155724; margin-top: 0;'>🚨 Критическое исправление + UX улучшения применены!</h3>\n";
echo "<p><strong>Проблема:</strong> Пустой список брендов при комбинации региональных + брендовых фильтров</p>\n";
echo "<p><strong>Решение:</strong> Убрали брендовые фильтры из API запроса + добавили понятные предупреждения</p>\n";
echo "<p><strong>Результат:</strong> API работает корректно + пользователи видят понятные сообщения при пустых результатах</p>\n";
echo "</div>\n";
echo "<div style='background: #cce5ff; padding: 15px; border: 1px solid #99ccff; border-radius: 5px; margin: 10px 0;'>\n";
echo "<h3 style='color: #0066cc; margin-top: 0;'>🎨 UX Улучшения:</h3>\n";
echo "<ul>\n";
echo "<li>✅ Понятные сообщения когда фильтры дают пустой результат</li>\n";
echo "<li>✅ Объяснение причины (какие бренды недоступны в каком регионе)</li>\n";
echo "<li>✅ Конкретные предложения по решению проблемы</li>\n";
echo "<li>✅ Кнопки для быстрого исправления ситуации</li>\n";
echo "</ul>\n";
echo "</div>\n";

// Используем функцию очистки кэша из ApiValidator
if (class_exists('\MyTyreFinder\Admin\ApiValidator')) {
    echo "<p>Очищаем кэш через ApiValidator...</p>\n";
    \MyTyreFinder\Admin\ApiValidator::clear_api_cache();
    echo "<p>✅ Кэш очищен через ApiValidator</p>\n";
} else {
    echo "<p>⚠️ Класс ApiValidator не найден, выполняем ручную очистку...</p>\n";
    
    global $wpdb;
    
    // Очищаем конкретные транзиенты
    $cache_keys = [
        'wheel_size_makes',
        'wheel_size_api_test', 
        'wheel_size_brand_filters_makes'
    ];
    
    foreach ($cache_keys as $key) {
        delete_transient($key);
        echo "<p>Удален транзиент: {$key}</p>\n";
    }
    
    // Очищаем все wheel_fit транзиенты
    $like_patterns = [
        '_transient_wheel_fit_makes%',
        '_transient_timeout_wheel_fit_makes%',
        '_transient_wheel_fit_models%', 
        '_transient_timeout_wheel_fit_models%',
        '_transient_wheel_fit_modifications%',
        '_transient_timeout_wheel_fit_modifications%',
        '_transient_wheel_fit_years%',
        '_transient_timeout_wheel_fit_years%',
        '_transient_wheel_fit_all_years%',
        '_transient_timeout_wheel_fit_all_years%',
        '_transient_models_%',
        '_transient_timeout_models_%',
        '_transient_mods_%',
        '_transient_timeout_mods_%'
    ];
    
    $where_conditions = array_map(function($pattern) {
        return "option_name LIKE '{$pattern}'";
    }, $like_patterns);
    
    $where = implode(' OR ', $where_conditions);
    $deleted = $wpdb->query("DELETE FROM {$wpdb->options} WHERE {$where}");
    
    echo "<p>Удалено {$deleted} записей кэша из базы данных</p>\n";
    
    // Очищаем объектный кэш
    wp_cache_flush();
    echo "<p>✅ Объектный кэш очищен</p>\n";
}

echo "<h3>Результат</h3>\n";
echo "<p>✅ Кэш успешно очищен!</p>\n";
echo "<p>Теперь фильтры по регионам и брендам должны работать корректно во всех режимах.</p>\n";

echo "<h3>🔧 Что дальше?</h3>\n";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>\n";
echo "<ol>\n";
echo "<li><strong>🚀 Запустите критический тест:</strong> <code>window.testCriticalFix()</code> в консоли браузера</li>\n";
echo "<li><strong>🎨 Запустите UX тест:</strong> <code>window.testUXImprovement()</code> в консоли браузера</li>\n";
echo "<li><strong>✅ Проверьте результаты:</strong> должно быть 'КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ УСПЕШНО!' и 'UX УЛУЧШЕНИЕ УСПЕШНО!'</li>\n";
echo "<li><strong>🎯 Протестируйте виджет:</strong> включите региональный фильтр + любой бренд</li>\n";
echo "<li><strong>📋 Убедитесь:</strong> теперь показываются ВСЕ бренды региона, а не пустой список</li>\n";
echo "<li><strong>🎨 Проверьте UX:</strong> при пустых результатах должны появляться понятные предупреждения</li>\n";
echo "<li><strong>🔄 Проверьте оба режима:</strong> 'by vehicle' и 'by year' должны работать одинаково</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<p><strong>Этот файл можно удалить после успешного тестирования.</strong></p>\n";
?>
