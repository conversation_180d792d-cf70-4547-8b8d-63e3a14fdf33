/**
 * Unit Tests for Admin Theme Cards
 * WSF-136: Icon positioning logic
 */

describe('WSFThemeCards', function() {
    
    beforeEach(function() {
        // Setup DOM
        document.body.innerHTML = `
            <div class="wsf-theme-card">
                <div class="wsf-theme-card__header">
                    <h3 class="wsf-theme-card__name">Test Theme</h3>
                    <div class="wsf-theme-card__actions">
                        <button class="wsf-theme-card__action">Edit</button>
                    </div>
                </div>
            </div>
        `;
        
        // Load the script if not already loaded
        if (typeof window.WSFThemeCards === 'undefined') {
            // In real tests, this would load the actual script
            window.WSFThemeCards = {
                calculateIconOffset: function(themeName) {
                    return themeName.length > 14 ? 16 : 0;
                },
                adjustCardActions: function(card) {
                    const nameElement = card.querySelector('.wsf-theme-card__name');
                    const actionsElement = card.querySelector('.wsf-theme-card__actions');
                    
                    if (!nameElement || !actionsElement) return;

                    const themeName = nameElement.textContent.trim();
                    const offset = this.calculateIconOffset(themeName);
                    
                    if (offset > 0) {
                        actionsElement.style.right = `calc(var(--wsf-card-padding) + ${offset}px)`;
                        actionsElement.setAttribute('data-offset', offset);
                    } else {
                        actionsElement.style.right = 'var(--wsf-card-padding)';
                        actionsElement.removeAttribute('data-offset');
                    }
                }
            };
        }
    });

    afterEach(function() {
        document.body.innerHTML = '';
    });

    describe('calculateIconOffset', function() {
        
        it('should return 0 for short theme names', function() {
            const shortNames = [
                'Light',
                'Dark',
                'Blue Theme',
                'Corporate',
                'Simple Design'  // exactly 14 chars
            ];
            
            shortNames.forEach(function(name) {
                const offset = window.WSFThemeCards.calculateIconOffset(name);
                expect(offset).toBe(0, `Failed for name: "${name}" (${name.length} chars)`);
            });
        });

        it('should return 16 for long theme names', function() {
            const longNames = [
                'Very Long Theme Name',
                'Corporate Branding Theme',
                'Extended Design System',
                'Professional Business Theme'
            ];
            
            longNames.forEach(function(name) {
                const offset = window.WSFThemeCards.calculateIconOffset(name);
                expect(offset).toBe(16, `Failed for name: "${name}" (${name.length} chars)`);
            });
        });

        it('should handle edge cases', function() {
            expect(window.WSFThemeCards.calculateIconOffset('')).toBe(0);
            expect(window.WSFThemeCards.calculateIconOffset('A')).toBe(0);
            expect(window.WSFThemeCards.calculateIconOffset('Exactly 15 Chars')).toBe(16); // 15 chars
        });

    });

    describe('adjustCardActions', function() {
        
        it('should not offset actions for short names', function() {
            const card = document.querySelector('.wsf-theme-card');
            const nameElement = card.querySelector('.wsf-theme-card__name');
            const actionsElement = card.querySelector('.wsf-theme-card__actions');
            
            nameElement.textContent = 'Short Name';
            
            window.WSFThemeCards.adjustCardActions(card);
            
            expect(actionsElement.style.right).toBe('var(--wsf-card-padding)');
            expect(actionsElement.hasAttribute('data-offset')).toBe(false);
        });

        it('should offset actions for long names', function() {
            const card = document.querySelector('.wsf-theme-card');
            const nameElement = card.querySelector('.wsf-theme-card__name');
            const actionsElement = card.querySelector('.wsf-theme-card__actions');
            
            nameElement.textContent = 'Very Long Theme Name That Exceeds Limit';
            
            window.WSFThemeCards.adjustCardActions(card);
            
            expect(actionsElement.style.right).toBe('calc(var(--wsf-card-padding) + 16px)');
            expect(actionsElement.getAttribute('data-offset')).toBe('16');
        });

        it('should handle missing elements gracefully', function() {
            const card = document.createElement('div');
            card.className = 'wsf-theme-card';
            
            // Should not throw error
            expect(function() {
                window.WSFThemeCards.adjustCardActions(card);
            }).not.toThrow();
        });

        it('should trim whitespace from theme names', function() {
            const card = document.querySelector('.wsf-theme-card');
            const nameElement = card.querySelector('.wsf-theme-card__name');
            const actionsElement = card.querySelector('.wsf-theme-card__actions');
            
            nameElement.textContent = '  Short  '; // 5 chars after trim
            
            window.WSFThemeCards.adjustCardActions(card);
            
            expect(actionsElement.style.right).toBe('var(--wsf-card-padding)');
        });

    });

    describe('Performance', function() {
        
        it('should handle multiple cards efficiently', function() {
            const startTime = performance.now();
            
            // Create 100 cards
            for (let i = 0; i < 100; i++) {
                const card = document.createElement('div');
                card.className = 'wsf-theme-card';
                card.innerHTML = `
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">Theme ${i}</h3>
                        <div class="wsf-theme-card__actions">
                            <button class="wsf-theme-card__action">Edit</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(card);
                window.WSFThemeCards.adjustCardActions(card);
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            // Should complete within 50ms for 100 cards
            expect(duration).toBeLessThan(50);
        });

    });

});
