/**
 * Проверка версии кода - убеждаемся, что новые исправления загружены
 */

(function() {
    'use strict';

    console.log('[Code Version Check] 🔍 Проверка версии кода...');

    // Функция проверки кода populateYears
    function checkPopulateYearsCode() {
        console.log('[Code Version Check] === Проверка кода populateYears ===');
        
        if (!window.wheelFitWidget) {
            console.error('[Code Version Check] ❌ window.wheelFitWidget недоступен!');
            return false;
        }
        
        const widget = window.wheelFitWidget;
        
        if (typeof widget.populateYears !== 'function') {
            console.error('[Code Version Check] ❌ populateYears не является функцией!');
            return false;
        }
        
        // Проверяем исходный код функции
        const functionCode = widget.populateYears.toString();
        
        console.log('[Code Version Check] Исходный код populateYears:');
        console.log(functionCode);
        
        // Проверяем, содержит ли код наши исправления
        const hasDirectTranslationCheck = functionCode.includes('window.WheelFitI18n && window.WheelFitI18n[');
        const hasComment = functionCode.includes('ИСПОЛЬЗУЕМ ПЕРЕВОДЫ НАПРЯМУЮ');
        
        console.log(`[Code Version Check] Содержит прямую проверку переводов: ${hasDirectTranslationCheck ? '✅' : '❌'}`);
        console.log(`[Code Version Check] Содержит комментарий об исправлении: ${hasComment ? '✅' : '❌'}`);
        
        if (hasDirectTranslationCheck && hasComment) {
            console.log('[Code Version Check] ✅ НОВЫЙ КОД ЗАГРУЖЕН!');
            return true;
        } else {
            console.error('[Code Version Check] ❌ СТАРЫЙ КОД! Нужно очистить кэш браузера!');
            return false;
        }
    }

    // Функция проверки других функций populate
    function checkAllPopulateFunctions() {
        console.log('[Code Version Check] === Проверка всех функций populate ===');
        
        if (!window.wheelFitWidget) {
            console.error('[Code Version Check] ❌ window.wheelFitWidget недоступен!');
            return false;
        }
        
        const widget = window.wheelFitWidget;
        const functions = ['populateMakes', 'populateModels', 'populateYears'];
        let allUpdated = true;
        
        functions.forEach(funcName => {
            if (typeof widget[funcName] === 'function') {
                const code = widget[funcName].toString();
                const hasUpdate = code.includes('window.WheelFitI18n && window.WheelFitI18n[');
                
                console.log(`[Code Version Check] ${funcName}: ${hasUpdate ? '✅ обновлен' : '❌ старый код'}`);
                
                if (!hasUpdate) {
                    allUpdated = false;
                }
            } else {
                console.log(`[Code Version Check] ${funcName}: ❌ не найден`);
                allUpdated = false;
            }
        });
        
        return allUpdated;
    }

    // Функция проверки initializeFormState
    function checkInitializeFormState() {
        console.log('[Code Version Check] === Проверка initializeFormState ===');
        
        if (!window.wheelFitWidget) {
            console.error('[Code Version Check] ❌ window.wheelFitWidget недоступен!');
            return false;
        }
        
        const widget = window.wheelFitWidget;
        
        if (typeof widget.initializeFormState !== 'function') {
            console.error('[Code Version Check] ❌ initializeFormState не найден!');
            return false;
        }
        
        const code = widget.initializeFormState.toString();
        const hasUpdate = code.includes('window.WheelFitI18n && window.WheelFitI18n[');
        
        console.log(`[Code Version Check] initializeFormState: ${hasUpdate ? '✅ обновлен' : '❌ старый код'}`);
        
        if (hasUpdate) {
            console.log('[Code Version Check] ✅ initializeFormState использует новый код!');
        } else {
            console.error('[Code Version Check] ❌ initializeFormState использует старый код!');
        }
        
        return hasUpdate;
    }

    // Функция тестирования с новым кодом
    function testWithNewCode() {
        console.log('[Code Version Check] === Тест с новым кодом ===');
        
        // Устанавливаем русские переводы
        window.WheelFitI18n = {
            'select_year_placeholder': 'Выберите год',
            'select_model_placeholder': 'Выберите модель',
            'select_make_placeholder': 'Выберите бренд'
        };
        
        console.log('[Code Version Check] Установлены русские переводы');
        
        if (!window.wheelFitWidget) {
            console.error('[Code Version Check] ❌ Виджет недоступен для тестирования');
            return;
        }
        
        const widget = window.wheelFitWidget;
        
        // Тестируем populateYears
        if (typeof widget.populateYears === 'function') {
            console.log('[Code Version Check] Тестируем populateYears с новым кодом...');
            
            widget.populateYears([
                { name: '2023', year: '2023' },
                { name: '2022', year: '2022' }
            ]);
            
            setTimeout(() => {
                const yearSelect = document.getElementById('wf-year');
                if (yearSelect) {
                    const option = yearSelect.querySelector('option[value=""]');
                    if (option) {
                        const text = option.textContent.trim();
                        const isRussian = text.includes('Выберите');
                        const isEnglish = text.includes('Choose');
                        
                        console.log(`[Code Version Check] Результат populateYears: "${text}"`);
                        console.log(`[Code Version Check] ${isRussian ? '✅ РУССКИЙ' : isEnglish ? '❌ АНГЛИЙСКИЙ' : '⚠️ ДРУГОЙ'}`);
                        
                        if (isRussian) {
                            console.log('[Code Version Check] 🎉 НОВЫЙ КОД РАБОТАЕТ!');
                        } else {
                            console.error('[Code Version Check] 💥 НОВЫЙ КОД НЕ РАБОТАЕТ!');
                        }
                    }
                }
            }, 100);
        }
    }

    // Основная функция проверки
    function runCodeVersionCheck() {
        console.log('[Code Version Check] 🚀 ЗАПУСК ПРОВЕРКИ ВЕРСИИ КОДА');
        
        // 1. Проверяем populateYears
        console.log('[Code Version Check] 1. Проверка populateYears...');
        const yearsOk = checkPopulateYearsCode();
        
        // 2. Проверяем все функции populate
        console.log('[Code Version Check] 2. Проверка всех функций populate...');
        const allOk = checkAllPopulateFunctions();
        
        // 3. Проверяем initializeFormState
        console.log('[Code Version Check] 3. Проверка initializeFormState...');
        const initOk = checkInitializeFormState();
        
        // 4. Тестируем с новым кодом
        if (yearsOk && allOk && initOk) {
            setTimeout(() => {
                console.log('[Code Version Check] 4. Тест с новым кодом...');
                testWithNewCode();
            }, 1000);
        } else {
            console.error('[Code Version Check] ❌ СТАРЫЙ КОД ОБНАРУЖЕН!');
            console.error('[Code Version Check] Нужно:');
            console.error('[Code Version Check] 1. Нажать Ctrl+F5 для принудительного обновления');
            console.error('[Code Version Check] 2. Или очистить кэш браузера');
            console.error('[Code Version Check] 3. Или отключить кэш в Developer Tools');
        }
        
        console.log('[Code Version Check] ✅ Проверка завершена');
    }

    // Глобальные функции
    window.codeVersionCheck = {
        run: runCodeVersionCheck,
        checkYears: checkPopulateYearsCode,
        checkAll: checkAllPopulateFunctions,
        checkInit: checkInitializeFormState,
        test: testWithNewCode
    };

    // Автоматический запуск
    setTimeout(() => {
        console.log('[Code Version Check] Автоматический запуск проверки...');
        runCodeVersionCheck();
    }, 1000);

    console.log('[Code Version Check] Проверка версии кода загружена. Доступные функции:');
    console.log('- codeVersionCheck.run() - полная проверка');
    console.log('- codeVersionCheck.test() - тест с новым кодом');

})();
