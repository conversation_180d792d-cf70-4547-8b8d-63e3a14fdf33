// Тест для проверки исправления фильтров брендов v3.0
// Файл: tests/test-brand-filters-fix.js

console.log('🔧 [Brand Filters Fix Test v3.0] Начинаем тестирование исправления фильтров брендов...');

// Функция для проверки AJAX запросов
async function testAjaxRequest(action, data = {}) {
    try {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('nonce', window.WheelFitData?.nonce || 'test-nonce');
        
        Object.keys(data).forEach(key => {
            formData.append(key, data[key]);
        });

        const response = await fetch(window.WheelFitData?.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        return result;
    } catch (error) {
        console.error(`[Brand Filters Fix Test] Ошибка в AJAX запросе ${action}:`, error);
        return { success: false, error: error.message };
    }
}

// Тест 1: Проверка что бренды загружаются с региональными фильтрами
async function testMakesWithRegionFilters() {
    console.log('[Brand Filters Fix Test] Тест 1: Загрузка брендов с региональными фильтрами...');
    
    const result = await testAjaxRequest('wf_get_makes');
    
    if (result.success && Array.isArray(result.data)) {
        const brandCount = result.data.length;
        console.log(`✅ [Brand Filters Fix Test] Получено ${brandCount} брендов с фильтрами`);
        
        if (brandCount > 0) {
            console.log('[Brand Filters Fix Test] Первые 5 брендов:', result.data.slice(0, 5).map(m => m.name || m.slug));
            return true;
        } else {
            console.error('❌ [Brand Filters Fix Test] Получен пустой список брендов - это баг!');
            return false;
        }
    } else {
        console.error('❌ [Brand Filters Fix Test] Ошибка загрузки брендов:', result);
        return false;
    }
}

// Тест 2: Проверка что бренды загружаются в режиме by year
async function testMakesByYearWithFilters() {
    console.log('[Brand Filters Fix Test] Тест 2: Загрузка брендов по году с фильтрами...');
    
    const testYear = 2020;
    const result = await testAjaxRequest('wf_get_makes_by_year', { year: testYear });
    
    if (result.success && Array.isArray(result.data)) {
        const brandCount = result.data.length;
        console.log(`✅ [Brand Filters Fix Test] Получено ${brandCount} брендов для года ${testYear}`);
        
        if (brandCount > 0) {
            console.log('[Brand Filters Fix Test] Первые 5 брендов:', result.data.slice(0, 5).map(m => m.name || m.slug));
            return true;
        } else {
            console.error('❌ [Brand Filters Fix Test] Получен пустой список брендов для года - это баг!');
            return false;
        }
    } else {
        console.error('❌ [Brand Filters Fix Test] Ошибка загрузки брендов по году:', result);
        return false;
    }
}

// Тест 3: Проверка логики - должны показываться ВСЕ бренды региона
async function testShowAllRegionBrands() {
    console.log('[Brand Filters Fix Test] Тест 3: Проверка что показываются ВСЕ бренды региона...');
    
    const result = await testAjaxRequest('wf_get_makes');
    
    if (result.success && Array.isArray(result.data)) {
        const brandCount = result.data.length;
        
        // Проверяем что брендов достаточно много (больше 20)
        // Если меньше, то возможно фильтры все еще ограничивают результат
        if (brandCount >= 20) {
            console.log(`✅ [Brand Filters Fix Test] Показано ${brandCount} брендов - логика работает правильно`);
            return true;
        } else {
            console.warn(`⚠️ [Brand Filters Fix Test] Показано только ${brandCount} брендов. Возможно фильтры все еще ограничивают результат.`);
            return false;
        }
    } else {
        console.error('❌ [Brand Filters Fix Test] Не удалось получить список брендов');
        return false;
    }
}

// Тест 4: Сравнение количества брендов в разных режимах
async function testConsistencyBetweenModes() {
    console.log('[Brand Filters Fix Test] Тест 4: Сравнение количества брендов в разных режимах...');
    
    const makesByVehicle = await testAjaxRequest('wf_get_makes');
    const makesByYear = await testAjaxRequest('wf_get_makes_by_year', { year: 2020 });
    
    if (makesByVehicle.success && makesByYear.success) {
        const vehicleCount = makesByVehicle.data.length;
        const yearCount = makesByYear.data.length;
        
        console.log(`[Brand Filters Fix Test] By Vehicle: ${vehicleCount} брендов, By Year: ${yearCount} брендов`);
        
        // Количество может отличаться, но не должно быть кардинально разным
        const ratio = Math.min(vehicleCount, yearCount) / Math.max(vehicleCount, yearCount);
        
        if (ratio > 0.5) { // Разница не больше чем в 2 раза
            console.log('✅ [Brand Filters Fix Test] Количество брендов в разных режимах согласовано');
            return true;
        } else {
            console.warn(`⚠️ [Brand Filters Fix Test] Большая разница в количестве брендов между режимами (ratio: ${ratio.toFixed(2)})`);
            return false;
        }
    } else {
        console.error('❌ [Brand Filters Fix Test] Не удалось получить данные для сравнения');
        return false;
    }
}

// Основная функция тестирования
async function runBrandFiltersFixTest() {
    console.log('🚀 [Brand Filters Fix Test v3.0] Запуск полного теста исправления фильтров брендов...');
    
    const tests = [
        { name: 'Makes with Region Filters', fn: testMakesWithRegionFilters },
        { name: 'Makes By Year with Filters', fn: testMakesByYearWithFilters },
        { name: 'Show All Region Brands', fn: testShowAllRegionBrands },
        { name: 'Consistency Between Modes', fn: testConsistencyBetweenModes }
    ];
    
    let passed = 0;
    let total = tests.length;
    
    for (const test of tests) {
        try {
            console.log(`\n--- Выполняется: ${test.name} ---`);
            const result = await test.fn();
            if (result) {
                passed++;
            }
            // Небольшая пауза между тестами
            await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
            console.error(`❌ [Brand Filters Fix Test] Ошибка в тесте ${test.name}:`, error);
        }
    }
    
    console.log(`\n📊 [Brand Filters Fix Test] Результаты: ${passed}/${total} тестов прошли успешно`);
    
    if (passed === total) {
        console.log('🎉 [Brand Filters Fix Test] Все тесты прошли! Фильтры брендов работают корректно.');
        console.log('✅ Теперь должны показываться ВСЕ бренды региона, а не только выбранные!');
    } else {
        console.log('⚠️ [Brand Filters Fix Test] Некоторые тесты не прошли. Возможно фильтры все еще ограничивают результаты.');
    }
    
    return { passed, total };
}

// Проверка виджета и запуск тестов
function initBrandFiltersFixTest() {
    if (typeof window.WheelFitData === 'undefined') {
        console.warn('[Brand Filters Fix Test] WheelFitData не найден. Тест может работать некорректно.');
    }
    
    // Запускаем тесты через небольшую задержку
    setTimeout(() => {
        runBrandFiltersFixTest().then(result => {
            console.log('[Brand Filters Fix Test] Тестирование завершено:', result);
        });
    }, 1000);
}

// Автозапуск при загрузке страницы
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initBrandFiltersFixTest);
} else {
    initBrandFiltersFixTest();
}

// Экспорт для ручного запуска
window.testBrandFiltersFixTest = runBrandFiltersFixTest;

console.log('[Brand Filters Fix Test] Тестовый скрипт загружен. Используйте window.testBrandFiltersFixTest() для ручного запуска.');
