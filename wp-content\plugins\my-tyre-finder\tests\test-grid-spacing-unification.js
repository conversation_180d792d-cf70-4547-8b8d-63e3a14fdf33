/**
 * Test script to verify Grid (2x2) layout spacing unification
 * between frontend and Live Preview in admin
 */

console.log('🧪 Testing Grid (2x2) spacing unification...');

function testGridGapUnification() {
    console.log('\n📐 Testing Grid gap unification...');
    
    // Find the Grid (2x2) form
    const gridForm = document.getElementById('tab-by-car');
    if (!gridForm || !gridForm.classList.contains('grid-2x2')) {
        console.log('❌ Grid (2x2) form not found');
        return false;
    }
    
    console.log('✅ Found Grid (2x2) form');
    
    // Check if gap-3 class is used (12px instead of 16px)
    const hasGap3 = gridForm.classList.contains('gap-3');
    const hasGap4 = gridForm.classList.contains('gap-4');
    
    console.log(`${hasGap3 ? '✅' : '❌'} Form uses gap-3 (12px) - unified with Live Preview`);
    console.log(`${!hasGap4 ? '✅' : '❌'} Form does not use gap-4 (16px) - old spacing removed`);
    
    // Measure actual gap using computed styles
    const computedStyle = window.getComputedStyle(gridForm);
    const actualGap = computedStyle.gap || computedStyle.gridGap;
    const expectedGap = '12px'; // 0.75rem
    
    console.log(`   Actual gap: ${actualGap}`);
    console.log(`   Expected gap: ${expectedGap}`);
    
    return hasGap3 && !hasGap4;
}

function testGarageButtonSpacing() {
    console.log('\n🚗 Testing Garage button spacing...');
    
    const gridForm = document.getElementById('tab-by-car');
    if (!gridForm || !gridForm.classList.contains('grid-2x2')) {
        console.log('❌ Grid (2x2) form not found');
        return false;
    }
    
    const garageButton = gridForm.querySelector('[data-garage-trigger]');
    if (!garageButton) {
        console.log('ℹ️ Garage button not found (may be disabled)');
        return true; // Not an error if garage is disabled
    }
    
    console.log('✅ Found Garage button in Grid form');
    
    // Check if mt-1 class is removed
    const hasMt1 = garageButton.classList.contains('mt-1');
    console.log(`${!hasMt1 ? '✅' : '❌'} Garage button does not have mt-1 class - unified with Live Preview`);
    
    // Check computed margin-top
    const computedStyle = window.getComputedStyle(garageButton);
    const marginTop = computedStyle.marginTop;
    
    console.log(`   Garage button margin-top: ${marginTop}`);
    
    // Should be 0 or very small
    const marginTopValue = parseFloat(marginTop);
    const hasMinimalMargin = marginTopValue <= 2; // Allow up to 2px tolerance
    
    console.log(`${hasMinimalMargin ? '✅' : '❌'} Garage button has minimal top margin (≤2px)`);
    
    return !hasMt1 && hasMinimalMargin;
}

function testSearchButtonLabelSpacing() {
    console.log('\n🔍 Testing search button label spacing...');
    
    const gridForm = document.getElementById('tab-by-car');
    if (!gridForm || !gridForm.classList.contains('grid-2x2')) {
        console.log('❌ Grid (2x2) form not found');
        return false;
    }
    
    const submitContainer = gridForm.querySelector('.ws-submit');
    if (!submitContainer) {
        console.log('❌ Submit container not found');
        return false;
    }
    
    const transparentLabel = submitContainer.querySelector('label[style*="height: 0"]');
    if (!transparentLabel) {
        console.log('❌ Transparent label not found');
        return false;
    }
    
    console.log('✅ Found transparent label above search button');
    
    // Check if label has zero height styling
    const style = transparentLabel.getAttribute('style');
    const hasZeroHeight = style.includes('height: 0');
    const hasZeroLineHeight = style.includes('line-height: 0');
    const hasZeroMargin = style.includes('margin: 0');
    const hasZeroPadding = style.includes('padding: 0');
    
    console.log(`${hasZeroHeight ? '✅' : '❌'} Label has height: 0`);
    console.log(`${hasZeroLineHeight ? '✅' : '❌'} Label has line-height: 0`);
    console.log(`${hasZeroMargin ? '✅' : '❌'} Label has margin: 0`);
    console.log(`${hasZeroPadding ? '✅' : '❌'} Label has padding: 0`);
    
    // Check computed height
    const computedStyle = window.getComputedStyle(transparentLabel);
    const actualHeight = computedStyle.height;
    const heightValue = parseFloat(actualHeight);
    const hasMinimalHeight = heightValue <= 2; // Allow up to 2px tolerance
    
    console.log(`   Label computed height: ${actualHeight}`);
    console.log(`${hasMinimalHeight ? '✅' : '❌'} Label has minimal height (≤2px)`);
    
    return hasZeroHeight && hasZeroLineHeight && hasZeroMargin && hasZeroPadding && hasMinimalHeight;
}

function testOverallCompactness() {
    console.log('\n📏 Testing overall form compactness...');
    
    const gridForm = document.getElementById('tab-by-car');
    if (!gridForm || !gridForm.classList.contains('grid-2x2')) {
        console.log('❌ Grid (2x2) form not found');
        return false;
    }
    
    // Measure form height
    const formRect = gridForm.getBoundingClientRect();
    const formHeight = formRect.height;
    
    console.log(`   Form height: ${formHeight}px`);
    
    // Check if form is reasonably compact (this is subjective, but should be under 200px for 2x2 grid)
    const isCompact = formHeight <= 200;
    console.log(`${isCompact ? '✅' : '❌'} Form is compact (≤200px height)`);
    
    // Count visible elements
    const formFields = gridForm.querySelectorAll('select, button[type="submit"], [data-garage-trigger]');
    console.log(`   Form elements found: ${formFields.length}`);
    
    return isCompact;
}

function runGridSpacingUnificationTests() {
    console.log('🚀 Starting Grid (2x2) spacing unification tests...\n');
    
    const results = {
        gridGap: testGridGapUnification(),
        garageSpacing: testGarageButtonSpacing(),
        labelSpacing: testSearchButtonLabelSpacing(),
        compactness: testOverallCompactness()
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Grid (2x2) spacing is now unified with Live Preview.');
    } else {
        console.log('⚠️ Some tests failed. Check the details above.');
    }
    
    console.log('\n📋 Unification Summary:');
    console.log('• Grid gap: 16px → 12px (gap-4 → gap-3)');
    console.log('• Garage button: removed mt-1 class');
    console.log('• Search label: height/margin/padding set to 0');
    console.log('• Overall: more compact layout matching Live Preview');
    
    return results;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runGridSpacingUnificationTests);
} else {
    runGridSpacingUnificationTests();
}

// Export for manual testing
window.testGridSpacingUnification = runGridSpacingUnificationTests;
