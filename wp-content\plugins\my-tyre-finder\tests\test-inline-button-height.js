/**
 * Test script for Inline Button Height Alignment
 * 
 * This test verifies that:
 * 1. <PERSON><PERSON> "Find Sizes" has the same height as form fields in Inline layout
 * 2. All elements in the row are visually aligned
 * 3. <PERSON><PERSON> maintains proper styling while matching field height
 * 4. No visual imbalance in the 1×4 inline layout
 * 5. Responsive behavior works correctly
 * 
 * Run this in browser console on /wp-admin/admin.php?page=wheel-size-appearance
 * Make sure to select "Inline (1×4)" layout first
 */

console.log('📏 Testing Inline Button Height Alignment...');

function testInlineButtonHeight() {
    console.log('📋 Starting inline button height tests...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.error('❌ Live Preview container not found');
        return;
    }
    
    console.log('✅ Live Preview container found');
    
    // Test 1: Check if we're in inline layout
    const isInlineLayout = testInlineLayout(previewContainer);
    
    if (!isInlineLayout) {
        console.warn('⚠️ Not in Inline layout - switch to "Inline (1×4)" to test button height');
        return;
    }
    
    // Test 2: Check button and field heights
    testHeightAlignment(previewContainer);
    
    // Test 3: Check visual alignment
    testVisualAlignment(previewContainer);
    
    // Test 4: Check button styling preservation
    testButtonStyling(previewContainer);
    
    // Test 5: Check responsive behavior
    testResponsiveBehavior(previewContainer);
    
    // Test 6: Generate alignment report
    generateHeightReport(previewContainer);
    
    console.log('🎉 Inline button height tests completed!');
}

function testInlineLayout(container) {
    console.log('🔍 Checking if we\'re in Inline layout...');
    
    // Look for inline layout indicators
    const inlineForm = container.querySelector('#tab-by-car.grid-2x2');
    const submitButton = container.querySelector('.ws-submit .btn-primary');
    
    if (inlineForm && submitButton) {
        console.log('✅ Inline layout detected');
        return true;
    } else {
        console.log('❌ Inline layout not detected');
        console.log('   Make sure to select "Inline (1×4)" in Form Layout dropdown');
        return false;
    }
}

function testHeightAlignment(container) {
    console.log('📏 Testing height alignment...');
    
    // Find form elements
    const selects = container.querySelectorAll('select');
    const submitButton = container.querySelector('.ws-submit .btn-primary');
    
    if (!submitButton) {
        console.error('❌ Submit button not found');
        return;
    }
    
    if (selects.length === 0) {
        console.error('❌ No select fields found');
        return;
    }
    
    console.log(`📊 Found ${selects.length} select fields and 1 submit button`);
    
    // Get heights
    const buttonRect = submitButton.getBoundingClientRect();
    const buttonHeight = buttonRect.height;
    
    console.log(`📊 Height measurements:`);
    console.log(`  Submit button: ${buttonHeight.toFixed(1)}px`);
    
    let allAligned = true;
    const expectedHeight = 44; // --ws-control-height
    const tolerance = 2; // 2px tolerance
    
    // Check button height
    const buttonCorrect = Math.abs(buttonHeight - expectedHeight) <= tolerance;
    
    if (buttonCorrect) {
        console.log(`  ✅ Button height correct (${buttonHeight.toFixed(1)}px ≈ ${expectedHeight}px)`);
    } else {
        console.warn(`  ⚠️ Button height incorrect (${buttonHeight.toFixed(1)}px, expected ${expectedHeight}px)`);
        allAligned = false;
    }
    
    // Check select fields
    selects.forEach((select, index) => {
        const selectRect = select.getBoundingClientRect();
        const selectHeight = selectRect.height;
        const selectCorrect = Math.abs(selectHeight - expectedHeight) <= tolerance;
        
        console.log(`  Select ${index + 1}: ${selectHeight.toFixed(1)}px ${selectCorrect ? '✅' : '⚠️'}`);
        
        if (!selectCorrect) {
            allAligned = false;
        }
    });
    
    // Check alignment between button and fields
    if (selects.length > 0) {
        const firstSelect = selects[0];
        const firstSelectHeight = firstSelect.getBoundingClientRect().height;
        const heightDiff = Math.abs(buttonHeight - firstSelectHeight);
        
        console.log(`  Height difference (button vs select): ${heightDiff.toFixed(1)}px`);
        
        const heightsMatch = heightDiff <= tolerance;
        
        if (heightsMatch) {
            console.log('  ✅ Button and select heights match');
        } else {
            console.warn('  ⚠️ Button and select heights don\'t match');
            allAligned = false;
        }
    }
    
    return { allAligned, buttonHeight, expectedHeight, selects: selects.length };
}

function testVisualAlignment(container) {
    console.log('👁️ Testing visual alignment...');
    
    const form = container.querySelector('#tab-by-car.grid-2x2');
    
    if (!form) {
        console.warn('⚠️ Inline form not found for visual test');
        return;
    }
    
    // Get all form controls in the grid
    const formControls = form.querySelectorAll('select, .btn-primary');
    
    if (formControls.length === 0) {
        console.warn('⚠️ No form controls found');
        return;
    }
    
    console.log(`📊 Visual alignment check (${formControls.length} controls):`);
    
    // Check if all controls have similar top positions (indicating they're aligned)
    const positions = Array.from(formControls).map(control => {
        const rect = control.getBoundingClientRect();
        return {
            element: control,
            top: rect.top,
            height: rect.height,
            tagName: control.tagName.toLowerCase(),
            className: control.className
        };
    });
    
    // Find the reference top position (most common)
    const topPositions = positions.map(p => Math.round(p.top));
    const referenceTop = topPositions[0]; // Use first element as reference
    
    let visuallyAligned = true;
    
    positions.forEach((pos, index) => {
        const topDiff = Math.abs(pos.top - referenceTop);
        const isAligned = topDiff <= 5; // 5px tolerance for visual alignment
        
        const elementDesc = pos.tagName === 'button' ? 'Button' : `Select ${index + 1}`;
        
        console.log(`  ${elementDesc}: top ${pos.top.toFixed(1)}px, height ${pos.height.toFixed(1)}px ${isAligned ? '✅' : '⚠️'}`);
        
        if (!isAligned) {
            visuallyAligned = false;
        }
    });
    
    if (visuallyAligned) {
        console.log('✅ All controls are visually aligned');
    } else {
        console.warn('⚠️ Some controls are not visually aligned');
    }
    
    return { visuallyAligned, controlsCount: formControls.length };
}

function testButtonStyling(container) {
    console.log('🎨 Testing button styling preservation...');
    
    const submitButton = container.querySelector('.ws-submit .btn-primary');
    
    if (!submitButton) {
        console.warn('⚠️ Submit button not found for styling test');
        return;
    }
    
    const buttonStyle = window.getComputedStyle(submitButton);
    
    console.log(`📊 Button styling check:`);
    
    // Check key styling properties
    const checks = [
        {
            property: 'background-color',
            value: buttonStyle.backgroundColor,
            test: (val) => val !== 'rgba(0, 0, 0, 0)' && val !== 'transparent',
            description: 'Has background color'
        },
        {
            property: 'color',
            value: buttonStyle.color,
            test: (val) => val !== 'rgba(0, 0, 0, 0)' && val !== 'transparent',
            description: 'Has text color'
        },
        {
            property: 'border-radius',
            value: buttonStyle.borderRadius,
            test: (val) => parseFloat(val) > 0,
            description: 'Has border radius'
        },
        {
            property: 'font-weight',
            value: buttonStyle.fontWeight,
            test: (val) => parseInt(val) >= 600 || val === 'bold',
            description: 'Has bold font weight'
        },
        {
            property: 'cursor',
            value: buttonStyle.cursor,
            test: (val) => val === 'pointer',
            description: 'Has pointer cursor'
        },
        {
            property: 'display',
            value: buttonStyle.display,
            test: (val) => val === 'flex',
            description: 'Uses flex display for centering'
        },
        {
            property: 'align-items',
            value: buttonStyle.alignItems,
            test: (val) => val === 'center',
            description: 'Centers content vertically'
        }
    ];
    
    let stylingPreserved = true;
    
    checks.forEach(check => {
        const passed = check.test(check.value);
        
        console.log(`  ${check.description}: ${check.value} ${passed ? '✅' : '⚠️'}`);
        
        if (!passed) {
            stylingPreserved = false;
        }
    });
    
    if (stylingPreserved) {
        console.log('✅ Button styling is properly preserved');
    } else {
        console.warn('⚠️ Some button styling might be affected');
    }
    
    return { stylingPreserved, checks: checks.length };
}

function testResponsiveBehavior(container) {
    console.log('📱 Testing responsive behavior...');
    
    const submitButton = container.querySelector('.ws-submit .btn-primary');
    
    if (!submitButton) {
        console.warn('⚠️ Submit button not found for responsive test');
        return;
    }
    
    // Get current viewport width
    const viewportWidth = window.innerWidth;
    
    console.log(`📊 Current viewport: ${viewportWidth}px`);
    
    const buttonStyle = window.getComputedStyle(submitButton);
    const buttonHeight = parseFloat(buttonStyle.height);
    
    if (viewportWidth <= 639) {
        // Mobile breakpoint
        const expectedMobileHeight = 48; // Slightly taller on mobile
        const isMobileHeight = Math.abs(buttonHeight - expectedMobileHeight) <= 2;
        
        console.log(`  Mobile height: ${buttonHeight}px ${isMobileHeight ? '✅' : '⚠️'} (expected ~${expectedMobileHeight}px)`);
        
        return { responsive: isMobileHeight, isMobile: true, height: buttonHeight };
    } else {
        // Desktop breakpoint
        const expectedDesktopHeight = 44; // Standard height on desktop
        const isDesktopHeight = Math.abs(buttonHeight - expectedDesktopHeight) <= 2;
        
        console.log(`  Desktop height: ${buttonHeight}px ${isDesktopHeight ? '✅' : '⚠️'} (expected ~${expectedDesktopHeight}px)`);
        
        return { responsive: isDesktopHeight, isMobile: false, height: buttonHeight };
    }
}

function generateHeightReport(container) {
    console.log('📋 Generating height alignment report...');
    
    const submitButton = container.querySelector('.ws-submit .btn-primary');
    const selects = container.querySelectorAll('select');
    
    if (!submitButton || selects.length === 0) {
        console.error('❌ Cannot generate report - required elements missing');
        return;
    }
    
    const buttonHeight = submitButton.getBoundingClientRect().height;
    const selectHeight = selects[0].getBoundingClientRect().height;
    const heightDiff = Math.abs(buttonHeight - selectHeight);
    
    const report = {
        layout: 'INLINE_1x4',
        buttonHeight: buttonHeight,
        selectHeight: selectHeight,
        heightDifference: heightDiff,
        aligned: heightDiff <= 2,
        expectedHeight: 44,
        status: 'CHECKING',
        issues: []
    };
    
    // Determine overall status
    if (report.aligned && Math.abs(buttonHeight - 44) <= 2) {
        report.status = '✅ PERFECT ALIGNMENT';
    } else if (report.aligned) {
        report.status = '⚠️ ALIGNED BUT WRONG HEIGHT';
    } else {
        report.status = '❌ MISALIGNED';
    }
    
    // Generate issues list
    if (!report.aligned) {
        report.issues.push(`Button and select heights don't match (${heightDiff.toFixed(1)}px difference)`);
    }
    
    if (Math.abs(buttonHeight - 44) > 2) {
        report.issues.push(`Button height should be 44px, got ${buttonHeight.toFixed(1)}px`);
    }
    
    if (Math.abs(selectHeight - 44) > 2) {
        report.issues.push(`Select height should be 44px, got ${selectHeight.toFixed(1)}px`);
    }
    
    console.log('\n📊 === HEIGHT ALIGNMENT REPORT ===');
    console.log(`Status: ${report.status}`);
    console.log(`Layout: ${report.layout}`);
    console.log(`Button height: ${report.buttonHeight.toFixed(1)}px`);
    console.log(`Select height: ${report.selectHeight.toFixed(1)}px`);
    console.log(`Height difference: ${report.heightDifference.toFixed(1)}px`);
    console.log(`Expected height: ${report.expectedHeight}px`);
    console.log(`Aligned: ${report.aligned ? '✅' : '❌'}`);
    
    if (report.issues.length > 0) {
        console.log('\n💡 Issues to fix:');
        report.issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue}`);
        });
    } else {
        console.log('\n🎉 No issues found - button height is perfectly aligned!');
    }
    
    // Store report for manual access
    window.heightAlignmentReport = report;
}

// Manual test helper functions
window.testInlineButtonHeight = function() {
    console.log('🔧 Manual inline button height test...');
    testInlineButtonHeight();
};

window.highlightInlineElements = function() {
    console.log('🎨 Highlighting inline elements...');
    
    const selects = document.querySelectorAll('select');
    const button = document.querySelector('.ws-submit .btn-primary');
    
    // Highlight selects in blue
    selects.forEach((select, index) => {
        select.style.outline = '3px solid blue';
        select.style.outlineOffset = '2px';
        
        // Add height label
        const height = select.getBoundingClientRect().height;
        select.title = `Select ${index + 1}: ${height.toFixed(1)}px`;
    });
    
    // Highlight button in red
    if (button) {
        button.style.outline = '3px solid red';
        button.style.outlineOffset = '2px';
        
        const height = button.getBoundingClientRect().height;
        button.title = `Button: ${height.toFixed(1)}px`;
    }
    
    console.log(`✅ Highlighted ${selects.length} selects (blue) and 1 button (red)`);
    
    setTimeout(() => {
        selects.forEach(select => {
            select.style.outline = '';
            select.style.outlineOffset = '';
            select.title = '';
        });
        
        if (button) {
            button.style.outline = '';
            button.style.outlineOffset = '';
            button.title = '';
        }
        
        console.log('🧹 Highlights removed');
    }, 5000);
};

window.measureInlineHeights = function() {
    console.log('📏 Measuring inline element heights...');
    
    const selects = document.querySelectorAll('select');
    const button = document.querySelector('.ws-submit .btn-primary');
    
    console.log('📊 Detailed height measurements:');
    
    selects.forEach((select, index) => {
        const rect = select.getBoundingClientRect();
        const style = window.getComputedStyle(select);
        
        console.log(`Select ${index + 1}:`);
        console.log(`  Height: ${rect.height.toFixed(1)}px`);
        console.log(`  CSS height: ${style.height}`);
        console.log(`  Padding: ${style.paddingTop} ${style.paddingBottom}`);
    });
    
    if (button) {
        const rect = button.getBoundingClientRect();
        const style = window.getComputedStyle(button);
        
        console.log(`Button:`);
        console.log(`  Height: ${rect.height.toFixed(1)}px`);
        console.log(`  CSS height: ${style.height}`);
        console.log(`  Padding: ${style.paddingTop} ${style.paddingBottom}`);
    }
};

// Auto-run the test
testInlineButtonHeight();

console.log('\n🔧 Manual test functions available:');
console.log('  - testInlineButtonHeight() - Run height alignment test');
console.log('  - highlightInlineElements() - Highlight elements (5s)');
console.log('  - measureInlineHeights() - Show detailed measurements');
console.log('  - heightAlignmentReport - Access detailed report');
