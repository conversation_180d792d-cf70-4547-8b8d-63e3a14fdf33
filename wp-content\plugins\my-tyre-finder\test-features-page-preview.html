<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Features Page Preview - Brand Filters UI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f0f0f1;
            margin: 0;
            padding: 20px;
        }

        .wrap {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: #1d2327;
            font-size: 23px;
            font-weight: 400;
            margin: 0 0 20px;
        }

        /* Features page layout improvements */
        .features-section {
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            margin-bottom: 20px;
            padding: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }

        .features-section h2 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
            color: #1d2327;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
        }

        .features-section .description {
            margin-bottom: 20px;
            color: #646970;
        }

        /* Scroll indicator */
        .scroll-indicator {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #2271b1;
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            animation: pulse 2s infinite;
            cursor: pointer;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Brand Filters section styling */
        .brand-filters__section {
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            margin-bottom: 20px;
            padding: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }

        .brand-filters__section h2 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
            color: #1d2327;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
        }

        .brand-filters__section .description {
            margin-bottom: 20px;
            color: #646970;
        }

        /* Brand search input in sticky panel */
        .sticky-controls .brand-search-input {
            width: 200px;
            padding: 6px 10px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
            font-size: 13px;
            margin: 0 10px;
        }

        .sticky-controls .brand-search-input:focus {
            border-color: #2271b1;
            outline: none;
            box-shadow: 0 0 0 1px #2271b1;
        }

        /* Collapsible instructions */
        .instructions-toggle {
            background: none;
            border: none;
            color: #2271b1;
            cursor: pointer;
            font-size: 14px;
            text-decoration: underline;
            padding: 0;
            margin-bottom: 10px;
        }

        .instructions-toggle:hover {
            color: #135e96;
        }

        .instructions-content {
            display: none;
            margin-bottom: 15px;
        }

        .instructions-content.show {
            display: block;
        }

        /* Sticky controls panel */
        .sticky-controls {
            position: sticky;
            top: 32px;
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 100;
        }

        .sticky-controls .controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .sticky-controls .legend-compact {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .sticky-controls .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .sticky-controls .color-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
        }

        .sticky-controls .count-badge {
            background: #8c8f94;
            color: white;
            padding: 1px 6px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 11px;
            min-width: 16px;
            text-align: center;
        }

        .sticky-controls .count-badge.included { background: #2271b1; }
        .sticky-controls .count-badge.excluded { background: #d63638; }
        .sticky-controls .count-badge.neutral { background: #8c8f94; }

        /* Brand tags */
        .brand-filters__container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 16px;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            background-color: #fff;
            max-height: 400px;
            overflow-y: auto;
        }

        .brand-tag {
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            position: relative !important;
            padding: 6px 12px;
            border: 1px solid #ccd0d4;
            border-radius: 3px;
            background: #f6f7f7;
            font-size: 13px;
            color: #2c3338;
        }

        .brand-tag:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
        }

        .brand-tag.is-included {
            background: #00a32a;
            color: white;
            border-color: #00a32a;
        }

        .brand-tag.is-excluded {
            background: #d63638;
            color: white;
            border-color: #d63638;
        }

        .brand-tag.is-neutral {
            background: #8c8f94;
            color: white;
            border-color: #8c8f94;
        }

        /* Add click indicator */
        .brand-tag::after {
            content: "👆 Click me";
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #2271b1;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
            white-space: nowrap;
            z-index: 10;
        }

        .brand-tag:hover::after {
            opacity: 1;
        }

        .brand-tag.is-included::after,
        .brand-tag.is-excluded::after {
            display: none;
        }

        /* Consistent button styling for both save buttons - WordPress standard size */
        .button-primary {
            background: #2271b1;
            border-color: #2271b1;
            color: #fff;
            font-weight: 400;
            padding: 0 12px;
            font-size: 13px;
            line-height: 2.15384615;
            min-height: 30px;
            border-radius: 3px;
            cursor: pointer;
            border: 1px solid;
        }

        .button-primary:hover {
            background: #135e96;
            border-color: #135e96;
        }
    </style>
</head>
<body>
    <div class="wrap">
        <h1>Wheel-Size – Features</h1>

        <!-- Scroll indicator -->
        <div class="scroll-indicator" onclick="scrollToBrandFilters()">
            ↓ Brand Filters Below
        </div>

        <!-- Main Features Section -->
        <div class="features-section">
            <h2>Main Features & Regional Settings</h2>
            <div class="description">
                Configure core functionality and regional filtering options.
            </div>
            <p>This section would contain the main features form...</p>
            <button class="button-primary">Save Main Features & Regional Settings</button>
        </div>

        <!-- Brand Filters Section -->
        <div class="brand-filters__section">
            <h2>Brand Filters</h2>

            <!-- Collapsible Instructions -->
            <button type="button" class="instructions-toggle" onclick="toggleInstructions()">
                ℹ️ How to use Brand Filters (click to show/hide)
            </button>

            <div class="instructions-content" id="brand-instructions">
                <div style="max-width: 700px;">
                    <p><strong>How to use Brand Filters:</strong></p>
                    <ul style="margin-left: 20px; margin-bottom: 15px;">
                        <li>Click on any brand name to change its state</li>
                        <li>Each click cycles through: Neutral (gray) → Included (green) → Excluded (red) → back to Neutral</li>
                        <li><strong>Included (green):</strong> Only these brands will appear in the finder</li>
                        <li><strong>Excluded (red):</strong> These brands will be hidden from the finder</li>
                        <li><strong>Neutral (gray):</strong> Default state - no special filtering applied</li>
                    </ul>
                    <p style="background: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <strong>💡 Tip:</strong>
                        If you select any brands as "Included", only those brands will show up. If you only use "Excluded", all brands except the excluded ones will appear.
                    </p>
                </div>
            </div>

            <div style="background: #e7f3ff; padding: 12px; border-left: 4px solid #2271b1; margin-bottom: 15px;">
                <strong>⚡ Priority Rule:</strong><br>
                When you have at least one brand marked as "Included" (green), all "Excluded" (red) brands are ignored. Only the "Included" brands will be shown.
            </div>

            <div style="background: #f0f6ff; padding: 12px; border-left: 4px solid #0073aa; margin-bottom: 15px;">
                <strong>🔗 Combined with Regional Filters:</strong><br>
                If you have both brand filters and regional filters active, the system will show only the brands that match BOTH your brand selection AND belong to your selected regions.
            </div>

            <!-- Brand Search -->
            <div class="brand-search-wrapper">
                <input type="text"
                       id="brand-search"
                       class="brand-search-input"
                       placeholder="Search brands..."
                       onkeyup="filterBrands(this.value)">
            </div>

            <!-- Sticky Controls Panel -->
            <div class="sticky-controls" id="sticky-controls">
                <div class="controls-row">
                    <div class="legend-compact">
                        <div class="legend-item">
                            <span class="color-dot" style="background: #00a32a;"></span>
                            Included:
                            <span id="include-count-sticky" class="count-badge included">2</span>
                        </div>
                        <div class="legend-item">
                            <span class="color-dot" style="background: #d63638;"></span>
                            Excluded:
                            <span id="exclude-count-sticky" class="count-badge excluded">1</span>
                        </div>
                        <div class="legend-item">
                            <span class="color-dot" style="background: #8c8f94;"></span>
                            Neutral:
                            <span id="neutral-count-sticky" class="count-badge neutral">5</span>
                        </div>
                    </div>
                    <button type="submit" class="button-primary">
                        Save Brand Filters
                    </button>
                </div>
            </div>

            <div class="brand-filters__container">
                <button type="button" class="brand-tag is-included" data-slug="toyota">Toyota</button>
                <button type="button" class="brand-tag is-included" data-slug="honda">Honda</button>
                <button type="button" class="brand-tag is-excluded" data-slug="ford">Ford</button>
                <button type="button" class="brand-tag is-neutral" data-slug="bmw">BMW</button>
                <button type="button" class="brand-tag is-neutral" data-slug="mercedes">Mercedes</button>
                <button type="button" class="brand-tag is-neutral" data-slug="audi">Audi</button>
                <button type="button" class="brand-tag is-neutral" data-slug="volkswagen">Volkswagen</button>
                <button type="button" class="brand-tag is-neutral" data-slug="nissan">Nissan</button>
            </div>

        </div>
    </div>

    <script>
        function scrollToBrandFilters() {
            const brandSection = document.querySelector('.brand-filters__section');
            if (brandSection) {
                brandSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // Toggle instructions visibility
        function toggleInstructions() {
            const content = document.getElementById('brand-instructions');
            content.classList.toggle('show');
        }

        // Filter brands by search term
        function filterBrands(searchTerm) {
            const brands = document.querySelectorAll('.brand-tag');
            const term = searchTerm.toLowerCase();

            brands.forEach(brand => {
                const brandName = brand.textContent.toLowerCase();
                if (brandName.includes(term)) {
                    brand.style.display = '';
                } else {
                    brand.style.display = 'none';
                }
            });
        }

        // Update sticky counters
        function updateStickyCounters() {
            const includedCount = document.querySelectorAll('.brand-tag.is-included').length;
            const excludedCount = document.querySelectorAll('.brand-tag.is-excluded').length;
            const neutralCount = document.querySelectorAll('.brand-tag.is-neutral').length;

            // Update sticky counters
            const includedSticky = document.getElementById('include-count-sticky');
            const excludedSticky = document.getElementById('exclude-count-sticky');
            const neutralSticky = document.getElementById('neutral-count-sticky');

            if (includedSticky) includedSticky.textContent = includedCount;
            if (excludedSticky) excludedSticky.textContent = excludedCount;
            if (neutralSticky) neutralSticky.textContent = neutralCount;
        }

        // Demo brand tag clicking
        document.querySelectorAll('.brand-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                if (this.classList.contains('is-neutral')) {
                    this.classList.remove('is-neutral');
                    this.classList.add('is-included');
                } else if (this.classList.contains('is-included')) {
                    this.classList.remove('is-included');
                    this.classList.add('is-excluded');
                } else {
                    this.classList.remove('is-excluded');
                    this.classList.add('is-neutral');
                }
                updateStickyCounters();
            });
        });

        // Hide scroll indicator when brand section is visible
        function checkScrollIndicator() {
            const indicator = document.querySelector('.scroll-indicator');
            const brandSection = document.querySelector('.brand-filters__section');

            if (!indicator || !brandSection) return;

            const rect = brandSection.getBoundingClientRect();
            const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

            if (isVisible) {
                indicator.style.display = 'none';
            } else {
                indicator.style.display = 'block';
            }
        }

        // Initialize
        window.addEventListener('scroll', checkScrollIndicator);
        window.addEventListener('load', function() {
            checkScrollIndicator();
            updateStickyCounters();
        });
    </script>
</body>
</html>
