<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Обновление дефолтных тем</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2563eb; margin-bottom: 20px; }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .status.error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .status.info { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .status.warning { background: #fef3c7; color: #92400e; border: 1px solid #fcd34d; }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
            font-weight: 500;
        }
        button:hover { background: #1d4ed8; }
        button:disabled { background: #9ca3af; cursor: not-allowed; }
        .log {
            background: #1f2937;
            color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .theme-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .theme-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        .theme-header {
            padding: 15px;
            font-weight: bold;
            text-align: center;
        }
        .theme-light .theme-header {
            background: #FFFFFF;
            color: #1A1D23;
            border-bottom: 1px solid #E5E7EB;
        }
        .theme-dark .theme-header {
            background: #0B1120;
            color: #F8FAFC;
            border-bottom: 1px solid #374151;
        }
        .color-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1px;
        }
        .color-swatch {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            text-shadow: 0 0 3px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Обновление дефолтных тем</h1>
        
        <div class="status info">
            <strong>ℹ️ Информация:</strong> Этот инструмент обновит дефолтные темы Light и Dark с новыми современными цветовыми схемами. Ваши пользовательские темы останутся без изменений.
        </div>

        <div>
            <button onclick="checkCurrentThemes()">🔍 Проверить текущие темы</button>
            <button onclick="updateDefaultThemes()">🔄 Обновить дефолтные темы</button>
            <button onclick="testThemes()">🧪 Тестировать темы</button>
        </div>

        <div id="status-container"></div>

        <div class="theme-preview">
            <div class="theme-card theme-light">
                <div class="theme-header">☀️ Light Theme</div>
                <div class="color-grid">
                    <div class="color-swatch" style="background: #2563EB; color: white;">Primary</div>
                    <div class="color-swatch" style="background: #FFFFFF; color: #1A1D23; border: 1px solid #E5E7EB;">Background</div>
                    <div class="color-swatch" style="background: #1A1D23; color: white;">Text</div>
                    <div class="color-swatch" style="background: #059669; color: white;">Success</div>
                    <div class="color-swatch" style="background: #D97706; color: white;">Warning</div>
                    <div class="color-swatch" style="background: #DC2626; color: white;">Error</div>
                </div>
            </div>
            
            <div class="theme-card theme-dark">
                <div class="theme-header">🌙 Dark Theme</div>
                <div class="color-grid">
                    <div class="color-swatch" style="background: #3B82F6; color: white;">Primary</div>
                    <div class="color-swatch" style="background: #0B1120; color: #F8FAFC;">Background</div>
                    <div class="color-swatch" style="background: #F8FAFC; color: #0B1120;">Text</div>
                    <div class="color-swatch" style="background: #10B981; color: white;">Success</div>
                    <div class="color-swatch" style="background: #F59E0B; color: white;">Warning</div>
                    <div class="color-swatch" style="background: #EF4444; color: white;">Error</div>
                </div>
            </div>
        </div>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        // API настройки
        const apiBase = '/wp-json/wheel-size/v1/themes';
        const nonce = document.querySelector('meta[name="wp-nonce"]')?.content || wpApiSettings?.nonce || 'test-nonce';

        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            container.appendChild(statusDiv);
            
            // Auto-remove after 10 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    if (statusDiv.parentNode) {
                        statusDiv.parentNode.removeChild(statusDiv);
                    }
                }, 10000);
            }
        }

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            logElement.style.display = 'block';
        }

        async function checkCurrentThemes() {
            log('Проверка текущих тем...');
            
            try {
                const response = await fetch(apiBase);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                const themes = data.themes || {};
                
                log(`Найдено тем: ${Object.keys(themes).length}`);
                log(`Доступные темы: ${Object.keys(themes).join(', ')}`);
                
                if (themes.light) {
                    const lightPrimary = themes.light['--wsf-primary'] || 'не установлен';
                    log(`Light theme primary: ${lightPrimary}`);
                    
                    if (lightPrimary === '#2563EB') {
                        showStatus('✅ Light тема уже обновлена с новыми цветами', 'success');
                    } else {
                        showStatus('⚠️ Light тема использует старые цвета: ' + lightPrimary, 'warning');
                    }
                }
                
                if (themes.dark) {
                    const darkPrimary = themes.dark['--wsf-primary'] || 'не установлен';
                    log(`Dark theme primary: ${darkPrimary}`);
                    
                    if (darkPrimary === '#3B82F6') {
                        showStatus('✅ Dark тема уже обновлена с новыми цветами', 'success');
                    } else {
                        showStatus('⚠️ Dark тема использует старые цвета: ' + darkPrimary, 'warning');
                    }
                }
                
            } catch (error) {
                log(`Ошибка: ${error.message}`);
                showStatus(`❌ Ошибка проверки тем: ${error.message}`, 'error');
            }
        }

        async function updateDefaultThemes() {
            log('Начинаем обновление дефолтных тем...');
            showStatus('🔄 Обновление дефолтных тем...', 'info');
            
            try {
                // Сначала удалим старые дефолтные темы
                log('Удаление старых дефолтных тем...');
                
                for (const themeSlug of ['light', 'dark']) {
                    try {
                        const deleteResponse = await fetch(`${apiBase}/${themeSlug}`, {
                            method: 'DELETE',
                            headers: {
                                'X-WP-Nonce': nonce
                            }
                        });
                        
                        if (deleteResponse.ok) {
                            log(`✅ Удалена старая тема: ${themeSlug}`);
                        } else {
                            log(`⚠️ Не удалось удалить тему ${themeSlug} (возможно, защищена)`);
                        }
                    } catch (e) {
                        log(`⚠️ Ошибка удаления темы ${themeSlug}: ${e.message}`);
                    }
                }
                
                // Подождем немного
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Теперь создадим новые темы с современными цветами
                const newThemes = {
                    'Light Theme': {
                        '--wsf-bg': '#FFFFFF',
                        '--wsf-surface': '#FAFBFC',
                        '--wsf-surface-hover': '#F1F3F5',
                        '--wsf-text': '#1A1D23',
                        '--wsf-text-primary': '#1A1D23',
                        '--wsf-text-secondary': '#6B7280',
                        '--wsf-text-muted': '#9CA3AF',
                        '--wsf-primary': '#2563EB',
                        '--wsf-hover': '#1D4ED8',
                        '--wsf-accent': '#0EA5E9',
                        '--wsf-on-primary': '#FFFFFF',
                        '--wsf-border': '#E5E7EB',
                        '--wsf-secondary': '#D1D5DB',
                        '--wsf-muted': '#F3F4F6',
                        '--wsf-input-bg': '#FFFFFF',
                        '--wsf-input-text': '#1A1D23',
                        '--wsf-input-border': '#D1D5DB',
                        '--wsf-input-placeholder': '#9CA3AF',
                        '--wsf-input-focus': '#2563EB',
                        '--wsf-success': '#059669',
                        '--wsf-warning': '#D97706',
                        '--wsf-error': '#DC2626',
                        '--wsf-focus-ring': 'rgba(37, 99, 235, 0.2)'
                    },
                    'Dark Theme': {
                        '--wsf-bg': '#0B1120',
                        '--wsf-surface': '#1A1F2E',
                        '--wsf-surface-hover': '#252A3A',
                        '--wsf-text': '#F8FAFC',
                        '--wsf-text-primary': '#F8FAFC',
                        '--wsf-text-secondary': '#CBD5E1',
                        '--wsf-text-muted': '#94A3B8',
                        '--wsf-primary': '#3B82F6',
                        '--wsf-hover': '#60A5FA',
                        '--wsf-accent': '#06B6D4',
                        '--wsf-on-primary': '#FFFFFF',
                        '--wsf-border': '#374151',
                        '--wsf-secondary': '#4B5563',
                        '--wsf-muted': '#1F2937',
                        '--wsf-input-bg': '#1A1F2E',
                        '--wsf-input-text': '#F8FAFC',
                        '--wsf-input-border': '#374151',
                        '--wsf-input-placeholder': '#9CA3AF',
                        '--wsf-input-focus': '#3B82F6',
                        '--wsf-success': '#10B981',
                        '--wsf-warning': '#F59E0B',
                        '--wsf-error': '#EF4444',
                        '--wsf-focus-ring': 'rgba(59, 130, 246, 0.4)'
                    }
                };
                
                for (const [themeName, properties] of Object.entries(newThemes)) {
                    try {
                        log(`Создание темы: ${themeName}...`);
                        
                        const createResponse = await fetch(apiBase, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-WP-Nonce': nonce
                            },
                            body: JSON.stringify({
                                name: themeName,
                                properties: properties
                            })
                        });
                        
                        if (createResponse.ok) {
                            const result = await createResponse.json();
                            log(`✅ Создана тема "${themeName}" с slug: ${result.slug}`);
                        } else {
                            const errorText = await createResponse.text();
                            log(`❌ Ошибка создания темы "${themeName}": ${errorText}`);
                        }
                        
                    } catch (e) {
                        log(`❌ Исключение при создании темы "${themeName}": ${e.message}`);
                    }
                }
                
                showStatus('✅ Дефолтные темы успешно обновлены с современными цветами!', 'success');
                log('Обновление завершено!');
                
                // Автоматически проверим результат
                setTimeout(checkCurrentThemes, 1000);
                
            } catch (error) {
                log(`Критическая ошибка: ${error.message}`);
                showStatus(`❌ Критическая ошибка обновления: ${error.message}`, 'error');
            }
        }

        async function testThemes() {
            log('Тестирование тем...');
            
            try {
                const response = await fetch(apiBase);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                const themes = data.themes || {};
                
                for (const [slug, theme] of Object.entries(themes)) {
                    log(`Тестирование темы: ${slug}`);
                    
                    // Тест активации
                    try {
                        const activateResponse = await fetch(`${apiBase}/active`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-WP-Nonce': nonce
                            },
                            body: JSON.stringify({ slug })
                        });
                        
                        if (activateResponse.ok) {
                            log(`  ✅ Активация темы ${slug}: успешно`);
                        } else {
                            log(`  ❌ Активация темы ${slug}: ошибка`);
                        }
                    } catch (e) {
                        log(`  ❌ Ошибка активации темы ${slug}: ${e.message}`);
                    }
                }
                
                showStatus('✅ Тестирование тем завершено', 'success');
                
            } catch (error) {
                log(`Ошибка тестирования: ${error.message}`);
                showStatus(`❌ Ошибка тестирования: ${error.message}`, 'error');
            }
        }

        // Автоматическая проверка при загрузке
        document.addEventListener('DOMContentLoaded', () => {
            log('Инструмент обновления тем загружен');
            checkCurrentThemes();
        });
    </script>
</body>
</html>
