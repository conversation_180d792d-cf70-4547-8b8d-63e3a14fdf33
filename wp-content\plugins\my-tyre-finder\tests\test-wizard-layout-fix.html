<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wizard Layout Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        
        .before-section {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after-section {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .widget-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* Wizard styles simulation */
        .wheel-fit-widget {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }
        
        .wsf-widget__header {
            margin-bottom: 24px;
        }
        
        .wsf-widget__title {
            text-align: center;
            font-size: 24px;
            font-weight: 800;
            color: #2563eb;
            margin: 0;
        }
        
        #wizard-header {
            margin-bottom: 24px;
        }
        
        .wizard-steps {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            font-weight: 600;
            color: #9ca3af;
            margin-bottom: 8px;
        }
        
        .wizard-step-name {
            color: #2563eb;
        }
        
        .wizard-step-name.active {
            color: #2563eb;
        }
        
        .progress-bar {
            background: #f3f4f6;
            border-radius: 9999px;
            height: 6px;
            margin-top: 8px;
        }
        
        .progress-fill {
            background: #2563eb;
            height: 6px;
            border-radius: 9999px;
            width: 12.5%;
            transition: width 0.5s ease;
        }
        
        .wsf-form-wrapper {
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .wizard-content {
            min-height: 200px;
            padding: 20px;
            text-align: center;
            color: #6b7280;
        }
        
        .brand-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 12px;
            margin-top: 20px;
        }
        
        .brand-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
            font-size: 12px;
            color: #374151;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">🧙‍♂️ Wizard Layout Fix Test</h1>
            <p style="color: #6b7280; font-size: 18px;">Тест изменения лейаута wizard формы: заголовок вверх, стадии под заголовком</p>
        </header>

        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h3 style="margin-top: 0; color: #92400e;">📋 Задача</h3>
            <p><strong>Требование:</strong> Перенести заголовок "Wheel & Tyre Finder" в верх формы, а стадии (Make, Model, Year, Modification, Wheel Options) поднять чуть выше - они должны быть под заголовком.</p>
            <p><strong>Файлы изменены:</strong></p>
            <ul>
                <li>✅ <code>templates/wizard-flow.twig</code> - создан новый шаблон с правильной структурой</li>
                <li>✅ <code>templates/finder-wizard.twig</code> - обновлена структура</li>
                <li>✅ <code>src/frontend/FormRenderer.php</code> - добавлена передача widget_title</li>
            </ul>
        </div>

        <div class="comparison-grid">
            <!-- Before -->
            <div class="comparison-section before-section">
                <h2 style="color: #dc2626; margin-top: 0;">❌ До изменений</h2>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <!-- Стадии были вверху -->
                        <div id="wizard-header">
                            <div class="wizard-steps">
                                <div class="wizard-step-name active">Make</div>
                                <div class="wizard-step-name">Model</div>
                                <div class="wizard-step-name">Year</div>
                                <div class="wizard-step-name">Modification</div>
                                <div class="wizard-step-name">Wheel Options</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                        
                        <div class="wsf-form-wrapper">
                            <!-- Заголовок был внутри формы -->
                            <div class="wsf-widget__header">
                                <h1 class="wsf-widget__title">Wheel & Tyre Finder</h1>
                            </div>
                            
                            <div class="wizard-content">
                                <h2 style="color: #2563eb; margin-bottom: 20px;">Select Manufacturer</h2>
                                <div class="brand-grid">
                                    <div class="brand-item">BMW</div>
                                    <div class="brand-item">Audi</div>
                                    <div class="brand-item">Mercedes</div>
                                    <div class="brand-item">Toyota</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #7f1d1d; margin-top: 15px; font-size: 14px;">
                    <strong>Проблема:</strong> Заголовок находился внутри формы, стадии были выше заголовка
                </p>
            </div>

            <!-- After -->
            <div class="comparison-section after-section">
                <h2 style="color: #059669; margin-top: 0;">✅ После изменений</h2>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <!-- Заголовок теперь вверху -->
                        <div class="wsf-widget__header">
                            <h1 class="wsf-widget__title">Wheel & Tyre Finder</h1>
                        </div>
                        
                        <!-- Стадии под заголовком -->
                        <div id="wizard-header">
                            <div class="wizard-steps">
                                <div class="wizard-step-name active">Make</div>
                                <div class="wizard-step-name">Model</div>
                                <div class="wizard-step-name">Year</div>
                                <div class="wizard-step-name">Modification</div>
                                <div class="wizard-step-name">Wheel Options</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                        
                        <div class="wsf-form-wrapper">
                            <div class="wizard-content">
                                <h2 style="color: #2563eb; margin-bottom: 20px;">Select Manufacturer</h2>
                                <div class="brand-grid">
                                    <div class="brand-item">BMW</div>
                                    <div class="brand-item">Audi</div>
                                    <div class="brand-item">Mercedes</div>
                                    <div class="brand-item">Toyota</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #065f46; margin-top: 15px; font-size: 14px;">
                    <strong>Решение:</strong> Заголовок перенесен в самый верх, стадии находятся под заголовком
                </p>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f0fdf4; border: 1px solid #16a34a; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #15803d;">✅ Результат изменений</h3>
            <p><strong>Новая структура wizard формы:</strong></p>
            <ol>
                <li><strong>Заголовок "Wheel & Tyre Finder"</strong> - в самом верху формы</li>
                <li><strong>Стадии (Make, Model, Year, Modification, Wheel Options)</strong> - под заголовком</li>
                <li><strong>Прогресс-бар</strong> - под стадиями</li>
                <li><strong>Контент формы</strong> - в нижней части</li>
            </ol>
            
            <p><strong>Технические изменения:</strong></p>
            <ul>
                <li>✅ Создан новый шаблон <code>wizard-flow.twig</code> с правильной структурой</li>
                <li>✅ Обновлен <code>FormRenderer.php</code> для передачи <code>widget_title</code></li>
                <li>✅ Заголовок вынесен из <code>wsf-form-wrapper</code> на уровень выше</li>
                <li>✅ Стадии перемещены под заголовок</li>
                <li>✅ Сохранена совместимость с переводами через <code>data-i18n</code></li>
            </ul>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #eff6ff; border: 1px solid #3b82f6; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1d4ed8;">🔧 Для тестирования</h3>
            <p>Чтобы увидеть изменения в действии:</p>
            <ol>
                <li>Перейдите в админку WordPress → Wheel-Size → Appearance</li>
                <li>Установите <strong>Form Layout: Wizard</strong></li>
                <li>Проверьте Live Preview - заголовок должен быть вверху, стадии под ним</li>
                <li>Проверьте на фронтенде сайта - структура должна быть такой же</li>
            </ol>
        </div>
    </div>
</body>
</html>
