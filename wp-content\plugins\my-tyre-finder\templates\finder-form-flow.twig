{# Dynamic Flow Form Template #}
{% set label_keys = {
  'make': 'label_make',
  'model': 'label_model',
  'year': 'label_year',
  'gen':  'label_generation',
  'mod':  'label_mods'
}%}
{% set label_fallback = {
  'make':'Make',
  'model':'Model',
  'year':'Year',
  'gen':'Generation',
  'mod':'Modification'
}%}
{% set placeholders = {
  'make': 'select_make_placeholder',
  'model': 'select_model_placeholder',
  'year': 'select_year_placeholder',
  'gen':  'select_gen_placeholder',
  'mod':  'select_mods_placeholder'
}%}
{% set id_map = {
  'make':'make',
  'model':'model',
  'year':'year',
  'gen':'generation',
  'mod':'modification'
}%}
<div class="wheel-fit-widget bg-wsf-bg wsf-root-font max-w-4xl mx-auto p-4 md:p-0{% if garage_enabled %} garage-enabled{% endif %}" data-flow-order="{{ flow_order_json|e }}">
  <div class="wsf-form-wrapper">
    {# Widget Title - FIRST in form wrapper #}
    <div class="wsf-widget__header mb-6">
      <h1 class="wsf-widget__title text-center text-2xl md:text-3xl font-extrabold tracking-tight text-wsf-text m-0" data-i18n="widget_title">{{ widget_title|e }}</h1>
    </div>

    {# Progress Indicator - MOVED INSIDE FORM WRAPPER, BELOW TITLE #}
    <div class="flex items-center gap-3 mb-8 px-0 font-semibold">
      <div class="flex items-center gap-2 md:gap-4 w-full">
        {% for step in flow_order %}
          <div class="flex items-center gap-2">
            <div id="step-indicator-{{ loop.index }}" class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold transition-colors">{{ loop.index }}</div>
            <span id="step-text-{{ loop.index }}" class="text-xs md:text-sm font-semibold transition-colors" data-i18n="{{ label_keys[step] }}">{{ label_fallback[step] }}</span>
          </div>
          {% if not loop.last %}<div id="progress-{{ loop.index }}" class="h-px flex-1 transition-colors"></div>{% endif %}
        {% endfor %}
      </div>
    </div>

    <form id="wheel-fit-form" class="space-y-6">
      {# Step Containers #}
      {% for step in flow_order %}
        <div id="step-{{ loop.index }}" class="step-container {% if loop.index>1 %}hidden{% endif %}">
          <label for="wf-{{ id_map[step] }}" class="block text-sm font-semibold text-wsf-text uppercase tracking-wide mb-2" data-i18n="{{ label_keys[step] }}">{{ label_fallback[step] }}</label>
          <div class="relative w-full">
            <select id="wf-{{ id_map[step] }}" name="{{ id_map[step] }}" class="wsf-input block w-full" required {% if loop.index>1 %}disabled{% endif %}>
              <option value="" data-i18n="{{ placeholders[step] }}">{{ placeholders[step] }}</option>
            </select>
            <div id="{{ id_map[step] }}-loader" class="hidden absolute right-4 top-1/2 -translate-y-1/2"><div class="animate-spin rounded-full h-5 w-5 border-b border-wsf-border border-wsf-border-2 border-wsf-primary"></div></div>
          </div>
        </div>
      {% endfor %}

      {% if not auto_search %}
        <div class="mt-6 text-center">
          <button type="submit" class="btn-primary w-full py-3 px-10" disabled>
            <span id="search-text" data-i18n="button_search">Find Tire & Wheel Sizes</span>
            <div id="search-loader" class="hidden inline-block ml-2"><div class="animate-spin rounded-full h-4 w-4 border-b border-wsf-border border-wsf-border-2 border-white"></div></div>
          </button>

          {# Garage Button - positioned in same container as search button for tight visual connection #}
          {% if garage_enabled %}
          <div class="flex justify-end mt-2">
            <button type="button" data-garage-trigger class="inline-flex items-center gap-2 text-sm text-wsf-text px-3 py-1.5 rounded-md hover:bg-wsf-surface hover:text-wsf-text transition">
              <i data-lucide="car" class="w-5 h-5"></i>
              <span class="font-semibold" data-i18n="label_garage">Garage</span>
              <span id="garage-count" class="wsf-garage-count-badge hidden"></span>
            </button>
          </div>
          {% endif %}
        </div>
      {% endif %}

      {# Garage Button for auto_search mode - positioned below form fields, aligned right #}
      {% if auto_search and garage_enabled %}
      <div class="mt-6 flex justify-end">
        <button type="button" data-garage-trigger class="inline-flex items-center gap-2 text-sm text-wsf-text px-3 py-1.5 rounded-md hover:bg-wsf-surface hover:text-wsf-text transition">
          <i data-lucide="car" class="w-5 h-5"></i>
          <span class="font-semibold" data-i18n="label_garage">Garage</span>
          <span id="garage-count" class="wsf-garage-count-badge hidden"></span>
        </button>
      </div>
      {% endif %}
    </form>
  </div>

  {# Results Section (copied from classic template) #}
  <div id="tire-search-results" class="mt-8"></div>

  <div class="max-w-4xl mx-auto">
    <section id="search-results" class="hidden mt-12 bg-wsf-bg shadow-lg rounded-xl p-6 md:p-8 transform transition-all duration-300 ease-in-out">
      <h2 class="text-lg font-semibold text-gray-900 mb-1" data-i18n="section_results">Search Results</h2>
      <p id="vehicle-label" class="text-base font-medium text-gray-900"></p>
      <div id="selected-modification-info" class="mt-1"></div>

      <div class="border-t border-wsf-border my-4"></div>

      {# Factory block #}
      <div id="factory-section" class="mb-8 hidden">
        <h3 class="text-sm font-bold text-gray-900 mb-3" data-i18n="section_factory">Factory Sizes</h3>
        <div id="factory-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
      </div>

      {# Optional block #}
      {% if not enable_oe_filter %}
      <div id="optional-section" class="mb-8 hidden">
        <h3 class="text-sm font-bold text-gray-900 mb-3" data-i18n="section_optional">Optional Sizes</h3>
        <div id="optional-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
      </div>
      {% endif %}

      {# No Results Message #}
      <div id="no-results" class="hidden text-center py-8">
        <h3 class="text-lg font-medium text-gray-900 mb-2" data-i18n="text_no_results_header">Sizes not found</h3>
        <p class="text-wsf-muted" data-i18n="text_no_results_body">Try selecting another modification or check your selection.</p>
      </div>
    </section>
  </div>

  {# Saved Searches are intentionally omitted in Flow template #}

</div>

{# Garage Feature Components #}
{% if garage_enabled %}
<div id="garage-overlay" class="hidden fixed inset-0 z-40 bg-black/25 backdrop-blur-md transition-opacity cursor-pointer"></div>
<aside id="garage-drawer" class="fixed right-0 top-0 h-full w-full max-w-sm bg-wsf-bg shadow-2xl transform translate-x-full transition-transform duration-300 z-50 flex flex-col" style="top: var(--wp-admin--admin-bar--height, 0); height: calc(100% - var(--wp-admin--admin-bar--height, 0));">
  <header class="flex items-center justify-between p-4 border-b border-wsf-border border-wsf-border border-wsf-border">
    <h2 class="text-xl font-bold text-wsf-text" data-i18n="garage_title">My Garage</h2>
    <button id="garage-close-btn" aria-label="Close" class="text-wsf-muted hover:text-wsf-text">
      <svg viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
        <line x1="18" y1="6" x2="6" y2="18"/>
        <line x1="6" y1="6" x2="18" y2="18"/>
      </svg>
    </button>
  </header>
  <ul id="garage-items-list" class="flex-1 min-h-0 overflow-y-auto p-4 space-y-4"></ul>
  <footer id="garage-footer" class="sticky bottom-4 flex-shrink-0 p-4 flex justify-end">
    <button id="garage-clear-all" class="btn-secondary">
      <i data-lucide="trash-2"></i>
      <span data-i18n="garage_clear_all">Clear&nbsp;all</span>
    </button>
  </footer>
</aside>
<div id="garage-toast" class="fixed left-1/2 -translate-x-1/2 bottom-6 bg-wsf-primary text-white text-sm font-medium px-4 py-2 rounded-lg shadow-lg transition opacity-0 translate-y-4 z-50">
  <span data-i18n="garage_saved_notification">Saved to Garage!</span>
</div>
{% endif %}