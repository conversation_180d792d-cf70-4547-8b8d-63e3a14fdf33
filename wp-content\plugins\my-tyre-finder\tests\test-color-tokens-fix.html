<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Tokens Fix Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .comparison-item {
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .comparison-item h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .before {
            background: #fff5f5;
            border-color: #fed7d7;
        }
        
        .after {
            background: #f0fff4;
            border-color: #c6f6d5;
        }
        
        .demo-widget {
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .demo-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .demo-field {
            display: flex;
            flex-direction: column;
        }
        
        .demo-label {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 5px;
        }
        
        .demo-select {
            height: 44px;
            padding: 0 12px;
            border: 1px solid;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .demo-button {
            height: 44px;
            padding: 0 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .demo-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* Before styles (incorrect) */
        .before-widget {
            background: #ffffff;
            color: #1f2937;
        }
        
        .before-widget .demo-label {
            color: #9ca3af; /* muted instead of text */
        }
        
        .before-widget .demo-select {
            background: #ffffff; /* widget bg instead of input bg */
            color: #1f2937;
            border-color: #e5e7eb;
        }
        
        .before-widget .demo-button {
            background: #ffffff; /* white background issue */
            color: #2563eb;
            border: 1px solid #2563eb;
        }
        
        /* After styles (correct) */
        .after-widget {
            background: var(--wsf-bg);
            color: var(--wsf-text-primary);
        }
        
        .after-widget .demo-label {
            color: var(--wsf-text);
        }
        
        .after-widget .demo-select {
            background: var(--wsf-input-bg);
            color: var(--wsf-input-text);
            border-color: var(--wsf-input-border);
        }
        
        .after-widget .demo-select:focus {
            border-color: var(--wsf-input-focus);
            box-shadow: 0 0 0 1px var(--wsf-input-focus);
            outline: none;
        }
        
        .after-widget .demo-button {
            background: var(--wsf-primary) !important;
            color: var(--wsf-text-inverse) !important;
        }
        
        .after-widget .demo-button:hover:not(:disabled) {
            background: var(--wsf-hover) !important;
        }
        
        .status {
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .fix-list {
            list-style: none;
            padding: 0;
        }
        
        .fix-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .fix-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        
        .theme-switcher {
            margin: 20px 0;
            text-align: center;
        }
        
        .theme-switcher button {
            margin: 0 10px;
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .theme-switcher button:hover {
            background: #f3f4f6;
        }
        
        .theme-switcher button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Color Tokens Fix Test</h1>
        <p>Демонстрация исправленного поведения цветовых токенов Background, Text и Primary</p>
        
        <div class="theme-switcher">
            <button onclick="setTheme('light')" class="active" id="light-btn">Light Theme</button>
            <button onclick="setTheme('dark')" id="dark-btn">Dark Theme</button>
        </div>
        
        <!-- Problem & Solution Overview -->
        <div class="test-section">
            <h3>Исправленные проблемы</h3>
            <ul class="fix-list">
                <li><strong>Background:</strong> Селекты теперь используют --wsf-input-bg вместо --wsf-bg</li>
                <li><strong>Text:</strong> Лейблы полей используют --wsf-text вместо --wsf-muted</li>
                <li><strong>Primary:</strong> Кнопки имеют !important правила для переопределения белой подложки</li>
                <li><strong>Widget Background:</strong> Добавлен фон для корневого блока виджета</li>
            </ul>
        </div>
        
        <!-- Before vs After Comparison -->
        <div class="test-section">
            <h3>Сравнение "До" и "После"</h3>
            <div class="comparison-grid">
                <div class="comparison-item before">
                    <h4>❌ До исправлений</h4>
                    <div class="demo-widget before-widget">
                        <div class="demo-form">
                            <div class="demo-field">
                                <label class="demo-label">Make</label>
                                <select class="demo-select">
                                    <option>BMW</option>
                                    <option>Audi</option>
                                </select>
                            </div>
                            <div class="demo-field">
                                <label class="demo-label">Model</label>
                                <select class="demo-select">
                                    <option>X5</option>
                                    <option>X3</option>
                                </select>
                            </div>
                            <button class="demo-button">Find Sizes</button>
                        </div>
                    </div>
                    <p><small>
                        • Селекты используют фон виджета<br>
                        • Лейблы серые (muted)<br>
                        • Кнопка с белой подложкой
                    </small></p>
                </div>
                
                <div class="comparison-item after">
                    <h4>✅ После исправлений</h4>
                    <div class="demo-widget after-widget wsf-finder-widget" data-wsf-theme="light">
                        <div class="demo-form">
                            <div class="demo-field">
                                <label class="demo-label">Make</label>
                                <select class="demo-select">
                                    <option>BMW</option>
                                    <option>Audi</option>
                                </select>
                            </div>
                            <div class="demo-field">
                                <label class="demo-label">Model</label>
                                <select class="demo-select">
                                    <option>X5</option>
                                    <option>X3</option>
                                </select>
                            </div>
                            <button class="demo-button">Find Sizes</button>
                        </div>
                    </div>
                    <p><small>
                        • Селекты используют input-bg<br>
                        • Лейблы используют text цвет<br>
                        • Кнопка с правильным primary цветом
                    </small></p>
                </div>
            </div>
        </div>
        
        <!-- Real Widget Test -->
        <div class="test-section">
            <h3>Тест с реальным виджетом</h3>
            <div class="wsf-finder-widget" id="test-widget" data-wsf-theme="light">
                <form class="space-y-4 p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="flex flex-col">
                            <label class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1">Make</label>
                            <select class="wsf-input block w-full">
                                <option value="">Select make...</option>
                                <option value="bmw">BMW</option>
                                <option value="audi">Audi</option>
                                <option value="mercedes">Mercedes-Benz</option>
                            </select>
                        </div>
                        <div class="flex flex-col">
                            <label class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1">Model</label>
                            <select class="wsf-input block w-full" disabled>
                                <option value="">Select make first</option>
                            </select>
                        </div>
                        <div class="flex flex-col">
                            <label class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1">Year</label>
                            <select class="wsf-input block w-full" disabled>
                                <option value="">Select model first</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex justify-center mt-6">
                        <button type="submit" class="btn-primary" disabled>Find Sizes</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- CSS Variables Display -->
        <div class="test-section">
            <h3>Текущие значения CSS переменных</h3>
            <div id="css-variables-display">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h4>Widget Variables:</h4>
                        <ul>
                            <li><code>--wsf-bg:</code> <span id="var-bg"></span></li>
                            <li><code>--wsf-text:</code> <span id="var-text"></span></li>
                            <li><code>--wsf-primary:</code> <span id="var-primary"></span></li>
                        </ul>
                    </div>
                    <div>
                        <h4>Input Variables:</h4>
                        <ul>
                            <li><code>--wsf-input-bg:</code> <span id="var-input-bg"></span></li>
                            <li><code>--wsf-input-text:</code> <span id="var-input-text"></span></li>
                            <li><code>--wsf-input-border:</code> <span id="var-input-border"></span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status success">
            ✅ Все исправления применены! Токены Background, Text и Primary теперь работают корректно.
        </div>
    </div>
    
    <script>
        function setTheme(theme) {
            const widget = document.getElementById('test-widget');
            const afterWidget = document.querySelector('.after-widget');
            const lightBtn = document.getElementById('light-btn');
            const darkBtn = document.getElementById('dark-btn');
            
            // Update widget themes
            widget.setAttribute('data-wsf-theme', theme);
            afterWidget.setAttribute('data-wsf-theme', theme);
            
            if (theme === 'dark') {
                widget.classList.add('wsf-theme-dark');
                afterWidget.classList.add('wsf-theme-dark');
            } else {
                widget.classList.remove('wsf-theme-dark');
                afterWidget.classList.remove('wsf-theme-dark');
            }
            
            // Update button states
            lightBtn.classList.toggle('active', theme === 'light');
            darkBtn.classList.toggle('active', theme === 'dark');
            
            // Update CSS variables display
            updateCSSVariablesDisplay();
        }
        
        function updateCSSVariablesDisplay() {
            const widget = document.getElementById('test-widget');
            const computedStyle = getComputedStyle(widget);
            
            document.getElementById('var-bg').textContent = computedStyle.getPropertyValue('--wsf-bg').trim();
            document.getElementById('var-text').textContent = computedStyle.getPropertyValue('--wsf-text').trim();
            document.getElementById('var-primary').textContent = computedStyle.getPropertyValue('--wsf-primary').trim();
            document.getElementById('var-input-bg').textContent = computedStyle.getPropertyValue('--wsf-input-bg').trim();
            document.getElementById('var-input-text').textContent = computedStyle.getPropertyValue('--wsf-input-text').trim();
            document.getElementById('var-input-border').textContent = computedStyle.getPropertyValue('--wsf-input-border').trim();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCSSVariablesDisplay();
            
            // Test button enabling
            const makeSelect = document.querySelector('#test-widget select');
            const button = document.querySelector('#test-widget button');
            
            makeSelect.addEventListener('change', function() {
                if (this.value) {
                    button.disabled = false;
                    button.textContent = 'Find Sizes (Enabled)';
                } else {
                    button.disabled = true;
                    button.textContent = 'Find Sizes';
                }
            });
        });
    </script>
</body>
</html>
