<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Color Tokens Reference - Layout Fix Test</title>
    
    <!-- Подключаем CSS файлы -->
    <link rel="stylesheet" href="../assets/css/admin-theme-panel.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #111827;
            font-size: 24px;
            font-weight: 600;
        }
        
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .controls button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .controls button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        /* Имитация модального окна Theme Editor */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 20px;
        }
        
        .modal-overlay.hidden {
            display: none;
        }
        
        /* Имитация wsf-theme-editor */
        .wsf-theme-editor {
            background: white;
            border-radius: 12px;
            width: 680px;
            max-width: 95vw;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            position: relative;
        }
        
        .wsf-theme-editor__header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .wsf-theme-editor__title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
        }
        
        .modal-close:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        /* Имитация wsf-theme-editor__content */
        .wsf-theme-editor__content {
            /* Стили будут применены через CSS патч */
        }
        
        .theme-form {
            padding: 20px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .help-button {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            color: #374151;
            margin-top: 12px;
        }
        
        .help-button:hover {
            background: #e5e7eb;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            z-index: 1001;
            transition: all 0.3s ease;
        }
        
        .status-indicator.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .status-indicator.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .status-indicator.hidden {
            opacity: 0;
            transform: translateY(-20px);
        }
        
        @media (max-width: 768px) {
            .test-container {
                padding: 20px;
            }
            
            .wsf-theme-editor {
                width: 100%;
                margin: 10px;
            }
            
            .modal-overlay {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Modal Color Tokens Reference - Layout Fix Test</h1>
        <p>Тест исправления проблем с layout окна "Color Tokens Reference" в модальном контексте.</p>
        
        <!-- Контролы -->
        <div class="controls">
            <h3>🎛️ Контролы тестирования</h3>
            <button onclick="openModal()" class="active">🔍 Открыть модалку с токенами</button>
            <button onclick="testMobileView()">📱 Мобильный вид</button>
            <button onclick="testDesktopView()">🖥️ Десктоп вид</button>
            <button onclick="testScrollBehavior()">📜 Тест прокрутки</button>
            <button onclick="runVerification()">✅ Проверить исправления</button>
        </div>
        
        <!-- Описание проблем и решений -->
        <div class="demo-section">
            <h2>🐞 Проблемы до исправления</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>❌ CSS Specificity Issues</h3>
                    <ul>
                        <li>Новые стили имели ту же специфичность</li>
                        <li>Tailwind @layer components vs utilities</li>
                        <li>Старые правила переопределяли новые</li>
                    </ul>
                </div>
                <div>
                    <h3>❌ Flex Container Issues</h3>
                    <ul>
                        <li>Отсутствие min-height: 0</li>
                        <li>Множественные области прокрутки</li>
                        <li>Контент растягивал модалку</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>✅ Примененные исправления</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>🎯 Единая область прокрутки</h3>
                    <ul>
                        <li><code>.wsf-theme-editor</code>: flex column, max-height: 90vh</li>
                        <li><code>.wsf-theme-editor__content</code>: flex: 1, min-height: 0</li>
                        <li>Только одна вертикальная прокрутка</li>
                    </ul>
                </div>
                <div>
                    <h3>🔧 Высокая специфичность</h3>
                    <ul>
                        <li>Дублированные селекторы для специфичности</li>
                        <li>!important для критических правил</li>
                        <li>@layer utilities в конце файла</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Инструкции по проверке -->
        <div class="demo-section">
            <h2>📋 Инструкции по проверке</h2>
            <ol>
                <li><strong>Откройте модалку</strong> - нажмите "Открыть модалку с токенами"</li>
                <li><strong>Проверьте прокрутку</strong> - должна быть только одна область прокрутки</li>
                <li><strong>Проверьте границы</strong> - последняя строка токенов должна быть видна</li>
                <li><strong>Проверьте адаптивность</strong> - протестируйте мобильный вид</li>
                <li><strong>Проверьте границы</strong> - не должно быть левой полосы/границы</li>
            </ol>
        </div>
    </div>
    
    <!-- Модальное окно -->
    <div class="modal-overlay hidden" id="modal-overlay" onclick="closeModal(event)">
        <div class="wsf-theme-editor" onclick="event.stopPropagation()">
            <div class="wsf-theme-editor__header">
                <h3 class="wsf-theme-editor__title">🎨 Theme Editor</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="wsf-theme-editor__content">
                <div class="theme-form">
                    <div class="form-group">
                        <label for="theme-name">Theme Name</label>
                        <input type="text" id="theme-name" value="Custom Theme" />
                    </div>
                    
                    <div class="form-group">
                        <label for="primary-color">Primary Color</label>
                        <input type="color" id="primary-color" value="#2563eb" />
                    </div>
                    
                    <div class="form-group">
                        <label for="background-color">Background Color</label>
                        <input type="color" id="background-color" value="#ffffff" />
                    </div>
                    
                    <button class="help-button" onclick="toggleColorTokens()">
                        📚 Show Color Tokens Reference
                    </button>
                </div>
                
                <!-- Color Tokens Reference -->
                <div class="wsf-cheat-sheet" id="color-tokens" style="display: none;">
                    <div class="wsf-cheat-sheet__header">
                        <h4 class="wsf-cheat-sheet__title">🎨 Color Tokens Reference</h4>
                        <button type="button" class="wsf-cheat-sheet__toggle" onclick="toggleTokensContent()">
                            <span class="wsf-cheat-sheet__toggle-text">Show Details</span>
                            <svg class="wsf-cheat-sheet__toggle-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="6,9 12,15 18,9"></polyline>
                            </svg>
                        </button>
                    </div>
                    <div class="wsf-cheat-sheet__content" id="tokens-content" style="display: none;">
                        <div class="wsf-cheat-sheet__table-wrapper">
                            <table class="wsf-cheat-sheet__table">
                                <thead>
                                    <tr>
                                        <th>Token</th>
                                        <th>Description</th>
                                        <th>CSS Variables</th>
                                        <th>Examples</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="wsf-cheat-sheet__row">
                                        <td class="wsf-cheat-sheet__token">Background</td>
                                        <td class="wsf-cheat-sheet__description">Main widget background color</td>
                                        <td class="wsf-cheat-sheet__vars">
                                            <div class="wsf-cheat-sheet__var-item">
                                                <code class="wsf-cheat-sheet__code">--wsf-bg</code>
                                                <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-bg')">📋</button>
                                            </div>
                                        </td>
                                        <td class="wsf-cheat-sheet__example">Widget container, form wrapper</td>
                                    </tr>
                                    <tr class="wsf-cheat-sheet__row">
                                        <td class="wsf-cheat-sheet__token">Primary</td>
                                        <td class="wsf-cheat-sheet__description">Primary brand color for buttons and accents</td>
                                        <td class="wsf-cheat-sheet__vars">
                                            <div class="wsf-cheat-sheet__var-item">
                                                <code class="wsf-cheat-sheet__code">--wsf-primary</code>
                                                <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-primary')">📋</button>
                                            </div>
                                        </td>
                                        <td class="wsf-cheat-sheet__example">Search button, links, focus states</td>
                                    </tr>
                                    <tr class="wsf-cheat-sheet__row">
                                        <td class="wsf-cheat-sheet__token">Text</td>
                                        <td class="wsf-cheat-sheet__description">Primary text color for content</td>
                                        <td class="wsf-cheat-sheet__vars">
                                            <div class="wsf-cheat-sheet__var-item">
                                                <code class="wsf-cheat-sheet__code">--wsf-text</code>
                                                <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-text')">📋</button>
                                            </div>
                                        </td>
                                        <td class="wsf-cheat-sheet__example">Labels, headings, body text</td>
                                    </tr>
                                    <tr class="wsf-cheat-sheet__row">
                                        <td class="wsf-cheat-sheet__token">Border</td>
                                        <td class="wsf-cheat-sheet__description">Border color for form elements</td>
                                        <td class="wsf-cheat-sheet__vars">
                                            <div class="wsf-cheat-sheet__var-item">
                                                <code class="wsf-cheat-sheet__code">--wsf-border</code>
                                                <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-border')">📋</button>
                                            </div>
                                        </td>
                                        <td class="wsf-cheat-sheet__example">Input borders, dividers</td>
                                    </tr>
                                    <tr class="wsf-cheat-sheet__row">
                                        <td class="wsf-cheat-sheet__token">Surface</td>
                                        <td class="wsf-cheat-sheet__description">Secondary background for cards and panels</td>
                                        <td class="wsf-cheat-sheet__vars">
                                            <div class="wsf-cheat-sheet__var-item">
                                                <code class="wsf-cheat-sheet__code">--wsf-surface</code>
                                                <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-surface')">📋</button>
                                            </div>
                                        </td>
                                        <td class="wsf-cheat-sheet__example">Cards, panels, overlays</td>
                                    </tr>
                                    <tr class="wsf-cheat-sheet__row">
                                        <td class="wsf-cheat-sheet__token">Muted</td>
                                        <td class="wsf-cheat-sheet__description">Muted text color for secondary content</td>
                                        <td class="wsf-cheat-sheet__vars">
                                            <div class="wsf-cheat-sheet__var-item">
                                                <code class="wsf-cheat-sheet__code">--wsf-muted</code>
                                                <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-muted')">📋</button>
                                            </div>
                                        </td>
                                        <td class="wsf-cheat-sheet__example">Placeholders, help text, captions</td>
                                    </tr>
                                    <tr class="wsf-cheat-sheet__row">
                                        <td class="wsf-cheat-sheet__token">Hover State</td>
                                        <td class="wsf-cheat-sheet__description">Hover state color for interactive elements</td>
                                        <td class="wsf-cheat-sheet__vars">
                                            <div class="wsf-cheat-sheet__var-item">
                                                <code class="wsf-cheat-sheet__code">--wsf-hover</code>
                                                <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-hover')">📋</button>
                                            </div>
                                        </td>
                                        <td class="wsf-cheat-sheet__example">Button hover, link hover</td>
                                    </tr>
                                    <tr class="wsf-cheat-sheet__row">
                                        <td class="wsf-cheat-sheet__token">Input Background</td>
                                        <td class="wsf-cheat-sheet__description">Background color for form inputs</td>
                                        <td class="wsf-cheat-sheet__vars">
                                            <div class="wsf-cheat-sheet__var-item">
                                                <code class="wsf-cheat-sheet__code">--wsf-input-bg</code>
                                                <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-input-bg')">📋</button>
                                            </div>
                                        </td>
                                        <td class="wsf-cheat-sheet__example">Select boxes, text inputs</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Индикатор статуса -->
    <div class="status-indicator hidden" id="status-indicator"></div>
    
    <script>
        let tokensVisible = false;
        let tokensContentVisible = false;
        
        function openModal() {
            document.getElementById('modal-overlay').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(event) {
            if (event && event.target !== event.currentTarget) return;
            document.getElementById('modal-overlay').classList.add('hidden');
            document.body.style.overflow = '';
        }
        
        function toggleColorTokens() {
            const tokens = document.getElementById('color-tokens');
            tokensVisible = !tokensVisible;
            
            if (tokensVisible) {
                tokens.style.display = 'block';
                event.target.textContent = '📚 Hide Color Tokens Reference';
            } else {
                tokens.style.display = 'none';
                tokensContentVisible = false;
                document.getElementById('tokens-content').style.display = 'none';
                event.target.textContent = '📚 Show Color Tokens Reference';
            }
        }
        
        function toggleTokensContent() {
            const content = document.getElementById('tokens-content');
            const toggle = document.querySelector('.wsf-cheat-sheet__toggle-text');
            const icon = document.querySelector('.wsf-cheat-sheet__toggle-icon');
            
            tokensContentVisible = !tokensContentVisible;
            
            if (tokensContentVisible) {
                content.style.display = 'block';
                toggle.textContent = 'Hide Details';
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                toggle.textContent = 'Show Details';
                icon.style.transform = 'rotate(0deg)';
            }
        }
        
        function testMobileView() {
            document.body.style.maxWidth = '375px';
            document.body.style.margin = '0 auto';
            showStatus('📱 Мобильный вид активирован', 'success');
        }
        
        function testDesktopView() {
            document.body.style.maxWidth = 'none';
            document.body.style.margin = '0';
            showStatus('🖥️ Десктоп вид восстановлен', 'success');
        }
        
        function testScrollBehavior() {
            openModal();
            toggleColorTokens();
            setTimeout(() => {
                toggleTokensContent();
                showStatus('📜 Проверьте: должна быть только одна область прокрутки', 'success');
            }, 500);
        }
        
        function runVerification() {
            const checks = [
                checkSingleScrollArea(),
                checkModalBounds(),
                checkTableVisibility(),
                checkBorderRemoval()
            ];
            
            const passed = checks.filter(Boolean).length;
            const total = checks.length;
            
            if (passed === total) {
                showStatus(`✅ Все проверки пройдены (${passed}/${total})`, 'success');
            } else {
                showStatus(`⚠️ Проверки: ${passed}/${total} пройдено`, 'error');
            }
        }
        
        function checkSingleScrollArea() {
            const scrollElements = document.querySelectorAll('[style*="overflow-y: auto"], [style*="overflow: auto"]');
            return scrollElements.length <= 1;
        }
        
        function checkModalBounds() {
            const modal = document.querySelector('.wsf-theme-editor');
            if (!modal) return false;
            
            const rect = modal.getBoundingClientRect();
            return rect.width <= window.innerWidth && rect.height <= window.innerHeight;
        }
        
        function checkTableVisibility() {
            const table = document.querySelector('.wsf-cheat-sheet__table');
            if (!table) return true;
            
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length === 0) return true;
            
            const lastRow = rows[rows.length - 1];
            const rect = lastRow.getBoundingClientRect();
            return rect.bottom <= window.innerHeight;
        }
        
        function checkBorderRemoval() {
            const cheatSheet = document.querySelector('.wsf-cheat-sheet');
            const table = document.querySelector('.wsf-cheat-sheet__table');
            
            if (!cheatSheet || !table) return true;
            
            const cheatSheetStyle = getComputedStyle(cheatSheet);
            const tableStyle = getComputedStyle(table);
            
            return cheatSheetStyle.borderLeftWidth === '0px' && tableStyle.borderLeftWidth === '0px';
        }
        
        function showStatus(message, type) {
            const indicator = document.getElementById('status-indicator');
            indicator.textContent = message;
            indicator.className = `status-indicator ${type}`;
            
            setTimeout(() => {
                indicator.classList.add('hidden');
            }, 3000);
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showStatus(`📋 Скопировано: ${text}`, 'success');
            });
        }
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Modal Color Tokens Reference Layout Fix Test загружен');
            console.log('📝 Откройте модалку и проверьте исправления');
        });
        
        // Закрытие модалки по Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
