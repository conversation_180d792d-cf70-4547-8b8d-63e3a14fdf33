<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Garage Modal Behavior Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-header {
            background: #dc2626;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
        }
        .status-good { color: #059669; }
        .status-bad { color: #dc2626; }
        .status-warning { color: #d97706; }
        .button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #1d4ed8;
        }
        .button.danger {
            background: #dc2626;
        }
        .button.danger:hover {
            background: #b91c1c;
        }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .test-area {
            background: #f3f4f6;
            padding: 20px;
            border-radius: 6px;
            margin: 10px 0;
            min-height: 100px;
        }
        .test-element {
            display: inline-block;
            padding: 8px 16px;
            margin: 5px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        .garage-trigger {
            background: #10b981;
            color: white;
            border-color: #059669;
        }
        /* Mock garage modal styles */
        #garage-overlay {
            position: fixed;
            inset: 0;
            z-index: 40;
            background: rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(4px);
            transition: opacity 0.3s;
        }
        #garage-overlay.hidden {
            display: none;
        }
        #garage-drawer {
            position: fixed;
            right: 0;
            top: 0;
            height: 100%;
            width: 100%;
            max-width: 24rem;
            background: white;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            transform: translateX(100%);
            transition: transform 0.3s;
            z-index: 50;
            display: flex;
            flex-direction: column;
        }
        #garage-drawer:not(.translate-x-full) {
            transform: translateX(0);
        }
        .garage-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        #garage-close-btn {
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            font-size: 1.5rem;
        }
        #garage-close-btn:hover {
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🐞 Garage Modal Behavior Fix Test</h1>
        <p>This page tests the fixes for incorrect garage modal behavior in WordPress admin.</p>
    </div>

    <div class="instructions">
        <h3>📋 Test Instructions</h3>
        <ol>
            <li><strong>Load this page</strong> on a WordPress admin page with the garage feature enabled</li>
            <li><strong>Run the automated test</strong> using the button below</li>
            <li><strong>Manual testing</strong>: Try clicking on different elements in the test area</li>
            <li><strong>Expected behavior</strong>: Only the green "Garage" button should open the modal</li>
        </ol>
    </div>

    <div class="test-container">
        <div class="test-section">
            <h3>🧪 Automated Tests</h3>
            <button class="button" onclick="runGarageModalTest()">Run Garage Modal Test</button>
            <button class="button" onclick="runUnintendedClickTest()">Test Unintended Clicks</button>
            <button class="button" onclick="runOverlayTest()">Test Overlay Behavior</button>
            <button class="button" onclick="clearConsole()">Clear Console</button>
        </div>

        <div class="test-section">
            <h3>🎯 Manual Test Area</h3>
            <p>Click on different elements below. Only the green "Garage" button should open the modal:</p>
            <div class="test-area">
                <div class="test-element">Random Div</div>
                <span class="test-element">Random Span</span>
                <p class="test-element">Paragraph with garage word</p>
                <button class="test-element">Regular Button</button>
                <button class="test-element garage-trigger" data-garage-trigger>Garage</button>
                <div class="test-element">Another div</div>
                <button class="test-element">Another button</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Test Status</h3>
            <div id="test-status">
                <p>Click "Run Garage Modal Test" to see current status...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🖥️ Console Output</h3>
            <div id="console-output" class="console-output">
                Console output will appear here...
            </div>
        </div>
    </div>

    <!-- Mock garage modal elements -->
    <div id="garage-overlay" class="hidden"></div>
    <aside id="garage-drawer" class="translate-x-full">
        <header class="garage-header">
            <h2>My Garage</h2>
            <button id="garage-close-btn">×</button>
        </header>
        <div style="padding: 1rem;">
            <p>Mock garage content for testing</p>
            <div id="garage-items-list"></div>
        </div>
    </aside>

    <script>
        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToPage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            logToPage(args.join(' '), 'log');
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            logToPage(args.join(' '), 'error');
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            logToPage(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        // Mock garage functions for testing
        function openDrawer() {
            console.log('[Mock] Opening garage drawer...');
            const overlay = document.getElementById('garage-overlay');
            const drawer = document.getElementById('garage-drawer');
            
            overlay.classList.remove('hidden');
            drawer.classList.remove('translate-x-full');
        }

        function closeDrawer() {
            console.log('[Mock] Closing garage drawer...');
            const overlay = document.getElementById('garage-overlay');
            const drawer = document.getElementById('garage-drawer');
            
            drawer.classList.add('translate-x-full');
            setTimeout(() => {
                overlay.classList.add('hidden');
            }, 300);
        }

        // Make functions globally available
        window.openDrawer = openDrawer;
        window.closeDrawer = closeDrawer;

        function runGarageModalTest() {
            console.log('Loading garage modal test...');
            loadAndRunScript('test-garage-modal-fix.js');
        }

        function runUnintendedClickTest() {
            console.log('=== Testing Unintended Clicks ===');
            
            const testElements = document.querySelectorAll('.test-area .test-element:not(.garage-trigger)');
            let unintendedTriggers = 0;
            
            const originalOpen = window.openDrawer;
            window.openDrawer = function() {
                unintendedTriggers++;
                console.log('❌ Unintended garage trigger detected!');
                originalOpen();
            };
            
            testElements.forEach((element, index) => {
                console.log(`Testing element ${index + 1}: ${element.textContent}`);
                element.click();
            });
            
            window.openDrawer = originalOpen;
            
            if (unintendedTriggers === 0) {
                console.log('✅ All unintended click tests passed!');
            } else {
                console.log(`❌ ${unintendedTriggers} unintended triggers detected`);
            }
        }

        function runOverlayTest() {
            console.log('=== Testing Overlay Behavior ===');
            
            // Open garage first
            openDrawer();
            
            setTimeout(() => {
                console.log('Testing overlay click...');
                const overlay = document.getElementById('garage-overlay');
                
                let closeTriggered = false;
                const originalClose = window.closeDrawer;
                window.closeDrawer = function() {
                    closeTriggered = true;
                    console.log('✅ Overlay click correctly triggered close');
                    originalClose();
                };
                
                // Simulate overlay click
                overlay.click();
                
                setTimeout(() => {
                    if (!closeTriggered) {
                        console.log('❌ Overlay click did not trigger close');
                    }
                    window.closeDrawer = originalClose;
                }, 100);
                
            }, 500);
        }

        function loadAndRunScript(scriptName) {
            const script = document.createElement('script');
            script.src = scriptName;
            script.onload = function() {
                console.log(`✅ ${scriptName} loaded and executed`);
            };
            script.onerror = function() {
                console.error(`❌ Failed to load ${scriptName}`);
            };
            document.head.appendChild(script);
        }

        // Initialize page
        window.addEventListener('load', function() {
            console.log('🚀 Garage modal test page loaded');
            console.log('Ready to test garage modal fixes...');
            
            // Set up mock event handlers similar to the real garage.js
            document.addEventListener('click', (e) => {
                const garageElement = e.target.closest('[data-garage-trigger]') ||
                                     e.target.closest('.garage-trigger');

                if (garageElement) {
                    console.log('[Mock] Garage trigger clicked:', e.target);
                    e.preventDefault();
                    e.stopPropagation();
                    openDrawer();
                    return;
                }

                // Test the improved text-based detection
                if (e.target.textContent && 
                    e.target.textContent.trim().toLowerCase().includes('garage') &&
                    (e.target.tagName === 'BUTTON' || 
                     e.target.classList.contains('garage-btn') ||
                     e.target.hasAttribute('data-garage') ||
                     e.target.closest('button'))) {
                    
                    console.log('[Mock] Text-based garage trigger clicked:', e.target);
                    e.preventDefault();
                    e.stopPropagation();
                    openDrawer();
                }
            });

            // Set up close handlers
            document.getElementById('garage-close-btn').addEventListener('click', closeDrawer);
            document.getElementById('garage-overlay').addEventListener('click', (e) => {
                if (e.target === document.getElementById('garage-overlay')) {
                    closeDrawer();
                }
            });
        });
    </script>
</body>
</html>
