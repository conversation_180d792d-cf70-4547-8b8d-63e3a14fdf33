<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Tokens Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .color-controls {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .color-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .color-input-group label {
            min-width: 120px;
            font-weight: 500;
        }
        .color-input-group input[type="color"] {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .token-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .token-item {
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .token-name {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .token-usage {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Color Tokens Test</h1>
        <p>Тест правильной работы цветовых токенов согласно дизайн-системе.</p>

        <!-- Color Controls -->
        <div class="color-controls">
            <h3>🎨 Primary Color Control</h3>
            <div class="color-input-group">
                <label for="primary-color">Primary Color:</label>
                <input type="color" id="primary-color" value="#2563eb">
                <span id="primary-value">#2563eb</span>
            </div>
            <p style="font-size: 14px; color: #666; margin-top: 10px;">
                Изменение этого цвета должно обновить все элементы с --wsf-primary токеном.
            </p>
        </div>

        <!-- Token Demonstrations -->
        <div class="test-section">
            <h3>1. --wsf-primary токен</h3>
            <p><strong>Применяется:</strong> фон главных действий, активные индикаторы, прогресс-бар</p>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb); --wsf-hover: var(--test-hover, #1d4ed8);">
                <div class="token-demo">
                    <div class="token-item">
                        <div class="token-name">Кнопка Search</div>
                        <div class="token-usage">btn-primary класс</div>
                        <button class="btn-primary">Find Sizes</button>
                    </div>
                    <div class="token-item">
                        <div class="token-name">Прогресс-бар</div>
                        <div class="token-usage">bg-[color:var(--wsf-primary)]</div>
                        <div class="bg-gray-200 rounded-full h-2">
                            <div class="bg-[color:var(--wsf-primary)] h-2 rounded-full transition-all duration-500" style="width: 60%"></div>
                        </div>
                    </div>
                    <div class="token-item">
                        <div class="token-name">Активный индикатор</div>
                        <div class="token-usage">bg-[color:var(--wsf-primary)]</div>
                        <div class="w-4 h-4 bg-[color:var(--wsf-primary)] rounded-full"></div>
                    </div>
                    <div class="token-item">
                        <div class="token-name">Badge/Counter</div>
                        <div class="token-usage">bg-[color:var(--wsf-primary)]</div>
                        <span class="inline-block px-2 py-1 rounded-full text-xs font-bold text-white bg-[color:var(--wsf-primary)]">3</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. --wsf-hover токен</h3>
            <p><strong>Применяется:</strong> hover состояния для primary элементов (~10% темнее)</p>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb); --wsf-hover: var(--test-hover, #1d4ed8);">
                <div class="token-demo">
                    <div class="token-item">
                        <div class="token-name">Кнопка при hover</div>
                        <div class="token-usage">btn-primary:hover</div>
                        <button class="btn-primary" onmouseenter="this.style.backgroundColor='var(--wsf-hover)'" onmouseleave="this.style.backgroundColor='var(--wsf-primary)'">
                            Hover Me
                        </button>
                    </div>
                    <div class="token-item">
                        <div class="token-name">Ссылка при hover</div>
                        <div class="token-usage">hover:text-[color:var(--wsf-hover)]</div>
                        <a href="#" class="text-[color:var(--wsf-primary)] hover:text-[color:var(--wsf-hover)] transition">Garage Link</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>3. Disabled состояния</h3>
            <p><strong>Правило:</strong> disabled кнопки сохраняют --wsf-primary цвет, но становятся полупрозрачными</p>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb); --wsf-hover: var(--test-hover, #1d4ed8);">
                <div class="token-demo">
                    <div class="token-item">
                        <div class="token-name">Активная кнопка</div>
                        <div class="token-usage">btn-primary</div>
                        <button class="btn-primary">Active Button</button>
                    </div>
                    <div class="token-item">
                        <div class="token-name">Disabled кнопка</div>
                        <div class="token-usage">btn-primary disabled</div>
                        <button class="btn-primary" disabled>Disabled Button</button>
                    </div>
                </div>
                <p style="margin-top: 15px; font-size: 14px; color: #666;">
                    ✅ Правильно: disabled кнопка имеет тот же цвет, но opacity: 0.5<br>
                    ❌ Неправильно: disabled кнопка меняет цвет на --wsf-muted (бирюзовый)
                </p>
            </div>
        </div>

        <div class="test-section">
            <h3>4. Другие токены</h3>
            <div class="wsf-finder-widget" style="--wsf-text: #1f2937; --wsf-muted: #6b7280; --wsf-border: #d1d5db; --wsf-surface: #f8fafc;">
                <div class="token-demo">
                    <div class="token-item" style="color: var(--wsf-text);">
                        <div class="token-name">--wsf-text</div>
                        <div class="token-usage">Основной текст</div>
                        <p>Заголовки и основной контент</p>
                    </div>
                    <div class="token-item" style="color: var(--wsf-muted);">
                        <div class="token-name">--wsf-muted</div>
                        <div class="token-usage">Второстепенный текст</div>
                        <p>Лейблы, плейсхолдеры, disabled текст</p>
                    </div>
                    <div class="token-item" style="border: 2px solid var(--wsf-border);">
                        <div class="token-name">--wsf-border</div>
                        <div class="token-usage">Рамки и разделители</div>
                        <p>Границы инпутов, модальных окон</p>
                    </div>
                    <div class="token-item" style="background: var(--wsf-surface);">
                        <div class="token-name">--wsf-surface</div>
                        <div class="token-usage">Фоновые поверхности</div>
                        <p>Карточки, выпадающие списки</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-results">
            <h3>🧪 Test Results</h3>
            <button onclick="runTokenTest()" style="background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: 500;">
                Run Color Token Test
            </button>
            <div id="test-results" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script>
        function updateColors() {
            const primaryColor = document.getElementById('primary-color').value;
            document.getElementById('primary-value').textContent = primaryColor;
            
            // Calculate hover color (10% darker)
            const hoverColor = darkenColor(primaryColor, 10);
            
            // Apply to all test widgets
            document.documentElement.style.setProperty('--test-primary', primaryColor);
            document.documentElement.style.setProperty('--test-hover', hoverColor);
            
            // Also apply to widgets directly
            document.querySelectorAll('.wsf-finder-widget').forEach(widget => {
                widget.style.setProperty('--wsf-primary', primaryColor);
                widget.style.setProperty('--wsf-hover', hoverColor);
            });
        }

        function darkenColor(hex, percent) {
            const num = parseInt(hex.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) - amt;
            const G = (num >> 8 & 0x00FF) - amt;
            const B = (num & 0x0000FF) - amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }

        function runTokenTest() {
            const results = [];
            
            // Test 1: Check if primary buttons use --wsf-primary
            const primaryButtons = document.querySelectorAll('.btn-primary:not([disabled])');
            const expectedColor = document.getElementById('primary-color').value;
            
            let primaryCorrect = true;
            primaryButtons.forEach(btn => {
                const style = window.getComputedStyle(btn);
                const bgColor = style.backgroundColor;
                // Convert to hex for comparison (simplified)
                if (!bgColor.includes('rgb')) {
                    primaryCorrect = false;
                }
            });
            
            // Test 2: Check disabled buttons opacity
            const disabledButtons = document.querySelectorAll('.btn-primary[disabled]');
            const disabledCorrect = Array.from(disabledButtons).every(btn => {
                const style = window.getComputedStyle(btn);
                return parseFloat(style.opacity) === 0.5;
            });
            
            // Test 3: Check if disabled buttons keep primary color
            let disabledColorCorrect = true;
            disabledButtons.forEach(btn => {
                const style = window.getComputedStyle(btn);
                const bgColor = style.backgroundColor;
                // Should not be muted color (simplified check)
                if (bgColor.includes('128, 128, 128')) { // gray-ish colors
                    disabledColorCorrect = false;
                }
            });
            
            // Display results
            const resultsDiv = document.getElementById('test-results');
            let html = '<h4>Color Token Test Results:</h4>';
            
            html += `<p><strong>Primary Buttons:</strong> ${primaryCorrect ? '✅ Using --wsf-primary' : '❌ Not using --wsf-primary'}</p>`;
            html += `<p><strong>Disabled Opacity:</strong> ${disabledCorrect ? '✅ Correct opacity (0.5)' : '❌ Incorrect opacity'}</p>`;
            html += `<p><strong>Disabled Color:</strong> ${disabledColorCorrect ? '✅ Keeping primary color' : '❌ Changing to muted color'}</p>`;
            
            const allPassed = primaryCorrect && disabledCorrect && disabledColorCorrect;
            html += `<p><strong>Overall:</strong> ${allPassed ? '✅ ALL TOKENS WORKING CORRECTLY' : '❌ SOME TOKENS HAVE ISSUES'}</p>`;
            
            if (allPassed) {
                html += '<p style="color: green; font-weight: bold;">🎉 Color token system is working as designed!</p>';
                html += '<p style="font-size: 14px; color: #666;">Primary Color → кнопка работает везде ✅</p>';
            } else {
                html += '<p style="color: red; font-weight: bold;">⚠️ Issues found with color token system</p>';
            }
            
            resultsDiv.innerHTML = html;
        }

        // Initialize
        document.getElementById('primary-color').addEventListener('change', updateColors);
        updateColors();
    </script>
</body>
</html>
