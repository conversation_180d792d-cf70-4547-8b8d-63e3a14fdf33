<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wizard UX Improvements Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            height: 700px;
            overflow-y: auto;
        }
        
        .before-section {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after-section {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .widget-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* Wizard styles simulation */
        .wheel-fit-widget {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }
        
        .wsf-widget__header {
            margin-bottom: 24px;
        }
        
        .wsf-widget__title {
            text-align: center;
            font-size: 24px;
            font-weight: 800;
            color: #2563eb;
            margin: 0;
        }
        
        #wizard-header {
            margin-bottom: 24px;
        }
        
        .wizard-steps {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            font-weight: 600;
            color: #9ca3af;
            margin-bottom: 8px;
        }
        
        .wizard-step-name {
            color: #2563eb;
        }
        
        .wizard-step-name.active {
            color: #2563eb;
        }
        
        .progress-bar {
            background: #f3f4f6;
            border-radius: 9999px;
            height: 6px;
            margin-top: 8px;
        }
        
        .progress-fill {
            background: #2563eb;
            height: 6px;
            border-radius: 9999px;
            width: 50%;
            transition: width 0.5s ease;
        }
        
        .wsf-form-wrapper {
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .wizard-content {
            text-align: left;
            color: #6b7280;
        }
        
        .wizard-content.improved {
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .wizard-content h2 {
            color: #2563eb;
            margin-bottom: 16px;
            font-size: 24px;
            font-weight: 700;
        }
        
        .wizard-content.broken h2 {
            margin-bottom: 4px;
            word-break: break-all;
            line-height: 1;
        }
        
        .wizard-content nav {
            margin-bottom: 16px;
            color: #6b7280;
            font-size: 14px;
        }
        
        .wizard-content.broken nav {
            margin-bottom: 8px;
        }
        
        .search-input {
            width: 100%;
            max-width: 400px;
            height: 48px;
            padding: 12px 48px 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 24px;
            transition: all 0.2s ease;
        }
        
        .search-input.broken {
            width: 120px;
            height: 32px;
            padding: 4px 8px;
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        .search-input:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }
        
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .models-grid.broken {
            grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .model-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 16px;
            text-align: center;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            word-break: break-word;
            line-height: 1.3;
        }
        
        .model-item.broken {
            padding: 6px 8px;
            font-size: 11px;
            min-height: 32px;
            line-height: 1.1;
        }
        
        .model-item:hover {
            background: #e5e7eb;
            border-color: #2563eb;
        }
        
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        .navigation-buttons.broken {
            margin-top: 12px;
            padding-top: 12px;
        }
        
        .nav-button {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-height: 44px;
        }
        
        .nav-button.broken {
            padding: 6px 12px;
            font-size: 12px;
            min-height: 28px;
            word-break: break-all;
            line-height: 1;
        }
        
        .nav-button.primary {
            background: #2563eb;
            color: white;
        }
        
        .nav-button.secondary {
            background: #e5e7eb;
            color: #374151;
        }
        
        .garage-button {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border-radius: 6px;
            background: #f3f4f6;
            color: #6b7280;
            font-size: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        
        .garage-button.broken {
            gap: 12px;
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .garage-badge {
            background: #2563eb;
            color: white;
            font-size: 11px;
            font-weight: 700;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 6px;
        }
        
        .garage-badge.broken {
            margin-left: 16px;
            width: 24px;
            height: 24px;
            font-size: 10px;
        }
        
        .scroll-indicator {
            background: #fbbf24;
            color: #92400e;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .scroll-indicator.fixed {
            background: #10b981;
            color: white;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">🎨 Wizard UX Improvements Test</h1>
            <p style="color: #6b7280; font-size: 18px;">Исправление визуальных и UX проблем</p>
        </header>

        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h3 style="margin-top: 0; color: #92400e;">🐛 Выявленные проблемы</h3>
            <p><strong>Критические UX проблемы:</strong></p>
            <ul>
                <li>❌ <strong>Съехавший заголовок:</strong> "Choose a model" разбит по буквам и строкам</li>
                <li>❌ <strong>Плотная компоновка:</strong> Нет достаточных отступов между элементами</li>
                <li>❌ <strong>Узкое поле поиска:</strong> Поисковое поле слишком маленькое</li>
                <li>❌ <strong>Неровные кнопки:</strong> Кнопки моделей имеют разную высоту</li>
                <li>❌ <strong>Сломанная кнопка Next:</strong> Текст разбит на две строки</li>
                <li>❌ <strong>Плохой Garage UI:</strong> Бейдж слишком далеко от текста</li>
            </ul>
        </div>

        <div class="comparison-grid">
            <!-- Before -->
            <div class="comparison-section before-section">
                <h2 style="color: #dc2626; margin-top: 0;">❌ До исправления</h2>
                <div class="scroll-indicator">⚠️ Множественные UX проблемы</div>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <!-- Заголовок вверху -->
                        <div class="wsf-widget__header">
                            <h1 class="wsf-widget__title">Wheel & Tyre Finder</h1>
                        </div>
                        
                        <!-- Стадии под заголовком -->
                        <div id="wizard-header">
                            <div class="wizard-steps">
                                <div class="wizard-step-name">Make</div>
                                <div class="wizard-step-name active">Model</div>
                                <div class="wizard-step-name">Year</div>
                                <div class="wizard-step-name">Modification</div>
                                <div class="wizard-step-name">Wheel Options</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                        
                        <div class="wsf-form-wrapper">
                            <div class="wizard-content broken">
                                <h2 style="word-break: break-all; line-height: 0.8;">C h o o s e   a   m o d e l</h2>
                                <nav>BMW</nav>
                                <input type="text" class="search-input broken" placeholder="Search...">
                                <div class="models-grid broken">
                                    <div class="model-item broken">CL</div>
                                    <div class="model-item broken">CLK</div>
                                    <div class="model-item broken">CLS</div>
                                    <div class="model-item broken">SL</div>
                                    <div class="model-item broken">SLK</div>
                                    <div class="model-item broken">SLR</div>
                                    <div class="model-item broken">SLS</div>
                                    <div class="model-item broken">AMG GT</div>
                                    <div class="model-item broken">A-Class</div>
                                    <div class="model-item broken">B-Class</div>
                                    <div class="model-item broken">C-Class</div>
                                    <div class="model-item broken">E-Class</div>
                                </div>
                                <div class="navigation-buttons broken">
                                    <button class="garage-button broken">
                                        🚗 Garage
                                        <span class="garage-badge broken">3</span>
                                    </button>
                                    <button class="nav-button primary broken" style="word-break: break-all;">N e x t</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #7f1d1d; margin-top: 15px; font-size: 14px;">
                    <strong>Проблемы:</strong> Сломанная разметка, плохой spacing, нечитаемые элементы
                </p>
            </div>

            <!-- After -->
            <div class="comparison-section after-section">
                <h2 style="color: #059669; margin-top: 0;">✅ После исправления</h2>
                <div class="scroll-indicator fixed">✅ Улучшенный UX</div>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <!-- Заголовок вверху -->
                        <div class="wsf-widget__header">
                            <h1 class="wsf-widget__title">Wheel & Tyre Finder</h1>
                        </div>
                        
                        <!-- Стадии под заголовком -->
                        <div id="wizard-header">
                            <div class="wizard-steps">
                                <div class="wizard-step-name">Make</div>
                                <div class="wizard-step-name active">Model</div>
                                <div class="wizard-step-name">Year</div>
                                <div class="wizard-step-name">Modification</div>
                                <div class="wizard-step-name">Wheel Options</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                        
                        <div class="wsf-form-wrapper">
                            <div class="wizard-content improved">
                                <h2>Choose a model</h2>
                                <nav>BMW</nav>
                                <input type="text" class="search-input" placeholder="Search models...">
                                <div class="models-grid">
                                    <div class="model-item">CL</div>
                                    <div class="model-item">CLK</div>
                                    <div class="model-item">CLS</div>
                                    <div class="model-item">SL</div>
                                    <div class="model-item">SLK</div>
                                    <div class="model-item">SLR</div>
                                    <div class="model-item">SLS</div>
                                    <div class="model-item">AMG GT</div>
                                    <div class="model-item">A-Class</div>
                                    <div class="model-item">B-Class</div>
                                    <div class="model-item">C-Class</div>
                                    <div class="model-item">E-Class</div>
                                </div>
                                <div class="navigation-buttons">
                                    <button class="garage-button">
                                        🚗 Garage
                                        <span class="garage-badge">3</span>
                                    </button>
                                    <button class="nav-button primary">Next</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #065f46; margin-top: 15px; font-size: 14px;">
                    <strong>Решение:</strong> Правильный spacing, читаемые элементы, улучшенный UX
                </p>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f0fdf4; border: 1px solid #16a34a; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #15803d;">✅ Выполненные исправления</h3>
            
            <p><strong>1. Исправлен заголовок и spacing:</strong></p>
            <ul>
                <li>✅ Убраны CSS правила, ломающие заголовки</li>
                <li>✅ Увеличены отступы между элементами (mb-4 → mb-6)</li>
                <li>✅ Добавлено правильное выравнивание текста</li>
            </ul>
            
            <p><strong>2. Улучшено поисковое поле:</strong></p>
            <ul>
                <li>✅ Увеличена высота с 40px до 48px</li>
                <li>✅ Улучшены отступы и размер шрифта</li>
                <li>✅ Добавлена максимальная ширина 400px</li>
                <li>✅ Улучшен placeholder текст</li>
            </ul>
            
            <p><strong>3. Выровнены кнопки моделей:</strong></p>
            <ul>
                <li>✅ Установлена минимальная высота 48px</li>
                <li>✅ Улучшены отступы (12px 16px)</li>
                <li>✅ Добавлено flex выравнивание</li>
                <li>✅ Увеличен gap между кнопками</li>
            </ul>
            
            <p><strong>4. Исправлена кнопка Next:</strong></p>
            <ul>
                <li>✅ Добавлено white-space: nowrap для навигационных кнопок</li>
                <li>✅ Установлена минимальная ширина</li>
                <li>✅ Улучшены отступы и размер шрифта</li>
            </ul>
            
            <p><strong>5. Улучшена кнопка Garage:</strong></p>
            <ul>
                <li>✅ Уменьшен gap между текстом и бейджем</li>
                <li>✅ Оптимизирован размер бейджа</li>
                <li>✅ Добавлены hover эффекты</li>
            </ul>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #eff6ff; border: 1px solid #3b82f6; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1d4ed8;">🔧 Для тестирования</h3>
            <p>Чтобы проверить улучшения:</p>
            <ol>
                <li>Перейдите в админку WordPress → Wheel-Size → Appearance</li>
                <li>Установите <strong>Form Layout: Wizard</strong></li>
                <li>В Live Preview пройдите до шага "Select Model"</li>
                <li>Проверьте читаемость заголовка</li>
                <li>Убедитесь в правильном размере поискового поля</li>
                <li>Проверьте выравнивание кнопок моделей</li>
                <li>Убедитесь, что кнопка Next не разбита</li>
                <li>Проверьте внешний вид кнопки Garage</li>
            </ol>
            
            <p><strong>Ожидаемый результат:</strong></p>
            <ul>
                <li>✅ Заголовок "Choose a model" отображается корректно</li>
                <li>✅ Достаточные отступы между всеми элементами</li>
                <li>✅ Поисковое поле удобного размера</li>
                <li>✅ Все кнопки моделей одинаковой высоты</li>
                <li>✅ Кнопка Next отображается в одну строку</li>
                <li>✅ Кнопка Garage с компактным бейджем</li>
            </ul>
        </div>
    </div>
</body>
</html>
