<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Styles Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 2rem;
            background: #f9fafb;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .button-group {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        .section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
        }
        h2 {
            margin-top: 0;
            color: #1f2937;
        }
        .mobile-test {
            max-width: 320px;
            border: 2px dashed #3b82f6;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Тест стилей кнопок</h1>

        <div style="background: #ffeb3b; padding: 1rem; border-radius: 0.375rem; margin-bottom: 1rem;">
            <strong>🔄 Обновлено:</strong> Удалено дублирование CSS стилей, добавлены inline стили с !important для фронта
        </div>
        
        <div class="section">
            <h2>Старая кнопка (btn-primary)</h2>
            <div class="button-group">
                <button class="btn-primary">
                    <i data-lucide="trash-2"></i>
                    <span>Clear all</span>
                </button>
                <button class="btn-primary" disabled>
                    <i data-lucide="trash-2"></i>
                    <span>Clear all (disabled)</span>
                </button>
            </div>
        </div>

        <div class="section">
            <h2>Новая кнопка (btn-secondary)</h2>
            <div class="button-group">
                <button class="btn-secondary">
                    <i data-lucide="trash-2"></i>
                    <span>Clear all</span>
                </button>
                <button class="btn-secondary" disabled>
                    <i data-lucide="trash-2"></i>
                    <span>Clear all (disabled)</span>
                </button>
            </div>
        </div>

        <div class="section">
            <h2>Сравнение в контексте</h2>
            <div style="background: #f3f4f6; padding: 1rem; border-radius: 0.375rem;">
                <p>Пример в светлом контейнере:</p>
                <div class="button-group">
                    <button class="btn-primary">
                        <i data-lucide="search"></i>
                        <span>Find Sizes</span>
                    </button>
                    <button class="btn-secondary">
                        <i data-lucide="trash-2"></i>
                        <span>Clear all</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Мобильная версия (≤640px)</h2>
            <div class="mobile-test">
                <p><strong>Симуляция мобильного экрана:</strong></p>
                <div class="button-group">
                    <button class="btn-secondary">
                        <i data-lucide="trash-2"></i>
                        <span>Clear all</span>
                    </button>
                </div>
                <p><small>На мобильных устройствах текст скрывается, остается только иконка</small></p>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>
</html>
