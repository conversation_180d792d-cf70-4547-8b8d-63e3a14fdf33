<?php

declare(strict_types=1);

namespace MyTyreFinder\Translations;

/**
 * File-based localisation helper (PO-less JSON).
 */
final class TranslationManager
{
    private const SAFE_FILE = '/^[a-z0-9_-]+\.json$/i';

    /** Return absolute path to languages directory. */
    public static function dir(): string
    {
        // plugin root = two levels up from this file (src/translations)
        return dirname(__DIR__, 2) . '/languages/';
    }

    /** Ensure directory + default en.json exist (called on activation). */
    public static function bootstrap(): void
    {
        if (!is_dir(self::dir())) {
            wp_mkdir_p(self::dir());
        }
        $default = self::dir() . 'en.json';
        if (!file_exists($default)) {
            self::save_locale_data('en', [
                "widget_title" => "Wheel & Tyre Finder",
                "label_make" => "Make",
                "label_model" => "Model",
                "label_year" => "Year",
                "label_modification" => "Modification",
                "select_make_placeholder" => "Choose a make",
                "placeholder_make" => "Choose a make",
                "placeholder_model" => "Choose a model",
                "select_model_placeholder" => "Choose a model",
                "select_year_placeholder" => "Choose a year",
                "placeholder_year" => "Choose a year",
                "select_mods_placeholder" => "Choose a modification",
                "placeholder_mod" => "Choose a modification",
                "button_search" => "Find Tire & Wheel Sizes",
                "section_results" => "Search Results",
                "section_factory" => "Factory Sizes",
                "section_optional" => "Optional Sizes",
                "badge_factory" => "FACTORY",
                "badge_optional" => "OPTIONAL",
                "fuel_petrol" => "PETROL",
                "fuel_diesel" => "DIESEL",
                "fuel_electric" => "EV",
                "fuel_hybrid" => "HYBRID",
                "fuel_natural_gas" => "NATURAL GAS",
                "text_no_results_header" => "Sizes not found",
                "text_no_results_body" => "Try selecting another modification or check your selection.",
                "label_garage" => "Garage",
                "heading_my_garage" => "My Garage",
                "toast_saved_garage" => "Saved to Garage!",
                "toast_already_garage" => "Already in Garage.",
                "garage_title" => "My Garage",
                "garage_load_button" => "Load",
                "garage_clear_all" => "Clear all",
                "garage_empty" => "Your garage is empty.",
                "tooltip_add_to_garage" => "Add to Garage",
                "garage_saved_notification" => "Saved to Garage!",
                "position_front" => "Front",
                "position_rear" => "Rear",
                "loading_makes" => "Loading makes...",
                "garage_already_notification" => "Already in Garage.",
                "garage_confirm_clear" => "Are you sure you want to clear your entire garage?"
            ]);
        }

        // Sample additional locales
        $samples = [
            'ru' => [
                "widget_title" => "Подбор шин и дисков",
                "label_make" => "Марка",
                "label_model" => "Модель",
                "label_year" => "Год",
                "label_modification" => "Модификация",
                "select_make_placeholder" => "Выберите марку",
                "placeholder_make" => "Выберите марку",
                "select_model_placeholder" => "Выберите модель",
                "placeholder_model" => "Выберите модель",
                "select_year_placeholder" => "Выберите год",
                "placeholder_year" => "Выберите год",
                "select_mods_placeholder" => "Выберите модификацию",
                "placeholder_mod" => "Выберите модификацию",
                "button_search" => "Подобрать размеры",
                "section_results" => "Результаты поиска",
                "section_factory" => "Заводские размеры",
                "section_optional" => "Опциональные размеры",
                "badge_factory" => "ЗАВОДСКОЙ",
                "badge_optional" => "ОПЦИЯ",
                "fuel_petrol" => "БЕНЗИН",
                "fuel_diesel" => "ДИЗЕЛЬ",
                "fuel_electric" => "ЭЛЕКТРО",
                "fuel_hybrid" => "ГИБРИД",
                "fuel_natural_gas" => "ГАЗ",
                "text_no_results_header" => "Размеры не найдены",
                "text_no_results_body" => "Попробуйте выбрать другую модификацию или проверьте свой выбор.",
                "label_garage" => "Гараж",
                "heading_my_garage" => "Мой гараж",
                "toast_saved_garage" => "Сохранено в гараж!",
                "toast_already_garage" => "Уже в гараже.",
                "garage_title" => "Мой гараж",
                "garage_load_button" => "Загрузить",
                "garage_clear_all" => "Очистить всё",
                "garage_empty" => "Ваш гараж пуст.",
                "tooltip_add_to_garage" => "Добавить в гараж",
                "garage_saved_notification" => "Добавлено в гараж!",
                "position_front" => "Передняя",
                "position_rear" => "Задняя",
                "loading_makes" => "Загрузка брендов...",
                "garage_already_notification" => "Уже в гараже.",
                "garage_confirm_clear" => "Вы уверены, что хотите очистить весь гараж?"
            ],
            'de' => [
                'label_make' => 'Marke',
                'label_model' => 'Modell',
                'label_year' => 'Jahr',
                'label_mods' => 'Modifikation',
                'button_search' => 'Suchen',
                'button_clear' => 'Löschen',
                'button_reset' => 'Zurücksetzen',
                'section_results' => 'Suchergebnisse',
                'section_factory' => 'Werksgrößen',
                'fuel_petrol' => 'BENZIN',
                'fuel_diesel' => 'DIESEL',
                'fuel_electric' => 'ELEKTRO',
                'widget_title'    => 'Tyre & Wheel Size Finder',
            ],
            'fr' => [
                'label_make' => 'Marque',
                'label_model' => 'Modèle',
                'label_year' => 'Année',
                'label_mods' => 'Modification',
                'button_search' => 'Rechercher',
                'button_clear' => 'Effacer',
                'button_reset' => 'Réinitialiser',
                'section_results' => 'Résultats de recherche',
                'section_factory' => 'Tailles d\'origine',
                'fuel_petrol' => 'ESSENCE',
                'fuel_diesel' => 'DIESEL',
                'fuel_electric' => 'ÉLECTRIQUE',
                'widget_title'    => 'Tyre & Wheel Size Finder',
            ],
            'es' => [
                'label_make' => 'Marca',
                'label_model' => 'Modelo',
                'label_year' => 'Año',
                'label_mods' => 'Modificación',
                'button_search' => 'Buscar',
                'button_clear' => 'Limpiar',
                'button_reset' => 'Restablecer',
                'section_results' => 'Resultados de búsqueda',
                'section_factory' => 'Medidas de fábrica',
                'fuel_petrol' => 'GASOLINA',
                'fuel_diesel' => 'DIÉSEL',
                'fuel_electric' => 'ELÉCTRICO',
                'widget_title'    => 'Tyre & Wheel Size Finder',
            ]
        ];
        foreach ($samples as $loc => $data) {
            $file = self::dir() . $loc . '.json';
            if (!file_exists($file)) {
                self::save_locale_data($loc, $data);
            }
        }
    }

    /** List available locale codes (without .json). */
    public static function get_available_locales(): array
    {
        if (!is_dir(self::dir())) {
            return [];
        }
        return array_values(array_map(static fn($f) => basename($f, '.json'), glob(self::dir() . '*.json') ?: []));
    }

    /** Get translation array for locale, with caching. */
    public static function get_locale_data(string $locale): array
    {
        $cache_key = "wheel_size_locale_{$locale}";
        $data = get_transient($cache_key);

        if (false !== $data) {
            $json_path = self::dir() . "/{$locale}.json";
            $file_keys_count = 0;
            if (is_readable($json_path)) {
                $file_data = json_decode(file_get_contents($json_path), true);
                if (is_array($file_data)) {
                    $file_keys_count = count(array_keys($file_data));
                }
            }
            if ($file_keys_count !== count($data)) {
                delete_transient($cache_key);
                $data = false;
            }
        }

        if (false === $data) {
            $data = self::read_from_file($locale);
            set_transient($cache_key, $data, DAY_IN_SECONDS);
        }

        return is_array($data) ? $data : [];
    }
    
    /** Read locale file directly from disk. */
    private static function read_from_file(string $locale): array
    {
        $file = self::sanitize_file($locale . '.json');
        if (!$file || !is_readable($file)) {
            return [];
        }
        $json = file_get_contents($file);
        $data = json_decode($json, true);
        return is_array($data) ? $data : [];
    }

    /** Save locale data. */
    public static function save_locale_data(string $locale, array $data): bool
    {
        $file = self::sanitize_file($locale . '.json', true);
        if (!$file) {
            return false;
        }
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        if (JSON_ERROR_NONE !== json_last_error()) {
            return false;
        }
        return false !== file_put_contents($file, $json . PHP_EOL, LOCK_EX);
    }

    public static function active_locale(): string
    {
        // 1) если админ явно задавал язык ― используем его
        $saved = get_option('wheel_size_active_language', '');
        if ($saved) {
            $available = self::get_available_locales();
            if (in_array($saved, $available, true)) {
                return $saved;
            }
        }

        // 2) до выбора админом показываем *всегда* английский
        //    (тем, кому нужен другой язык, зайдут в «Translations» и сохран-ят)
        return 'en';
    }

    /** Translate key. */
    public static function get(string $key): string
    {
        static $cache = [];
        $locale = self::active_locale();
        if (!isset($cache[$locale])) {
            $cache[$locale] = self::get_locale_data($locale);
        }
        // Alias handling: some locale files use label_mods instead of label_modification
        if ($key === 'label_modification' && !isset($cache[$locale][$key]) && isset($cache[$locale]['label_mods'])) {
            return $cache[$locale]['label_mods'];
        }

        // Primary lookup
        if (isset($cache[$locale][$key])) {
            return $cache[$locale][$key];
        }

        // Fallback to English if key missing in current locale
        if (!isset($cache['en'])) {
            $cache['en'] = self::get_locale_data('en');
        }
        return $cache['en'][$key] ?? $key;
    }

    /** Clear translation cache for a specific locale or all locales. */
    public static function clear_cache(string $locale = ''): void
    {
        if ($locale) {
            delete_transient("wheel_size_locale_{$locale}");
        } else {
            // Clear cache for all available locales
            $locales = self::get_available_locales();
            foreach ($locales as $loc) {
                delete_transient("wheel_size_locale_{$loc}");
            }
        }
    }

    /** Validate filename and return absolute path. */
    private static function sanitize_file(string $filename, bool $ensure_dir = false): ?string
    {
        if (!preg_match(self::SAFE_FILE, $filename)) {
            return null;
        }
        $dir = self::dir();
        if ($ensure_dir && !is_dir($dir)) {
            wp_mkdir_p($dir);
        }
        return $dir . $filename;
    }
}
