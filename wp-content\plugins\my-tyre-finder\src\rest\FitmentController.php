<?php

declare(strict_types=1);

namespace MyTyreFinder\Rest;

use WP_REST_Request;

/**
 * Provides REST endpoints for tyre fitment finder.
 */
final class FitmentController
{
    public function register(): void
    {
        register_rest_route('tyre-finder/v1', '/endpoint', [
            'methods'  => ['GET'],
            'callback' => [$this, 'handle'],
            'permission_callback' => '__return_true',
        ]);
    }

    public function handle(WP_REST_Request $request)
    {
        // Handle REST request.
        return rest_ensure_response(['success' => true]);
    }
} 