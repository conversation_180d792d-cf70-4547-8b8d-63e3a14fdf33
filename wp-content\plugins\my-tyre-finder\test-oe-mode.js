// Тест OE only mode (Show factory sizes only)
// Запустите в консоли: window.testOEMode()

window.testOEMode = function() {
    console.log('🏭 [OE Mode Test] Тестируем OE only mode (Show factory sizes only)...');
    
    // Проверяем настройку в WheelFitData
    const oeEnabled = !!(window.WheelFitData && window.WheelFitData.enableOeFilter);
    console.log('📊 Настройка OE only mode:', oeEnabled ? 'ВКЛЮЧЕНА' : 'ВЫКЛЮЧЕНА');
    console.log('📋 WheelFitData.enableOeFilter:', window.WheelFitData?.enableOeFilter);
    
    // Проверяем константы в JavaScript
    const frontendOEMode = typeof OE_ONLY_MODE !== 'undefined' ? OE_ONLY_MODE : null;
    const wizardOEMode = typeof WIZARD_OE_ONLY_MODE !== 'undefined' ? WIZARD_OE_ONLY_MODE : null;

    console.log('📊 Константы JavaScript:');
    console.log('• OE_ONLY_MODE (frontend):', frontendOEMode);
    console.log('• WIZARD_OE_ONLY_MODE (admin):', wizardOEMode);

    if (frontendOEMode !== null) {
        console.log('✅ Константа OE_ONLY_MODE (frontend) определена');
    } else {
        console.warn('⚠️ Константа OE_ONLY_MODE (frontend) не определена - возможно finder.js не загружен');
    }

    if (wizardOEMode !== null) {
        console.log('✅ Константа WIZARD_OE_ONLY_MODE (admin) определена');
    } else {
        console.warn('⚠️ Константа WIZARD_OE_ONLY_MODE (admin) не определена - возможно wizard.js не загружен');
    }
    
    // Проверяем соответствие настройки и констант
    const frontendMatch = frontendOEMode !== null && oeEnabled === frontendOEMode;
    const wizardMatch = wizardOEMode !== null && oeEnabled === wizardOEMode;

    if (frontendMatch && wizardMatch) {
        console.log('✅ Настройка и все константы соответствуют друг другу');
    } else {
        console.warn('⚠️ Несоответствие между настройкой и константами!');
        console.log(`Настройка: ${oeEnabled}`);
        console.log(`Frontend константа: ${frontendOEMode} (${frontendMatch ? 'совпадает' : 'не совпадает'})`);
        console.log(`Wizard константа: ${wizardOEMode} (${wizardMatch ? 'совпадает' : 'не совпадает'})`);
    }
    
    // Проверяем виджеты
    const hasMainWidget = !!window.wheelFitWidget;
    const hasWizard = !!window.wheelFitWizard;

    console.log('📊 Доступные виджеты:');
    console.log('• Main widget (frontend):', hasMainWidget ? 'найден' : 'не найден');
    console.log('• Wizard (admin):', hasWizard ? 'найден' : 'не найден');

    if (!hasMainWidget && !hasWizard) {
        console.error('❌ Ни один виджет не найден');
        return { error: 'No widgets found' };
    }
    
    console.log('\n--- Тест функциональности OE Mode ---');
    
    // Проверяем метод displayResults
    if (typeof window.wheelFitWidget.displayResults === 'function') {
        console.log('✅ Метод displayResults найден');
        
        // Создаем тестовые данные
        const testData = {
            factory_sizes: [
                {
                    tire_full: '225/50R17',
                    rim: '17x7.5',
                    rim_diameter: '17',
                    rim_width: '7.5',
                    rim_offset: '45',
                    is_stock: true,
                    make: 'BMW',
                    model: '3 Series',
                    modification: '320i'
                },
                {
                    tire_full: '225/45R18',
                    rim: '18x8.0',
                    rim_diameter: '18',
                    rim_width: '8.0',
                    rim_offset: '47',
                    is_stock: true,
                    make: 'BMW',
                    model: '3 Series',
                    modification: '320i'
                }
            ],
            optional_sizes: [
                {
                    tire_full: '235/40R19',
                    rim: '19x8.5',
                    rim_diameter: '19',
                    rim_width: '8.5',
                    rim_offset: '50',
                    is_stock: false,
                    make: 'BMW',
                    model: '3 Series',
                    modification: '320i'
                },
                {
                    tire_full: '245/35R20',
                    rim: '20x9.0',
                    rim_diameter: '20',
                    rim_width: '9.0',
                    rim_offset: '52',
                    is_stock: false,
                    make: 'BMW',
                    model: '3 Series',
                    modification: '320i'
                }
            ],
            total_count: 4
        };
        
        console.log('📊 Тестовые данные созданы:');
        console.log(`• Factory sizes: ${testData.factory_sizes.length}`);
        console.log(`• Optional sizes: ${testData.optional_sizes.length}`);
        
        // Симулируем поиск для тестирования
        window.wheelFitWidget.selectedData = {
            make: 'bmw',
            model: '3-series',
            year: 2020,
            modification: '320i'
        };
        
        try {
            // Вызываем displayResults с тестовыми данными
            window.wheelFitWidget.displayResults(testData);
            console.log('✅ Метод displayResults выполнен успешно');
            
            // Проверяем результат
            setTimeout(() => {
                const factorySection = document.getElementById('factory-section');
                const optionalSection = document.getElementById('optional-section');
                const factoryGrid = document.getElementById('factory-grid');
                const optionalGrid = document.getElementById('optional-grid');
                
                console.log('\n--- Результаты отображения ---');
                
                if (factorySection) {
                    const factoryVisible = !factorySection.classList.contains('hidden');
                    console.log(`📦 Factory section: ${factoryVisible ? 'ВИДИМАЯ' : 'СКРЫТАЯ'}`);
                    
                    if (factoryGrid) {
                        const factoryCards = factoryGrid.children.length;
                        console.log(`📊 Factory cards: ${factoryCards}`);
                    }
                } else {
                    console.warn('⚠️ Factory section не найдена');
                }
                
                if (optionalSection) {
                    const optionalVisible = !optionalSection.classList.contains('hidden');
                    console.log(`🔧 Optional section: ${optionalVisible ? 'ВИДИМАЯ' : 'СКРЫТАЯ'}`);
                    
                    if (optionalGrid) {
                        const optionalCards = optionalGrid.children.length;
                        console.log(`📊 Optional cards: ${optionalCards}`);
                    }
                    
                    // Проверяем логику OE mode
                    if (OE_ONLY_MODE && optionalVisible) {
                        console.error('❌ ОШИБКА: Optional section видимая при включенном OE mode!');
                    } else if (OE_ONLY_MODE && !optionalVisible) {
                        console.log('✅ КОРРЕКТНО: Optional section скрыта при включенном OE mode');
                    } else if (!OE_ONLY_MODE && optionalVisible) {
                        console.log('✅ КОРРЕКТНО: Optional section видимая при выключенном OE mode');
                    } else if (!OE_ONLY_MODE && !optionalVisible) {
                        console.warn('⚠️ Optional section скрыта при выключенном OE mode (возможно нет данных)');
                    }
                } else {
                    console.warn('⚠️ Optional section не найдена');
                }
                
                // Проверяем no-results элемент
                const noResults = document.getElementById('no-results');
                if (noResults) {
                    const noResultsVisible = !noResults.classList.contains('hidden');
                    console.log(`❌ No results: ${noResultsVisible ? 'ВИДИМЫЙ' : 'СКРЫТЫЙ'}`);
                    
                    if (OE_ONLY_MODE) {
                        // В OE mode результат зависит только от factory sizes
                        const shouldShowResults = testData.factory_sizes.length > 0;
                        if (noResultsVisible === !shouldShowResults) {
                            console.log('✅ КОРРЕКТНО: No-results логика работает правильно в OE mode');
                        } else {
                            console.error('❌ ОШИБКА: No-results логика неправильная в OE mode');
                        }
                    }
                }
                
            }, 100);
            
        } catch (error) {
            console.error('❌ Ошибка при вызове displayResults:', error);
        }

    } else {
        console.error('❌ Метод displayResults не найден в main widget');
    }

    // Тест wizard если доступен
    if (hasWizard && typeof window.wheelFitWizard.displayResults === 'function') {
        console.log('\n--- Тест Wizard OE Mode ---');

        try {
            // Симулируем выбор для wizard
            window.wheelFitWizard.selection = {
                make: { slug: 'bmw', name: 'BMW' },
                model: { slug: '3-series', name: '3 Series' },
                year: 2020,
                modification: { slug: '320i', name: '320i' }
            };

            // Вызываем displayResults с тестовыми данными
            window.wheelFitWizard.displayResults(testData);
            console.log('✅ Wizard displayResults выполнен успешно');

            // Проверяем результат wizard
            setTimeout(() => {
                const wizardContainer = document.getElementById('wizard-results');
                if (wizardContainer) {
                    const factorySection = wizardContainer.querySelector('#factory-section');
                    const optionalSection = wizardContainer.querySelector('#optional-section');

                    console.log('\n--- Результаты Wizard ---');

                    if (factorySection) {
                        const factoryVisible = !factorySection.classList.contains('hidden');
                        console.log(`📦 Wizard Factory section: ${factoryVisible ? 'ВИДИМАЯ' : 'СКРЫТАЯ'}`);
                    }

                    if (optionalSection) {
                        const optionalVisible = !optionalSection.classList.contains('hidden');
                        console.log(`🔧 Wizard Optional section: ${optionalVisible ? 'ВИДИМАЯ' : 'СКРЫТАЯ'}`);

                        // Проверяем логику OE mode для wizard
                        if (wizardOEMode && optionalVisible) {
                            console.error('❌ ОШИБКА WIZARD: Optional section видимая при включенном OE mode!');
                        } else if (wizardOEMode && !optionalVisible) {
                            console.log('✅ КОРРЕКТНО WIZARD: Optional section скрыта при включенном OE mode');
                        } else if (!wizardOEMode && optionalVisible) {
                            console.log('✅ КОРРЕКТНО WIZARD: Optional section видимая при выключенном OE mode');
                        }
                    }
                } else {
                    console.warn('⚠️ Wizard results container не найден');
                }
            }, 150);

        } catch (error) {
            console.error('❌ Ошибка при тестировании wizard:', error);
        }

    } else if (hasWizard) {
        console.warn('⚠️ Wizard найден, но метод displayResults отсутствует');
    }
    
    console.log('\n--- Анализ результатов ---');
    
    const allChecksPass = (
        (frontendOEMode !== null || wizardOEMode !== null) &&
        (frontendMatch || wizardMatch) &&
        (hasMainWidget || hasWizard) &&
        (window.wheelFitWidget?.displayResults || window.wheelFitWizard?.displayResults)
    );
    
    if (allChecksPass) {
        console.log('🎉 OE MODE ТЕСТ УСПЕШЕН!');
        console.log('✅ Константа OE_ONLY_MODE определена корректно');
        console.log('✅ Настройка передается из PHP в JavaScript');
        console.log('✅ Виджет и методы доступны');
        
        if (oeEnabled) {
            console.log('\n🏭 OE Mode ВКЛЮЧЕН:');
            console.log('• Frontend: Показываются только заводские размеры (factory)');
            console.log('• Admin/Wizard: Optional section должна быть скрыта');
            console.log('• Результат зависит только от наличия factory sizes');
        } else {
            console.log('\n🔧 OE Mode ВЫКЛЮЧЕН:');
            console.log('• Frontend: Показываются и заводские, и дополнительные размеры');
            console.log('• Admin/Wizard: Optional section должна быть видимой (если есть данные)');
            console.log('• Результат зависит от наличия любых размеров');
        }
        
    } else {
        console.error('❌ OE MODE ТЕСТ НЕ ПРОШЕЛ');
        console.log('Проблемы:');
        if (frontendOEMode === null && wizardOEMode === null) console.log('• Ни одна константа OE mode не определена');
        if (!frontendMatch && frontendOEMode !== null) console.log('• Несоответствие настройки и frontend константы');
        if (!wizardMatch && wizardOEMode !== null) console.log('• Несоответствие настройки и wizard константы');
        if (!hasMainWidget && !hasWizard) console.log('• Ни один виджет не найден');
        if (!window.wheelFitWidget?.displayResults && !window.wheelFitWizard?.displayResults) console.log('• Методы displayResults не найдены');
    }
    
    console.log('\n--- Следующие шаги ---');
    if (allChecksPass) {
        console.log('1. ✅ Протестируйте поиск размеров на реальном автомобиле');
        console.log('2. ✅ Переключите настройку OE mode в админке и проверьте изменения');
        console.log('3. ✅ Убедитесь что optional sizes скрываются/показываются корректно');
    } else {
        console.log('1. 🔧 Очистите кэш и перезагрузите страницу');
        console.log('2. 🔧 Проверьте настройки в WordPress admin > Wheel-Size > Features');
        console.log('3. 🔧 Убедитесь что finder.js загружается корректно');
    }
    
    return {
        oeEnabled,
        frontendOEMode,
        wizardOEMode,
        frontendMatch,
        wizardMatch,
        hasMainWidget,
        hasWizard,
        overallSuccess: allChecksPass
    };
};

console.log('🏭 [OE Mode Test] Скрипт загружен.');
console.log('📋 Этот тест проверяет работу OE only mode (Show factory sizes only).');
console.log('🚀 Запустите: window.testOEMode()');
