/**
 * Test script for Step-by-Step Selector Alignment
 * 
 * This test verifies that:
 * 1. Text in selectors is properly centered vertically
 * 2. Font family, size, weight, and line-height are consistent with Live Preview
 * 3. Padding and alignment match the admin preview
 * 4. Styles are not overridden by WordPress theme
 * 5. All selectors have uniform appearance
 * 
 * Run this in browser console on both:
 * - Frontend: actual page with Step-by-Step widget
 * - Admin: /wp-admin/admin.php?page=wheel-size-appearance (Live Preview)
 */

console.log('📐 Testing Step-by-Step Selector Alignment...');

function testStepByStepSelectorAlignment() {
    console.log('📋 Starting Step-by-Step selector alignment tests...');
    
    const isAdmin = window.location.href.includes('/wp-admin/');
    const context = isAdmin ? 'ADMIN (Live Preview)' : 'FRONTEND';
    
    console.log(`🔍 Testing in: ${context}`);
    
    // Find widget container
    const widgetContainer = document.querySelector('.wheel-fit-widget, .wsf-finder-widget');
    if (!widgetContainer) {
        console.error('❌ Widget container not found');
        return;
    }
    
    console.log('✅ Widget container found');
    
    // Test 1: Check if we're in Step-by-Step layout
    const isStepByStepLayout = testStepByStepLayout(widgetContainer);
    
    if (!isStepByStepLayout) {
        console.warn('⚠️ Not in Step-by-Step layout - switch to "Step-by-Step" to test selector alignment');
        return;
    }
    
    // Test 2: Check selector font consistency
    testSelectorFonts(widgetContainer);
    
    // Test 3: Check vertical alignment
    testVerticalAlignment(widgetContainer);
    
    // Test 4: Check padding and spacing
    testPaddingAndSpacing(widgetContainer);
    
    // Test 5: Check theme override resistance
    testThemeOverrides(widgetContainer);
    
    // Test 6: Compare with expected values
    testExpectedValues(widgetContainer);
    
    // Test 7: Generate alignment report
    generateAlignmentReport(widgetContainer, context);
    
    console.log('🎉 Step-by-Step selector alignment tests completed!');
}

function testStepByStepLayout(container) {
    console.log('🔍 Checking if we\'re in Step-by-Step layout...');
    
    // Look for Step-by-Step layout indicators
    const stepContainers = container.querySelectorAll('.step-container');
    const selectors = container.querySelectorAll('.step-container select, select.wsf-input');
    
    if (stepContainers.length >= 3 && selectors.length >= 3) {
        console.log('✅ Step-by-Step layout detected');
        console.log(`   Found ${stepContainers.length} step containers`);
        console.log(`   Found ${selectors.length} selectors`);
        return true;
    } else {
        console.log('❌ Step-by-Step layout not detected');
        console.log('   Make sure to select "Step-by-Step" in Form Layout dropdown');
        return false;
    }
}

function testSelectorFonts(container) {
    console.log('🔤 Testing selector fonts...');
    
    const selectors = container.querySelectorAll('.step-container select, select.wsf-input');
    
    if (selectors.length === 0) {
        console.error('❌ No selectors found for font test');
        return;
    }
    
    console.log(`📊 Font analysis for ${selectors.length} selectors:`);
    
    const expectedFont = 'Inter';
    const expectedSize = '14px'; // 0.875rem
    const expectedWeight = '500';
    const expectedLineHeight = '1.3';
    
    let fontConsistent = true;
    
    selectors.forEach((select, index) => {
        const style = window.getComputedStyle(select);
        
        const fontFamily = style.fontFamily;
        const fontSize = style.fontSize;
        const fontWeight = style.fontWeight;
        const lineHeight = style.lineHeight;
        
        console.log(`  Selector ${index + 1}:`);
        console.log(`    Font family: ${fontFamily}`);
        console.log(`    Font size: ${fontSize}`);
        console.log(`    Font weight: ${fontWeight}`);
        console.log(`    Line height: ${lineHeight}`);
        
        // Check font family (should contain Inter)
        const hasInterFont = fontFamily.toLowerCase().includes('inter');
        const correctSize = fontSize === expectedSize || Math.abs(parseFloat(fontSize) - 14) <= 1;
        const correctWeight = fontWeight === expectedWeight || fontWeight === 'normal';
        const correctLineHeight = lineHeight === expectedLineHeight || Math.abs(parseFloat(lineHeight) - 1.3) <= 0.1;
        
        console.log(`    Inter font: ${hasInterFont ? '✅' : '❌'}`);
        console.log(`    Size correct: ${correctSize ? '✅' : '❌'} (expected ${expectedSize})`);
        console.log(`    Weight correct: ${correctWeight ? '✅' : '❌'} (expected ${expectedWeight})`);
        console.log(`    Line height correct: ${correctLineHeight ? '✅' : '❌'} (expected ${expectedLineHeight})`);
        
        if (!hasInterFont || !correctSize || !correctWeight || !correctLineHeight) {
            fontConsistent = false;
        }
    });
    
    if (fontConsistent) {
        console.log('✅ All selectors have consistent fonts');
    } else {
        console.warn('⚠️ Some selectors have inconsistent fonts');
    }
    
    return { fontConsistent, selectorCount: selectors.length };
}

function testVerticalAlignment(container) {
    console.log('📏 Testing vertical alignment...');
    
    const selectors = container.querySelectorAll('.step-container select, select.wsf-input');
    
    if (selectors.length === 0) {
        console.error('❌ No selectors found for alignment test');
        return;
    }
    
    console.log(`📊 Vertical alignment analysis:`);
    
    const expectedHeight = 44; // --ws-control-height
    let alignmentCorrect = true;
    
    selectors.forEach((select, index) => {
        const style = window.getComputedStyle(select);
        const rect = select.getBoundingClientRect();
        
        const height = rect.height;
        const display = style.display;
        const alignItems = style.alignItems;
        const padding = style.padding;
        const paddingTop = style.paddingTop;
        const paddingBottom = style.paddingBottom;
        
        console.log(`  Selector ${index + 1}:`);
        console.log(`    Height: ${height.toFixed(1)}px`);
        console.log(`    Display: ${display}`);
        console.log(`    Align items: ${alignItems}`);
        console.log(`    Padding: ${padding}`);
        
        // Check height
        const correctHeight = Math.abs(height - expectedHeight) <= 2;
        
        // Check vertical centering
        const hasFlexDisplay = display === 'flex';
        const hasCenterAlign = alignItems === 'center';
        const hasEqualPadding = paddingTop === paddingBottom;
        
        console.log(`    Height correct: ${correctHeight ? '✅' : '❌'} (expected ~${expectedHeight}px)`);
        console.log(`    Flex display: ${hasFlexDisplay ? '✅' : '❌'}`);
        console.log(`    Center align: ${hasCenterAlign ? '✅' : '❌'}`);
        console.log(`    Equal padding: ${hasEqualPadding ? '✅' : '❌'}`);
        
        if (!correctHeight || !hasFlexDisplay || !hasCenterAlign) {
            alignmentCorrect = false;
        }
    });
    
    if (alignmentCorrect) {
        console.log('✅ All selectors are properly aligned');
    } else {
        console.warn('⚠️ Some selectors have alignment issues');
    }
    
    return { alignmentCorrect, selectorCount: selectors.length };
}

function testPaddingAndSpacing(container) {
    console.log('📦 Testing padding and spacing...');
    
    const selectors = container.querySelectorAll('.step-container select, select.wsf-input');
    
    if (selectors.length === 0) {
        console.error('❌ No selectors found for padding test');
        return;
    }
    
    console.log(`📊 Padding and spacing analysis:`);
    
    const expectedPadding = '12px'; // 0.75rem
    let paddingConsistent = true;
    
    selectors.forEach((select, index) => {
        const style = window.getComputedStyle(select);
        
        const paddingLeft = style.paddingLeft;
        const paddingRight = style.paddingRight;
        const paddingTop = style.paddingTop;
        const paddingBottom = style.paddingBottom;
        const borderRadius = style.borderRadius;
        
        console.log(`  Selector ${index + 1}:`);
        console.log(`    Padding left: ${paddingLeft}`);
        console.log(`    Padding right: ${paddingRight}`);
        console.log(`    Padding top: ${paddingTop}`);
        console.log(`    Padding bottom: ${paddingBottom}`);
        console.log(`    Border radius: ${borderRadius}`);
        
        // Check horizontal padding (should be 12px)
        const correctLeftPadding = Math.abs(parseFloat(paddingLeft) - 12) <= 2;
        const correctRightPadding = parseFloat(paddingRight) >= 32; // Should be larger due to dropdown arrow
        const hasRadius = parseFloat(borderRadius) >= 6; // Should be 8px (0.5rem)
        
        console.log(`    Left padding correct: ${correctLeftPadding ? '✅' : '❌'} (expected ~${expectedPadding})`);
        console.log(`    Right padding correct: ${correctRightPadding ? '✅' : '❌'} (should be >32px for arrow)`);
        console.log(`    Border radius: ${hasRadius ? '✅' : '❌'} (should be >=6px)`);
        
        if (!correctLeftPadding || !correctRightPadding || !hasRadius) {
            paddingConsistent = false;
        }
    });
    
    if (paddingConsistent) {
        console.log('✅ All selectors have consistent padding');
    } else {
        console.warn('⚠️ Some selectors have padding issues');
    }
    
    return { paddingConsistent, selectorCount: selectors.length };
}

function testThemeOverrides(container) {
    console.log('🎨 Testing theme override resistance...');
    
    const selectors = container.querySelectorAll('.step-container select, select.wsf-input');
    
    if (selectors.length === 0) {
        console.error('❌ No selectors found for theme test');
        return;
    }
    
    console.log(`📊 Theme override analysis:`);
    
    let themeResistant = true;
    
    selectors.forEach((select, index) => {
        const style = window.getComputedStyle(select);
        
        const appearance = style.appearance || style.webkitAppearance;
        const backgroundColor = style.backgroundColor;
        const borderColor = style.borderColor;
        const color = style.color;
        
        console.log(`  Selector ${index + 1}:`);
        console.log(`    Appearance: ${appearance}`);
        console.log(`    Background: ${backgroundColor}`);
        console.log(`    Border: ${borderColor}`);
        console.log(`    Color: ${color}`);
        
        // Check if custom styling is applied
        const hasCustomAppearance = appearance === 'none';
        const hasCustomBackground = backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent';
        const hasCustomBorder = borderColor !== 'rgba(0, 0, 0, 0)' && borderColor !== 'transparent';
        const hasCustomColor = color !== 'rgba(0, 0, 0, 0)' && color !== 'transparent';
        
        console.log(`    Custom appearance: ${hasCustomAppearance ? '✅' : '❌'}`);
        console.log(`    Custom background: ${hasCustomBackground ? '✅' : '❌'}`);
        console.log(`    Custom border: ${hasCustomBorder ? '✅' : '❌'}`);
        console.log(`    Custom color: ${hasCustomColor ? '✅' : '❌'}`);
        
        if (!hasCustomAppearance || !hasCustomBackground || !hasCustomBorder || !hasCustomColor) {
            themeResistant = false;
        }
    });
    
    if (themeResistant) {
        console.log('✅ Selectors resist theme overrides');
    } else {
        console.warn('⚠️ Some selectors may be affected by theme');
    }
    
    return { themeResistant, selectorCount: selectors.length };
}

function testExpectedValues(container) {
    console.log('🎯 Testing expected values...');
    
    const selectors = container.querySelectorAll('.step-container select, select.wsf-input');
    
    if (selectors.length === 0) {
        console.error('❌ No selectors found for expected values test');
        return;
    }
    
    const expectedValues = {
        height: 44,
        fontSize: 14,
        fontWeight: 500,
        lineHeight: 1.3,
        paddingLeft: 12,
        borderRadius: 8
    };
    
    console.log(`📊 Expected values comparison:`);
    console.log(`Expected: height=${expectedValues.height}px, fontSize=${expectedValues.fontSize}px, fontWeight=${expectedValues.fontWeight}, lineHeight=${expectedValues.lineHeight}, paddingLeft=${expectedValues.paddingLeft}px, borderRadius=${expectedValues.borderRadius}px`);
    
    let allMatch = true;
    
    selectors.forEach((select, index) => {
        const style = window.getComputedStyle(select);
        const rect = select.getBoundingClientRect();
        
        const actualValues = {
            height: rect.height,
            fontSize: parseFloat(style.fontSize),
            fontWeight: parseInt(style.fontWeight) || (style.fontWeight === 'normal' ? 400 : 500),
            lineHeight: parseFloat(style.lineHeight),
            paddingLeft: parseFloat(style.paddingLeft),
            borderRadius: parseFloat(style.borderRadius)
        };
        
        console.log(`  Selector ${index + 1} actual values:`);
        console.log(`    Height: ${actualValues.height.toFixed(1)}px ${Math.abs(actualValues.height - expectedValues.height) <= 2 ? '✅' : '❌'}`);
        console.log(`    Font size: ${actualValues.fontSize}px ${Math.abs(actualValues.fontSize - expectedValues.fontSize) <= 1 ? '✅' : '❌'}`);
        console.log(`    Font weight: ${actualValues.fontWeight} ${Math.abs(actualValues.fontWeight - expectedValues.fontWeight) <= 100 ? '✅' : '❌'}`);
        console.log(`    Line height: ${actualValues.lineHeight} ${Math.abs(actualValues.lineHeight - expectedValues.lineHeight) <= 0.2 ? '✅' : '❌'}`);
        console.log(`    Padding left: ${actualValues.paddingLeft}px ${Math.abs(actualValues.paddingLeft - expectedValues.paddingLeft) <= 2 ? '✅' : '❌'}`);
        console.log(`    Border radius: ${actualValues.borderRadius}px ${Math.abs(actualValues.borderRadius - expectedValues.borderRadius) <= 2 ? '✅' : '❌'}`);
        
        // Check if all values are within tolerance
        const heightMatch = Math.abs(actualValues.height - expectedValues.height) <= 2;
        const fontSizeMatch = Math.abs(actualValues.fontSize - expectedValues.fontSize) <= 1;
        const fontWeightMatch = Math.abs(actualValues.fontWeight - expectedValues.fontWeight) <= 100;
        const lineHeightMatch = Math.abs(actualValues.lineHeight - expectedValues.lineHeight) <= 0.2;
        const paddingMatch = Math.abs(actualValues.paddingLeft - expectedValues.paddingLeft) <= 2;
        const radiusMatch = Math.abs(actualValues.borderRadius - expectedValues.borderRadius) <= 2;
        
        if (!heightMatch || !fontSizeMatch || !fontWeightMatch || !lineHeightMatch || !paddingMatch || !radiusMatch) {
            allMatch = false;
        }
    });
    
    if (allMatch) {
        console.log('✅ All selectors match expected values');
    } else {
        console.warn('⚠️ Some selectors don\'t match expected values');
    }
    
    return { allMatch, selectorCount: selectors.length };
}

function generateAlignmentReport(container, context) {
    console.log('📋 Generating alignment report...');
    
    const selectors = container.querySelectorAll('.step-container select, select.wsf-input');
    
    if (selectors.length === 0) {
        console.error('❌ Cannot generate report - no selectors found');
        return;
    }
    
    // Run all tests and collect results
    const fontTest = testSelectorFonts(container);
    const alignmentTest = testVerticalAlignment(container);
    const paddingTest = testPaddingAndSpacing(container);
    const themeTest = testThemeOverrides(container);
    const expectedTest = testExpectedValues(container);
    
    const report = {
        context: context,
        layout: 'STEP_BY_STEP',
        selectorCount: selectors.length,
        fontConsistent: fontTest?.fontConsistent || false,
        alignmentCorrect: alignmentTest?.alignmentCorrect || false,
        paddingConsistent: paddingTest?.paddingConsistent || false,
        themeResistant: themeTest?.themeResistant || false,
        expectedValuesMatch: expectedTest?.allMatch || false,
        status: 'CHECKING',
        issues: []
    };
    
    // Determine overall status
    const allPassed = report.fontConsistent && report.alignmentCorrect && 
                     report.paddingConsistent && report.themeResistant && 
                     report.expectedValuesMatch;
    
    if (allPassed) {
        report.status = '✅ PERFECT ALIGNMENT';
    } else if (report.alignmentCorrect && report.fontConsistent) {
        report.status = '⚠️ MOSTLY ALIGNED, MINOR ISSUES';
    } else {
        report.status = '❌ ALIGNMENT NEEDS FIXING';
    }
    
    // Generate issues list
    if (!report.fontConsistent) {
        report.issues.push('Font consistency issues (family, size, weight, or line-height)');
    }
    
    if (!report.alignmentCorrect) {
        report.issues.push('Vertical alignment issues (height, display, or centering)');
    }
    
    if (!report.paddingConsistent) {
        report.issues.push('Padding and spacing inconsistencies');
    }
    
    if (!report.themeResistant) {
        report.issues.push('Theme overrides affecting selector styling');
    }
    
    if (!report.expectedValuesMatch) {
        report.issues.push('Values don\'t match Live Preview expectations');
    }
    
    console.log('\n📊 === SELECTOR ALIGNMENT REPORT ===');
    console.log(`Status: ${report.status}`);
    console.log(`Context: ${report.context}`);
    console.log(`Layout: ${report.layout}`);
    console.log(`Selector count: ${report.selectorCount}`);
    console.log(`Font consistent: ${report.fontConsistent ? '✅' : '❌'}`);
    console.log(`Alignment correct: ${report.alignmentCorrect ? '✅' : '❌'}`);
    console.log(`Padding consistent: ${report.paddingConsistent ? '✅' : '❌'}`);
    console.log(`Theme resistant: ${report.themeResistant ? '✅' : '❌'}`);
    console.log(`Expected values match: ${report.expectedValuesMatch ? '✅' : '❌'}`);
    
    if (report.issues.length > 0) {
        console.log('\n💡 Issues to fix:');
        report.issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue}`);
        });
    } else {
        console.log('\n🎉 No issues found - selectors are perfectly aligned!');
    }
    
    // Store report for manual access
    window.selectorAlignmentReport = report;
}

// Manual test helper functions
window.testStepByStepSelectorAlignment = function() {
    console.log('🔧 Manual Step-by-Step selector alignment test...');
    testStepByStepSelectorAlignment();
};

window.highlightSelectors = function() {
    console.log('🎨 Highlighting selectors...');
    
    const selectors = document.querySelectorAll('.step-container select, select.wsf-input');
    
    selectors.forEach((select, index) => {
        select.style.outline = '3px solid red';
        select.style.outlineOffset = '2px';
        
        const style = window.getComputedStyle(select);
        const height = select.getBoundingClientRect().height;
        
        select.title = `Selector ${index + 1}: ${height.toFixed(1)}px, ${style.fontSize}, ${style.fontFamily.split(',')[0]}`;
    });
    
    console.log(`✅ Highlighted ${selectors.length} selectors`);
    
    setTimeout(() => {
        selectors.forEach(select => {
            select.style.outline = '';
            select.style.outlineOffset = '';
            select.title = '';
        });
        console.log('🧹 Highlights removed');
    }, 5000);
};

// Auto-run the test
testStepByStepSelectorAlignment();

console.log('\n🔧 Manual test functions available:');
console.log('  - testStepByStepSelectorAlignment() - Run alignment test');
console.log('  - highlightSelectors() - Highlight selectors (5s)');
console.log('  - selectorAlignmentReport - Access detailed report');
