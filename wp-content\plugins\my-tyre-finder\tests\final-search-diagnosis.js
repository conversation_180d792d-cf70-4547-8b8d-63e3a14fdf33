// Final diagnosis script for "Sizes not found" issue
console.log('=== FINAL SEARCH DIAGNOSIS ===');

// Step 1: Check basic setup
console.log('1. Basic Setup Check:');
const hasWidget = !!window.wheelFitWidget;
const hasAjaxData = !!window.WheelFitData;
const hasSelectedData = hasWidget && !!window.wheelFitWidget.selectedData;

console.log(`   Widget exists: ${hasWidget ? '✅' : '❌'}`);
console.log(`   AJAX data exists: ${hasAjaxData ? '✅' : '❌'}`);
console.log(`   Selected data exists: ${hasSelectedData ? '✅' : '❌'}`);

if (!hasWidget || !hasAjaxData || !hasSelectedData) {
    console.log('❌ Basic setup incomplete - cannot proceed');
    return;
}

// Step 2: Analyze selected data
console.log('2. Selected Data Analysis:');
const selectedData = window.wheelFitWidget.selectedData;
console.log('   Current data:', selectedData);

const requiredFields = ['make', 'model', 'modification'];
const optionalFields = ['year', 'generation'];

const missingRequired = requiredFields.filter(field => !selectedData[field]);
const hasOptional = optionalFields.some(field => selectedData[field]);

console.log(`   Missing required fields: ${missingRequired.length > 0 ? missingRequired.join(', ') : 'None'}`);
console.log(`   Has year or generation: ${hasOptional ? '✅' : '❌'}`);

if (missingRequired.length > 0 || !hasOptional) {
    console.log('❌ Insufficient data for search');
    return;
}

// Step 3: Test direct API call
console.log('3. Direct API Test:');

async function testAPI() {
    const formData = new FormData();
    formData.append('action', 'wf_search_sizes');
    formData.append('nonce', window.WheelFitData.nonce);
    
    // Add all selected data
    Object.keys(selectedData).forEach(key => {
        if (selectedData[key]) {
            formData.append(key, selectedData[key]);
        }
    });
    
    console.log('   Sending request with data:', Object.fromEntries(formData.entries()));
    
    try {
        const response = await fetch(window.WheelFitData.ajaxurl, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            console.log(`   ❌ HTTP Error: ${response.status} ${response.statusText}`);
            return null;
        }
        
        const result = await response.json();
        console.log('   Raw API response:', result);
        
        if (result.success) {
            const data = result.data;
            const factoryCount = data?.factory_sizes?.length || 0;
            const optionalCount = data?.optional_sizes?.length || 0;
            const totalCount = data?.total_count || 0;
            
            console.log(`   ✅ API Success: Factory=${factoryCount}, Optional=${optionalCount}, Total=${totalCount}`);
            
            if (factoryCount === 0 && optionalCount === 0) {
                console.log('   🔍 ROOT CAUSE: API returns empty results');
                console.log('   Possible reasons:');
                console.log('     - Vehicle not in API database');
                console.log('     - Incorrect parameter format');
                console.log('     - API key issues');
                console.log('     - Modification slug mismatch');
                
                // Log sample data for debugging
                if (data.factory_sizes) console.log('   Factory sizes array:', data.factory_sizes);
                if (data.optional_sizes) console.log('   Optional sizes array:', data.optional_sizes);
            } else {
                console.log('   ✅ API returns data - issue is in frontend processing');
                console.log('   Sample factory size:', data.factory_sizes?.[0]);
                console.log('   Sample optional size:', data.optional_sizes?.[0]);
            }
            
            return result;
        } else {
            console.log(`   ❌ API Error: ${result.data || 'Unknown error'}`);
            return null;
        }
    } catch (error) {
        console.log(`   ❌ Request failed: ${error.message}`);
        return null;
    }
}

// Step 4: Test widget search method
console.log('4. Widget Search Method Test:');

async function testWidgetSearch() {
    console.log('   Calling widget.searchSizes()...');
    
    // Monitor console logs during search
    const originalLog = console.log;
    const searchLogs = [];
    
    console.log = function(...args) {
        const message = args.join(' ');
        if (message.includes('[searchSizes]') || message.includes('[displayResults]')) {
            searchLogs.push(message);
        }
        originalLog.apply(console, args);
    };
    
    try {
        await window.wheelFitWidget.searchSizes();
        console.log = originalLog; // Restore
        
        console.log('   ✅ Widget search completed');
        console.log('   Search logs captured:', searchLogs.length);
        searchLogs.forEach(log => console.log(`     ${log}`));
        
        // Check final DOM state
        setTimeout(() => {
            const resultsContainer = document.getElementById('search-results');
            const noResults = document.getElementById('no-results');
            const factoryGrid = document.getElementById('factory-grid');
            const optionalGrid = document.getElementById('optional-grid');
            
            const state = {
                resultsVisible: resultsContainer && !resultsContainer.classList.contains('hidden'),
                noResultsVisible: noResults && !noResults.classList.contains('hidden'),
                factoryCards: factoryGrid ? factoryGrid.children.length : 0,
                optionalCards: optionalGrid ? optionalGrid.children.length : 0
            };
            
            console.log('   Final DOM state:', state);
            
            if (state.noResultsVisible && state.factoryCards === 0 && state.optionalCards === 0) {
                console.log('   🔍 CONFIRMED: "Sizes not found" is displayed');
            } else if (state.factoryCards > 0 || state.optionalCards > 0) {
                console.log('   ✅ Size cards are present - display is working');
            }
        }, 1000);
        
    } catch (error) {
        console.log = originalLog; // Restore
        console.log(`   ❌ Widget search failed: ${error.message}`);
    }
}

// Step 5: Compare with working garage data
console.log('5. Garage Data Comparison:');

if (typeof LocalStorageHandler !== 'undefined') {
    const storage = new LocalStorageHandler();
    const garage = storage.getGarage();
    
    if (garage.length > 0) {
        const workingItem = garage[0];
        console.log('   Working garage item:', workingItem);
        
        // Compare data structures
        const comparison = {};
        ['make', 'model', 'year', 'generation', 'modification'].forEach(field => {
            comparison[field] = {
                garage: workingItem[field],
                current: selectedData[field],
                match: workingItem[field] === selectedData[field]
            };
        });
        
        console.log('   Data comparison:', comparison);
        
        const mismatches = Object.keys(comparison).filter(field => !comparison[field].match);
        if (mismatches.length > 0) {
            console.log(`   ⚠️ Mismatches found in: ${mismatches.join(', ')}`);
            console.log('   This might explain why garage works but manual search doesn\'t');
        } else {
            console.log('   ✅ Data matches garage item exactly');
        }
    } else {
        console.log('   No garage items to compare');
    }
} else {
    console.log('   LocalStorageHandler not available');
}

// Run all tests
console.log('\n=== RUNNING DIAGNOSIS ===');

async function runDiagnosis() {
    const apiResult = await testAPI();
    await testWidgetSearch();
    
    console.log('\n=== DIAGNOSIS COMPLETE ===');
    console.log('Summary:');
    
    if (!apiResult) {
        console.log('🔴 ISSUE: API request failed');
        console.log('   Check: Network connectivity, AJAX URL, nonce validity');
    } else if (apiResult.success) {
        const hasData = (apiResult.data?.factory_sizes?.length || 0) + (apiResult.data?.optional_sizes?.length || 0) > 0;
        
        if (!hasData) {
            console.log('🔴 ISSUE: API returns empty results');
            console.log('   Check: Vehicle exists in API, parameter format, API key');
        } else {
            console.log('🟡 ISSUE: API returns data but frontend doesn\'t display it');
            console.log('   Check: displayResults function, DOM elements, JavaScript errors');
        }
    } else {
        console.log('🔴 ISSUE: API returns error');
        console.log('   Check: Backend PHP errors, API service status');
    }
    
    console.log('\nNext steps:');
    console.log('1. Check WordPress error logs for PHP errors');
    console.log('2. Verify API key in admin settings');
    console.log('3. Test with known working vehicle data');
    console.log('4. Check browser network tab for failed requests');
}

runDiagnosis();
