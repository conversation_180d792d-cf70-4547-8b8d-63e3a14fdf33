/**
 * Тест CSS переменных в виджете
 * Запустите в консоли браузера на странице с виджетом
 */

(function() {
    'use strict';

    console.log('🎨 === ТЕСТ CSS ПЕРЕМЕННЫХ ВИДЖЕТА ===');

    // Найти виджет
    const widget = document.querySelector('.wheel-fit-widget, .wsf-finder-widget, #widget-preview');
    if (!widget) {
        console.log('❌ Виджет не найден');
        return;
    }

    console.log('✅ Виджет найден:', widget.id || widget.className);

    // Проверить CSS переменные
    function checkCSSVariables() {
        console.log('\n1️⃣ Проверка CSS переменных...');
        
        const variables = [
            '--wsf-bg',
            '--wsf-text', 
            '--wsf-primary',
            '--wsf-hover',
            '--wsf-border',
            '--wsf-muted'
        ];
        
        variables.forEach(varName => {
            const value = widget.style.getPropertyValue(varName);
            console.log(`${varName}: ${value || 'не установлена'}`);
        });
    }

    // Проверить элементы с жестко заданными цветами
    function checkHardcodedColors() {
        console.log('\n2️⃣ Проверка жестко заданных цветов...');
        
        const problematicClasses = [
            'bg-white',
            'bg-slate-50', 
            'text-slate-900',
            'text-slate-600',
            'bg-blue-600',
            'border-slate-300'
        ];
        
        let foundProblems = 0;
        
        problematicClasses.forEach(className => {
            const elements = widget.querySelectorAll(`.${className}`);
            if (elements.length > 0) {
                console.log(`❌ Найдено ${elements.length} элементов с классом .${className}`);
                foundProblems += elements.length;
            }
        });
        
        if (foundProblems === 0) {
            console.log('✅ Жестко заданные цвета не найдены');
        } else {
            console.log(`⚠️ Найдено ${foundProblems} элементов с жестко заданными цветами`);
        }
        
        return foundProblems === 0;
    }

    // Проверить элементы с CSS переменными
    function checkVariableUsage() {
        console.log('\n3️⃣ Проверка использования CSS переменных...');
        
        const goodClasses = [
            'bg-wsf-bg',
            'text-wsf-text',
            'bg-wsf-primary',
            'border-wsf-border',
            'text-wsf-muted'
        ];
        
        let foundGood = 0;
        
        goodClasses.forEach(className => {
            const elements = widget.querySelectorAll(`.${className}`);
            if (elements.length > 0) {
                console.log(`✅ Найдено ${elements.length} элементов с классом .${className}`);
                foundGood += elements.length;
            }
        });
        
        if (foundGood === 0) {
            console.log('⚠️ Элементы с CSS переменными не найдены');
        } else {
            console.log(`✅ Найдено ${foundGood} элементов с CSS переменными`);
        }
        
        return foundGood > 0;
    }

    // Тест применения тем
    function testThemeApplication() {
        console.log('\n4️⃣ Тест применения тем...');
        
        const testThemes = {
            light: {
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#1f2937',
                '--wsf-primary': '#2563eb',
                '--wsf-border': '#e5e7eb',
                '--wsf-muted': '#6b7280'
            },
            dark: {
                '--wsf-bg': '#1e1e1e',
                '--wsf-text': '#f3f4f6',
                '--wsf-primary': '#7dd3fc',
                '--wsf-border': '#374151',
                '--wsf-muted': '#9ca3af'
            }
        };
        
        console.log('Применение светлой темы...');
        Object.entries(testThemes.light).forEach(([prop, value]) => {
            widget.style.setProperty(prop, value);
        });
        
        setTimeout(() => {
            console.log('Применение темной темы...');
            Object.entries(testThemes.dark).forEach(([prop, value]) => {
                widget.style.setProperty(prop, value);
            });
            
            setTimeout(() => {
                console.log('Возврат к светлой теме...');
                Object.entries(testThemes.light).forEach(([prop, value]) => {
                    widget.style.setProperty(prop, value);
                });
                console.log('✅ Тест применения тем завершен');
            }, 2000);
        }, 2000);
    }

    // Создать визуальный индикатор
    function createIndicator(results) {
        const indicator = document.createElement('div');
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 300px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        const score = Object.values(results).filter(Boolean).length;
        const total = Object.keys(results).length;
        const percentage = Math.round((score / total) * 100);
        
        indicator.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h3 style="margin: 0; font-size: 14px; color: #333;">
                    🎨 CSS Переменные
                </h3>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="border: none; background: #dc3545; color: white; 
                               border-radius: 4px; padding: 4px 8px; cursor: pointer;">✕</button>
            </div>
            
            <div style="font-size: 18px; font-weight: bold; color: ${percentage >= 80 ? '#28a745' : percentage >= 60 ? '#ffc107' : '#dc3545'}; text-align: center; margin-bottom: 8px;">
                ${percentage}%
            </div>
            
            <div style="font-size: 12px; line-height: 1.4;">
                ${Object.entries(results).map(([test, passed]) => `
                    <div style="display: flex; align-items: center; margin-bottom: 4px;">
                        <span style="margin-right: 8px;">${passed ? '✅' : '❌'}</span>
                        <span>${test}</span>
                    </div>
                `).join('')}
            </div>
            
            <div style="margin-top: 12px; font-size: 11px; color: #666;">
                ${percentage >= 80 ? 'Виджет готов к темизации!' : 
                  percentage >= 60 ? 'Частично готов' : 
                  'Требуется доработка'}
            </div>
        `;
        
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            if (document.body.contains(indicator)) {
                indicator.remove();
            }
        }, 15000);
    }

    // Основная функция
    function runTest() {
        checkCSSVariables();
        
        const results = {
            'Нет жестких цветов': checkHardcodedColors(),
            'Используются переменные': checkVariableUsage()
        };
        
        console.log('\n📊 === РЕЗУЛЬТАТЫ ===');
        Object.entries(results).forEach(([test, passed]) => {
            console.log(`${passed ? '✅' : '❌'} ${test}`);
        });
        
        createIndicator(results);
        testThemeApplication();
        
        console.log('\n💡 Рекомендации:');
        if (!results['Нет жестких цветов']) {
            console.log('1. Замените жестко заданные цвета на CSS переменные');
            console.log('2. Пересоберите CSS: npm run build:widget');
        }
        if (!results['Используются переменные']) {
            console.log('3. Убедитесь, что Tailwind конфигурация включает wsf-* цвета');
        }
        
        // Добавляем функции для ручного тестирования
        window.applyLightTheme = () => {
            Object.entries({
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#1f2937',
                '--wsf-primary': '#2563eb',
                '--wsf-border': '#e5e7eb'
            }).forEach(([prop, value]) => {
                widget.style.setProperty(prop, value);
            });
            console.log('✅ Применена светлая тема');
        };
        
        window.applyDarkTheme = () => {
            Object.entries({
                '--wsf-bg': '#1e1e1e',
                '--wsf-text': '#f3f4f6',
                '--wsf-primary': '#7dd3fc',
                '--wsf-border': '#374151'
            }).forEach(([prop, value]) => {
                widget.style.setProperty(prop, value);
            });
            console.log('✅ Применена темная тема');
        };
        
        console.log('\n🎮 Ручное тестирование:');
        console.log('applyLightTheme() - светлая тема');
        console.log('applyDarkTheme() - темная тема');
    }

    runTest();

})();
