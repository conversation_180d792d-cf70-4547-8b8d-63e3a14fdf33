<?php

declare(strict_types=1);

namespace MyTyreFinder\Admin;

use MyTyreFinder\Frontend\FormRenderer;

require_once __DIR__ . '/ApiPage.php';
require_once __DIR__ . '/ApiValidator.php';
require_once __DIR__ . '/AppearancePage.php';
require_once __DIR__ . '/BrandFilters.php';
require_once __DIR__ . '/FeaturesPage.php';
require_once __DIR__ . '/AnalyticsPage.php';
require_once __DIR__ . '/LogsPage.php';
require_once __DIR__ . '/Translations.php';

/**
 * Admin panel functionality for Wheel-Size plugin
 */
final class Admin
{
    public function register(): void
    {
        // Always register API page
        (new ApiPage())->register();
        // Register all admin sub-pages unconditionally. Their UI will be disabled automatically
        // when the API key is missing, so we no longer hide them completely.
        (new AppearancePage())->register();
        (new FeaturesPage())->register();
        (new AnalyticsPage())->register();
        (new LogsPage())->register();
        (new Translations())->register();

        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_init', [$this, 'handle_activation_redirect']);
        add_action('admin_notices', [$this, 'show_api_configuration_notice']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        // Misc AJAX handlers
        add_action('wp_ajax_wheel_size_reset_stats', [$this, 'ajax_reset_stats']);
        add_action('wp_ajax_wheel_size_reset_analytics', [$this, 'ajax_reset_analytics']);
        add_action('wp_ajax_nopriv_wf_search_by_tire', [$this, 'ajax_search_by_tire']);
        add_action('wp_ajax_wf_search_by_tire', [$this, 'ajax_search_by_tire']);
        add_action('wp_ajax_nopriv_wf_get_tire_widths', [$this, 'ajax_get_tire_widths']);
        add_action('wp_ajax_wf_get_tire_widths', [$this, 'ajax_get_tire_widths']);
        add_action('wp_ajax_nopriv_wf_get_tire_profiles', [$this, 'ajax_get_tire_profiles']);
        add_action('wp_ajax_wf_get_tire_profiles', [$this, 'ajax_get_tire_profiles']);
        add_action('wp_ajax_nopriv_wf_get_tire_diameters', [$this, 'ajax_get_tire_diameters']);
        add_action('wp_ajax_wf_get_tire_diameters', [$this, 'ajax_get_tire_diameters']);
        add_action('wp_ajax_nopriv_wf_get_makes_details', [$this, 'ajax_get_makes_details']);
        add_action('wp_ajax_wf_get_makes_details', [$this, 'ajax_get_makes_details']);
        
        // AJAX for Wizard
        add_action('wp_ajax_nopriv_wizard_get_makes', [$this, 'ajax_wizard_get_makes']);
        add_action('wp_ajax_wizard_get_makes', [$this, 'ajax_wizard_get_makes']);
        add_action('wp_ajax_nopriv_wizard_get_models', [$this, 'ajax_wizard_get_models']);
        add_action('wp_ajax_wizard_get_models', [$this, 'ajax_wizard_get_models']);
        add_action('wp_ajax_nopriv_wizard_get_years', [$this, 'ajax_wizard_get_years']);
        add_action('wp_ajax_wizard_get_years', [$this, 'ajax_wizard_get_years']);
        add_action('wp_ajax_nopriv_wizard_get_modifications', [$this, 'ajax_wizard_get_modifications']);
        add_action('wp_ajax_wizard_get_modifications', [$this, 'ajax_wizard_get_modifications']);
        add_action('wp_ajax_nopriv_wizard_search', [$this, 'ajax_wizard_search']);
        add_action('wp_ajax_wizard_search', [$this, 'ajax_wizard_search']);

        // Log updates for region filter option
        add_action('update_option_wheel_size_regions', [$this, 'log_region_filter_update'], 10, 3);
        BrandFilters::register();

        // One-time compatibility fix: if layout is not wizard but legacy flow option is still flow1 → reset to none
        add_action('init', static function(){
            $layout_opt = get_option('wheel_size_form_layout', 'stepper');
            $flow_opt   = get_option('wheel_size_active_flow', 'none');
            if ($layout_opt !== 'wizard' && $flow_opt !== 'none') {
                update_option('wheel_size_active_flow', 'none');
            }
        }, 20);
    }

    /**
     * Register plugin settings
     */
    public function register_settings(): void
    {
        // Filter Settings
        register_setting('wheel_size_filter_settings', 'wheel_size_allowed_makes');
        register_setting('wheel_size_filter_settings', 'wheel_size_blocked_makes');

        // Brand Filter settings
        register_setting('wheel_size_feature_settings', 'wheel_size_include_makes');
        register_setting('wheel_size_feature_settings', 'wheel_size_exclude_makes');

        // Feature flags
        // register_setting('wheel_size_feature_settings', 'wheel_size_enable_translations', [
        //     'type' => 'boolean',
        //     'default' => false,
        //     'sanitize_callback' => 'rest_sanitize_boolean',
        // ]);
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook): void
    {
        if (strpos($hook, 'wheel-size') === false) {
            return;
        }

        /* --------------------------------------------------------------
         * When the API key is NOT configured we still show plugin pages
         * but must disable every interactive control to prevent changes
         * or API calls. We enqueue a tiny inline script & style to set
         * the disabled attribute and apply a faded look. Skip the API
         * settings screen itself so the user can enter the key.
         * ------------------------------------------------------------ */

        if (!ApiValidator::is_api_configured() && !ApiValidator::is_api_settings_page()) {
            // Style
            if (!wp_style_is('wheel-size-disabled-ui', 'registered')) {
                wp_register_style('wheel-size-disabled-ui', false);
            }
            wp_enqueue_style('wheel-size-disabled-ui');
            wp_add_inline_style('wheel-size-disabled-ui', '.ws-disabled-ui, .ws-disabled-ui *{opacity:0.55!important;pointer-events:none!important;cursor:not-allowed!important;} .wsf-theme-panel .wsf-theme-panel__content, .wsf-theme-panel .wsf-theme-panel__content *{opacity:0.55!important;pointer-events:none!important;cursor:not-allowed!important;}');

            // Script (runs after DOM ready)
            wp_register_script('wheel-size-disabled-ui', '', [], '1.0.0', true);
            wp_enqueue_script('wheel-size-disabled-ui');
            wp_add_inline_script('wheel-size-disabled-ui', 'document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll("#wpbody-content .wrap input, #wpbody-content .wrap select, #wpbody-content .wrap textarea, #wpbody-content .wrap button, #wpbody-content .wrap a.button").forEach(function(el){el.classList.add("ws-disabled-ui");el.setAttribute("disabled","disabled");});});');
        }

        // Do not load legacy preview on pages with their own Twig previews.
        $no_preview_pages = ['wheel-size-appearance'];
        $page_slug = str_replace('wheel-size_page_', '', $hook);

        if (in_array($page_slug, $no_preview_pages, true)) {
            return;
        }

        $plugin_file_path = dirname(__DIR__, 2) . '/my-tyre-finder.php';

        $admin_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/admin.css';
        if (file_exists($admin_css_path)) {
            wp_enqueue_style(
                'wheel-size-admin',
                plugins_url('assets/css/admin.css', $plugin_file_path),
                [],
                filemtime($admin_css_path)
            );
        }

        $admin_js_path = plugin_dir_path($plugin_file_path) . 'assets/js/admin.js';
        if (file_exists($admin_js_path)) {
            wp_enqueue_script(
                'wheel-size-admin',
                plugins_url('assets/js/admin.js', $plugin_file_path),
                ['jquery'],
                filemtime($admin_js_path),
                true
            );
        }
    }

    /**
     * Check if Garage feature is enabled (public static so it can be used from front-end code)
     */
    public static function garage_enabled(): bool
    {
        $features = get_option('wheel_size_features', ['enable_garage' => false]);
        return (bool) ($features['enable_garage'] ?? false);
    }

    /* ================= LOGS AJAX =================== */
    public function ajax_reset_stats(): void
    {
        if (
            !isset($_POST['nonce']) ||
            !wp_verify_nonce(sanitize_key($_POST['nonce']), 'wheel_size_reset_stats_nonce')
        ) {
            wp_send_json_error('Invalid nonce', 403);
        }
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied', 403);
        }
        delete_option('wheel_size_api_stats');
        wp_send_json_success();
    }

    public function ajax_reset_analytics(): void
    {
        if (
            !isset($_POST['nonce']) ||
            !wp_verify_nonce(sanitize_key($_POST['nonce']), 'wheel_size_reset_analytics_nonce')
        ) {
            wp_send_json_error('Invalid nonce', 403);
        }
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied', 403);
        }
        delete_option('wheel_size_searches');
        wp_send_json_success();
    }

    public function ajax_search_by_tire(): void
    {
        // Check API configuration first
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured. Please configure your API key in the plugin settings.', 403);
            return;
        }

        if (!isset($_POST['nonce']) || !wp_verify_nonce(sanitize_text_field($_POST['nonce']), 'wheel_fit_nonce')) {
            wp_send_json_error('Invalid nonce.', 403);
            return;
        }

        $width = !empty($_POST['section_width']) ? (int)$_POST['section_width'] : 0;
        $profile = !empty($_POST['aspect_ratio']) ? (int)$_POST['aspect_ratio'] : 0;
        $diameter = !empty($_POST['rim_diameter']) ? (float)$_POST['rim_diameter'] : 0;

        if (!$width || !$profile || !$diameter) {
            wp_send_json_error('Missing required tire size parameters.', 400);
            return;
        }

        try {
            $api = new \MyTyreFinder\Services\WheelSizeApi();
            $results = $api->searchByTire($width, $profile, $diameter);

            wp_send_json_success($results);
            
        } catch (\Throwable $e) {
            error_log('AJAX search by tire error: ' . $e->getMessage());
            wp_send_json_error('An unexpected server error occurred.', 500);
        }
    }

    public function ajax_get_tire_widths(): void
    {
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        $api = new \MyTyreFinder\Services\WheelSizeApi();
        wp_send_json_success($api->getTireWidths());
    }

    public function ajax_get_tire_profiles(): void
    {
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        $width = !empty($_POST['section_width']) ? (int)$_POST['section_width'] : null;
        $api = new \MyTyreFinder\Services\WheelSizeApi();
        wp_send_json_success($api->getTireAspectRatios($width));
    }

    public function ajax_get_tire_diameters(): void
    {
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        $width = !empty($_POST['section_width']) ? (int)$_POST['section_width'] : null;
        $profile = !empty($_POST['aspect_ratio']) ? (int)$_POST['aspect_ratio'] : null;
        $api = new \MyTyreFinder\Services\WheelSizeApi();
        wp_send_json_success($api->getTireRimDiameters($width, $profile));
    }

    public function ajax_get_makes_details(): void
    {
        // Prevent API usage when key is missing
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        if (!isset($_POST['nonce']) || !wp_verify_nonce(sanitize_text_field($_POST['nonce']), 'wheel_fit_nonce')) {
            wp_send_json_error('Invalid nonce.', 403);
            return;
        }

        $slugs_json = stripslashes($_POST['slugs'] ?? '[]');
        $slugs = json_decode($slugs_json, true);

        if (!is_array($slugs) || empty($slugs)) {
            wp_send_json_success([]); // Return empty success if no slugs provided
            return;
        }

        try {
            $api = new \MyTyreFinder\Services\WheelSizeApi();
            $results = $api->getMakes($slugs, false); // Don't apply brand filters for specific slugs

            // Re-key the array by slug for easy lookup on the frontend
            $keyed_results = [];
            foreach ($results as $make) {
                if (isset($make['slug'])) {
                    $keyed_results[$make['slug']] = $make;
                }
            }
            wp_send_json_success($keyed_results);

        } catch (\Throwable $e) {
            error_log('AJAX get makes details error: ' . $e->getMessage());
            wp_send_json_error('An unexpected server error occurred.', 500);
        }
    }

    public function ajax_wizard_get_makes(): void {
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        $api = new \MyTyreFinder\Services\WheelSizeApi();
        wp_send_json_success($api->getMakes(null, true)); // Apply brand filters for wizard
    }
    public function ajax_wizard_get_models(): void {
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        $make = sanitize_key($_POST['make'] ?? '');
        if (!$make) { wp_send_json_error('Make is required.', 400); }
        $api = new \MyTyreFinder\Services\WheelSizeApi();
        wp_send_json_success($api->getModels($make));
    }
    public function ajax_wizard_get_years(): void {
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        $make = sanitize_key($_POST['make'] ?? '');
        $model = sanitize_key($_POST['model'] ?? '');
        if (!$make || !$model) { wp_send_json_error('Make and model are required.', 400); }
        $api = new \MyTyreFinder\Services\WheelSizeApi();
        wp_send_json_success($api->getYears($make, $model));
    }
    public function ajax_wizard_get_modifications(): void {
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        $make = sanitize_key($_POST['make'] ?? '');
        $model = sanitize_key($_POST['model'] ?? '');
        $year = (int) ($_POST['year'] ?? 0);
        if (!$make || !$model || !$year) { wp_send_json_error('Make, model and year are required.', 400); }
        $api = new \MyTyreFinder\Services\WheelSizeApi();
        wp_send_json_success($api->getModifications($make, $model, $year));
    }
    public function ajax_wizard_search(): void {
        if (!ApiValidator::is_api_configured()) {
            wp_send_json_error('API key not configured.', 403);
            return;
        }
        $make = sanitize_key($_POST['make'] ?? '');
        $model = sanitize_key($_POST['model'] ?? '');
        $year = (int) ($_POST['year'] ?? 0);
        $modification = sanitize_key($_POST['modification'] ?? '');
        if (!$make || !$model || !$year) { wp_send_json_error('Make, model and year are required.', 400); }
        $api = new \MyTyreFinder\Services\WheelSizeApi();
        wp_send_json_success($api->searchByModel($make, $model, $year, $modification ?: null));
    }

    // Log updates for region filter option
    public function log_region_filter_update($old_value, $value, $option): void
    {
        // Prepare values for readable log
        $old = is_array($old_value) ? implode(', ', array_map('sanitize_key', $old_value)) : (string) $old_value;
        $new = is_array($value) ? implode(', ', array_map('sanitize_key', $value)) : (string) $value;
        error_log('Wheel-Size Region Filter updated: [' . $old . '] → [' . $new . ']');
    }

    /**
     * Рендер только Wizard-flow независимо от настроек.
     * [wheel_fit_wizard]
     */
    public function render_wizard(): string
    {
        $flow_cb = static fn($v) => 'flow1';
        $layout_cb = static fn($v) => 'none';

        // временно переопределяем опции, чтобы не трогать глобальные настройки
        add_filter('option_wheel_size_active_flow', $flow_cb, 10, 1);
        add_filter('option_wheel_size_form_layout', $layout_cb, 10, 1);

        $html = $this->render_form();

        // убираем фильтры, чтобы не повлиять на остальные вызовы
        remove_filter('option_wheel_size_active_flow', $flow_cb, 10);
        remove_filter('option_wheel_size_form_layout', $layout_cb, 10);

        return $html;
    }

    private function render_form(): string {
        return (new FormRenderer())->render();
    }

    /**
     * Handle activation redirect to API settings page
     */
    public function handle_activation_redirect(): void
    {
        // Only redirect if we have the activation flag and API is not configured
        if (ApiValidator::get_and_clear_activation_redirect() && !ApiValidator::is_api_configured()) {
            // Only redirect if user can manage options and we're not already on the API page
            if (current_user_can('manage_options') && !ApiValidator::is_api_settings_page()) {
                wp_safe_redirect(ApiValidator::get_api_settings_url());
                exit;
            }
        }
    }

    /**
     * Show admin notice when API is not configured
     */
    public function show_api_configuration_notice(): void
    {
        if (!ApiValidator::should_show_admin_notice()) {
            return;
        }

        // Don't show on the API settings page itself
        if (ApiValidator::is_api_settings_page()) {
            return;
        }

        $api_settings_url = ApiValidator::get_api_settings_url();

        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>Wheel-Size Plugin:</strong> Plugin functionality is disabled until you configure a valid API key. ';
        echo '<a href="' . esc_url($api_settings_url) . '">Configure API Settings</a></p>';
        echo '</div>';
    }
}