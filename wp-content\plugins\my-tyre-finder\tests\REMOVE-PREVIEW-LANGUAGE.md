# Удаление селектора Preview Language

## Изменение
Убран лишний селектор "Preview Language" из админ-панели Form Configuration, так как язык уже устанавливается в разделе Translations.

## Причина
Селектор "Preview Language" создавал путаницу и дублировал функциональность, которая уже есть в разделе Translations. Пользователи должны устанавливать язык в одном месте - в Translations.

## Что было удалено

### 1. HTML селектор ❌
```html
<!-- Preview Language -->
<div class="flex items-center">
    <label for="preview_language">Preview Language</label>
    <select id="preview_language" name="preview_language">
        <option value="en">English</option>
        <option value="ru">Русский</option>
        <!-- ... другие языки ... -->
    </select>
    <p>Language for preview (does not affect frontend)</p>
</div>
```

### 2. JavaScript переменная ❌
```javascript
const previewLanguageSelect = document.getElementById('preview_language');
```

### 3. Использование в AJAX ❌
```javascript
preview_locale: previewLanguageSelect ? previewLanguageSelect.value : 'fallback'
```

### 4. Обработчик событий ❌
```javascript
[colorInput, autoSearchCheckbox, searchFlowSelect, previewLanguageSelect].forEach(...)
```

## Что осталось

### ✅ Простая логика:
```javascript
preview_locale: '<?php echo esc_js(\MyTyreFinder\Translations\TranslationManager::active_locale()); ?>'
```

Теперь предварительный просмотр всегда использует язык, установленный в разделе **Translations**.

## Преимущества

### 1. Упрощение интерфейса ✅
- Меньше элементов управления
- Нет дублирования функциональности
- Более понятный UX

### 2. Единое место управления языком ✅
- Язык устанавливается только в **Tire Finder → Translations**
- Предварительный просмотр автоматически использует выбранный язык
- Нет путаницы между разными настройками языка

### 3. Упрощение кода ✅
- Меньше JavaScript переменных
- Упрощенная логика AJAX-запросов
- Меньше обработчиков событий

## Как теперь работает

### Установка языка:
1. Перейдите в **Tire Finder → Translations**
2. Выберите нужный язык
3. Сохраните настройки

### Предварительный просмотр:
1. Перейдите в **Tire Finder → Appearance**
2. Предварительный просмотр автоматически использует язык из Translations
3. Изменение Form Layout/Search Flow сохраняет выбранный язык

## Файлы изменены

### `src/admin/AppearancePage.php`:
- Удален HTML блок с селектором языка
- Убрана JavaScript переменная `previewLanguageSelect`
- Упрощена логика AJAX-запроса
- Убран обработчик событий для селектора языка

## Результат

Теперь админ-панель **Form Configuration** содержит только необходимые настройки:
- ✅ Form Layout
- ✅ Search Flow  
- ✅ Auto Search
- ✅ Primary Color
- ❌ ~~Preview Language~~ (убрано)

Язык управляется централизованно через **Translations**, что делает интерфейс более понятным и последовательным.

## Проверка

### ✅ Что должно работать:
1. Предварительный просмотр использует язык из Translations
2. Изменение Form Layout/Search Flow сохраняет переводы
3. Нет лишних элементов в Form Configuration
4. Все переводы работают корректно

### 🔍 Что проверить:
- [ ] В Form Configuration нет селектора "Preview Language"
- [ ] Предварительный просмотр показывает правильный язык
- [ ] Изменение настроек не сбрасывает язык
- [ ] Язык меняется только через Translations

Интерфейс стал чище и понятнее! 🎉
