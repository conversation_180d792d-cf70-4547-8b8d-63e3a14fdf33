<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест переводов в админке</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-weight: bold;
            font-size: 14px;
        }
        .control-group select, .control-group button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .widget-preview {
            border: 2px solid #007cba;
            border-radius: 8px;
            padding: 20px;
            background: white;
            min-height: 300px;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn-primary {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Тест переводов в админке</h1>
        <p>Этот тест проверяет, что переводы правильно применяются в админ-панели при изменении настроек.</p>
        
        <div class="test-controls">
            <div class="control-group">
                <label for="test-locale">Язык:</label>
                <select id="test-locale">
                    <option value="en">English</option>
                    <option value="ru">Русский</option>
                    <option value="de">Deutsch</option>
                    <option value="fr">Français</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="test-search-flow">Search Flow:</label>
                <select id="test-search-flow">
                    <option value="by_vehicle">By Vehicle</option>
                    <option value="by_year">By Year</option>
                    <option value="by_generation">By Generation</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="test-form-layout">Form Layout:</label>
                <select id="test-form-layout">
                    <option value="popup-horizontal">Popup Horizontal</option>
                    <option value="inline">Inline</option>
                    <option value="stepper">Stepper</option>
                    <option value="wizard">Wizard</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>&nbsp;</label>
                <button id="apply-translations" type="button">Применить переводы</button>
                <button id="test-admin-function" type="button">Тест админ-функции</button>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>Предварительный просмотр виджета</h2>
        <div id="widget-preview" class="widget-preview">
            <div class="wsf-finder-widget" data-wsf-theme="default">
                <h2 data-i18n="widget_title">Wheel & Tyre Finder</h2>
                <form id="wheel-fit-form">
                    <div class="form-group">
                        <label for="wf-make" data-i18n="label_make">Make</label>
                        <select id="wf-make" name="make">
                            <option value="" data-i18n="select_make_placeholder">Choose a make</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="wf-model" data-i18n="label_model">Model</label>
                        <select id="wf-model" name="model" disabled>
                            <option value="" data-i18n="select_make_first_placeholder">Select make first</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="wf-year" data-i18n="label_year">Year</label>
                        <select id="wf-year" name="year" disabled>
                            <option value="" data-i18n="select_model_first_placeholder">Select model first</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="wf-modification" data-i18n="label_mods">Modification</label>
                        <select id="wf-modification" name="modification" disabled>
                            <option value="" data-i18n="select_year_first_placeholder">Select year first</option>
                        </select>
                    </div>
                    <button type="submit" class="btn-primary" data-i18n="button_search">Find Tire & Wheel Sizes</button>
                </form>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>Лог тестирования</h2>
        <div id="test-log" class="test-log">
            <div class="log-entry log-info">Система тестирования инициализирована.</div>
        </div>
    </div>

    <script>
        // Переводы для тестирования
        const translations = {
            en: {
                'widget_title': 'Wheel & Tyre Finder',
                'label_make': 'Make',
                'label_model': 'Model',
                'label_year': 'Year',
                'label_mods': 'Modification',
                'select_make_placeholder': 'Choose a make',
                'select_model_placeholder': 'Choose a model',
                'select_year_placeholder': 'Choose a year',
                'select_mods_placeholder': 'Choose a modification',
                'select_make_first_placeholder': 'Select make first',
                'select_model_first_placeholder': 'Select model first',
                'select_year_first_placeholder': 'Select year first',
                'button_search': 'Find Tire & Wheel Sizes'
            },
            ru: {
                'widget_title': 'Подбор шин и дисков',
                'label_make': 'Марка',
                'label_model': 'Модель',
                'label_year': 'Год',
                'label_mods': 'Модификация',
                'select_make_placeholder': 'Выберите марку',
                'select_model_placeholder': 'Выберите модель',
                'select_year_placeholder': 'Выберите год',
                'select_mods_placeholder': 'Выберите модификацию',
                'select_make_first_placeholder': 'Сначала выберите марку',
                'select_model_first_placeholder': 'Сначала выберите модель',
                'select_year_first_placeholder': 'Сначала выберите год',
                'button_search': 'Подобрать размеры'
            },
            de: {
                'widget_title': 'Rad & Reifen Finder',
                'label_make': 'Marke',
                'label_model': 'Modell',
                'label_year': 'Jahr',
                'label_mods': 'Modifikation',
                'select_make_placeholder': 'Marke wählen',
                'select_model_placeholder': 'Modell wählen',
                'select_year_placeholder': 'Jahr wählen',
                'select_mods_placeholder': 'Modifikation wählen',
                'select_make_first_placeholder': 'Zuerst Marke wählen',
                'select_model_first_placeholder': 'Zuerst Modell wählen',
                'select_year_first_placeholder': 'Zuerst Jahr wählen',
                'button_search': 'Reifen- & Radgrößen finden'
            },
            fr: {
                'widget_title': 'Recherche de Roues et Pneus',
                'label_make': 'Marque',
                'label_model': 'Modèle',
                'label_year': 'Année',
                'label_mods': 'Modification',
                'select_make_placeholder': 'Choisir une marque',
                'select_model_placeholder': 'Choisir un modèle',
                'select_year_placeholder': 'Choisir une année',
                'select_mods_placeholder': 'Choisir une modification',
                'select_make_first_placeholder': 'Sélectionner la marque d\'abord',
                'select_model_first_placeholder': 'Sélectionner le modèle d\'abord',
                'select_year_first_placeholder': 'Sélectionner l\'année d\'abord',
                'button_search': 'Trouver les tailles de pneus et roues'
            }
        };

        // Инициализация с английским
        window.WheelFitI18n = translations.en;

        // Утилиты для тестирования
        const TestUtils = {
            log: function(message, type = 'info') {
                const logContainer = document.getElementById('test-log');
                const entry = document.createElement('div');
                entry.className = `log-entry log-${type}`;
                entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                logContainer.appendChild(entry);
                logContainer.scrollTop = logContainer.scrollHeight;
                console.log(`[Test ${type.toUpperCase()}] ${message}`);
            },

            // Функция прямого применения переводов (копия из админ-скрипта)
            applyDirectTranslations: function(container, translations) {
                if (!container || !translations || typeof translations !== 'object') {
                    this.log('Неверные параметры для прямого перевода', 'error');
                    return false;
                }

                let applied = 0;
                
                try {
                    // Применяем переводы к текстовому содержимому
                    const textElements = container.querySelectorAll('[data-i18n]');
                    this.log(`Найдено ${textElements.length} текстовых элементов для перевода`, 'info');
                    
                    textElements.forEach(el => {
                        const key = el.dataset.i18n;
                        if (key && translations[key]) {
                            const oldText = el.textContent.trim();
                            el.textContent = translations[key];
                            this.log(`Переведено "${key}": "${oldText}" → "${translations[key]}"`, 'success');
                            applied++;
                        }
                    });

                    // Специальная обработка для option элементов в select
                    const selectElements = container.querySelectorAll('select');
                    this.log(`Найдено ${selectElements.length} select элементов`, 'info');
                    
                    selectElements.forEach(select => {
                        const placeholderOption = select.querySelector('option[value=""]');
                        if (placeholderOption) {
                            const key = placeholderOption.dataset.i18n;
                            if (key && translations[key]) {
                                const oldText = placeholderOption.textContent.trim();
                                placeholderOption.textContent = translations[key];
                                this.log(`Переведена опция "${key}": "${oldText}" → "${translations[key]}"`, 'success');
                                applied++;
                            }
                        }
                    });

                    this.log(`Применено ${applied} прямых переводов`, applied > 0 ? 'success' : 'warning');
                    return applied > 0;
                    
                } catch (error) {
                    this.log(`Ошибка при прямом применении переводов: ${error.message}`, 'error');
                    return false;
                }
            },

            testTranslations: function(locale) {
                this.log(`Тестирование переводов для языка: ${locale}`, 'info');
                
                // Обновляем глобальные переводы
                window.WheelFitI18n = translations[locale] || translations.en;
                
                // Применяем переводы
                const container = document.getElementById('widget-preview');
                const result = this.applyDirectTranslations(container, window.WheelFitI18n);
                
                if (result) {
                    this.log(`✅ Переводы для ${locale} применены успешно`, 'success');
                } else {
                    this.log(`❌ Не удалось применить переводы для ${locale}`, 'error');
                }
                
                return result;
            }
        };

        // Обработчики событий
        document.getElementById('apply-translations').addEventListener('click', function() {
            const locale = document.getElementById('test-locale').value;
            TestUtils.testTranslations(locale);
        });

        document.getElementById('test-admin-function').addEventListener('click', function() {
            TestUtils.log('Тестирование функции из админ-скрипта...', 'info');
            
            // Проверяем, доступна ли функция applyDirectTranslations
            if (typeof applyDirectTranslations === 'function') {
                TestUtils.log('✅ Функция applyDirectTranslations доступна', 'success');
                const container = document.getElementById('widget-preview');
                const result = applyDirectTranslations(container, window.WheelFitI18n);
                TestUtils.log(`Результат: ${result}`, result ? 'success' : 'warning');
            } else {
                TestUtils.log('❌ Функция applyDirectTranslations недоступна', 'error');
            }
        });

        // Инициализация
        TestUtils.log('Тест переводов в админке инициализирован', 'success');
        
        // Применяем начальные переводы
        setTimeout(() => {
            TestUtils.testTranslations('en');
        }, 500);
    </script>
</body>
</html>
