/**
 * Comprehensive Width Audit for Live Preview
 * 
 * This script performs a complete audit of all width-related CSS rules
 * affecting the Live Preview in WordPress admin.
 * 
 * Run in browser console on /wp-admin/admin.php?page=wheel-size-appearance
 */

console.log('🔍 === COMPREHENSIVE WIDTH AUDIT ===');

const auditResults = {
    widgetContainer: {},
    formElements: {},
    wizardElements: {},
    gridElements: {},
    overflowIssues: [],
    cssConflicts: [],
    recommendations: []
};

function comprehensiveWidthAudit() {
    console.log('📋 Starting comprehensive width audit...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.error('❌ Live Preview container not found');
        return;
    }
    
    // Audit 1: Widget Container
    auditWidgetContainer(previewContainer);
    
    // Audit 2: Form Elements
    auditFormElements(previewContainer);
    
    // Audit 3: Wizard Elements
    auditWizardElements(previewContainer);
    
    // Audit 4: Grid Elements
    auditGridElements(previewContainer);
    
    // Audit 5: Overflow Detection
    detectOverflowIssues(previewContainer);
    
    // Audit 6: CSS Conflicts
    detectCSSConflicts();
    
    // Audit 7: Generate Recommendations
    generateRecommendations();
    
    // Display Results
    displayAuditResults();
}

function auditWidgetContainer(container) {
    console.log('🏠 Auditing widget container...');
    
    const widgets = container.querySelectorAll('.wheel-fit-widget, .wsf-finder-widget');
    
    widgets.forEach((widget, index) => {
        const computedStyle = window.getComputedStyle(widget);
        const rect = widget.getBoundingClientRect();
        
        auditResults.widgetContainer[`widget_${index}`] = {
            width: computedStyle.width,
            maxWidth: computedStyle.maxWidth,
            margin: computedStyle.margin,
            actualWidth: rect.width,
            expectedMaxWidth: '896px', // 56rem
            isCorrect: Math.abs(parseFloat(computedStyle.maxWidth) - 896) < 10,
            isCentered: computedStyle.marginLeft === 'auto' && computedStyle.marginRight === 'auto'
        };
    });
}

function auditFormElements(container) {
    console.log('📝 Auditing form elements...');
    
    const formElements = container.querySelectorAll('select, input, .wsf-input');
    
    formElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        const parent = element.closest('.wheel-fit-widget, .wsf-finder-widget');
        const parentRect = parent ? parent.getBoundingClientRect() : null;
        
        auditResults.formElements[`element_${index}`] = {
            tagName: element.tagName,
            id: element.id,
            classes: Array.from(element.classList),
            width: computedStyle.width,
            maxWidth: computedStyle.maxWidth,
            actualWidth: rect.width,
            parentWidth: parentRect ? parentRect.width : null,
            isFullWidth: computedStyle.width === '100%',
            exceedsParent: parentRect ? rect.width > parentRect.width : false
        };
    });
}

function auditWizardElements(container) {
    console.log('🧙 Auditing wizard elements...');
    
    const wizardElements = [
        { selector: '#wizard-header', name: 'Wizard Header' },
        { selector: '#wizard-nav', name: 'Wizard Navigation' },
        { selector: '.wizard-step-name', name: 'Step Names' },
        { selector: '#wizard-makes-grid', name: 'Makes Grid' },
        { selector: '.wizard-step', name: 'Wizard Steps' },
        { selector: '#wizard-progress-bar', name: 'Progress Bar' }
    ];
    
    wizardElements.forEach(({ selector, name }) => {
        const elements = container.querySelectorAll(selector);
        
        if (elements.length > 0) {
            elements.forEach((element, index) => {
                const computedStyle = window.getComputedStyle(element);
                const rect = element.getBoundingClientRect();
                
                auditResults.wizardElements[`${name.toLowerCase().replace(/\s+/g, '_')}_${index}`] = {
                    name,
                    selector,
                    width: computedStyle.width,
                    maxWidth: computedStyle.maxWidth,
                    actualWidth: rect.width,
                    overflow: computedStyle.overflow,
                    textOverflow: computedStyle.textOverflow,
                    whiteSpace: computedStyle.whiteSpace
                };
            });
        }
    });
}

function auditGridElements(container) {
    console.log('📐 Auditing grid elements...');
    
    const gridElements = container.querySelectorAll('.grid, [class*="grid-cols"]');
    
    gridElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        auditResults.gridElements[`grid_${index}`] = {
            classes: Array.from(element.classList),
            display: computedStyle.display,
            gridTemplateColumns: computedStyle.gridTemplateColumns,
            gap: computedStyle.gap,
            width: computedStyle.width,
            maxWidth: computedStyle.maxWidth,
            actualWidth: rect.width,
            justifyContent: computedStyle.justifyContent
        };
    });
}

function detectOverflowIssues(container) {
    console.log('🌊 Detecting overflow issues...');
    
    const allElements = container.querySelectorAll('*');
    const containerRect = container.getBoundingClientRect();
    
    allElements.forEach(element => {
        const rect = element.getBoundingClientRect();
        
        // Check if element extends beyond container
        if (rect.right > containerRect.right + 5) { // 5px tolerance
            auditResults.overflowIssues.push({
                element: element.tagName + (element.id ? '#' + element.id : '') + (element.className ? '.' + Array.from(element.classList).slice(0,2).join('.') : ''),
                actualWidth: rect.width,
                containerWidth: containerRect.width,
                overflow: rect.right - containerRect.right
            });
        }
    });
}

function detectCSSConflicts() {
    console.log('⚔️ Detecting CSS conflicts...');
    
    // Check for conflicting stylesheets
    const stylesheets = Array.from(document.styleSheets);
    const widthRules = [];
    
    stylesheets.forEach(sheet => {
        try {
            const rules = sheet.cssRules || sheet.rules;
            Array.from(rules).forEach(rule => {
                if (rule.selectorText && rule.selectorText.includes('#widget-preview')) {
                    if (rule.style && (rule.style.width || rule.style.maxWidth)) {
                        widthRules.push({
                            selector: rule.selectorText,
                            width: rule.style.width,
                            maxWidth: rule.style.maxWidth,
                            important: rule.style.cssText.includes('!important'),
                            sheet: sheet.href || 'inline'
                        });
                    }
                }
            });
        } catch (e) {
            // CORS or access issues
        }
    });
    
    auditResults.cssConflicts = widthRules;
}

function generateRecommendations() {
    console.log('💡 Generating recommendations...');
    
    // Check widget container
    Object.values(auditResults.widgetContainer).forEach(widget => {
        if (!widget.isCorrect) {
            auditResults.recommendations.push(`Widget max-width should be 896px (56rem), currently: ${widget.maxWidth}`);
        }
        if (!widget.isCentered) {
            auditResults.recommendations.push('Widget should be centered with margin: 0 auto');
        }
    });
    
    // Check form elements
    Object.values(auditResults.formElements).forEach(element => {
        if (!element.isFullWidth) {
            auditResults.recommendations.push(`Form element should have width: 100%, currently: ${element.width}`);
        }
        if (element.exceedsParent) {
            auditResults.recommendations.push(`Form element exceeds parent width: ${element.actualWidth}px > ${element.parentWidth}px`);
        }
    });
    
    // Check overflow issues
    if (auditResults.overflowIssues.length > 0) {
        auditResults.recommendations.push(`Found ${auditResults.overflowIssues.length} elements with overflow issues`);
    }
    
    // Check CSS conflicts
    const conflictingRules = auditResults.cssConflicts.filter(rule => 
        rule.maxWidth && rule.maxWidth !== '56rem' && rule.maxWidth !== '896px'
    );
    if (conflictingRules.length > 0) {
        auditResults.recommendations.push(`Found ${conflictingRules.length} conflicting max-width rules`);
    }
}

function displayAuditResults() {
    console.log('\n📊 === AUDIT RESULTS ===');
    
    console.log('\n🏠 Widget Container Results:');
    console.table(auditResults.widgetContainer);
    
    console.log('\n📝 Form Elements Results:');
    console.table(auditResults.formElements);
    
    console.log('\n🧙 Wizard Elements Results:');
    console.table(auditResults.wizardElements);
    
    console.log('\n📐 Grid Elements Results:');
    console.table(auditResults.gridElements);
    
    if (auditResults.overflowIssues.length > 0) {
        console.log('\n🌊 Overflow Issues:');
        console.table(auditResults.overflowIssues);
    } else {
        console.log('\n✅ No overflow issues detected');
    }
    
    console.log('\n⚔️ CSS Conflicts:');
    console.table(auditResults.cssConflicts);
    
    console.log('\n💡 Recommendations:');
    auditResults.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
    });
    
    if (auditResults.recommendations.length === 0) {
        console.log('✅ No recommendations - everything looks good!');
    }
}

// Export results for further analysis
window.widthAuditResults = auditResults;
window.runWidthAudit = comprehensiveWidthAudit;

// Auto-run the audit
comprehensiveWidthAudit();

console.log('\n🔧 Manual functions available:');
console.log('  - runWidthAudit() - Run the audit again');
console.log('  - widthAuditResults - Access detailed results');
console.log('  - console.table(widthAuditResults.widgetContainer) - View widget data');
console.log('  - console.table(widthAuditResults.formElements) - View form elements data');
