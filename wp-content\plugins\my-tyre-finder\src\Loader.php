<?php

declare(strict_types=1);

namespace MyTyreFinder;

/**
 * Collects and registers WordPress hooks (actions and filters).
 */
final class Loader
{
    /** @var array<array{hook:string,callback:callable,priority:int,accepted_args:int}> */
    private array $actions = [];

    /** @var array<array{hook:string,callback:callable,priority:int,accepted_args:int}> */
    private array $filters = [];

    /**
     * Queue a WordPress action to be added on run().
     */
    public function add_action(string $hook, callable $callback, int $priority = 10, int $accepted_args = 1): void
    {
        $this->actions[] = [
            'hook'          => $hook,
            'callback'      => $callback,
            'priority'      => $priority,
            'accepted_args' => $accepted_args,
        ];
    }

    /**
     * Queue a WordPress filter to be added on run().
     */
    public function add_filter(string $hook, callable $callback, int $priority = 10, int $accepted_args = 1): void
    {
        $this->filters[] = [
            'hook'          => $hook,
            'callback'      => $callback,
            'priority'      => $priority,
            'accepted_args' => $accepted_args,
        ];
    }

    /**
     * Register all queued actions and filters with WordPress.
     */
    public function run(): void
    {
        foreach ($this->actions as $action) {
            add_action($action['hook'], $action['callback'], $action['priority'], $action['accepted_args']);
        }

        foreach ($this->filters as $filter) {
            add_filter($filter['hook'], $filter['callback'], $filter['priority'], $filter['accepted_args']);
        }
    }
} 