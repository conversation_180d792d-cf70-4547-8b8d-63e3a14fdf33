{# Horizontal Wheel-Fit Form Template (Full Width) #}
<div class="wheel-fit-widget bg-wsf-bg wsf-root-font max-w-5xl mx-auto p-4 md:p-0{% if garage_enabled %} garage-enabled{% endif %}" data-flow-order="{{ flow_order_json|e }}">
    {# Hidden stepper indicator (kept for JS compatibility) #}
    <div class="hidden">
        <div id="step-indicator-1"></div>
        <div id="step-indicator-2"></div>
        <div id="step-indicator-3"></div>
        <div id="step-indicator-4"></div>
        <div id="step-text-1"></div>
        <div id="step-text-2"></div>
        <div id="step-text-3"></div>
        <div id="step-text-4"></div>
        <div id="progress-1"></div>
        <div id="progress-2"></div>
        <div id="progress-3"></div>
    </div>

    {# By Vehicle Form - Horizontal Layout #}
    <div class="w-full">
        <div class="wsf-form-wrapper">
            {# Widget Title - moved inside form wrapper #}
            <div class="wsf-widget__header mb-6">
                <h1 class="wsf-widget__title text-center text-2xl md:text-3xl font-extrabold tracking-tight text-wsf-text m-0" data-i18n="widget_title">{{ widget_title|e }}</h1>
            </div>

            {% set grid_cols_class = auto_search ? 'md:grid-cols-4' : 'md:grid-cols-5' %}
            <form id="tab-by-car" class="tab-panel grid grid-cols-1 sm:grid-cols-2 {{ grid_cols_class }} gap-4 w-full {{ auto_search ? 'auto-search-mode' : '' }}" role="tabpanel">
                {# Dynamic Field Blocks based on flow_order #}
                {% set order = flow_order is not null ? flow_order : ['make', 'model', 'year', 'mod'] %}
                {% for field in order %}
                    {% include 'fields/' ~ field ~ '.twig' %}
                {% endfor %}

                {% if not auto_search %}
                <div class="ws-submit flex flex-col basis-full w-full md:col-span-1 justify-start">
                    <label class="block text-xs font-semibold text-transparent mb-1 select-none">&nbsp;</label>
                    <button type="submit" class="btn-primary flex items-center justify-center" disabled>
                        <span id="search-text" data-i18n="button_search">Find Sizes</span>
                        <div id="search-loader" class="hidden inline-block ml-2"><div class="animate-spin rounded-full h-4 w-4 border-b border-wsf-border border-wsf-border border-wsf-border border-wsf-border border-wsf-border-2 border-white"></div></div>
                    </button>

                    {# Garage Button - positioned below search button within same form block #}
                    {% if garage_enabled %}
                    <div class="flex justify-end mt-2">
                        <button type="button" data-garage-trigger class="inline-flex items-center gap-2 text-sm text-wsf-muted px-3 py-1.5 rounded-md hover:bg-wsf-bg hover:text-wsf-text transition">
                            <i data-lucide="car" class="w-5 h-5"></i>
                            <span class="font-semibold" data-i18n="label_garage">Garage</span>
                            <span id="garage-count" class="wsf-garage-count-badge hidden"></span>
                        </button>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

            </form>

            {# Garage Button for auto_search mode - positioned below form, outside grid #}
            {% if auto_search and garage_enabled %}
            <div class="flex justify-end mt-4 w-full">
                <button type="button" data-garage-trigger class="inline-flex items-center gap-2 text-sm text-wsf-muted px-3 py-1.5 rounded-md hover:bg-wsf-bg hover:text-wsf-text transition">
                    <i data-lucide="car" class="w-5 h-5"></i>
                    <span class="font-semibold" data-i18n="label_garage">Garage</span>
                    <span id="garage-count" class="wsf-garage-count-badge hidden"></span>
                </button>
            </div>
            {% endif %}
        </div>
    </div>

     <!-- Results Section -->
    <div class="w-full">
        <section id="search-results" class="hidden mt-12 bg-wsf-bg shadow-lg rounded-xl p-6 md:p-8 transform transition-all duration-300 ease-in-out">
            {# ... same results structure as other templates ... #}
            <h2 class="text-lg font-semibold text-wsf-text mb-1" data-i18n="section_results">Search Results</h2>
            <p id="vehicle-label" class="text-base font-medium text-gray-900"></p>
            <div id="selected-modification-info" class="mt-1"></div>
            <div class="border-t border-wsf-border my-4"></div>
            <div id="factory-section" class="mb-8 hidden">
                <h3 class="text-sm font-bold text-wsf-text mb-3" data-i18n="section_factory">Factory Sizes</h3>
                <div id="factory-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
            </div>
            <div id="optional-section" class="mb-8 hidden">
                <h3 class="text-sm font-bold text-wsf-text mb-3" data-i18n="section_optional">Optional Sizes</h3>
                <div id="optional-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
            </div>
            <div id="no-results" class="hidden text-center py-8">
                <h3 class="text-lg font-medium text-wsf-text mb-2" data-i18n="text_no_results_header">Sizes not found</h3>
                <p class="text-wsf-muted" data-i18n="text_no_results_body">Try selecting another modification or check your selection.</p>
            </div>
        </section>

        {# Saved Searches (localStorage) #}
        <div id="saved-searches" class="hidden mt-12">
            <h4 class="mb-4 flex items-center gap-2 text-sm font-semibold text-wsf-muted">
                <i data-lucide="history" class="w-4 h-4 text-wsf-muted" aria-hidden="true"></i>
                Your recent searches
            </h4>
            <ul id="history-list" class="space-y-3"></ul>
            <p id="history-empty" class="hidden text-sm text-wsf-muted">No recent searches yet</p>
        </div>
    </div>
</div>

{# All styles are now enqueued via Frontend.php, no inline styles needed here. #}

{# Garage Feature Components #}
{% if garage_enabled %}
<div id="garage-overlay" class="hidden fixed inset-0 z-40 bg-black/25 backdrop-blur-md transition-opacity cursor-pointer"></div>
<aside id="garage-drawer" class="fixed right-0 top-0 h-full w-full max-w-sm bg-wsf-bg shadow-2xl transform translate-x-full transition-transform duration-300 z-50 flex flex-col" style="top: var(--wp-admin--admin-bar--height, 0); height: calc(100% - var(--wp-admin--admin-bar--height, 0));">
  <header class="flex items-center justify-between p-4 border-b border-wsf-border border-wsf-border border-wsf-border border-wsf-border border-wsf-border border-wsf-border">
    <h2 class="text-xl font-bold text-wsf-text" data-i18n="garage_title">My Garage</h2>
    <button id="garage-close-btn" aria-label="Close" class="text-wsf-muted hover:text-wsf-text">
      <svg viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
        <line x1="18" y1="6" x2="6" y2="18"/>
        <line x1="6" y1="6" x2="18" y2="18"/>
      </svg>
    </button>
  </header>
  <ul id="garage-items-list" class="flex-1 min-h-0 overflow-y-auto p-4 space-y-4"></ul>
  <footer id="garage-footer" class="sticky bottom-4 flex-shrink-0 p-4 flex justify-end">
    <button id="garage-clear-all" class="btn-secondary">
      <i data-lucide="trash-2"></i>
      <span data-i18n="garage_clear_all">Clear&nbsp;all</span>
    </button>
  </footer>
</aside>
<div id="garage-toast" class="fixed left-1/2 -translate-x-1/2 bottom-6 bg-wsf-primary text-white text-sm font-medium px-4 py-2 rounded-lg shadow-lg transition opacity-0 translate-y-4 z-50">
  <span data-i18n="garage_saved_notification">Saved to Garage!</span>
</div>
{% endif %}

{# Update WheelFitData for live preview compatibility #}
<script>
if (window.WheelFitData) {
    window.WheelFitData.autoSearch = {{ auto_search ? 'true' : 'false' }};
    window.WheelFitData.formLayout = '{{ form_layout|e }}';
    console.log('[Template] Updated WheelFitData for live preview:', window.WheelFitData);
}
</script>