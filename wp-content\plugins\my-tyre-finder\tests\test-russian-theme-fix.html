<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Russian Theme Names Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .problem-demo {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .solution-demo {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .test-form {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 5px;
            color: #374151;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin-right: 10px;
        }
        
        .form-button:hover {
            background: #1d4ed8;
        }
        
        .form-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .status-log {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .status-success {
            color: #10b981;
        }
        
        .status-error {
            color: #ef4444;
        }
        
        .status-info {
            color: #3b82f6;
        }
        
        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .highlight-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .test-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .test-example {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        
        .test-example h5 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        
        .test-example .example-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .test-example .example-slug {
            font-family: monospace;
            font-size: 12px;
            color: #6b7280;
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Russian Theme Names Fix</h1>
        <p>Исправление проблемы с темами, содержащими русские символы в названиях.</p>
        
        <!-- Problem Description -->
        <div class="test-section">
            <h3>❌ Проблема</h3>
            <div class="problem-demo">
                <h4>ДО исправления:</h4>
                <p>Темы с русскими названиями не активировались и не удалялись через интерфейс.</p>
                <div class="highlight-box">
                    <strong>Проблемные названия:</strong> "Тёмная тема", "Красивый дизайн", "Тема 2024"
                </div>
                
                <p><strong>Причины:</strong></p>
                <ul>
                    <li>Slug'и с русскими символами не кодировались в URL'ах</li>
                    <li>HTML атрибуты не экранировались</li>
                    <li>JavaScript не обрабатывал Unicode символы корректно</li>
                </ul>
                
                <div class="code-block">
// ДО (проблемный код):
url: wpApiSettings.root + 'wheel-size/v1/themes/' + slug,  // slug может содержать русские символы
data-theme-slug="${slug}"  // не экранировано
                </div>
            </div>
        </div>
        
        <!-- Solution Description -->
        <div class="test-section">
            <h3>✅ Решение</h3>
            <div class="solution-demo">
                <h4>ПОСЛЕ исправления:</h4>
                <p>Все slug'и правильно кодируются и экранируются для безопасной работы с Unicode.</p>
                
                <div class="code-block">
// ПОСЛЕ (исправленный код):
url: wpApiSettings.root + 'wheel-size/v1/themes/' + encodeURIComponent(slug),
data-theme-slug="${this.escapeHtml(slug)}"

// Добавлена функция escapeHtml:
escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
                </div>
                
                <h4>Исправленные файлы:</h4>
                <ul>
                    <li><code>assets/js/admin-theme-panel.js</code> - URL кодирование и HTML экранирование</li>
                    <li><code>src/includes/ThemeManager.php</code> - улучшенная генерация slug'ов</li>
                    <li><code>src/rest/ThemeController.php</code> - поддержка Unicode в REST API</li>
                </ul>
            </div>
        </div>
        
        <!-- Live Test -->
        <div class="test-section">
            <h3>🧪 Тест создания темы с русским названием</h3>
            
            <div class="test-form">
                <div class="form-group">
                    <label class="form-label">Название темы (русские символы):</label>
                    <input type="text" class="form-input" id="theme-name" value="Тёмная тема 2024" placeholder="Введите название темы">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Основной цвет:</label>
                    <input type="color" class="form-input" id="theme-primary" value="#2563eb" style="width: 100px;">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Цвет фона:</label>
                    <input type="color" class="form-input" id="theme-bg" value="#ffffff" style="width: 100px;">
                </div>
                
                <button class="form-button" onclick="testCreateTheme()">Создать тему</button>
                <button class="form-button" onclick="testActivateTheme()" id="activate-btn" disabled>Активировать</button>
                <button class="form-button" onclick="testDeleteTheme()" id="delete-btn" disabled>Удалить</button>
                <button class="form-button" onclick="clearLog()">Очистить лог</button>
            </div>
            
            <div class="status-log" id="status-log">
                <div class="status-info">Готов к тестированию. Нажмите "Создать тему" для начала.</div>
            </div>
        </div>
        
        <!-- Examples -->
        <div class="test-section">
            <h3>📝 Примеры поддерживаемых названий</h3>
            
            <div class="test-examples">
                <div class="test-example">
                    <h5>Русские символы</h5>
                    <div class="example-name">Тёмная тема</div>
                    <div class="example-slug">slug: темная-тема</div>
                </div>
                
                <div class="test-example">
                    <h5>Смешанный текст</h5>
                    <div class="example-name">Theme 2024</div>
                    <div class="example-slug">slug: theme-2024</div>
                </div>
                
                <div class="test-example">
                    <h5>Только цифры</h5>
                    <div class="example-name">123</div>
                    <div class="example-slug">slug: theme-123</div>
                </div>
                
                <div class="test-example">
                    <h5>Спецсимволы</h5>
                    <div class="example-name">Тема №1!</div>
                    <div class="example-slug">slug: тема-1</div>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Технические детали</h3>
            
            <h4>Исправления в JavaScript:</h4>
            <div class="code-block">
// 1. URL кодирование для AJAX запросов
url: wpApiSettings.root + 'wheel-size/v1/themes/' + encodeURIComponent(slug)

// 2. HTML экранирование для атрибутов
data-theme-slug="${this.escapeHtml(slug)}"

// 3. Функция экранирования
escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
            </div>
            
            <h4>Поддержка в PHP:</h4>
            <ul>
                <li><strong>ThemeManager::generate_slug()</strong> - улучшенная генерация slug'ов с поддержкой Unicode</li>
                <li><strong>REST API regex</strong> - `(?P<slug>[^/]+)` поддерживает любые символы кроме "/"</li>
                <li><strong>validate_theme_slug()</strong> - валидация с поддержкой международных символов</li>
                <li><strong>sanitize_text_field()</strong> - безопасная очистка входных данных</li>
            </ul>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить в WordPress</h4>
            <ol>
                <li><strong>Очистите кэш</strong> WordPress и браузера</li>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>Нажмите <strong>"Add New Theme"</strong></li>
                <li>Введите название с русскими символами: <strong>"Тёмная тема"</strong></li>
                <li>Настройте цвета и сохраните</li>
                <li>Убедитесь, что тема:
                    <ul>
                        <li>Создалась успешно ✅</li>
                        <li>Отображается в списке ✅</li>
                        <li>Активируется при клике ✅</li>
                        <li>Удаляется через меню ✅</li>
                    </ul>
                </li>
            </ol>
            
            <h4>🎯 Ожидаемый результат</h4>
            <ul>
                <li>✅ Темы с русскими названиями работают корректно</li>
                <li>✅ Все операции (создание, активация, удаление) функционируют</li>
                <li>✅ Нет ошибок в консоли браузера</li>
                <li>✅ Slug'и генерируются безопасно</li>
            </ul>
        </div>
    </div>

    <script>
        let createdThemeSlug = null;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('status-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = `status-${type}`;
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('status-log').innerHTML = '<div class="status-info">Лог очищен. Готов к новому тестированию.</div>';
        }
        
        async function testCreateTheme() {
            const name = document.getElementById('theme-name').value.trim();
            const primary = document.getElementById('theme-primary').value;
            const bg = document.getElementById('theme-bg').value;
            
            if (!name) {
                log('Введите название темы', 'error');
                return;
            }
            
            log(`Создание темы: "${name}"`, 'info');
            log(`Slug будет сгенерирован автоматически`, 'info');
            
            try {
                // Имитация создания темы
                const mockSlug = generateMockSlug(name);
                createdThemeSlug = mockSlug;
                
                log(`Тема создана успешно!`, 'success');
                log(`Сгенерированный slug: "${mockSlug}"`, 'success');
                log(`URL будет: /themes/${encodeURIComponent(mockSlug)}`, 'info');
                log(`HTML атрибут: data-theme-slug="${escapeHtml(mockSlug)}"`, 'info');
                
                // Активируем кнопки
                document.getElementById('activate-btn').disabled = false;
                document.getElementById('delete-btn').disabled = false;
                
            } catch (error) {
                log(`Ошибка создания темы: ${error.message}`, 'error');
            }
        }
        
        async function testActivateTheme() {
            if (!createdThemeSlug) {
                log('Сначала создайте тему', 'error');
                return;
            }
            
            log(`Активация темы: "${createdThemeSlug}"`, 'info');
            log(`URL: PUT /themes/active`, 'info');
            log(`Body: {"slug": "${createdThemeSlug}"}`, 'info');
            
            // Имитация активации
            setTimeout(() => {
                log(`Тема активирована успешно!`, 'success');
                log(`Slug корректно обработан в URL и JSON`, 'success');
            }, 500);
        }
        
        async function testDeleteTheme() {
            if (!createdThemeSlug) {
                log('Сначала создайте тему', 'error');
                return;
            }
            
            if (!confirm(`Удалить тему "${createdThemeSlug}"?`)) {
                return;
            }
            
            log(`Удаление темы: "${createdThemeSlug}"`, 'info');
            log(`URL: DELETE /themes/${encodeURIComponent(createdThemeSlug)}`, 'info');
            
            // Имитация удаления
            setTimeout(() => {
                log(`Тема удалена успешно!`, 'success');
                log(`Slug корректно закодирован в URL`, 'success');
                
                // Деактивируем кнопки
                document.getElementById('activate-btn').disabled = true;
                document.getElementById('delete-btn').disabled = true;
                createdThemeSlug = null;
            }, 500);
        }
        
        function generateMockSlug(name) {
            // Имитация логики ThemeManager::generate_slug()
            let slug = name.toLowerCase();
            
            // Заменяем пробелы и спецсимволы на дефисы
            slug = slug.replace(/[\s\.,;:!?\'"()[\]{}№]+/g, '-');
            
            // Убираем множественные дефисы
            slug = slug.replace(/-+/g, '-');
            
            // Убираем дефисы в начале и конце
            slug = slug.replace(/^-+|-+$/g, '');
            
            // Если пустой, используем fallback
            if (!slug) {
                slug = 'custom-theme';
            }
            
            return slug;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            log('Тест готов к использованию', 'info');
            log('Введите название темы с русскими символами и нажмите "Создать тему"', 'info');
        });
    </script>
</body>
</html>
