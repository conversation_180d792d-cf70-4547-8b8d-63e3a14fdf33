<?php
/**
 * Force update default themes with new modern color schemes
 * 
 * This script will replace the existing light and dark themes in the database
 * with the new modern color palettes defined in ThemeManager.php
 */

// Ensure WordPress is loaded
if (!defined('ABSPATH')) {
    die('This script must be run within WordPress context');
}

// Load the ThemeManager class
require_once __DIR__ . '/../src/includes/ThemeManager.php';

use MyTyreFinder\Includes\ThemeManager;

echo "🔄 Force Update Default Themes\n";
echo "==============================\n\n";

// Step 1: Check current themes
echo "1️⃣ Checking current themes...\n";

try {
    $current_themes = get_option('wsf_theme_presets', []);
    $active_theme = get_option('wsf_active_theme', 'light');
    
    echo "  📊 Current themes in database: " . count($current_themes) . "\n";
    echo "  📋 Available themes: " . implode(', ', array_keys($current_themes)) . "\n";
    echo "  🎯 Active theme: {$active_theme}\n";
    
    if (isset($current_themes['light'])) {
        echo "  🔍 Light theme primary color: " . ($current_themes['light']['--wsf-primary'] ?? 'not set') . "\n";
    }
    if (isset($current_themes['dark'])) {
        echo "  🔍 Dark theme primary color: " . ($current_themes['dark']['--wsf-primary'] ?? 'not set') . "\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ Error checking current themes: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Step 2: Get new default themes from ThemeManager
echo "2️⃣ Getting new default themes...\n";

try {
    // Use reflection to access the private method
    $reflection = new ReflectionClass(ThemeManager::class);
    $getDefaultThemesMethod = $reflection->getMethod('get_default_themes');
    $getDefaultThemesMethod->setAccessible(true);
    
    $new_default_themes = $getDefaultThemesMethod->invoke(null);
    
    echo "  ✅ New default themes loaded\n";
    echo "  🎨 Light theme primary color: " . $new_default_themes['light']['--wsf-primary'] . "\n";
    echo "  🎨 Dark theme primary color: " . $new_default_themes['dark']['--wsf-primary'] . "\n";
    echo "  📊 Light theme properties: " . count($new_default_themes['light']) . "\n";
    echo "  📊 Dark theme properties: " . count($new_default_themes['dark']) . "\n";
    
} catch (Exception $e) {
    echo "  ❌ Error getting new default themes: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Step 3: Backup existing custom themes
echo "3️⃣ Backing up custom themes...\n";

$custom_themes = [];
foreach ($current_themes as $slug => $theme_data) {
    if (!in_array($slug, ['light', 'dark'])) {
        $custom_themes[$slug] = $theme_data;
        echo "  💾 Backed up custom theme: {$slug}\n";
    }
}

if (empty($custom_themes)) {
    echo "  ℹ️  No custom themes to backup\n";
} else {
    echo "  ✅ Backed up " . count($custom_themes) . " custom themes\n";
}

echo "\n";

// Step 4: Update themes in database
echo "4️⃣ Updating themes in database...\n";

try {
    // Merge new default themes with existing custom themes
    $updated_themes = array_merge($new_default_themes, $custom_themes);
    
    // Update the database
    $update_result = update_option('wsf_theme_presets', $updated_themes, false);
    
    if ($update_result) {
        echo "  ✅ Themes updated successfully in database\n";
        echo "  📊 Total themes after update: " . count($updated_themes) . "\n";
    } else {
        echo "  ⚠️  Database update returned false (might already be up to date)\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ Error updating themes: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Step 5: Verify the update
echo "5️⃣ Verifying the update...\n";

try {
    // Clear any caches and get fresh data
    wp_cache_delete('wsf_theme_presets', 'options');
    
    $verified_themes = ThemeManager::get_themes();
    
    echo "  🔍 Verification results:\n";
    echo "    📊 Total themes: " . count($verified_themes) . "\n";
    echo "    📋 Available themes: " . implode(', ', array_keys($verified_themes)) . "\n";
    
    if (isset($verified_themes['light'])) {
        $light_primary = $verified_themes['light']['--wsf-primary'] ?? 'not set';
        $expected_light = '#2563EB';
        $light_match = $light_primary === $expected_light;
        echo "    " . ($light_match ? '✅' : '❌') . " Light theme primary: {$light_primary} " . ($light_match ? '(updated!)' : "(expected {$expected_light})") . "\n";
    }
    
    if (isset($verified_themes['dark'])) {
        $dark_primary = $verified_themes['dark']['--wsf-primary'] ?? 'not set';
        $expected_dark = '#3B82F6';
        $dark_match = $dark_primary === $expected_dark;
        echo "    " . ($dark_match ? '✅' : '❌') . " Dark theme primary: {$dark_primary} " . ($dark_match ? '(updated!)' : "(expected {$expected_dark})") . "\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ Error during verification: " . $e->getMessage() . "\n";
}

echo "\n";

// Step 6: Test theme activation
echo "6️⃣ Testing theme activation...\n";

try {
    // Test light theme
    $light_result = ThemeManager::set_active_theme('light');
    echo "  " . ($light_result ? '✅' : '❌') . " Light theme activation: " . ($light_result ? 'success' : 'failed') . "\n";
    
    // Test dark theme
    $dark_result = ThemeManager::set_active_theme('dark');
    echo "  " . ($dark_result ? '✅' : '❌') . " Dark theme activation: " . ($dark_result ? 'success' : 'failed') . "\n";
    
    // Restore original active theme
    ThemeManager::set_active_theme($active_theme);
    echo "  🔄 Restored original active theme: {$active_theme}\n";
    
} catch (Exception $e) {
    echo "  ❌ Error testing theme activation: " . $e->getMessage() . "\n";
}

echo "\n";

// Summary
echo "🏁 Update Summary\n";
echo "=================\n";

$final_themes = ThemeManager::get_themes();
$light_updated = isset($final_themes['light']['--wsf-primary']) && $final_themes['light']['--wsf-primary'] === '#2563EB';
$dark_updated = isset($final_themes['dark']['--wsf-primary']) && $final_themes['dark']['--wsf-primary'] === '#3B82F6';

if ($light_updated && $dark_updated) {
    echo "🎉 SUCCESS! Default themes updated with modern color schemes\n";
    echo "\n";
    echo "✨ New Light Theme Features:\n";
    echo "  • Pure white background (#FFFFFF)\n";
    echo "  • High contrast text (#1A1D23)\n";
    echo "  • Modern blue primary (#2563EB)\n";
    echo "  • Professional status colors\n";
    echo "\n";
    echo "🌙 New Dark Theme Features:\n";
    echo "  • Deep blue-gray background (#0B1120)\n";
    echo "  • Eye-friendly light text (#F8FAFC)\n";
    echo "  • Bright blue primary (#3B82F6)\n";
    echo "  • Optimized for night use\n";
    echo "\n";
    echo "📝 Next steps:\n";
    echo "  1. Refresh your admin panel to see the new colors\n";
    echo "  2. Check Theme Presets section for updated themes\n";
    echo "  3. Test theme switching functionality\n";
    echo "  4. Verify colors in the tire finder widget\n";
} else {
    echo "⚠️  PARTIAL UPDATE - Some themes may not have updated correctly\n";
    echo "  Light theme updated: " . ($light_updated ? 'Yes' : 'No') . "\n";
    echo "  Dark theme updated: " . ($dark_updated ? 'Yes' : 'No') . "\n";
    echo "\n";
    echo "🔧 Try running this script again or check for errors above\n";
}

echo "\nDone! 🎉\n";
