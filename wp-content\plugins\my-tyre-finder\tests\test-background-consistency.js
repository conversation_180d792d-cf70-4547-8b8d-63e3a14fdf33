/**
 * Background Consistency Test
 * Проверяет корректное применение Background к контейнеру формы, а не к select элементам
 */

(function() {
    'use strict';

    console.log('🎨 Background Consistency Test - Starting...');

    const config = {
        // Селекторы для проверки
        formWrapperSelector: '.wsf-form-wrapper',
        selectSelectors: [
            '.wsf-form-wrapper select',
            '.wsf-form-wrapper input[type="text"]',
            '.wsf-form-wrapper input[type="email"]',
            '.wsf-form-wrapper .wsf-input'
        ],
        
        // Ожидаемые CSS свойства
        expectedFormWrapperProps: {
            'background-color': 'var(--wsf-bg)',
            'border-radius': 'var(--wsf-radius-lg)',
            'padding': '1.5rem',
            'border': '1px solid var(--wsf-border-light)'
        },
        
        expectedInputProps: {
            'background-color': 'var(--wsf-input-bg)',
            'color': 'var(--wsf-input-text)',
            'border-color': 'var(--wsf-input-border)'
        }
    };

    // Функция для получения computed стилей
    function getComputedStyleValue(element, property) {
        return getComputedStyle(element).getPropertyValue(property).trim();
    }

    // Функция для проверки CSS переменных
    function getCSSVariableValue(variableName) {
        return getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
    }

    // Проверка наличия обёртки формы
    function testFormWrapperExists() {
        console.log('\n1️⃣ Проверка наличия .wsf-form-wrapper...');
        
        const formWrappers = document.querySelectorAll(config.formWrapperSelector);
        
        if (formWrappers.length === 0) {
            console.error('❌ .wsf-form-wrapper не найден на странице');
            return false;
        }
        
        console.log(`✅ Найдено ${formWrappers.length} обёрток формы`);
        return true;
    }

    // Проверка применения Background к обёртке формы
    function testFormWrapperBackground() {
        console.log('\n2️⃣ Проверка применения Background к обёртке формы...');
        
        const formWrappers = document.querySelectorAll(config.formWrapperSelector);
        let allPassed = true;
        
        formWrappers.forEach((wrapper, index) => {
            console.log(`\n   Обёртка ${index + 1}:`);
            
            // Проверяем background-color
            const bgColor = getComputedStyleValue(wrapper, 'background-color');
            const expectedBg = getCSSVariableValue('--wsf-bg');
            
            if (bgColor && expectedBg) {
                console.log(`   ✅ background-color: ${bgColor}`);
            } else {
                console.log(`   ❌ background-color не применен корректно`);
                allPassed = false;
            }
            
            // Проверяем border-radius
            const borderRadius = getComputedStyleValue(wrapper, 'border-radius');
            if (borderRadius && borderRadius !== '0px') {
                console.log(`   ✅ border-radius: ${borderRadius}`);
            } else {
                console.log(`   ⚠️ border-radius не применен`);
            }
            
            // Проверяем padding
            const padding = getComputedStyleValue(wrapper, 'padding');
            if (padding && padding !== '0px') {
                console.log(`   ✅ padding: ${padding}`);
            } else {
                console.log(`   ⚠️ padding не применен`);
            }
        });
        
        return allPassed;
    }

    // Проверка, что select элементы НЕ используют Background
    function testSelectElementsBackground() {
        console.log('\n3️⃣ Проверка фона select элементов...');
        
        let allPassed = true;
        const results = {
            correct: 0,
            incorrect: 0,
            details: []
        };
        
        config.selectSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            
            elements.forEach(element => {
                const bgColor = getComputedStyleValue(element, 'background-color');
                const wsfBg = getCSSVariableValue('--wsf-bg');
                const wsfInputBg = getCSSVariableValue('--wsf-input-bg');
                
                // Проверяем, что элемент НЕ использует --wsf-bg
                if (bgColor === wsfBg && wsfBg !== wsfInputBg) {
                    console.log(`   ❌ ${selector}: использует --wsf-bg вместо --wsf-input-bg`);
                    results.incorrect++;
                    results.details.push({
                        selector,
                        issue: 'uses-wsf-bg-instead-of-input-bg',
                        bgColor,
                        expected: wsfInputBg
                    });
                    allPassed = false;
                } else {
                    console.log(`   ✅ ${selector}: корректный фон (${bgColor})`);
                    results.correct++;
                }
            });
        });
        
        console.log(`\n   📊 Результат: ${results.correct} ✅ / ${results.incorrect} ❌`);
        return allPassed;
    }

    // Проверка разделения ответственности
    function testResponsibilitySeparation() {
        console.log('\n4️⃣ Проверка разделения ответственности...');
        
        const wsfBg = getCSSVariableValue('--wsf-bg');
        const wsfInputBg = getCSSVariableValue('--wsf-input-bg');
        
        if (wsfBg === wsfInputBg) {
            console.log('   ⚠️ --wsf-bg и --wsf-input-bg имеют одинаковые значения');
            console.log('   💡 Рекомендуется использовать разные цвета для демонстрации разделения');
            return true; // Это не ошибка, просто рекомендация
        }
        
        console.log(`   ✅ --wsf-bg: ${wsfBg}`);
        console.log(`   ✅ --wsf-input-bg: ${wsfInputBg}`);
        console.log('   ✅ Цвета разделены корректно');
        
        return true;
    }

    // Проверка CSS правил в стилях
    function testCSSRules() {
        console.log('\n5️⃣ Проверка CSS правил...');
        
        const stylesheets = Array.from(document.styleSheets);
        let foundFormWrapperRule = false;
        let foundInputRules = false;
        
        try {
            stylesheets.forEach(sheet => {
                if (sheet.cssRules) {
                    Array.from(sheet.cssRules).forEach(rule => {
                        if (rule.selectorText) {
                            if (rule.selectorText.includes('.wsf-form-wrapper')) {
                                foundFormWrapperRule = true;
                                console.log('   ✅ Найдено CSS правило для .wsf-form-wrapper');
                            }
                            
                            if (rule.selectorText.includes('.wsf-form-wrapper select') || 
                                rule.selectorText.includes('.wsf-form-wrapper input')) {
                                foundInputRules = true;
                                console.log('   ✅ Найдены CSS правила для элементов внутри .wsf-form-wrapper');
                            }
                        }
                    });
                }
            });
        } catch (e) {
            console.log('   ⚠️ Не удалось проверить CSS правила (CORS ограничения)');
            return true; // Не считаем это ошибкой
        }
        
        if (!foundFormWrapperRule) {
            console.log('   ❌ CSS правило для .wsf-form-wrapper не найдено');
            return false;
        }
        
        if (!foundInputRules) {
            console.log('   ❌ CSS правила для элементов внутри .wsf-form-wrapper не найдены');
            return false;
        }
        
        return true;
    }

    // Основная функция тестирования
    function runBackgroundTest() {
        console.log('\n📋 Запуск тестов Background...');
        
        const results = {
            formWrapperExists: testFormWrapperExists(),
            formWrapperBackground: testFormWrapperBackground(),
            selectElementsBackground: testSelectElementsBackground(),
            responsibilitySeparation: testResponsibilitySeparation(),
            cssRules: testCSSRules()
        };
        
        const passedTests = Object.values(results).filter(Boolean).length;
        const totalTests = Object.keys(results).length;
        
        console.log('\n' + '='.repeat(60));
        console.log(`📊 Результаты тестирования: ${passedTests}/${totalTests} тестов пройдено`);
        
        if (passedTests === totalTests) {
            console.log('🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Background применяется корректно.');
        } else {
            console.log('⚠️ Некоторые тесты не пройдены. Требуется исправление.');
            
            // Детали неудачных тестов
            Object.entries(results).forEach(([testName, passed]) => {
                if (!passed) {
                    console.log(`   ❌ ${testName}: FAILED`);
                }
            });
        }
        console.log('='.repeat(60));
        
        return passedTests === totalTests;
    }

    // Функция для демонстрации различий
    function demonstrateDifference() {
        console.log('\n🔍 Демонстрация различий...');
        
        // Создаем временные элементы для демонстрации
        const testContainer = document.createElement('div');
        testContainer.style.position = 'fixed';
        testContainer.style.top = '-1000px';
        testContainer.innerHTML = `
            <div class="wsf-form-wrapper">
                <select class="test-select"></select>
            </div>
            <select class="test-select-without-wrapper"></select>
        `;
        document.body.appendChild(testContainer);
        
        const wrapperSelect = testContainer.querySelector('.wsf-form-wrapper select');
        const standaloneSelect = testContainer.querySelector('.test-select-without-wrapper');
        const wrapper = testContainer.querySelector('.wsf-form-wrapper');
        
        console.log('   📦 Обёртка формы:');
        console.log(`      background: ${getComputedStyleValue(wrapper, 'background-color')}`);
        console.log(`      padding: ${getComputedStyleValue(wrapper, 'padding')}`);
        
        console.log('   🔘 Select внутри обёртки:');
        console.log(`      background: ${getComputedStyleValue(wrapperSelect, 'background-color')}`);
        
        console.log('   🔘 Select без обёртки:');
        console.log(`      background: ${getComputedStyleValue(standaloneSelect, 'background-color')}`);
        
        // Удаляем тестовые элементы
        document.body.removeChild(testContainer);
    }

    // Автозапуск при загрузке страницы
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            runBackgroundTest();
            demonstrateDifference();
        });
    } else {
        runBackgroundTest();
        demonstrateDifference();
    }

    // Экспорт для ручного запуска
    window.testBackgroundConsistency = runBackgroundTest;
    window.demonstrateBackgroundDifference = demonstrateDifference;

})();
