/**
 * Font Consistency Test
 * Проверяет, что все элементы виджета используют одинаковый шрифт
 */

(function() {
    'use strict';

    console.log('🔤 Font Consistency Test - Starting...');

    const config = {
        // Ожидаемый шрифт (Tailwind font-sans stack)
        expectedFontStack: [
            'ui-sans-serif',
            'system-ui', 
            '-apple-system',
            'BlinkMacSystemFont',
            'Segoe UI',
            'Roboto',
            'Helvetica Neue',
            'Arial',
            'Noto Sans',
            'sans-serif'
        ],
        
        // Селекторы для проверки
        testSelectors: [
            '.wheel-fit-widget h1',           // Заголовок виджета
            '.wheel-fit-widget h2',           // Заголовки секций
            '.wheel-fit-widget h3',           // Подзаголовки
            '.wheel-fit-widget p',            // Параграфы
            '.wheel-fit-widget label',        // Лейблы
            '.wheel-fit-widget select',       // Селекты
            '.wheel-fit-widget button',       // Кнопки
            '.wheel-fit-widget span',         // Спаны
            '.wheel-fit-widget div',          // Дивы с текстом
            '#search-results',                // Результаты поиска
            '#vehicle-label',                 // Лейбл автомобиля
            '.wsf-finder-widget',             // Альтернативный класс виджета
        ]
    };

    // Функция для нормализации font-family строки
    function normalizeFontFamily(fontFamily) {
        return fontFamily
            .toLowerCase()
            .replace(/['"]/g, '')
            .split(',')
            .map(font => font.trim())
            .filter(font => font.length > 0);
    }

    // Проверка, содержит ли font-family ожидаемые шрифты
    function isFontStackValid(fontFamily) {
        const normalized = normalizeFontFamily(fontFamily);
        
        // Проверяем, что первые несколько шрифтов из ожидаемого стека присутствуют
        const expectedStart = config.expectedFontStack.slice(0, 3).map(f => f.toLowerCase());
        const actualStart = normalized.slice(0, 3);
        
        return expectedStart.some(expected => 
            actualStart.some(actual => actual.includes(expected))
        );
    }

    // Основная функция тестирования
    function runFontTest() {
        console.log('\n📋 Проверка консистентности шрифтов...');
        
        const results = {
            passed: 0,
            failed: 0,
            details: []
        };

        // Проверяем корневой элемент виджета
        const widget = document.querySelector('.wheel-fit-widget, .wsf-finder-widget');
        if (!widget) {
            console.error('❌ Виджет не найден на странице');
            return false;
        }

        console.log('✅ Виджет найден:', widget.className);

        // Проверяем класс wsf-root-font
        if (widget.classList.contains('wsf-root-font')) {
            console.log('✅ Класс wsf-root-font применен к корневому элементу');
        } else {
            console.warn('⚠️ Класс wsf-root-font НЕ найден на корневом элементе');
        }

        // Проверяем каждый селектор
        config.testSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            
            if (elements.length === 0) {
                console.log(`⚪ ${selector}: элементы не найдены`);
                return;
            }

            let selectorPassed = true;
            const fontFamilies = new Set();

            elements.forEach((element, index) => {
                const computedStyle = getComputedStyle(element);
                const fontFamily = computedStyle.fontFamily;
                fontFamilies.add(fontFamily);

                if (!isFontStackValid(fontFamily)) {
                    selectorPassed = false;
                    results.details.push({
                        selector,
                        element: element.tagName.toLowerCase() + (element.className ? '.' + element.className.split(' ').join('.') : ''),
                        fontFamily,
                        status: 'failed'
                    });
                }
            });

            if (selectorPassed) {
                console.log(`✅ ${selector}: ${elements.length} элементов - шрифт корректный`);
                results.passed++;
            } else {
                console.log(`❌ ${selector}: ${elements.length} элементов - найдены проблемы с шрифтом`);
                results.failed++;
            }

            // Показываем уникальные font-family для этого селектора
            if (fontFamilies.size > 1) {
                console.log(`   📝 Найдено ${fontFamilies.size} разных шрифтов:`);
                fontFamilies.forEach(ff => {
                    console.log(`      - ${ff}`);
                });
            }
        });

        // Выводим детали ошибок
        if (results.details.length > 0) {
            console.log('\n🔍 Детали проблем:');
            results.details.forEach(detail => {
                console.log(`   ❌ ${detail.selector} (${detail.element}): ${detail.fontFamily}`);
            });
        }

        // Итоговый результат
        console.log(`\n📊 Результат: ${results.passed} ✅ / ${results.failed} ❌`);
        
        if (results.failed === 0) {
            console.log('🎉 Все тесты пройдены! Шрифты консистентны.');
            return true;
        } else {
            console.log('⚠️ Найдены проблемы с консистентностью шрифтов.');
            return false;
        }
    }

    // Функция для проверки CSS правил
    function checkCssRules() {
        console.log('\n🎨 Проверка CSS правил...');
        
        // Проверяем, что .wsf-root-font определен
        const stylesheets = Array.from(document.styleSheets);
        let foundWsfRootFont = false;
        
        try {
            stylesheets.forEach(sheet => {
                if (sheet.cssRules) {
                    Array.from(sheet.cssRules).forEach(rule => {
                        if (rule.selectorText && rule.selectorText.includes('.wsf-root-font')) {
                            foundWsfRootFont = true;
                            console.log('✅ Найдено CSS правило .wsf-root-font');
                            console.log(`   📝 ${rule.cssText}`);
                        }
                    });
                }
            });
        } catch (e) {
            console.log('⚠️ Не удалось проверить CSS правила (CORS ограничения)');
        }

        if (!foundWsfRootFont) {
            console.log('❌ CSS правило .wsf-root-font не найдено');
        }
    }

    // Запуск тестов
    function runAllTests() {
        console.log('🚀 Запуск всех тестов...');
        
        checkCssRules();
        const fontTestResult = runFontTest();
        
        console.log('\n' + '='.repeat(50));
        if (fontTestResult) {
            console.log('🎯 УСПЕХ: Шрифты настроены корректно!');
        } else {
            console.log('❌ ОШИБКА: Требуется исправление шрифтов.');
        }
        console.log('='.repeat(50));
        
        return fontTestResult;
    }

    // Автозапуск при загрузке страницы
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllTests);
    } else {
        runAllTests();
    }

    // Экспорт для ручного запуска
    window.testFontConsistency = runAllTests;

})();
