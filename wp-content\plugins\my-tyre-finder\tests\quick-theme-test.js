/**
 * Quick theme integration test
 * Run in browser console for instant verification
 */

(function() {
    'use strict';

    console.log('⚡ === QUICK THEME INTEGRATION TEST ===');

    // Check if CSS variable exists
    const rootStyles = getComputedStyle(document.documentElement);
    const adminPanelBg = rootStyles.getPropertyValue('--wsf-admin-panel-bg').trim();
    
    console.log(`📋 CSS Variable: ${adminPanelBg || 'NOT FOUND'}`);

    // Check theme panel
    const themePanel = document.querySelector('.wsf-theme-panel');
    if (themePanel) {
        const bgColor = getComputedStyle(themePanel).backgroundColor;
        console.log(`🎨 Panel Background: ${bgColor}`);
    } else {
        console.log('❌ Theme panel not found');
        return;
    }

    // Count themes
    const themes = document.querySelectorAll('.wsf-theme-card');
    console.log(`🎯 Found ${themes.length} themes`);

    // Test function
    window.testThemeBackground = (color) => {
        document.documentElement.style.setProperty('--wsf-admin-panel-bg', color);
        console.log(`✅ Applied background: ${color}`);
    };

    // Quick tests
    console.log('\n🧪 Quick tests available:');
    console.log('testThemeBackground("#1e1e1e") - Dark background');
    console.log('testThemeBackground("#ffffff") - Light background');
    console.log('testThemeBackground("#f0f9ff") - Blue tint');

    // Auto-test
    console.log('\n🔄 Running auto-test...');
    
    let step = 0;
    const colors = ['#1e1e1e', '#f0f9ff', '#ffffff'];
    const names = ['Dark', 'Blue', 'Light'];
    
    const interval = setInterval(() => {
        if (step >= colors.length) {
            clearInterval(interval);
            console.log('✅ Auto-test completed');
            return;
        }
        
        const color = colors[step];
        const name = names[step];
        
        document.documentElement.style.setProperty('--wsf-admin-panel-bg', color);
        console.log(`${step + 1}. Applied ${name} theme: ${color}`);
        
        step++;
    }, 1000);

    // Status indicator
    const indicator = document.createElement('div');
    indicator.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #007cba;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
        font-family: monospace;
    `;
    indicator.textContent = `Themes: ${themes.length} | Variable: ${adminPanelBg ? 'OK' : 'MISSING'}`;
    document.body.appendChild(indicator);

    setTimeout(() => indicator.remove(), 5000);

})();
