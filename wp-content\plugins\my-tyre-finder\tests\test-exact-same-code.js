/**
 * Тест точно такого же кода как при первой загрузке
 * Проверяет, что AJAX использует идентичный код
 */

(function() {
    'use strict';

    console.log('[Exact Same Code Test] 🔍 Тест идентичного кода...');

    // Функция проверки состояния селекторов
    function checkSelectors() {
        console.log('[Exact Same Code Test] === Проверка селекторов ===');
        
        const selectors = ['wf-make', 'wf-model', 'wf-year', 'wf-modification', 'wf-generation'];
        let russianCount = 0;
        let englishCount = 0;
        
        selectors.forEach(id => {
            const select = document.getElementById(id);
            if (select) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    const text = placeholderOption.textContent.trim();
                    const dataI18n = placeholderOption.getAttribute('data-i18n');
                    
                    const isRussian = text.includes('Выберите') || text.includes('Загрузка') || text.includes('Сначала');
                    const isEnglish = text.includes('Choose') || text.includes('Select') || text.includes('Loading');
                    
                    if (isRussian) russianCount++;
                    if (isEnglish) englishCount++;
                    
                    const status = isRussian ? '✅ RU' : isEnglish ? '❌ EN' : '⚠️ OTHER';
                    console.log(`[Exact Same Code Test] ${id}: ${status} "${text}" (data-i18n: ${dataI18n})`);
                }
            }
        });
        
        console.log(`[Exact Same Code Test] Итого: ${russianCount} русских, ${englishCount} английских`);
        
        if (englishCount === 0 && russianCount > 0) {
            console.log('[Exact Same Code Test] ✅ ВСЕ СЕЛЕКТОРЫ НА РУССКОМ - КОД РАБОТАЕТ!');
            return true;
        } else if (englishCount > 0) {
            console.error('[Exact Same Code Test] ❌ ЕСТЬ АНГЛИЙСКИЕ СЕЛЕКТОРЫ - КОД НЕ РАБОТАЕТ!');
            return false;
        } else {
            console.warn('[Exact Same Code Test] ⚠️ НЕОПРЕДЕЛЕННОЕ СОСТОЯНИЕ');
            return false;
        }
    }

    // Функция сравнения кода
    function compareCode() {
        console.log('[Exact Same Code Test] === Сравнение кода ===');
        
        console.log('[Exact Same Code Test] Код первой загрузки (строки 658-671):');
        console.log('1. window.addEventListener("load", () => {');
        console.log('2.   applyStaticTranslations(initialPreviewContainer);');
        console.log('3.   window.wheelFitWidget = new WheelFitWidget();');
        console.log('4. });');
        
        console.log('[Exact Same Code Test] Старый AJAX код (строки 596-615):');
        console.log('1. window.WheelFitI18n = data.i18n || {};');
        console.log('2. previewContainer.innerHTML = data.data.html;');
        console.log('3. applyStaticTranslations(previewContainer);');
        console.log('4. window.wheelFitWidget = new WheelFitWidget();');
        
        console.log('[Exact Same Code Test] Новый AJAX код (должен быть идентичен):');
        console.log('1. window.WheelFitI18n = res.data.i18n || {};');
        console.log('2. previewContainer.innerHTML = res.data.html;');
        console.log('3. applyStaticTranslations(previewContainer);');
        console.log('4. window.wheelFitWidget = new WheelFitWidget();');
        
        console.log('[Exact Same Code Test] ✅ Код синхронизирован');
    }

    // Функция мониторинга изменений
    function monitorChanges() {
        console.log('[Exact Same Code Test] === Мониторинг изменений ===');
        
        // Слушаем изменения настроек
        document.addEventListener('change', function(e) {
            if (e.target && (e.target.id === 'search_flow' || e.target.id === 'form_layout')) {
                console.log(`[Exact Same Code Test] 🔄 Изменение ${e.target.id}: ${e.target.value}`);
                
                // Проверяем через разные интервалы
                setTimeout(() => {
                    console.log('[Exact Same Code Test] Проверка через 500ms:');
                    checkSelectors();
                }, 500);
                
                setTimeout(() => {
                    console.log('[Exact Same Code Test] Проверка через 1 секунду:');
                    checkSelectors();
                }, 1000);
                
                setTimeout(() => {
                    console.log('[Exact Same Code Test] Проверка через 2 секунды:');
                    const result = checkSelectors();
                    
                    if (result) {
                        console.log('[Exact Same Code Test] 🎉 УСПЕХ! Код работает как при первой загрузке!');
                    } else {
                        console.error('[Exact Same Code Test] 💥 ПРОВАЛ! Код все еще не работает!');
                        console.error('[Exact Same Code Test] Нужно найти, что еще отличается от первой загрузки');
                    }
                }, 2000);
            }
        });
        
        console.log('[Exact Same Code Test] Мониторинг настроен. Измените настройки для проверки.');
    }

    // Функция проверки доступности функций
    function checkFunctions() {
        console.log('[Exact Same Code Test] === Проверка доступности функций ===');
        
        const functions = [
            'applyStaticTranslations',
            'WheelFitWidget',
            'WheelWizard'
        ];
        
        functions.forEach(funcName => {
            const isAvailable = typeof window[funcName] !== 'undefined';
            console.log(`[Exact Same Code Test] ${funcName}: ${isAvailable ? '✅ доступна' : '❌ недоступна'}`);
        });
        
        // Проверяем глобальные переводы
        const hasTranslations = window.WheelFitI18n && Object.keys(window.WheelFitI18n).length > 0;
        console.log(`[Exact Same Code Test] WheelFitI18n: ${hasTranslations ? '✅ доступны' : '❌ недоступны'}`);
        
        if (hasTranslations) {
            console.log(`[Exact Same Code Test] Количество переводов: ${Object.keys(window.WheelFitI18n).length}`);
        }
    }

    // Основная функция тестирования
    function runExactSameCodeTest() {
        console.log('[Exact Same Code Test] 🚀 ЗАПУСК ТЕСТА ИДЕНТИЧНОГО КОДА');
        
        // 1. Проверяем доступность функций
        console.log('[Exact Same Code Test] 1. Проверка функций...');
        checkFunctions();
        
        // 2. Сравниваем код
        console.log('[Exact Same Code Test] 2. Сравнение кода...');
        compareCode();
        
        // 3. Проверяем текущее состояние
        console.log('[Exact Same Code Test] 3. Текущее состояние...');
        checkSelectors();
        
        // 4. Запускаем мониторинг
        console.log('[Exact Same Code Test] 4. Запуск мониторинга...');
        monitorChanges();
        
        console.log('[Exact Same Code Test] ✅ Тест готов. Измените Search Flow или Form Layout для проверки.');
    }

    // Глобальные функции
    window.testExactSameCode = {
        runTest: runExactSameCodeTest,
        checkSelectors: checkSelectors,
        compareCode: compareCode,
        checkFunctions: checkFunctions,
        monitor: monitorChanges
    };

    // Автоматический запуск
    setTimeout(() => {
        console.log('[Exact Same Code Test] Автоматический запуск...');
        runExactSameCodeTest();
    }, 1000);

    console.log('[Exact Same Code Test] Тест загружен. Доступные функции:');
    console.log('- testExactSameCode.runTest() - полный тест');
    console.log('- testExactSameCode.checkSelectors() - проверка селекторов');

})();
