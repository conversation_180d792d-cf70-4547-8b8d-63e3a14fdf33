/**
 * Test script to verify selector width in Inline layout
 * Run in browser console on admin appearance page
 */

console.log('📏 === SELECTOR WIDTH TEST ===');

const layoutSelect = document.getElementById('form_layout');
const autoSearchCheckbox = document.getElementById('auto_search_on_last_input');
const previewContainer = document.getElementById('widget-preview');

if (!layoutSelect || !autoSearchCheckbox || !previewContainer) {
    console.log('❌ Required elements not found');
    return;
}

function testSelectorWidth() {
    const previewWidget = previewContainer.querySelector('.wheel-fit-widget');
    if (!previewWidget) {
        console.log('   ❌ Preview widget not found');
        return;
    }

    const form = previewWidget.querySelector('form');
    const selectors = previewWidget.querySelectorAll('select[id^="wf-"]');
    const submitButton = previewWidget.querySelector('button[type="submit"]');
    const garageButton = previewWidget.querySelector('[data-garage-trigger]');
    
    console.log('\nSelector Width Test:');
    console.log('   Layout:', layoutSelect.value);
    console.log('   Auto-search enabled:', autoSearchCheckbox.checked);
    console.log('   Form found:', !!form);
    console.log('   Selectors found:', selectors.length);
    console.log('   Submit button visible:', submitButton ? !submitButton.classList.contains('hidden') : false);
    console.log('   Garage button found:', !!garageButton);
    
    if (form) {
        const formStyle = window.getComputedStyle(form);
        console.log('   Form display:', formStyle.display);
        console.log('   Form grid-template-columns:', formStyle.gridTemplateColumns);
        
        // Check form width
        const formRect = form.getBoundingClientRect();
        console.log('   Form width:', Math.round(formRect.width) + 'px');
        
        // Check selector widths
        if (selectors.length > 0) {
            console.log('\n   Selector Details:');
            let totalSelectorWidth = 0;
            
            selectors.forEach((selector, index) => {
                const selectorRect = selector.getBoundingClientRect();
                const container = selector.closest('[id^="step-"]') || selector.parentElement;
                const containerRect = container.getBoundingClientRect();
                
                console.log(`     Selector ${index + 1} (${selector.id}):`);
                console.log(`       Width: ${Math.round(selectorRect.width)}px`);
                console.log(`       Container width: ${Math.round(containerRect.width)}px`);
                
                totalSelectorWidth += containerRect.width;
            });
            
            console.log(`\n   Total selector area width: ${Math.round(totalSelectorWidth)}px`);
            console.log(`   Form utilization: ${Math.round((totalSelectorWidth / formRect.width) * 100)}%`);
            
            // Check if selectors fill the form properly
            const utilizationPercentage = (totalSelectorWidth / formRect.width) * 100;
            if (autoSearchCheckbox.checked) {
                console.log(`   Expected: ~100% utilization (auto-search mode)`);
                console.log(`   Result: ${utilizationPercentage > 90 ? '✅' : '❌'} (${Math.round(utilizationPercentage)}%)`);
            } else {
                console.log(`   Expected: ~80% utilization (with submit button)`);
                console.log(`   Result: ${utilizationPercentage < 90 && utilizationPercentage > 70 ? '✅' : '❌'} (${Math.round(utilizationPercentage)}%)`);
            }
        }
    }
    
    // Check garage button position
    if (garageButton) {
        const garageContainer = garageButton.parentElement;
        const isInsideForm = form && form.contains(garageButton);
        
        console.log('\n   Garage Button:');
        console.log('     Inside form:', isInsideForm ? '❌' : '✅');
        console.log('     Container classes:', garageContainer.className);
        
        if (!isInsideForm && form) {
            const formRect = form.getBoundingClientRect();
            const garageRect = garageButton.getBoundingClientRect();
            const isBelow = garageRect.top > formRect.bottom;
            
            console.log('     Positioned below form:', isBelow ? '✅' : '❌');
        }
    }
}

// Test current state
console.log('\n1. Current State:');
testSelectorWidth();

// Test with Inline layout and auto-search
console.log('\n🔄 Testing Inline + Auto-search...');

const originalLayout = layoutSelect.value;
const originalAutoSearch = autoSearchCheckbox.checked;

// Set to Inline layout with auto-search
layoutSelect.value = 'popup-horizontal';
autoSearchCheckbox.checked = true;

layoutSelect.dispatchEvent(new Event('change', { bubbles: true }));
autoSearchCheckbox.dispatchEvent(new Event('change', { bubbles: true }));

setTimeout(() => {
    console.log('\n2. Inline + Auto-search:');
    testSelectorWidth();
    
    // Test without auto-search
    autoSearchCheckbox.checked = false;
    autoSearchCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
    
    setTimeout(() => {
        console.log('\n3. Inline without auto-search:');
        testSelectorWidth();
        
        // Restore original settings
        layoutSelect.value = originalLayout;
        autoSearchCheckbox.checked = originalAutoSearch;
        layoutSelect.dispatchEvent(new Event('change', { bubbles: true }));
        autoSearchCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
        
        console.log('\n✅ Test completed. Check selector utilization percentages above.');
        
    }, 2000);
}, 2000);

console.log('\n⏳ Running width test, please wait...');
