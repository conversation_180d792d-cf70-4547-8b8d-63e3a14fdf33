/**
 * Live Preview Width Matching Fix
 *
 * SINGLE SOURCE OF TRUTH for Live Preview width management
 *
 * This file ensures:
 * 1. Widget container matches search results width (56rem/896px)
 * 2. All form elements use 100% width within the container
 * 3. No horizontal overflow in admin preview
 * 4. Consistent centering and spacing
 *
 * Replaces conflicting rules from AppearancePage.php
 */

/* Ensure full width for inputs in admin preview and frontend */
.wsf-input,
.wheel-fit-widget select,
.wsf-finder-widget select,
.wheel-fit-widget input,
.wsf-finder-widget input {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Additional selectors for comprehensive coverage */
#widget-preview select,
#widget-preview .wsf-input,
#widget-preview input[type="text"],
#widget-preview input[type="email"],
#widget-preview input[type="search"] {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Override WordPress admin form field containers */
#widget-preview .form-field,
#widget-preview .form-table td,
#widget-preview .form-wrap,
#widget-preview .wp-admin select {
  width: 100% !important;
  max-width: 100% !important;
}

/* Ensure flex containers don't constrain width */
#widget-preview .flex,
#widget-preview .flex-col,
#widget-preview .w-full {
  width: 100% !important;
  flex: 1 1 auto !important;
}

/* Fix WordPress admin interference with widget layout - match search results width */
#widget-preview .wheel-fit-widget,
#widget-preview .wsf-finder-widget {
  width: 100% !important;
  max-width: 56rem !important; /* 896px - same as max-w-4xl used by search results */
  margin: 0 auto !important; /* Center the widget */
}

/* Ensure form containers don't constrain width but limit max-width */
#widget-preview form,
#widget-preview .wsf-form-wrapper,
#widget-preview .form-container {
  width: 100% !important;
  max-width: 100% !important; /* Allow full width within widget container */
}

/* Override any WordPress admin table/form styling */
#widget-preview .form-table,
#widget-preview .form-table td,
#widget-preview .form-table th {
  width: auto !important;
  max-width: none !important;
}

/* Ensure step containers maintain reasonable width */
#widget-preview .step-container,
#widget-preview [id^="step-"] {
  width: 100% !important;
  max-width: 100% !important; /* Allow full width within widget container */
}

/* Wizard header and navigation - constrain to widget width */
#widget-preview #wizard-header {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 auto !important;
}

#widget-preview #wizard-nav {
  width: 100% !important;
  max-width: 100% !important;
}

#widget-preview .wizard-step-name {
  max-width: 100% !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* Ensure wizard steps don't exceed widget width */
#widget-preview .wizard-step {
  width: 100% !important;
  max-width: 100% !important;
  min-height: auto !important;
  height: auto !important;
}

/* Specific fixes for wizard steps */
#widget-preview #wheel-fit-wizard select,
#widget-preview #wheel-fit-wizard input,
#widget-preview #wheel-fit-wizard .wsf-input {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Removed problematic global grid width overrides that broke wizard layout */

/* WIZARD: скрыть заголовок "Select Manufacturer" в админ-превью */
#widget-preview #wizard-step-1 > h2,
#widget-preview #step-make > h2,
#widget-preview .wizard-step:first-child > h2 {
  display: none !important;
}

/* WIZARD: скрыть левый лейбл только в админ-превью */
#widget-preview #step-make .wsf-side-label,
#widget-preview #wizard-step-1 .wsf-side-label {
  display: none !important;
}

/* REMOVED: Problematic flex rules that broke wizard vertical layout */
/* These rules were causing horizontal layout instead of vertical hierarchy */

/*
АЛЬТЕРНАТИВНЫЙ ВАРИАНТ (если лейбл нужно оставить видимым):
Раскомментировать блок ниже и закомментировать блоки выше

#widget-preview #step-make .wsf-side-label,
#widget-preview #wizard-step-1 .wsf-side-label,
#widget-preview #step-make > .flex > :first-child {
  position: absolute !important;
  inset-inline-start: 0 !important;
  inset-block-start: 0 !important;
  pointer-events: none !important;
  width: auto !important;
}

#widget-preview #step-make > .flex > :nth-child(2),
#widget-preview #wizard-step-1 > .flex > :nth-child(2) {
  width: 100% !important;
  max-width: 100% !important;
}
*/

/* WIZARD: раскрепостить обёртки шага Make */
#widget-preview #step-make,
#widget-preview #step-make > *,
#widget-preview #wizard-step-1,
#widget-preview #wizard-step-1 > *,
#widget-preview .wizard-step:first-child,
#widget-preview .wizard-step:first-child > * {
  width: 100% !important;
  max-width: 100% !important;
}

/* Снести любые tailwind max-w-* внутри шага */
#widget-preview #step-make [class*="max-w-"],
#widget-preview #wizard-step-1 [class*="max-w-"],
#widget-preview .wizard-step:first-child [class*="max-w-"] {
  max-width: 100% !important;
}

/* WIZARD VERTICAL HIERARCHY: Force proper vertical layout with maximum specificity */
#widget-preview #wheel-fit-wizard .wizard-step,
#widget-preview #wheel-fit-wizard #wizard-step-1,
#widget-preview #wheel-fit-wizard #wizard-step-2,
#widget-preview #wheel-fit-wizard #wizard-step-3,
#widget-preview #wheel-fit-wizard #wizard-step-4,
#widget-preview .wizard-step,
#widget-preview #wizard-step-1,
#widget-preview #wizard-step-2,
#widget-preview #wizard-step-3,
#widget-preview #wizard-step-4 {
  /* Ensure wizard steps use natural block layout, not forced flex */
  display: block !important;
  flex: none !important;
  align-items: initial !important;
  gap: initial !important;
}

#widget-preview #wheel-fit-wizard .wizard-step > h2,
#widget-preview #wheel-fit-wizard .wizard-step > nav,
#widget-preview #wheel-fit-wizard .wizard-step > p,
#widget-preview #wheel-fit-wizard .wizard-step > .wizard-search-wrapper,
#widget-preview #wheel-fit-wizard .wizard-step > div:not(.grid):not([class*="grid-cols"]):not(#wizard-makes-grid),
#widget-preview .wizard-step > h2,
#widget-preview .wizard-step > nav,
#widget-preview .wizard-step > p,
#widget-preview .wizard-step > .wizard-search-wrapper,
#widget-preview .wizard-step > div:not(.grid):not([class*="grid-cols"]):not(#wizard-makes-grid) {
  /* Allow specific elements to stack vertically, but preserve grid layouts */
  display: block !important;
  width: 100% !important;
}

/* Exception: Keep grid layouts as grid */
#widget-preview #wheel-fit-wizard .wizard-step .grid,
#widget-preview #wheel-fit-wizard .wizard-step [class*="grid-cols"],
#widget-preview .wizard-step .grid,
#widget-preview .wizard-step [class*="grid-cols"],
#widget-preview #wizard-makes-grid,
#widget-preview #wizard-models-list {
  display: grid !important;
  width: 100% !important;
}

/* Ensure grid items display correctly */
#widget-preview #wizard-makes-grid > *,
#widget-preview #wizard-models-list > * {
  display: block !important;
}

/* WIZARD STEP 2: Header Layout (Title + Models Count) */
#widget-preview #wizard-step-2 .flex {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  width: 100% !important;
  margin-bottom: 12px !important;
}

#widget-preview #wizard-step-2 h2 {
  font-size: 18px !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin: 0 !important;
  flex: 1 !important;
}

#widget-preview #wizard-step-2 #wizard-models-count {
  display: block !important;
  text-align: right !important;
  flex-shrink: 0 !important;
  margin-left: 16px !important;
  font-size: 13px !important;
  color: #9ca3af !important;
}

/* WIZARD STEP 2: Breadcrumbs */
#widget-preview #wizard-step-2 #wizard-breadcrumbs-2 {
  display: block !important;
  text-align: left !important;
  margin-bottom: 16px !important;
  font-size: 14px !important;
  color: #6b7280 !important;
}

/* WIZARD STEP 2: Search Input Layout */
#widget-preview #wizard-step-2 .wizard-search-wrapper,
#widget-preview #wizard-step-2 .search-wrapper {
  display: block !important;
  width: 100% !important;
  max-width: 100% !important;
  margin-bottom: 20px !important;
  margin-top: 0 !important;
  position: relative !important;
}

/* Override max-width constraint from JavaScript */
#widget-preview #wizard-step-2 .wizard-search-wrapper.max-w-md {
  max-width: 100% !important;
}

#widget-preview #wizard-step-2 .wizard-search-wrapper input,
#widget-preview #wizard-step-2 .search-wrapper input,
#widget-preview #wizard-step-2 input[type="text"] {
  width: 100% !important;
  max-width: 100% !important;
  height: 44px !important;
  padding: 12px 44px 12px 16px !important;
  font-size: 15px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  background: white !important;
}

/* Search icon positioning - target the absolute positioned div */
#widget-preview #wizard-step-2 .wizard-search-wrapper .absolute,
#widget-preview #wizard-step-2 .search-wrapper .absolute {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  padding-right: 12px !important;
  pointer-events: none !important;
}

#widget-preview #wizard-step-2 .wizard-search-wrapper svg,
#widget-preview #wizard-step-2 .search-wrapper svg {
  width: 18px !important;
  height: 18px !important;
  color: #9ca3af !important;
}

/* WIZARD VERTICAL HIERARCHY: Proper spacing and layout with maximum specificity */
#widget-preview #wheel-fit-wizard .wizard-step h2,
#widget-preview .wizard-step h2 {
  /* Step titles - prominent and well-spaced */
  display: block !important;
  margin-bottom: 20px !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
}

#widget-preview #wheel-fit-wizard .wizard-step nav,
#widget-preview .wizard-step nav {
  /* Breadcrumbs - tight spacing under title */
  display: block !important;
  margin-bottom: 12px !important;
  font-size: 14px !important;
  color: var(--wsf-text-muted, #6b7280) !important;
  font-weight: 500 !important;
}

#widget-preview #wheel-fit-wizard .wizard-step p[id*="-count"],
#widget-preview .wizard-step p[id*="-count"] {
  /* Count labels - secondary info with moderate spacing */
  display: block !important;
  margin-bottom: 24px !important;
  font-size: 13px !important;
  color: var(--wsf-text-muted, #9ca3af) !important;
  font-weight: 400 !important;
}

#widget-preview #wheel-fit-wizard .wizard-step .wizard-search-wrapper,
#widget-preview .wizard-step .wizard-search-wrapper {
  /* Search input - proper spacing before main content */
  display: block !important;
  margin-bottom: 24px !important;
  max-width: 400px !important;
}

/* Сам грид производителей — занять всю ширину виджета */
#widget-preview #wizard-makes-grid {
  width: 100% !important;
  max-width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
  gap: 12px !important;
}

/* Элементы грида */
#widget-preview #wizard-makes-grid > * {
  display: grid !important;
  grid-template-rows: 1fr auto !important;
  place-items: center !important;
  height: 96px !important;
  padding: 8px !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

#widget-preview #wizard-makes-grid > * img {
  max-width: 64px !important;
  max-height: 40px !important;
  object-fit: contain !important;
}

#widget-preview #wizard-makes-grid > * span {
  margin-top: 6px !important;
  font-size: 12px !important;
  line-height: 1.1 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  width: 100% !important;
  text-align: center !important;
}

/* Form grid width for horizontal layouts */
#widget-preview #tab-by-car {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: auto !important;
}

/* Wizard progress bar */
#widget-preview #wizard-progress-bar {
  width: 100% !important;
  max-width: 100% !important;
}

/* Fix wizard height issues in admin preview */
#widget-preview #wheel-fit-wizard {
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

#widget-preview #wheel-fit-wizard .wsf-form-wrapper {
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

#widget-preview #wheel-fit-wizard .relative {
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

/* Fix specific wizard step containers */
#widget-preview #wizard-step-1,
#widget-preview #wizard-step-2,
#widget-preview #wizard-step-3,
#widget-preview #wizard-step-4,
#widget-preview #wizard-results {
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

/* Fix wizard lists */
#widget-preview #wizard-models-list,
#widget-preview #wizard-years-list,
#widget-preview #wizard-modifications-list {
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

/* AGGRESSIVE HEIGHT RESET FOR ADMIN PREVIEW - Force all wizard elements to auto height */
#widget-preview #wheel-fit-wizard,
#widget-preview #wheel-fit-wizard *,
#widget-preview #wheel-fit-wizard .wizard-step,
#widget-preview #wheel-fit-wizard .wsf-form-wrapper,
#widget-preview #wheel-fit-wizard .relative,
#widget-preview #wizard-step-1,
#widget-preview #wizard-step-2,
#widget-preview #wizard-step-3,
#widget-preview #wizard-step-4,
#widget-preview #wizard-results {
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

/* Remove any padding/margin that might create excessive space in admin */
#widget-preview #wheel-fit-wizard .wizard-step {
  padding-top: 0 !important;
  padding-bottom: 20px !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Ensure hidden steps take no space in admin */
#widget-preview #wheel-fit-wizard .wizard-step.hidden {
  display: none !important;
  height: 0 !important;
  min-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
}

/* Force container to not expand beyond content */
#widget-preview .wsf-finder-widget,
#widget-preview .wheel-fit-widget {
  min-height: auto !important;
  height: auto !important;
  max-height: none !important;
}

/* Fix for any remaining WordPress admin interference */
.wp-admin #widget-preview select,
.wp-admin #widget-preview input,
.wp-admin #widget-preview .wsf-input {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Ensure block-level elements maintain full width */
#widget-preview .block {
  width: 100% !important;
  display: block !important;
}

/* Fix for Tailwind w-full class in admin context */
#widget-preview .w-full {
  width: 100% !important;
}

/* Responsive fixes for admin preview */
@media (min-width: 768px) {
  #widget-preview select,
  #widget-preview input,
  #widget-preview .wsf-input {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Keep consistent width with search results on all screen sizes */
  #widget-preview .wheel-fit-widget,
  #widget-preview .wsf-finder-widget {
    max-width: 56rem !important; /* 896px - consistent with max-w-4xl */
  }
}

@media (min-width: 1024px) {
  /* Keep consistent width with search results on desktop */
  #widget-preview .wheel-fit-widget,
  #widget-preview .wsf-finder-widget {
    max-width: 56rem !important; /* 896px - consistent with max-w-4xl */
  }
}

/* Live Preview container - prevent overflow and center content */
#widget-preview {
  display: flex !important;
  justify-content: center !important;
  align-items: flex-start !important;
  padding: 1rem !important;
  overflow-x: hidden !important; /* Prevent horizontal scroll */
  width: 100% !important;
  box-sizing: border-box !important;
}

/* Ensure max-w-4xl classes maintain their intended width */
#widget-preview .max-w-4xl {
  max-width: 56rem !important; /* 896px - preserve original max-w-4xl value */
}

/* Override max-w-full only when it might interfere */
#widget-preview .max-w-full {
  max-width: 56rem !important; /* Match widget width */
}

/* Text overflow handling for long localized labels */
#widget-preview label,
#widget-preview .wizard-step-name {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* Allow text wrapping for multi-line content */
#widget-preview .wizard-step-name {
  white-space: normal !important; /* Override for step names */
  line-height: 1.2 !important;
  max-height: 2.4em !important; /* 2 lines max */
  overflow: hidden !important;
}

/* CRITICAL FIX: Override all conflicting text rules for wizard elements */

/* 1. HEADERS - Prevent text breaking */
#widget-preview .wizard-step h2,
#widget-preview .wizard-step h2 *,
#widget-preview .wsf-widget__title,
#widget-preview .wsf-widget__title * {
  white-space: normal !important;
  word-break: normal !important;
  overflow: visible !important;
  text-overflow: initial !important;
  hyphens: none !important;
  word-wrap: normal !important;
  overflow-wrap: normal !important;
  text-wrap: wrap !important;
  line-height: 1.2 !important;
}

/* 2. NAVIGATION BUTTONS - Keep text intact */
#widget-preview #wizard-next-btn,
#widget-preview #wizard-back-btn,
#widget-preview button[data-i18n*="button_"] {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow: visible !important;
  text-overflow: initial !important;
  min-width: 80px !important;
  padding: 8px 16px !important;
  text-align: center !important;
  hyphens: none !important;
}

/* 3. WIZARD LIST ITEMS - Unified styling */
#widget-preview .list-item,
#widget-preview #wizard-models-list button,
#widget-preview #wizard-years-list button,
#widget-preview #wizard-modifications-list button,
#widget-preview #wizard-makes-grid button {
  /* Reset all text breaking */
  white-space: normal !important;
  word-break: normal !important;
  overflow: visible !important;
  text-overflow: initial !important;
  hyphens: auto !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;

  /* Unified layout */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;

  /* Consistent sizing */
  min-width: 80px !important;
  min-height: 48px !important;
  max-width: 100% !important;
  padding: 12px 16px !important;
  line-height: 1.3 !important;

  /* Visual consistency */
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

/* 4. TEXT INSIDE LIST ITEMS */
#widget-preview .list-item span,
#widget-preview .list-item *,
#widget-preview #wizard-models-list button span,
#widget-preview #wizard-years-list button span,
#widget-preview #wizard-modifications-list button span,
#widget-preview #wizard-makes-grid button span {
  white-space: normal !important;
  word-break: normal !important;
  overflow: visible !important;
  text-overflow: initial !important;
  hyphens: auto !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  line-height: 1.3 !important;
  display: block !important;
  text-align: center !important;
}

/* 5. SPECIAL CASE: Modifications grid layout */
#widget-preview #wizard-modifications-list .list-item {
  min-height: 60px !important;
  padding: 16px !important;
  flex-direction: column !important;
  gap: 4px !important;
}

/* 6. OVERRIDE TAILWIND UTILITY CLASSES */
#widget-preview .wsf-truncate,
#widget-preview .wsf-whitespace-nowrap,
#widget-preview .wsf-break-all,
#widget-preview .wsf-overflow-hidden {
  white-space: normal !important;
  word-break: normal !important;
  overflow: visible !important;
  text-overflow: initial !important;
}

/* 7. SEARCH INPUT */
#widget-preview .wizard-search-wrapper input {
  white-space: normal !important;
  word-break: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

/* Fix search input width */
#widget-preview .wizard-search-wrapper {
  margin-bottom: 16px !important;
}

#widget-preview .wizard-search-wrapper input {
  min-width: 200px !important;
  width: 100% !important;
  max-width: 400px !important;
  height: 40px !important;
  padding: 8px 40px 8px 12px !important;
  font-size: 14px !important;
}

/* Improve spacing in wizard steps */
#widget-preview .wizard-step > div {
  padding: 24px !important;
  margin-bottom: 20px !important;
}

#widget-preview .wizard-step h2 {
  margin-bottom: 16px !important;
  white-space: normal !important;
  word-break: normal !important;
  overflow: visible !important;
  text-overflow: initial !important;
}

#widget-preview .wizard-step nav {
  margin-bottom: 16px !important;
}

/* Better garage button styling */
#widget-preview [data-garage-trigger] {
  white-space: nowrap !important;
  gap: 6px !important;
}

#widget-preview .wsf-garage-count-badge {
  margin-left: 4px !important;
  min-width: 20px !important;
  height: 20px !important;
  line-height: 20px !important;
  font-size: 11px !important;
}

/* Step Indicators Fix for Live Preview - make badges compact and circular */
#widget-preview .flex.items-center.gap-3.mb-8 {
  max-width: 56rem !important;
  margin: 0 auto !important;
  width: 100% !important; /* Full width within 56rem container */
}

#widget-preview .flex-1 {
  width: 100% !important;
  flex: 1 1 100% !important;
}

#widget-preview .flex.items-center.gap-2.md\\:gap-4 {
  width: 100% !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  gap: 0 !important;
}

/* Step containers (circle + text) - compact (note: exclude wrapper that also has md:gap-4) */
#widget-preview .flex.items-center.gap-2:not(.md\\:gap-4) {
  flex: 0 0 auto !important;
  width: auto !important;
  min-width: 0 !important; /* Allow text to shrink if needed */
}

/* Progress bars between steps - fill remaining space */
#widget-preview [id^="progress-"] {
  flex: 1 1 auto !important;
  margin: 0 16px !important;
  min-width: 0 !important;
}

@media (max-width: 640px) {
  #widget-preview .flex.items-center.gap-2.md\\:gap-4 {
    flex-wrap: wrap !important;
    gap: 8px !important;
    justify-content: center !important; /* Center on mobile when wrapping */
  }
  
  /* Hide progress bars on mobile to prevent layout issues */
  #widget-preview [id^="progress-"] {
    display: none !important;
  }
}

/* Debug styles (remove in production) */
/*
#widget-preview select:hover,
#widget-preview input:hover,
#widget-preview .wsf-input:hover {
  outline: 2px solid red !important;
  outline-offset: 2px !important;
}
*/

/* ========================================
   UNIFIED STEP INDICATORS - FINAL RULES
   ======================================== */

/* ОСТОРОЖНАЯ унификация ТОЛЬКО индикаторов шагов - 24x24px во всех состояниях */
/* Применяется ТОЛЬКО к элементам внутри Live Preview, чтобы не сломать другие части */
#widget-preview .wsf-step-index,
#widget-preview [id^="step-indicator-"] {
  width: 24px !important;
  height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
  max-width: 24px !important;
  max-height: 24px !important;
  flex: 0 0 24px !important;
  border-radius: 50% !important;
  aspect-ratio: 1/1 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
  margin: 0 !important;
  border-width: 1px !important; /* Сохраняем границы для состояний */
  transform: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 12px !important;
  line-height: 1 !important;
  font-weight: 600 !important;
  overflow: hidden !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Цвета состояний — БЕЗ влияния на размер, ТОЛЬКО цвета */
/* Активный шаг */
#widget-preview .wizard-step.is-active .wsf-step-index,
#widget-preview .wizard-step.is-active [id^="step-indicator-"],
#widget-preview .wsf-step-index.is-active,
#widget-preview [id^="step-indicator-"].is-active {
  background-color: var(--ws-primary, #2563eb) !important;
  color: #ffffff !important;
  border-color: var(--ws-primary, #2563eb) !important;
}

/* Завершённый шаг */
#widget-preview .wizard-step.is-done .wsf-step-index,
#widget-preview .wizard-step.is-done [id^="step-indicator-"],
#widget-preview .wsf-step-index.is-done,
#widget-preview [id^="step-indicator-"].is-done {
  background-color: var(--ws-pill-done, #e8f0ff) !important;
  color: var(--ws-primary, #2563eb) !important;
  border-color: var(--ws-primary, #2563eb) !important;
}

/* Обычный/неактивный шаг */
#widget-preview .wizard-step:not(.is-active):not(.is-done) .wsf-step-index,
#widget-preview .wizard-step:not(.is-active):not(.is-done) [id^="step-indicator-"],
#widget-preview .wsf-step-index:not(.is-active):not(.is-done),
#widget-preview [id^="step-indicator-"]:not(.is-active):not(.is-done) {
  background-color: var(--ws-pill-idle, #f3f4f6) !important;
  color: var(--ws-text-muted, #6b7280) !important;
  border-color: var(--ws-border, #d1d5db) !important;
}

/* Полосы прогресса между шагами — ОНИ должны растягиваться */
#widget-preview [id^="progress-"],
#widget-preview .wizard-progress,
#widget-preview .progress-bar {
  flex: 1 1 auto !important;
  min-width: 0 !important;
  height: 2px !important;
  border-radius: 1px !important;
  background-color: var(--ws-progress-bg, #e5e7eb) !important;
  margin: 0 8px !important;
}

/* REMOVED: Problematic flex rules that forced horizontal layout */
/* These rules were making wizard steps display as flex containers with horizontal alignment */
/* This conflicted with the vertical hierarchy we want for wizard content */

/* ========================================
   STEP-BY-STEP PROGRESS INDICATORS INSIDE FORM
   ======================================== */

/* Панель прогресса Step-by-Step внутри формы - наследует стили темы */
#widget-preview .wsf-form-wrapper .flex.items-center.gap-3 {
  /* Наследование стилей от карточки */
  background-color: inherit !important;
  color: inherit !important;
  border: none !important;
  box-shadow: none !important;

  /* Правильные отступы от заголовка */
  margin-top: 1.5rem !important; /* Отступ от заголовка виджета */
  margin-bottom: 2rem !important; /* Отступ до контента формы */

  /* Центрирование и ширина */
  width: 100% !important;
  max-width: 56rem !important;
  margin-left: auto !important;
  margin-right: auto !important;

  /* Убираем лишние отступы по бокам */
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* Индикаторы шагов Step-by-Step наследуют цвета темы */
#widget-preview .wsf-form-wrapper [id^="step-indicator-"] {
  background-color: var(--wsf-surface, #f3f4f6) !important;
  color: var(--wsf-text-muted, #6b7280) !important;
  border: 1px solid var(--wsf-border, #d1d5db) !important;
  width: 24px !important;
  height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
  flex: 0 0 24px !important;
  border-radius: 50% !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

/* Активный индикатор Step-by-Step */
#widget-preview .wsf-form-wrapper [id^="step-indicator-"].active {
  background-color: var(--wsf-primary, #2563eb) !important;
  color: #ffffff !important;
  border-color: var(--wsf-primary, #2563eb) !important;
}

/* Завершенный индикатор Step-by-Step */
#widget-preview .wsf-form-wrapper [id^="step-indicator-"].completed {
  background-color: var(--wsf-success, #10b981) !important;
  color: #ffffff !important;
  border-color: var(--wsf-success, #10b981) !important;
}

/* Текст шагов Step-by-Step наследует цвета темы */
#widget-preview .wsf-form-wrapper [id^="step-text-"] {
  color: var(--wsf-text-muted, #6b7280) !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

/* Активный текст шага Step-by-Step */
#widget-preview .wsf-form-wrapper [id^="step-text-"].active {
  color: var(--wsf-primary, #2563eb) !important;
}

/* Прогресс-линии между шагами Step-by-Step */
#widget-preview .wsf-form-wrapper [id^="progress-"] {
  background-color: var(--wsf-border, #e5e7eb) !important;
  height: 2px !important;
  flex: 1 1 auto !important;
  min-width: 0 !important;
  transition: all 0.2s ease !important;
}

/* Активная прогресс-линия Step-by-Step */
#widget-preview .wsf-form-wrapper [id^="progress-"].active {
  background-color: var(--wsf-primary, #2563eb) !important;
}

/* Адаптивность для Step-by-Step панели */
@media (max-width: 767px) {
  #widget-preview .wsf-form-wrapper .flex.items-center.gap-3 {
    margin-top: 1rem !important;
    margin-bottom: 1.5rem !important;
    gap: 0.5rem !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
  }

  #widget-preview .wsf-form-wrapper [id^="step-indicator-"] {
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
    min-height: 20px !important;
    flex: 0 0 20px !important;
    font-size: 10px !important;
  }

  #widget-preview .wsf-form-wrapper [id^="step-text-"] {
    font-size: 0.75rem !important;
  }

  /* Скрываем прогресс-линии на мобильных для экономии места */
  #widget-preview .wsf-form-wrapper [id^="progress-"] {
    display: none !important;
  }
}

/* ========================================
   INLINE LAYOUT (1×4) - BUTTON HEIGHT FIX
   ======================================== */

/* Выравнивание высоты кнопки "Find Sizes" с полями формы в Inline лейауте */
#widget-preview .wheel-fit-widget .ws-submit .btn-primary,
#widget-preview .wsf-finder-widget .ws-submit .btn-primary {
  /* Устанавливаем точно такую же высоту, как у селекторов */
  height: var(--ws-control-height, 44px) !important;
  min-height: var(--ws-control-height, 44px) !important;
  max-height: var(--ws-control-height, 44px) !important;

  /* Убираем вертикальные отступы, чтобы контент поместился в заданную высоту */
  padding-top: 0 !important;
  padding-bottom: 0 !important;

  /* Сохраняем горизонтальные отступы для красоты */
  padding-left: 1.5rem !important; /* 24px */
  padding-right: 1.5rem !important; /* 24px */

  /* Центрируем содержимое по вертикали */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /* Убираем лишние отступы и границы */
  box-sizing: border-box !important;

  /* Сохраняем остальные стили кнопки */
  font-size: 0.875rem !important; /* 14px - как у селекторов */
  font-weight: 600 !important;
  line-height: 1.3 !important; /* Как у селекторов */

  /* Убираем трансформации, которые могут влиять на высоту */
  transform: none !important;
}

/* Состояние hover - убираем трансформацию, которая может изменить высоту */
#widget-preview .wheel-fit-widget .ws-submit .btn-primary:hover:not(:disabled),
#widget-preview .wsf-finder-widget .ws-submit .btn-primary:hover:not(:disabled) {
  transform: none !important; /* Убираем translateY(-1px) */
  /* Сохраняем изменение цвета при hover */
  background-color: var(--wsf-hover) !important;
}

/* Состояние focus - убираем изменения высоты */
#widget-preview .wheel-fit-widget .ws-submit .btn-primary:focus,
#widget-preview .wsf-finder-widget .ws-submit .btn-primary:focus {
  transform: none !important;
  /* Сохраняем focus ring */
  outline: none !important;
  box-shadow: 0 0 0 2px var(--wsf-primary), 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
}

/* Лоадер внутри кнопки - подгоняем под новую высоту */
#widget-preview .ws-submit .btn-primary #search-loader {
  height: 16px !important;
  width: 16px !important;
}

#widget-preview .ws-submit .btn-primary #search-loader > div {
  height: 16px !important;
  width: 16px !important;
}

/* Адаптивность для Inline лейаута */
@media (max-width: 639px) {
  /* На мобильных кнопка может быть чуть выше для удобства нажатия */
  #widget-preview .wheel-fit-widget .ws-submit .btn-primary,
  #widget-preview .wsf-finder-widget .ws-submit .btn-primary {
    height: 48px !important; /* Чуть выше на мобильных */
    min-height: 48px !important;
    max-height: 48px !important;
    font-size: 0.9rem !important; /* Чуть больше шрифт */
  }
}

/* ========================================
   GRID (2×2) LAYOUT - GARAGE SPACING FIX
   ======================================== */

/* Уменьшение отступа между кнопкой "Find Sizes" и блоком Garage в Grid (2×2) лейауте */
#widget-preview .wheel-fit-widget #tab-by-car.grid-2x2,
#widget-preview .wsf-finder-widget #tab-by-car.grid-2x2 {
  /* Уменьшаем общий gap между элементами сетки */
  gap: 0.75rem !important; /* Было 1rem (16px), стало 0.75rem (12px) */
}

/* Специфичные правила для блока Garage в Grid лейауте */
#widget-preview #tab-by-car.grid-2x2 .flex.flex-col.w-full.sm\\:col-span-2.items-end {
  /* Убираем лишние отступы у контейнера Garage */
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* Кнопка Garage - убираем верхний отступ */
#widget-preview #tab-by-car.grid-2x2 [data-garage-trigger] {
  margin-top: 0 !important; /* Убираем mt-1 */
  margin-bottom: 0.25rem !important; /* Добавляем небольшой отступ снизу */
}

/* Контейнер кнопки "Find Sizes" в Grid лейауте */
#widget-preview #tab-by-car.grid-2x2 .ws-submit {
  /* Убираем лишние отступы */
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
}

/* Прозрачный label над кнопкой "Find Sizes" - убираем отступ */
#widget-preview #tab-by-car.grid-2x2 .ws-submit label {
  margin-bottom: 0 !important; /* Убираем mb-1 */
  height: 0 !important; /* Убираем высоту прозрачного элемента */
  line-height: 0 !important;
  padding: 0 !important;
}

/* Сама кнопка "Find Sizes" в Grid лейауте */
#widget-preview #tab-by-car.grid-2x2 .ws-submit .btn-primary {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Дополнительная оптимизация для компактности Grid лейаута */
#widget-preview #tab-by-car.grid-2x2 > .flex-col:not(.ws-submit) {
  /* Уменьшаем отступы у полей формы для более компактного вида */
  margin-bottom: 0 !important;
}

/* ========================================
   SIZE CARDS - GARAGE BUTTON OVERLAP FIX (ADMIN PREVIEW)
   ======================================== */

/* Базовые стили для карточек размеров в админке */
#widget-preview .size-card {
  position: relative !important;
  min-height: auto !important; /* Убираем фиксированную высоту */
  padding-bottom: 2.5rem !important; /* Место для кнопки + отступ */
}

/* Карточки с двумя строками размеров (Front/Rear) в админке */
#widget-preview .size-card:has(.space-y-0\.5) {
  padding-bottom: 3rem !important; /* Больше места для двухстрочного контента */
}

/* Кнопка добавления в гараж в админке - улучшенное позиционирование */
#widget-preview .save-to-garage {
  position: absolute !important;
  bottom: 0.5rem !important; /* 8px от низа */
  right: 0.5rem !important; /* 8px от правого края */
  z-index: 10 !important;
  padding: 0.25rem !important; /* 4px */
  border-radius: 50% !important;
  border: none !important;
  background: transparent !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  /* Убираем возможность перекрытия текста */
  pointer-events: auto !important;
}

/* Адаптивные правила для мобильных устройств в админке */
@media (max-width: 640px) {
  #widget-preview .size-card {
    padding-bottom: 2.75rem !important; /* Немного больше места на мобильных */
  }

  #widget-preview .size-card:has(.space-y-0\.5) {
    padding-bottom: 3.25rem !important; /* Еще больше для двухстрочных на мобильных */
  }

  #widget-preview .save-to-garage {
    bottom: 0.375rem !important; /* 6px от низа на мобильных */
    right: 0.375rem !important; /* 6px от правого края на мобильных */
  }
}

/* Очень маленькие экраны (320px) в админке */
@media (max-width: 375px) {
  #widget-preview .size-card {
    padding-bottom: 3rem !important;
  }

  #widget-preview .size-card:has(.space-y-0\.5) {
    padding-bottom: 3.5rem !important; /* Максимальное место для двухстрочных */
  }
}

/* Адаптивность для Grid лейаута на мобильных */
@media (max-width: 639px) {
  #widget-preview .wheel-fit-widget #tab-by-car.grid-2x2,
  #widget-preview .wsf-finder-widget #tab-by-car.grid-2x2 {
    gap: 0.5rem !important; /* Еще более компактно на мобильных */
  }

  /* На мобильных убираем все лишние отступы */
  #widget-preview #tab-by-car.grid-2x2 [data-garage-trigger] {
    margin-bottom: 0.125rem !important; /* Минимальный отступ */
  }
}

/* ========================================
   STEP-BY-STEP LAYOUT - BUTTON SIZE & SPACING UNIFICATION
   ======================================== */

/* Выравнивание высоты кнопки "Search" с полями формы в Step-by-Step layout */
#widget-preview .wheel-fit-widget #wheel-fit-form .btn-primary,
#widget-preview .wsf-finder-widget #wheel-fit-form .btn-primary {
  /* Устанавливаем точно такую же высоту, как у селекторов */
  height: var(--ws-control-height, 44px) !important;
  min-height: var(--ws-control-height, 44px) !important;
  max-height: var(--ws-control-height, 44px) !important;

  /* Убираем вертикальные отступы, чтобы контент поместился в заданную высоту */
  padding-top: 0 !important;
  padding-bottom: 0 !important;

  /* Сохраняем горизонтальные отступы как в Live Preview */
  padding-left: 2.5rem !important; /* 40px - как px-10 */
  padding-right: 2.5rem !important; /* 40px - как px-10 */

  /* Обеспечиваем полную ширину как в Live Preview */
  width: 100% !important;
  max-width: 100% !important;

  /* Центрируем содержимое по вертикали */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /* Убираем лишние отступы и границы */
  box-sizing: border-box !important;

  /* Сохраняем остальные стили кнопки */
  font-size: 0.875rem !important; /* 14px - как у селекторов */
  font-weight: 600 !important;
  line-height: 1.3 !important; /* Как у селекторов */

  /* Убираем трансформации, которые могут влиять на высоту */
  transform: none !important;
}

/* Состояние hover - убираем трансформацию, которая может изменить высоту */
#widget-preview .wheel-fit-widget #wheel-fit-form .btn-primary:hover:not(:disabled),
#widget-preview .wsf-finder-widget #wheel-fit-form .btn-primary:hover:not(:disabled) {
  transform: none !important; /* Убираем translateY(-1px) */
  /* Сохраняем изменение цвета при hover */
  background-color: var(--wsf-hover) !important;
}

/* Состояние focus - убираем изменения высоты */
#widget-preview .wheel-fit-widget #wheel-fit-form .btn-primary:focus,
#widget-preview .wsf-finder-widget #wheel-fit-form .btn-primary:focus {
  transform: none !important;
  /* Сохраняем focus ring */
  outline: none !important;
  box-shadow: 0 0 0 2px var(--wsf-primary), 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
}

/* Контейнер кнопки поиска в Step-by-Step - уменьшаем отступ сверху */
#widget-preview .wheel-fit-widget #wheel-fit-form .mt-6,
#widget-preview .wsf-finder-widget #wheel-fit-form .mt-6 {
  margin-top: 1rem !important; /* Уменьшаем с 24px (mt-6) до 16px (mt-4) */
}

/* Лоадер внутри кнопки Step-by-Step - подгоняем под новую высоту */
#widget-preview #wheel-fit-form .btn-primary #search-loader {
  height: 16px !important;
  width: 16px !important;
}

#widget-preview #wheel-fit-form .btn-primary #search-loader > div {
  height: 16px !important;
  width: 16px !important;
}

/* Адаптивность для Step-by-Step layout */
@media (max-width: 639px) {
  /* На мобильных кнопка может быть чуть выше для удобства нажатия */
  #widget-preview .wheel-fit-widget #wheel-fit-form .btn-primary,
  #widget-preview .wsf-finder-widget #wheel-fit-form .btn-primary {
    height: 48px !important; /* Чуть выше на мобильных */
    min-height: 48px !important;
    max-height: 48px !important;
    font-size: 0.9rem !important; /* Чуть больше шрифт */
    padding-left: 2rem !important; /* Меньше отступы на мобильных */
    padding-right: 2rem !important;

    /* Обеспечиваем полную ширину и на мобильных */
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Еще больше уменьшаем отступ на мобильных */
  #widget-preview .wheel-fit-widget #wheel-fit-form .mt-6,
  #widget-preview .wsf-finder-widget #wheel-fit-form .mt-6 {
    margin-top: 0.75rem !important; /* 12px на мобильных */
  }
}
