// file: assets/js/admin-brands.js
(() => {
    document.addEventListener('DOMContentLoaded', () => {
        const form = document.getElementById('brand-filters-form');
        if (!form) {
            return;
        }

        const container = form.querySelector('.brand-filters__container');
        const hiddenInputsContainer = form.querySelector('#brand-filters-hidden-inputs');
        const includeCountEl = form.querySelector('#include-count');
        const excludeCountEl = form.querySelector('#exclude-count');
        const neutralCountEl = form.querySelector('#neutral-count');
        const priorityNote = form.querySelector('.brand-filters__priority-note');
        const allTags = Array.from(form.querySelectorAll('.brand-tag'));

        const updateState = () => {
            let includeCount = 0;
            let excludeCount = 0;

            const includeSlugs = [];
            const excludeSlugs = [];

            allTags.forEach(tag => {
                if (tag.classList.contains('is-included')) {
                    includeCount++;
                    includeSlugs.push(tag.dataset.slug);
                } else if (tag.classList.contains('is-excluded')) {
                    excludeCount++;
                    excludeSlugs.push(tag.dataset.slug);
                }
            });

            // Update counters
            if (includeCountEl) includeCountEl.textContent = includeCount;
            if (excludeCountEl) excludeCountEl.textContent = excludeCount;
            if (neutralCountEl) neutralCountEl.textContent = allTags.length - includeCount - excludeCount;

            // Add visual priority indication
            if (includeCount > 0) {
                container.classList.add('has-includes');
                // Update priority note to show active state
                if (priorityNote) {
                    priorityNote.style.backgroundColor = '#dcfce7'; // green background
                    priorityNote.style.borderColor = '#22c55e';
                    priorityNote.style.color = '#166534';
                }
            } else {
                container.classList.remove('has-includes');
                // Reset priority note to default state
                if (priorityNote) {
                    priorityNote.style.backgroundColor = '#e0f2fe'; // blue background
                    priorityNote.style.borderColor = '#0284c7';
                    priorityNote.style.color = '#0c4a6e';
                }
            }

            // Update hidden inputs
            hiddenInputsContainer.innerHTML = '';
            includeSlugs.forEach(slug => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'wheel_size_include_makes[]';
                input.value = slug;
                hiddenInputsContainer.appendChild(input);
            });
            excludeSlugs.forEach(slug => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'wheel_size_exclude_makes[]';
                input.value = slug;
                hiddenInputsContainer.appendChild(input);
            });
        };

        container.addEventListener('click', (e) => {
            const tag = e.target.closest('.brand-tag');
            if (!tag) {
                return;
            }

            e.preventDefault();

            if (tag.classList.contains('is-included')) {
                tag.classList.remove('is-included');
                tag.classList.add('is-excluded');
                tag.setAttribute('aria-checked', 'true');
            } else if (tag.classList.contains('is-excluded')) {
                tag.classList.remove('is-excluded');
                tag.classList.add('is-neutral');
                tag.setAttribute('aria-checked', 'false');
            } else { // is-neutral
                tag.classList.remove('is-neutral');
                tag.classList.add('is-included');
                tag.setAttribute('aria-checked', 'true');
            }

            updateState();
        });

        // Initial state update on page load
        updateState();
    });
})(); 