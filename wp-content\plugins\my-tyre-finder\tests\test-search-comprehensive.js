// Comprehensive test for search functionality issue
console.log('=== Comprehensive Search Functionality Test ===');

// Helper function to wait for element
function waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
        const element = document.querySelector(selector);
        if (element) {
            resolve(element);
            return;
        }
        
        const observer = new MutationObserver((mutations, obs) => {
            const element = document.querySelector(selector);
            if (element) {
                obs.disconnect();
                resolve(element);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        setTimeout(() => {
            observer.disconnect();
            reject(new Error(`Element ${selector} not found within ${timeout}ms`));
        }, timeout);
    });
}

// Test 1: Verify widget and data
console.log('1. Widget and data verification:');
if (!window.wheelFitWidget) {
    console.log('   ❌ Widget not found - cannot proceed with tests');
    return;
}

const widget = window.wheelFitWidget;
const selectedData = widget.selectedData;

console.log('   ✅ Widget found');
console.log('   Selected data:', selectedData);
console.log('   Required fields check:', {
    make: !!selectedData.make,
    model: !!selectedData.model,
    year: !!selectedData.year,
    generation: !!selectedData.generation,
    modification: !!selectedData.modification
});

// Test 2: Manual API test
console.log('2. Manual API test:');

async function testDirectAPI() {
    if (!window.WheelFitData) {
        console.log('   ❌ WheelFitData not available');
        return;
    }
    
    const testData = {
        make: 'acura',
        model: 'rl',
        year: 2003,
        modification: '35i-v6-208-hp'
    };
    
    console.log('   Testing with data:', testData);
    
    try {
        const formData = new FormData();
        formData.append('action', 'wf_search_sizes');
        formData.append('nonce', window.WheelFitData.nonce);
        Object.keys(testData).forEach(key => formData.append(key, testData[key]));
        
        const response = await fetch(window.WheelFitData.ajaxurl, {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        console.log('   API Response:', result);
        
        if (result.success) {
            const factoryCount = result.data?.factory_sizes?.length || 0;
            const optionalCount = result.data?.optional_sizes?.length || 0;
            console.log('   ✅ API Success - Factory:', factoryCount, 'Optional:', optionalCount);
            
            if (factoryCount === 0 && optionalCount === 0) {
                console.log('   ⚠️ API returns empty results - this is the root cause');
                console.log('   Check: 1) API key validity, 2) Vehicle data exists in API, 3) Parameter format');
            }
        } else {
            console.log('   ❌ API Failed:', result.data);
        }
    } catch (error) {
        console.log('   ❌ API Error:', error);
    }
}

// Test 3: Compare garage data vs current data
console.log('3. Garage vs current data comparison:');

if (typeof LocalStorageHandler !== 'undefined') {
    const storage = new LocalStorageHandler();
    const garage = storage.getGarage();
    
    if (garage.length > 0) {
        const garageItem = garage[0];
        console.log('   Garage item sample:', garageItem);
        console.log('   Current selected data:', selectedData);
        
        // Check for differences
        const differences = [];
        ['make', 'model', 'year', 'modification'].forEach(field => {
            if (garageItem[field] !== selectedData[field]) {
                differences.push(`${field}: garage="${garageItem[field]}" vs current="${selectedData[field]}"`);
            }
        });
        
        if (differences.length > 0) {
            console.log('   ⚠️ Data differences found:', differences);
        } else {
            console.log('   ✅ Data matches garage item');
        }
    }
}

// Test 4: Test widget search method
console.log('4. Widget search method test:');

async function testWidgetSearch() {
    console.log('   Triggering widget.searchSizes()...');
    
    try {
        await widget.searchSizes();
        console.log('   ✅ Search method completed');
        
        // Check DOM state after search
        setTimeout(() => {
            const resultsContainer = document.getElementById('search-results');
            const noResults = document.getElementById('no-results');
            const factoryGrid = document.getElementById('factory-grid');
            const optionalGrid = document.getElementById('optional-grid');
            
            console.log('   DOM state after search:', {
                resultsVisible: resultsContainer && !resultsContainer.classList.contains('hidden'),
                noResultsVisible: noResults && !noResults.classList.contains('hidden'),
                factoryCards: factoryGrid ? factoryGrid.children.length : 0,
                optionalCards: optionalGrid ? optionalGrid.children.length : 0
            });
        }, 500);
        
    } catch (error) {
        console.log('   ❌ Search method failed:', error);
    }
}

// Test 5: Check for common issues
console.log('5. Common issues check:');

// Check DOM elements
const requiredElements = [
    'search-results',
    'no-results',
    'factory-grid',
    'optional-grid',
    'factory-section',
    'optional-section'
];

requiredElements.forEach(id => {
    const element = document.getElementById(id);
    console.log(`   ${id}: ${element ? '✅' : '❌'}`);
});

// Check for JavaScript errors
let errorCount = 0;
const originalError = console.error;
console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
};

// Test 6: Full end-to-end test
console.log('6. Full end-to-end test:');

async function fullTest() {
    console.log('   Starting full test...');
    
    // Step 1: Test direct API
    await testDirectAPI();
    
    // Step 2: Test widget search
    await testWidgetSearch();
    
    // Step 3: Check for errors
    setTimeout(() => {
        console.log(`   JavaScript errors during test: ${errorCount}`);
        console.error = originalError; // Restore
    }, 2000);
}

// Run tests
console.log('\n=== Running Tests ===');
fullTest().then(() => {
    console.log('\n=== Test Summary ===');
    console.log('Check the logs above for:');
    console.log('1. API response data - if empty, the issue is in the backend/API');
    console.log('2. Widget search completion - if fails, issue is in frontend');
    console.log('3. DOM state after search - if no cards generated, issue is in displayResults');
    console.log('4. Data differences between garage and current - might indicate parameter issues');
    console.log('\nMost likely causes:');
    console.log('- API returns empty results (check API key, vehicle data availability)');
    console.log('- Parameter format mismatch (check make/model/year/modification format)');
    console.log('- Backend error (check WordPress error logs)');
    console.log('- Frontend display issue (check displayResults function)');
});

console.log('\n=== Comprehensive test initiated ===');
