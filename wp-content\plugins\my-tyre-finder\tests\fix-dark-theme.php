<?php
/**
 * Dark Theme Fix Script
 * Add this as a temporary admin page to fix dark theme issues
 * 
 * Usage: Add this to your functions.php temporarily or run via WP-CLI
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    die('This script must be run in WordPress context');
}

// Add admin menu item (temporary)
add_action('admin_menu', function() {
    add_submenu_page(
        'wheel-size',
        'Fix Dark Theme',
        'Fix Dark Theme',
        'manage_options',
        'fix-dark-theme',
        'wsf_fix_dark_theme_page'
    );
});

function wsf_fix_dark_theme_page() {
    if (!current_user_can('manage_options')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }
    
    echo '<div class="wrap">';
    echo '<h1>Dark Theme Fix</h1>';
    
    // Handle form submission
    if (isset($_POST['action'])) {
        $action = sanitize_text_field($_POST['action']);
        
        switch ($action) {
            case 'check_themes':
                wsf_check_themes();
                break;
            case 'reinitialize_themes':
                wsf_reinitialize_themes();
                break;
            case 'test_dark_activation':
                wsf_test_dark_activation();
                break;
            case 'force_dark_theme':
                wsf_force_dark_theme();
                break;
        }
    }
    
    // Display current status
    wsf_display_theme_status();
    
    // Display action buttons
    wsf_display_action_buttons();
    
    echo '</div>';
}

function wsf_display_theme_status() {
    echo '<h2>Current Theme Status</h2>';
    
    if (!class_exists('MyTyreFinder\Includes\ThemeManager')) {
        echo '<div class="notice notice-error"><p>❌ ThemeManager class not found. Make sure the plugin is activated.</p></div>';
        return;
    }
    
    try {
        $themes = \MyTyreFinder\Includes\ThemeManager::get_themes();
        $active_theme = \MyTyreFinder\Includes\ThemeManager::get_active_theme();
        
        echo '<table class="widefat">';
        echo '<tr><th>Property</th><th>Value</th><th>Status</th></tr>';
        
        echo '<tr>';
        echo '<td>Available Themes</td>';
        echo '<td>' . implode(', ', array_keys($themes)) . '</td>';
        echo '<td>' . (count($themes) >= 2 ? '✅' : '❌') . '</td>';
        echo '</tr>';
        
        echo '<tr>';
        echo '<td>Dark Theme Exists</td>';
        echo '<td>' . (isset($themes['dark']) ? 'Yes' : 'No') . '</td>';
        echo '<td>' . (isset($themes['dark']) ? '✅' : '❌') . '</td>';
        echo '</tr>';
        
        echo '<tr>';
        echo '<td>Light Theme Exists</td>';
        echo '<td>' . (isset($themes['light']) ? 'Yes' : 'No') . '</td>';
        echo '<td>' . (isset($themes['light']) ? '✅' : '❌') . '</td>';
        echo '</tr>';
        
        echo '<tr>';
        echo '<td>Active Theme</td>';
        echo '<td>' . $active_theme . '</td>';
        echo '<td>ℹ️</td>';
        echo '</tr>';
        
        echo '</table>';
        
        if (isset($themes['dark'])) {
            echo '<h3>Dark Theme Properties</h3>';
            echo '<pre>' . json_encode($themes['dark'], JSON_PRETTY_PRINT) . '</pre>';
        }
        
    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>❌ Error: ' . $e->getMessage() . '</p></div>';
    }
}

function wsf_display_action_buttons() {
    echo '<h2>Actions</h2>';
    echo '<form method="post">';
    
    echo '<p>';
    echo '<button type="submit" name="action" value="check_themes" class="button">Check Themes</button> ';
    echo '<button type="submit" name="action" value="reinitialize_themes" class="button">Reinitialize Themes</button> ';
    echo '<button type="submit" name="action" value="test_dark_activation" class="button">Test Dark Activation</button> ';
    echo '<button type="submit" name="action" value="force_dark_theme" class="button button-primary">Force Dark Theme</button>';
    echo '</p>';
    
    echo '</form>';
}

function wsf_check_themes() {
    echo '<div class="notice notice-info"><p>🔍 Checking themes...</p></div>';
    
    $themes_option = get_option('wsf_theme_presets', null);
    $active_option = get_option('wsf_active_theme', null);
    
    echo '<h3>Database Options</h3>';
    echo '<p><strong>wsf_theme_presets:</strong> ' . ($themes_option ? 'EXISTS' : 'NOT SET') . '</p>';
    echo '<p><strong>wsf_active_theme:</strong> ' . ($active_option ? $active_option : 'NOT SET') . '</p>';
    
    if ($themes_option) {
        echo '<p><strong>Themes in DB:</strong> ' . implode(', ', array_keys($themes_option)) . '</p>';
    }
}

function wsf_reinitialize_themes() {
    echo '<div class="notice notice-info"><p>🔄 Reinitializing themes...</p></div>';
    
    try {
        // Delete existing themes
        delete_option('wsf_theme_presets');
        delete_option('wsf_active_theme');
        
        // Force re-initialization
        $themes = \MyTyreFinder\Includes\ThemeManager::get_themes();
        
        echo '<div class="notice notice-success"><p>✅ Themes reinitialized successfully!</p></div>';
        echo '<p>New themes: ' . implode(', ', array_keys($themes)) . '</p>';
        
    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>❌ Error reinitializing themes: ' . $e->getMessage() . '</p></div>';
    }
}

function wsf_test_dark_activation() {
    echo '<div class="notice notice-info"><p>🧪 Testing dark theme activation...</p></div>';
    
    try {
        $dark_theme = \MyTyreFinder\Includes\ThemeManager::get_theme('dark');
        
        if (!$dark_theme) {
            echo '<div class="notice notice-error"><p>❌ Dark theme not found</p></div>';
            return;
        }
        
        $result = \MyTyreFinder\Includes\ThemeManager::set_active_theme('dark');
        
        if ($result) {
            echo '<div class="notice notice-success"><p>✅ Dark theme activated successfully!</p></div>';
            
            // Verify
            $active = \MyTyreFinder\Includes\ThemeManager::get_active_theme();
            echo '<p>Current active theme: ' . $active . '</p>';
        } else {
            echo '<div class="notice notice-error"><p>❌ Dark theme activation failed</p></div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>❌ Error testing dark theme: ' . $e->getMessage() . '</p></div>';
    }
}

function wsf_force_dark_theme() {
    echo '<div class="notice notice-info"><p>🔧 Force activating dark theme...</p></div>';
    
    try {
        // Ensure themes exist
        $themes = \MyTyreFinder\Includes\ThemeManager::get_themes();
        
        if (!isset($themes['dark'])) {
            // Force create dark theme
            $themes['dark'] = [
                'name' => 'Dark Theme',
                '--wsf-primary' => '#7dd3fc',
                '--wsf-bg' => '#1e1e1e',
                '--wsf-text' => '#f3f4f6',
                '--wsf-border' => '#374151',
                '--wsf-hover' => '#0ea5e9',
                '--wsf-secondary' => '#9ca3af',
                '--wsf-accent' => '#60a5fa',
                '--wsf-muted' => '#6b7280',
                '--wsf-success' => '#34d399',
                '--wsf-warning' => '#fbbf24',
                '--wsf-error' => '#f87171'
            ];
            
            update_option('wsf_theme_presets', $themes, false);
            echo '<div class="notice notice-success"><p>✅ Dark theme created in database</p></div>';
        }
        
        // Force set active theme
        $result = update_option('wsf_active_theme', 'dark');
        
        if ($result) {
            echo '<div class="notice notice-success"><p>✅ Dark theme force activated!</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>❌ Failed to force activate dark theme</p></div>';
        }
        
    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>❌ Error force activating dark theme: ' . $e->getMessage() . '</p></div>';
    }
}
?>
