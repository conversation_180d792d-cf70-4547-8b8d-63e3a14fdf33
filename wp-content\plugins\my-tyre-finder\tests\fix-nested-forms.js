/**
 * Fix Nested Forms - Diagnostic and Fix Script for WordPress Plugin
 * 
 * This script identifies and fixes nested form elements that can cause
 * HTML validation issues and break wizard functionality.
 */

console.log('🔍 Starting Nested Forms Diagnostic...');

// 1. Check for nested form elements
function checkNestedForms() {
    console.log('\n📋 Checking for nested form elements...');
    
    const allForms = document.querySelectorAll('form');
    let nestedFormsFound = false;
    
    allForms.forEach((form, index) => {
        const nestedForms = form.querySelectorAll('form');
        if (nestedForms.length > 0) {
            console.error(`❌ Nested forms found in form #${index + 1}:`, form);
            console.error(`   Contains ${nestedForms.length} nested form(s):`, nestedForms);
            nestedFormsFound = true;
        }
    });
    
    if (!nestedFormsFound) {
        console.log('✅ No nested forms detected');
    }
    
    return !nestedFormsFound;
}

// 2. Check wizard structure specifically
function checkWizardStructure() {
    console.log('\n🧙 Checking wizard structure...');
    
    const wizard = document.getElementById('wheel-fit-wizard');
    if (!wizard) {
        console.log('ℹ️ Wizard not found (may be using different layout)');
        return true;
    }
    
    console.log('✅ Wizard container found');
    
    // Check for forms inside wizard
    const formsInWizard = wizard.querySelectorAll('form');
    if (formsInWizard.length > 0) {
        console.error(`❌ Found ${formsInWizard.length} form(s) inside wizard:`, formsInWizard);
        return false;
    }
    
    console.log('✅ No forms found inside wizard (correct structure)');
    
    // Check for proper button structure
    const brandButtons = wizard.querySelectorAll('#wizard-makes-grid button');
    console.log(`✅ Found ${brandButtons.length} brand buttons`);
    
    // Check button types
    let invalidButtons = 0;
    brandButtons.forEach(button => {
        if (button.type !== 'button') {
            console.warn(`⚠️ Button without type="button":`, button);
            invalidButtons++;
        }
    });
    
    if (invalidButtons === 0) {
        console.log('✅ All buttons have correct type="button"');
    }
    
    return formsInWizard.length === 0;
}

// 3. Check for form elements that should be buttons
function checkFormInputsInWizard() {
    console.log('\n🔘 Checking for form inputs that should be buttons...');
    
    const wizard = document.getElementById('wheel-fit-wizard');
    if (!wizard) return true;
    
    const inputs = wizard.querySelectorAll('input[type="radio"], input[type="checkbox"]');
    if (inputs.length > 0) {
        console.warn(`⚠️ Found ${inputs.length} radio/checkbox inputs in wizard:`, inputs);
        console.log('💡 These should probably be buttons for better UX');
        return false;
    }
    
    console.log('✅ No radio/checkbox inputs found in wizard');
    return true;
}

// 4. Fix nested forms by converting to buttons
function fixNestedForms() {
    console.log('\n🔧 Attempting to fix nested form issues...');
    
    const allForms = document.querySelectorAll('form');
    let fixesApplied = 0;
    
    allForms.forEach((form, index) => {
        const nestedForms = form.querySelectorAll('form');
        nestedForms.forEach(nestedForm => {
            console.log(`🔧 Converting nested form to div...`);
            
            // Create a div to replace the form
            const div = document.createElement('div');
            div.className = nestedForm.className;
            div.id = nestedForm.id;
            
            // Move all children to the div
            while (nestedForm.firstChild) {
                div.appendChild(nestedForm.firstChild);
            }
            
            // Replace the form with the div
            nestedForm.parentNode.replaceChild(div, nestedForm);
            fixesApplied++;
        });
    });
    
    if (fixesApplied > 0) {
        console.log(`✅ Fixed ${fixesApplied} nested form(s)`);
    } else {
        console.log('ℹ️ No nested forms to fix');
    }
    
    return fixesApplied;
}

// 5. Ensure wizard buttons are properly configured
function fixWizardButtons() {
    console.log('\n🔘 Fixing wizard button configuration...');
    
    const wizard = document.getElementById('wheel-fit-wizard');
    if (!wizard) return 0;
    
    let fixesApplied = 0;
    
    // Fix brand buttons
    const brandButtons = wizard.querySelectorAll('#wizard-makes-grid button');
    brandButtons.forEach(button => {
        if (!button.type || button.type !== 'button') {
            button.type = 'button';
            fixesApplied++;
        }
    });
    
    // Fix model buttons
    const modelButtons = wizard.querySelectorAll('#wizard-models-list button');
    modelButtons.forEach(button => {
        if (!button.type || button.type !== 'button') {
            button.type = 'button';
            fixesApplied++;
        }
    });
    
    // Fix year buttons
    const yearButtons = wizard.querySelectorAll('#wizard-years-list button');
    yearButtons.forEach(button => {
        if (!button.type || button.type !== 'button') {
            button.type = 'button';
            fixesApplied++;
        }
    });
    
    // Fix modification buttons
    const modButtons = wizard.querySelectorAll('#wizard-modifications-list button');
    modButtons.forEach(button => {
        if (!button.type || button.type !== 'button') {
            button.type = 'button';
            fixesApplied++;
        }
    });
    
    if (fixesApplied > 0) {
        console.log(`✅ Fixed ${fixesApplied} button type(s)`);
    } else {
        console.log('ℹ️ All buttons already properly configured');
    }
    
    return fixesApplied;
}

// 6. Check admin form context
function checkAdminFormContext() {
    console.log('\n🏛️ Checking admin form context...');
    
    const adminForms = document.querySelectorAll('form[method="post"]');
    const widgetPreview = document.getElementById('widget-preview');
    
    if (adminForms.length > 0 && widgetPreview) {
        console.log(`ℹ️ Found ${adminForms.length} admin form(s)`);
        
        // Check if preview is inside any admin form
        let previewInForm = false;
        adminForms.forEach((form, index) => {
            if (form.contains(widgetPreview)) {
                console.error(`❌ Widget preview is inside admin form #${index + 1}`);
                previewInForm = true;
            }
        });
        
        if (!previewInForm) {
            console.log('✅ Widget preview is not inside admin forms');
        }
        
        return !previewInForm;
    }
    
    console.log('ℹ️ No admin context detected');
    return true;
}

// 7. Main diagnostic function
function runDiagnostic() {
    console.log('🚀 Running comprehensive form structure diagnostic...\n');
    
    const results = {
        nestedForms: checkNestedForms(),
        wizardStructure: checkWizardStructure(),
        formInputs: checkFormInputsInWizard(),
        adminContext: checkAdminFormContext()
    };
    
    console.log('\n📊 Diagnostic Results:');
    console.log('- Nested Forms:', results.nestedForms ? '✅ PASS' : '❌ FAIL');
    console.log('- Wizard Structure:', results.wizardStructure ? '✅ PASS' : '❌ FAIL');
    console.log('- Form Inputs:', results.formInputs ? '✅ PASS' : '❌ FAIL');
    console.log('- Admin Context:', results.adminContext ? '✅ PASS' : '❌ FAIL');
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('\n🎉 All checks passed! No nested form issues detected.');
    } else {
        console.log('\n⚠️ Issues detected. Running auto-fix...');
        runAutoFix();
    }
    
    return results;
}

// 8. Auto-fix function
function runAutoFix() {
    console.log('\n🔧 Running auto-fix procedures...');
    
    const fixes = {
        nestedForms: fixNestedForms(),
        wizardButtons: fixWizardButtons()
    };
    
    const totalFixes = Object.values(fixes).reduce((sum, count) => sum + count, 0);
    
    if (totalFixes > 0) {
        console.log(`\n✅ Applied ${totalFixes} fix(es). Re-running diagnostic...`);
        setTimeout(() => runDiagnostic(), 1000);
    } else {
        console.log('\n⚠️ No automatic fixes available. Manual intervention may be required.');
        console.log('\n💡 Recommendations:');
        console.log('1. Ensure wizard layout is selected in admin settings');
        console.log('2. Check that search flow is set to "By Vehicle"');
        console.log('3. Verify no custom code is injecting forms into wizard');
    }
    
    return fixes;
}

// Export functions for manual use
window.WizardFormFixer = {
    runDiagnostic,
    runAutoFix,
    checkNestedForms,
    checkWizardStructure,
    fixNestedForms,
    fixWizardButtons
};

// Auto-run diagnostic when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runDiagnostic);
} else {
    runDiagnostic();
}

console.log('\n💡 Manual usage: WizardFormFixer.runDiagnostic() or WizardFormFixer.runAutoFix()');
