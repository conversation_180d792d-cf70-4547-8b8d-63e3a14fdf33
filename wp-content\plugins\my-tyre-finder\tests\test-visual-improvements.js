/**
 * Тест визуальных улучшений панели тем
 * Запустите в консоли браузера на странице админки с темами
 */

(function() {
    'use strict';

    console.log('✨ === ТЕСТ ВИЗУАЛЬНЫХ УЛУЧШЕНИЙ ===');

    // 1. Проверка вертикального выравнивания
    function checkVerticalAlignment() {
        console.log('\n1️⃣ Проверка вертикального выравнивания...');
        
        const themePanel = document.querySelector('.wsf-theme-panel');
        if (!themePanel) {
            console.warn('⚠️ Панель тем не найдена');
            return false;
        }
        
        const styles = window.getComputedStyle(themePanel);
        const marginTop = styles.marginTop;
        
        console.log('Margin-top панели тем:', marginTop);
        
        if (marginTop && marginTop !== '0px') {
            console.log('✅ Панель имеет верхний отступ для выравнивания');
            return true;
        } else {
            console.log('❌ Панель не имеет верхнего отступа');
            return false;
        }
    }

    // 2. Проверка заголовка панели
    function checkPanelTitle() {
        console.log('\n2️⃣ Проверка заголовка панели...');
        
        const title = document.querySelector('.wsf-theme-panel__title');
        if (!title) {
            console.warn('⚠️ Заголовок панели не найден');
            return false;
        }
        
        const styles = window.getComputedStyle(title);
        const marginBottom = styles.marginBottom;
        
        console.log('Margin-bottom заголовка:', marginBottom);
        
        if (marginBottom && marginBottom !== '0px') {
            console.log('✅ Заголовок имеет нижний отступ');
            return true;
        } else {
            console.log('❌ Заголовок не имеет нижнего отступа');
            return false;
        }
    }

    // 3. Проверка аббревиатур в свотчах
    function checkSwatchAbbreviations() {
        console.log('\n3️⃣ Проверка аббревиатур в свотчах...');
        
        const swatches = document.querySelectorAll('.wsf-theme-card__swatch');
        if (swatches.length === 0) {
            console.warn('⚠️ Свотчи не найдены');
            return false;
        }
        
        let abbreviationsCorrect = 0;
        let tooltipsCorrect = 0;
        
        swatches.forEach((swatch, index) => {
            const label = swatch.getAttribute('data-label');
            const labelFull = swatch.getAttribute('data-label-full');
            const title = swatch.getAttribute('title');
            
            console.log(`Свотч ${index + 1}:`, {
                label: label,
                labelFull: labelFull,
                title: title
            });
            
            // Проверяем аббревиатуры
            if (label && label.length <= 3) {
                abbreviationsCorrect++;
                console.log(`  ✅ Свотч ${index + 1} имеет короткую аббревиатуру: ${label}`);
            }
            
            // Проверяем tooltip'ы
            if (title && title.includes(':')) {
                tooltipsCorrect++;
                console.log(`  ✅ Свотч ${index + 1} имеет информативный tooltip`);
            }
        });
        
        console.log(`📊 Аббревиатуры: ${abbreviationsCorrect}/${swatches.length}`);
        console.log(`📊 Tooltip'ы: ${tooltipsCorrect}/${swatches.length}`);
        
        return abbreviationsCorrect > 0 && tooltipsCorrect > 0;
    }

    // 4. Проверка flex-wrap: nowrap
    function checkFlexWrap() {
        console.log('\n4️⃣ Проверка flex-wrap: nowrap...');
        
        const colorContainers = document.querySelectorAll('.wsf-theme-card__colors');
        if (colorContainers.length === 0) {
            console.warn('⚠️ Контейнеры цветов не найдены');
            return false;
        }
        
        let nowrapCorrect = 0;
        
        colorContainers.forEach((container, index) => {
            const styles = window.getComputedStyle(container);
            const flexWrap = styles.flexWrap;
            
            console.log(`Контейнер ${index + 1} flex-wrap:`, flexWrap);
            
            if (flexWrap === 'nowrap') {
                nowrapCorrect++;
                console.log(`  ✅ Контейнер ${index + 1} имеет flex-wrap: nowrap`);
            } else {
                console.log(`  ❌ Контейнер ${index + 1} НЕ имеет flex-wrap: nowrap`);
            }
        });
        
        console.log(`📊 Nowrap: ${nowrapCorrect}/${colorContainers.length}`);
        return nowrapCorrect === colorContainers.length;
    }

    // 5. Проверка отступов между карточками
    function checkCardSpacing() {
        console.log('\n5️⃣ Проверка отступов между карточками...');
        
        const cards = document.querySelectorAll('.wsf-theme-card');
        if (cards.length === 0) {
            console.warn('⚠️ Карточки тем не найдены');
            return false;
        }
        
        let spacingCorrect = 0;
        
        cards.forEach((card, index) => {
            const styles = window.getComputedStyle(card);
            const marginBottom = styles.marginBottom;
            const marginBottomValue = parseInt(marginBottom);
            
            console.log(`Карточка ${index + 1} margin-bottom:`, marginBottom);
            
            if (marginBottomValue >= 20) {
                spacingCorrect++;
                console.log(`  ✅ Карточка ${index + 1} имеет достаточный отступ`);
            } else {
                console.log(`  ❌ Карточка ${index + 1} имеет недостаточный отступ`);
            }
        });
        
        console.log(`📊 Отступы: ${spacingCorrect}/${cards.length}`);
        return spacingCorrect > 0;
    }

    // 6. Проверка псевдоэлементов рамок
    function checkInsetShadows() {
        console.log('\n6️⃣ Проверка inset box-shadow...');
        
        const swatches = document.querySelectorAll('.wsf-theme-card__swatch');
        if (swatches.length === 0) {
            console.warn('⚠️ Свотчи не найдены');
            return false;
        }
        
        let shadowsCorrect = 0;
        
        swatches.forEach((swatch, index) => {
            const beforeStyles = window.getComputedStyle(swatch, '::before');
            const boxShadow = beforeStyles.boxShadow;
            
            console.log(`Свотч ${index + 1} ::before box-shadow:`, boxShadow);
            
            if (boxShadow && boxShadow !== 'none' && boxShadow.includes('inset')) {
                shadowsCorrect++;
                console.log(`  ✅ Свотч ${index + 1} имеет inset box-shadow`);
            } else {
                console.log(`  ❌ Свотч ${index + 1} НЕ имеет inset box-shadow`);
            }
        });
        
        console.log(`📊 Inset shadows: ${shadowsCorrect}/${swatches.length}`);
        return shadowsCorrect > 0;
    }

    // Создание визуального отчета
    function createVisualReport(results) {
        console.log('\n📋 === СОЗДАНИЕ ВИЗУАЛЬНОГО ОТЧЕТА ===');
        
        const reportContainer = document.createElement('div');
        reportContainer.id = 'visual-improvements-report';
        reportContainer.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        const passedTests = Object.values(results).filter(Boolean).length;
        const totalTests = Object.keys(results).length;
        const percentage = Math.round((passedTests / totalTests) * 100);
        
        reportContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h3 style="margin: 0; font-size: 16px; color: #333;">
                    ✨ Отчет об улучшениях
                </h3>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="border: none; background: #dc3545; color: white; 
                               border-radius: 4px; padding: 4px 8px; cursor: pointer;">✕</button>
            </div>
            
            <div style="margin-bottom: 16px;">
                <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: ${percentage >= 80 ? '#28a745' : percentage >= 60 ? '#ffc107' : '#dc3545'};">
                        ${percentage}%
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        ${passedTests} из ${totalTests} тестов пройдено
                    </div>
                </div>
            </div>
            
            <div style="font-size: 13px; line-height: 1.4;">
                ${Object.entries(results).map(([test, passed]) => `
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span style="margin-right: 8px;">${passed ? '✅' : '❌'}</span>
                        <span>${test}</span>
                    </div>
                `).join('')}
            </div>
            
            <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
                💡 Все улучшения направлены на профессиональный вид панели тем
            </div>
        `;
        
        document.body.appendChild(reportContainer);
        console.log('✅ Визуальный отчет создан в левом верхнем углу');
    }

    // Основная функция тестирования
    function runAllTests() {
        console.log('🚀 Запуск тестирования визуальных улучшений...\n');
        
        const results = {
            'Вертикальное выравнивание': checkVerticalAlignment(),
            'Отступ заголовка': checkPanelTitle(),
            'Аббревиатуры свотчей': checkSwatchAbbreviations(),
            'Flex-wrap nowrap': checkFlexWrap(),
            'Отступы карточек': checkCardSpacing(),
            'Inset box-shadow': checkInsetShadows()
        };
        
        console.log('\n📊 === ИТОГОВЫЕ РЕЗУЛЬТАТЫ ===');
        Object.entries(results).forEach(([test, passed]) => {
            console.log(`${passed ? '✅' : '❌'} ${test}`);
        });
        
        const passedTests = Object.values(results).filter(Boolean).length;
        const totalTests = Object.keys(results).length;
        
        console.log(`\n🎯 Общий результат: ${passedTests}/${totalTests} тестов пройдено`);
        
        if (passedTests === totalTests) {
            console.log('🎉 Все визуальные улучшения применены успешно!');
        } else if (passedTests >= totalTests * 0.8) {
            console.log('👍 Большинство улучшений применено, есть небольшие недочеты');
        } else {
            console.log('⚠️ Требуется дополнительная работа над улучшениями');
        }
        
        createVisualReport(results);
        
        console.log('\n💡 Советы:');
        console.log('1. Проверьте визуальный отчет в левом верхнем углу');
        console.log('2. Сравните с исходным состоянием панели');
        console.log('3. Обратите внимание на детали: отступы, выравнивание, читаемость');
    }

    // Запускаем тестирование
    runAllTests();

})();
