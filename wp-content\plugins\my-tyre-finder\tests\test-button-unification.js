/**
 * Quick test to verify button size unification in Inline (1x4) layout
 */

console.log('🧪 Testing button size unification...');

function testButtonUnification() {
    // Find search button
    const searchButton = document.querySelector('.ws-submit .btn-primary');
    if (!searchButton) {
        console.log('❌ Search button not found');
        return false;
    }

    // Find dropdown for comparison
    const dropdown = document.querySelector('select');
    if (!dropdown) {
        console.log('❌ Dropdown not found');
        return false;
    }

    // Get heights
    const buttonHeight = parseFloat(window.getComputedStyle(searchButton).height);
    const dropdownHeight = parseFloat(window.getComputedStyle(dropdown).height);

    console.log(`Button height: ${buttonHeight}px`);
    console.log(`Dropdown height: ${dropdownHeight}px`);

    // Check if heights match (within 2px tolerance)
    const heightsMatch = Math.abs(buttonHeight - dropdownHeight) <= 2;
    console.log(`${heightsMatch ? '✅' : '❌'} Heights match: ${heightsMatch}`);

    return heightsMatch;
}

// Run test
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testButtonUnification);
} else {
    testButtonUnification();
}

window.testButtonUnification = testButtonUnification;
