/**
 * Theme Presets Design Test
 * Проверяет соответствие блока Theme Presets общему стилю WordPress admin
 */

(function() {
    'use strict';

    console.log('🎨 === THEME PRESETS DESIGN TEST ===');

    const results = {
        colors: false,
        typography: false,
        spacing: false,
        states: false,
        responsive: false,
        accessibility: false,
        errors: []
    };

    // Test 1: Colors and Background
    function testColors() {
        console.log('\n1️⃣ Testing Colors and Background...');
        
        const themePanel = document.querySelector('.wsf-theme-panel');
        if (!themePanel) {
            console.error('❌ Theme panel not found');
            results.errors.push('Theme panel element not found');
            return false;
        }

        const computedStyle = getComputedStyle(themePanel);
        const checks = [
            {
                name: 'Background Color',
                expected: 'rgb(255, 255, 255)', // white
                actual: computedStyle.backgroundColor
            },
            {
                name: 'Border Color',
                expected: /rgb\(229, 231, 235\)|rgb\(209, 213, 219\)/, // gray-200 or gray-300
                actual: computedStyle.borderColor
            },
            {
                name: 'Border Radius',
                expected: /8px|0\.5rem/, // rounded-lg
                actual: computedStyle.borderRadius
            }
        ];

        let passedChecks = 0;
        checks.forEach(check => {
            const matches = check.expected instanceof RegExp ? 
                check.expected.test(check.actual) : 
                check.actual === check.expected;
                
            if (matches) {
                console.log(`✅ ${check.name}: ${check.actual}`);
                passedChecks++;
            } else {
                console.warn(`⚠️ ${check.name}: expected ${check.expected}, got ${check.actual}`);
                results.errors.push(`${check.name} mismatch`);
            }
        });

        const success = passedChecks >= checks.length * 0.8;
        results.colors = success;
        return success;
    }

    // Test 2: Typography
    function testTypography() {
        console.log('\n2️⃣ Testing Typography...');
        
        const title = document.querySelector('.wsf-theme-panel__title');
        if (!title) {
            console.error('❌ Theme panel title not found');
            results.errors.push('Theme panel title not found');
            return false;
        }

        const computedStyle = getComputedStyle(title);
        const checks = [
            {
                name: 'Font Size',
                expected: /18px|1\.125rem/, // text-lg
                actual: computedStyle.fontSize
            },
            {
                name: 'Font Weight',
                expected: /500|medium/, // font-medium
                actual: computedStyle.fontWeight
            },
            {
                name: 'Text Color',
                expected: /rgb\(17, 24, 39\)|rgb\(31, 41, 55\)/, // text-gray-900
                actual: computedStyle.color
            }
        ];

        let passedChecks = 0;
        checks.forEach(check => {
            const matches = check.expected instanceof RegExp ? 
                check.expected.test(check.actual) : 
                check.actual === check.expected;
                
            if (matches) {
                console.log(`✅ ${check.name}: ${check.actual}`);
                passedChecks++;
            } else {
                console.warn(`⚠️ ${check.name}: expected ${check.expected}, got ${check.actual}`);
            }
        });

        const success = passedChecks >= checks.length * 0.6; // Lower threshold for typography
        results.typography = success;
        return success;
    }

    // Test 3: Spacing
    function testSpacing() {
        console.log('\n3️⃣ Testing Spacing...');
        
        const themePanel = document.querySelector('.wsf-theme-panel');
        const content = document.querySelector('.wsf-theme-panel__content');
        
        if (!themePanel || !content) {
            console.error('❌ Theme panel elements not found');
            results.errors.push('Theme panel elements not found');
            return false;
        }

        const panelStyle = getComputedStyle(themePanel);
        const contentStyle = getComputedStyle(content);
        
        const checks = [
            {
                name: 'Panel Padding',
                expected: /24px|1\.5rem/, // p-6
                actual: panelStyle.padding
            },
            {
                name: 'Content Gap',
                expected: /16px|1rem/, // gap-4
                actual: contentStyle.gap
            }
        ];

        let passedChecks = 0;
        checks.forEach(check => {
            const matches = check.expected instanceof RegExp ? 
                check.expected.test(check.actual) : 
                check.actual === check.expected;
                
            if (matches) {
                console.log(`✅ ${check.name}: ${check.actual}`);
                passedChecks++;
            } else {
                console.warn(`⚠️ ${check.name}: expected ${check.expected}, got ${check.actual}`);
            }
        });

        const success = passedChecks >= checks.length * 0.5; // Lower threshold
        results.spacing = success;
        return success;
    }

    // Test 4: Interactive States
    function testStates() {
        console.log('\n4️⃣ Testing Interactive States...');
        
        const themeCards = document.querySelectorAll('.wsf-theme-card');
        if (themeCards.length === 0) {
            console.error('❌ No theme cards found');
            results.errors.push('No theme cards found');
            return false;
        }

        let statesWorking = 0;
        const totalStates = 3; // hover, focus, active

        // Test hover state
        const firstCard = themeCards[0];
        const originalBg = getComputedStyle(firstCard).backgroundColor;
        
        // Simulate hover
        firstCard.dispatchEvent(new MouseEvent('mouseenter'));
        setTimeout(() => {
            const hoverBg = getComputedStyle(firstCard).backgroundColor;
            if (hoverBg !== originalBg) {
                console.log('✅ Hover state working');
                statesWorking++;
            } else {
                console.warn('⚠️ Hover state not working');
            }
            
            firstCard.dispatchEvent(new MouseEvent('mouseleave'));
        }, 100);

        // Test focus state
        if (firstCard.hasAttribute('tabindex')) {
            console.log('✅ Focus state supported (tabindex present)');
            statesWorking++;
        } else {
            console.warn('⚠️ Focus state not supported');
        }

        // Test active state
        const activeCard = document.querySelector('.wsf-theme-card--active');
        if (activeCard) {
            const activeStyle = getComputedStyle(activeCard);
            if (activeStyle.borderColor.includes('59, 130, 246') || // blue-500
                activeStyle.borderColor.includes('37, 99, 235')) {   // blue-600
                console.log('✅ Active state styling working');
                statesWorking++;
            } else {
                console.warn('⚠️ Active state styling not working');
            }
        }

        const success = statesWorking >= totalStates * 0.6;
        results.states = success;
        return success;
    }

    // Test 5: Responsive Design
    function testResponsive() {
        console.log('\n5️⃣ Testing Responsive Design...');
        
        const grid = document.querySelector('.wsf-theme-cards-grid');
        if (!grid) {
            console.error('❌ Theme cards grid not found');
            results.errors.push('Theme cards grid not found');
            return false;
        }

        const originalWidth = window.innerWidth;
        const isMobile = originalWidth <= 640;
        
        console.log(`📱 Current viewport: ${originalWidth}px (${isMobile ? 'Mobile' : 'Desktop'})`);
        
        const computedStyle = getComputedStyle(grid);
        
        if (isMobile) {
            // On mobile, should be two columns
            const isResponsive = computedStyle.gridTemplateColumns.includes('1fr 1fr') ||
                                computedStyle.gridTemplateColumns.includes('repeat(2');
            if (isResponsive) {
                console.log('✅ Mobile layout: two column grid');
            } else {
                console.warn('⚠️ Mobile layout: expected two columns, got', computedStyle.gridTemplateColumns);
            }
            results.responsive = isResponsive;
            return isResponsive;
        } else {
            // On desktop, should be single column
            const isResponsive = computedStyle.gridTemplateColumns === '1fr' ||
                                computedStyle.gridTemplateColumns === 'none';
            if (isResponsive) {
                console.log('✅ Desktop layout: single column grid');
            } else {
                console.warn('⚠️ Desktop layout: expected single column, got', computedStyle.gridTemplateColumns);
            }
            results.responsive = isResponsive;
            return isResponsive;
        }
    }

    // Test 6: Accessibility
    function testAccessibility() {
        console.log('\n6️⃣ Testing Accessibility...');
        
        const themeCards = document.querySelectorAll('.wsf-theme-card');
        let accessibilityScore = 0;
        const maxScore = 4;

        // Check for proper ARIA labels
        const cardsWithAria = Array.from(themeCards).filter(card => 
            card.hasAttribute('aria-label') || card.hasAttribute('role')
        );
        if (cardsWithAria.length > 0) {
            console.log('✅ ARIA labels found on theme cards');
            accessibilityScore++;
        }

        // Check for keyboard navigation
        const cardsWithTabindex = Array.from(themeCards).filter(card => 
            card.hasAttribute('tabindex')
        );
        if (cardsWithTabindex.length > 0) {
            console.log('✅ Keyboard navigation supported');
            accessibilityScore++;
        }

        // Check for focus indicators
        const firstCard = themeCards[0];
        if (firstCard) {
            firstCard.focus();
            const focusedStyle = getComputedStyle(firstCard);
            if (focusedStyle.outline !== 'none' || focusedStyle.boxShadow.includes('ring')) {
                console.log('✅ Focus indicators present');
                accessibilityScore++;
            } else {
                console.warn('⚠️ Focus indicators missing');
            }
        }

        // Check for semantic HTML
        const headings = document.querySelectorAll('.wsf-theme-panel h1, .wsf-theme-panel h2, .wsf-theme-panel h3');
        if (headings.length > 0) {
            console.log('✅ Semantic headings found');
            accessibilityScore++;
        }

        const success = accessibilityScore >= maxScore * 0.5;
        results.accessibility = success;
        console.log(`📊 Accessibility score: ${accessibilityScore}/${maxScore}`);
        return success;
    }

    // Generate final report
    function generateReport() {
        console.log('\n📋 === DESIGN TEST REPORT ===');
        
        const tests = [
            { name: 'Colors & Background', result: results.colors },
            { name: 'Typography', result: results.typography },
            { name: 'Spacing', result: results.spacing },
            { name: 'Interactive States', result: results.states },
            { name: 'Responsive Design', result: results.responsive },
            { name: 'Accessibility', result: results.accessibility }
        ];

        const passedTests = tests.filter(test => test.result).length;
        const totalTests = tests.length;
        const successRate = (passedTests / totalTests * 100).toFixed(1);

        console.log(`\n🎯 Overall Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
        
        tests.forEach(test => {
            console.log(`   ${test.result ? '✅' : '❌'} ${test.name}`);
        });

        if (results.errors.length > 0) {
            console.log('\n⚠️ Issues Found:');
            results.errors.forEach((error, idx) => {
                console.log(`   ${idx + 1}. ${error}`);
            });
        }

        console.log('\n💡 Design Status:');
        if (successRate >= 80) {
            console.log('🎉 Excellent! Theme Presets design matches WordPress admin style.');
        } else if (successRate >= 60) {
            console.log('👍 Good! Minor design improvements needed.');
        } else {
            console.log('⚠️ Needs work. Several design issues found.');
        }

        return { successRate, passedTests, totalTests };
    }

    // Run all tests
    function runDesignTest() {
        console.log('Starting Theme Presets design test...\n');

        testColors();
        testTypography();
        testSpacing();
        testStates();
        testResponsive();
        testAccessibility();

        return generateReport();
    }

    // Export to window for manual access
    window.themePresetsDesignTest = {
        run: runDesignTest,
        individual: {
            testColors,
            testTypography,
            testSpacing,
            testStates,
            testResponsive,
            testAccessibility
        },
        results: results
    };

    // Auto-run test
    runDesignTest();

})();
