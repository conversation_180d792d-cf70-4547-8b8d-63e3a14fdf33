// Test script to verify that By Vehicle mode is forced and By Size mode is disabled
console.log('=== Force By Vehicle Mode Test ===');

// Test results tracking
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function logResult(test, status, message) {
    const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${test}: ${message}`);
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Test 1: Check if By Vehicle tab is visible and active
console.log('\n1. By Vehicle Tab Visibility Test:');
const byVehicleTab = document.querySelector('[data-tab="by-car"]');
const byVehiclePanel = document.getElementById('tab-by-car');

if (byVehicleTab) {
    logResult('By Vehicle tab exists', 'pass', 'Tab button found');
    
    const isSelected = byVehicleTab.getAttribute('aria-selected') === 'true';
    logResult('By Vehicle tab is active', isSelected ? 'pass' : 'fail', 
        `aria-selected="${byVehicleTab.getAttribute('aria-selected')}"`);
} else {
    logResult('By Vehicle tab exists', 'fail', 'Tab button not found');
}

if (byVehiclePanel) {
    logResult('By Vehicle panel exists', 'pass', 'Panel found');
    
    const isVisible = !byVehiclePanel.classList.contains('hidden');
    const ariaHidden = byVehiclePanel.getAttribute('aria-hidden') === 'false';
    
    logResult('By Vehicle panel is visible', isVisible ? 'pass' : 'fail', 
        `hidden class: ${byVehiclePanel.classList.contains('hidden')}`);
    logResult('By Vehicle panel aria-hidden', ariaHidden ? 'pass' : 'fail', 
        `aria-hidden="${byVehiclePanel.getAttribute('aria-hidden')}"`);
} else {
    logResult('By Vehicle panel exists', 'fail', 'Panel not found');
}

// Test 2: Check if By Size tab is hidden
console.log('\n2. By Size Tab Hidden Test:');
const bySizeTab = document.querySelector('[data-tab="by-size"]');
const bySizePanel = document.getElementById('tab-by-size');

if (bySizeTab) {
    logResult('By Size tab hidden', 'fail', 'Tab button should not exist but was found');
} else {
    logResult('By Size tab hidden', 'pass', 'Tab button correctly not found');
}

if (bySizePanel) {
    logResult('By Size panel hidden', 'fail', 'Panel should not exist but was found');
} else {
    logResult('By Size panel hidden', 'pass', 'Panel correctly not found');
}

// Test 3: Check tab container behavior
console.log('\n3. Tab Container Test:');
const tabContainer = document.querySelector('[role="tablist"]');

if (tabContainer) {
    const tabButtons = tabContainer.querySelectorAll('.select-tab');
    const tabCount = tabButtons.length;
    
    logResult('Tab container exists', 'pass', `Found ${tabCount} tab(s)`);
    
    if (tabCount === 1) {
        logResult('Single tab mode', 'pass', 'Only one tab exists (By Vehicle)');
    } else if (tabCount === 0) {
        logResult('No tabs mode', 'pass', 'No tabs displayed (single mode)');
    } else {
        logResult('Multiple tabs', 'fail', `${tabCount} tabs found, expected 1 or 0`);
    }
} else {
    logResult('Tab container behavior', 'pass', 'No tab container (single mode)');
}

// Test 4: Check widget configuration
console.log('\n4. Widget Configuration Test:');
if (window.WheelFitData) {
    const showByVehicle = window.WheelFitData.showByVehicle;
    const showBySize = window.WheelFitData.showBySize;
    
    logResult('showByVehicle config', showByVehicle ? 'pass' : 'fail', 
        `showByVehicle: ${showByVehicle}`);
    logResult('showBySize config', !showBySize ? 'pass' : 'fail', 
        `showBySize: ${showBySize}`);
} else {
    logResult('Widget configuration', 'warning', 'WheelFitData not available');
}

// Test 5: Check form functionality
console.log('\n5. Form Functionality Test:');
const makeSelect = document.getElementById('wf-make');
const modelSelect = document.getElementById('wf-model');
const yearSelect = document.getElementById('wf-year');

if (makeSelect && modelSelect && yearSelect) {
    logResult('Vehicle form fields exist', 'pass', 'Make, Model, Year selectors found');
    
    // Check if make selector is enabled (should be in By Vehicle mode)
    const makeEnabled = !makeSelect.disabled;
    logResult('Make selector enabled', makeEnabled ? 'pass' : 'fail', 
        `Make selector disabled: ${makeSelect.disabled}`);
} else {
    logResult('Vehicle form fields', 'warning', 'Some vehicle form fields not found');
}

// Test 6: Check tire size form is not visible
console.log('\n6. Tire Size Form Hidden Test:');
const tireWidthSelect = document.getElementById('tire-width');
const tireProfileSelect = document.getElementById('tire-profile');
const tireDiameterSelect = document.getElementById('tire-diameter');

const tireSizeFieldsExist = tireWidthSelect || tireProfileSelect || tireDiameterSelect;

if (!tireSizeFieldsExist) {
    logResult('Tire size fields hidden', 'pass', 'No tire size form fields found');
} else {
    const visibleTireSizeFields = [];
    if (tireWidthSelect && !tireWidthSelect.closest('.hidden')) visibleTireSizeFields.push('width');
    if (tireProfileSelect && !tireProfileSelect.closest('.hidden')) visibleTireSizeFields.push('profile');
    if (tireDiameterSelect && !tireDiameterSelect.closest('.hidden')) visibleTireSizeFields.push('diameter');
    
    if (visibleTireSizeFields.length === 0) {
        logResult('Tire size fields hidden', 'pass', 'Tire size fields exist but are hidden');
    } else {
        logResult('Tire size fields hidden', 'fail', 
            `Visible tire size fields: ${visibleTireSizeFields.join(', ')}`);
    }
}

// Test 7: Check JavaScript widget initialization
console.log('\n7. Widget Initialization Test:');
if (window.wheelFitWidget) {
    logResult('Widget initialized', 'pass', 'wheelFitWidget instance found');
    
    // Check if widget is in correct mode
    if (window.wheelFitWidget.mode) {
        const mode = window.wheelFitWidget.mode;
        const isVehicleMode = mode === 'byVehicle' || mode === 'byGeneration' || mode === 'byYear';
        
        logResult('Widget in vehicle mode', isVehicleMode ? 'pass' : 'fail', 
            `Widget mode: ${mode}`);
    } else {
        logResult('Widget mode detection', 'warning', 'Widget mode not detectable');
    }
} else {
    logResult('Widget initialization', 'warning', 'wheelFitWidget not found');
}

// Test 8: Check tab switching functionality is disabled
console.log('\n8. Tab Switching Disabled Test:');
const allTabButtons = document.querySelectorAll('.select-tab');

if (allTabButtons.length <= 1) {
    logResult('Tab switching disabled', 'pass', 'No multiple tabs to switch between');
} else {
    // Try to click on different tabs to see if switching is possible
    let switchingPossible = false;
    allTabButtons.forEach((tab, index) => {
        if (index > 0) { // Skip first tab
            tab.click();
            // Check if any panel visibility changed
            const panels = document.querySelectorAll('.tab-panel');
            panels.forEach(panel => {
                if (!panel.classList.contains('hidden') && panel.id !== 'tab-by-car') {
                    switchingPossible = true;
                }
            });
        }
    });
    
    logResult('Tab switching disabled', !switchingPossible ? 'pass' : 'fail', 
        switchingPossible ? 'Tab switching is still possible' : 'Tab switching properly disabled');
}

// Test 9: Check admin settings are forced
console.log('\n9. Admin Settings Override Test:');
// This test would need to be run in admin context, but we can check if the forcing is working
if (typeof window.WheelFitData !== 'undefined') {
    const data = window.WheelFitData;
    
    // These should always be forced regardless of database settings
    const vehicleForced = data.showByVehicle === true;
    const sizeForced = data.showBySize === false;
    
    logResult('Vehicle mode forced', vehicleForced ? 'pass' : 'fail', 
        `showByVehicle forced to: ${data.showByVehicle}`);
    logResult('Size mode forced', sizeForced ? 'pass' : 'fail', 
        `showBySize forced to: ${data.showBySize}`);
} else {
    logResult('Admin settings override', 'warning', 'Cannot test - WheelFitData not available');
}

// Test 10: Check template rendering
console.log('\n10. Template Rendering Test:');
const widgetContainer = document.querySelector('.wheel-fit-widget');

if (widgetContainer) {
    logResult('Widget container exists', 'pass', 'Main widget container found');
    
    // Check for vehicle-specific elements
    const vehicleElements = widgetContainer.querySelectorAll('#wf-make, #wf-model, #wf-year');
    const hasVehicleElements = vehicleElements.length > 0;
    
    logResult('Vehicle elements rendered', hasVehicleElements ? 'pass' : 'fail', 
        `Found ${vehicleElements.length} vehicle form elements`);
    
    // Check that size elements are not rendered or are hidden
    const sizeElements = widgetContainer.querySelectorAll('#tire-width, #tire-profile, #tire-diameter');
    const visibleSizeElements = Array.from(sizeElements).filter(el => 
        !el.closest('.hidden') && !el.closest('[style*="display: none"]')
    );
    
    logResult('Size elements hidden', visibleSizeElements.length === 0 ? 'pass' : 'fail', 
        `${visibleSizeElements.length} visible size elements found`);
} else {
    logResult('Widget container', 'warning', 'Widget container not found');
}

// Final summary
setTimeout(() => {
    console.log('\n=== Force By Vehicle Mode Test Summary ===');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⚠️ Warnings: ${testResults.warnings}`);
    
    const totalTests = testResults.passed + testResults.failed + testResults.warnings;
    const successRate = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
    
    console.log(`\n📊 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 All critical tests passed! By Vehicle mode is properly forced.');
        console.log('\n📋 Verification Results:');
        console.log('✅ By Vehicle tab is active and visible');
        console.log('✅ By Size tab is completely hidden');
        console.log('✅ Tab switching is disabled');
        console.log('✅ Vehicle form fields are functional');
        console.log('✅ Size form fields are hidden');
        console.log('✅ Configuration is properly forced');
    } else {
        console.log('\n⚠️ Some tests failed. Please review the failures above.');
    }
    
    console.log('\n🔧 Implementation Summary:');
    console.log('- Admin tab selection UI has been removed');
    console.log('- Database settings are overridden by filters');
    console.log('- Frontend always renders in By Vehicle mode');
    console.log('- By Size functionality is completely disabled');
    console.log('- Preview system forces correct settings');
    
}, 100);

console.log('\n=== Force By Vehicle mode test initiated ===');
