// Test script for invalid API key validation
console.log('=== Invalid API Key Validation Test ===');

// Test cases for invalid API keys
const INVALID_API_KEYS = [
    { key: '123456789', description: 'Simple numbers' },
    { key: 'abcdefgh', description: 'Simple letters' },
    { key: 'test-key-123', description: 'Text with dashes' },
    { key: '12345678901234567890123456789012', description: '32 digits (wrong format)' },
    { key: 'abcdefghijklmnopqrstuvwxyz123456', description: '32 chars with letters' },
    { key: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ123456', description: '32 uppercase chars' },
    { key: '7174c9ea41a8b5edf19b31c40f9eb88', description: '31 hex chars (too short)' },
    { key: '7174c9ea41a8b5edf19b31c40f9eb8899', description: '34 hex chars (too long)' },
    { key: '', description: 'Empty string' },
    { key: '   ', description: 'Whitespace only' },
    { key: 'gggggggggggggggggggggggggggggggg', description: '32 invalid hex chars' },
    { key: '7174c9ea-41a8-b5ed-f19b-31c40f9eb889', description: 'Valid hex with dashes' }
];

// Test results tracking
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function logResult(test, status, message) {
    const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${test}: ${message}`);
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Check if we're on the API settings page
function checkApiSettingsPage() {
    const apiKeyInput = document.getElementById('api_key');
    const testButton = document.getElementById('test-api-key');
    
    if (!apiKeyInput || !testButton) {
        console.log('❌ This test must be run on the Wheel-Size → API Settings page');
        console.log('📋 Please navigate to the API Settings page and run this test again');
        return false;
    }
    
    return true;
}

// Test individual API key
async function testApiKey(testCase) {
    return new Promise((resolve) => {
        const apiKeyInput = document.getElementById('api_key');
        const testButton = document.getElementById('test-api-key');
        const statusSpan = document.getElementById('api-test-status');
        const resultDiv = document.getElementById('api-test-result');
        
        // Set the test key
        apiKeyInput.value = testCase.key;
        
        // Clear previous results
        if (statusSpan) statusSpan.textContent = '';
        if (resultDiv) resultDiv.innerHTML = '';
        
        console.log(`\n🔑 Testing: ${testCase.description} ("${testCase.key}")`);
        
        // Monitor for status changes
        let resolved = false;
        const originalStatus = statusSpan ? statusSpan.textContent : '';
        
        const statusMonitor = setInterval(() => {
            if (resolved) {
                clearInterval(statusMonitor);
                return;
            }
            
            const currentStatus = statusSpan ? statusSpan.textContent : '';
            const currentResult = resultDiv ? resultDiv.textContent : '';
            
            // Check for completion
            if (currentStatus.includes('✅') || currentStatus.includes('❌')) {
                resolved = true;
                clearInterval(statusMonitor);
                
                const isValid = currentStatus.includes('✅');
                const isInvalid = currentStatus.includes('❌');
                
                if (isInvalid) {
                    logResult(`Invalid key rejected: ${testCase.description}`, 'pass', 
                        `Correctly rejected: ${currentResult.substring(0, 100)}`);
                } else if (isValid) {
                    logResult(`Invalid key rejected: ${testCase.description}`, 'fail', 
                        `INCORRECTLY ACCEPTED: ${currentResult.substring(0, 100)}`);
                } else {
                    logResult(`Invalid key rejected: ${testCase.description}`, 'warning', 
                        `Unclear result: ${currentResult.substring(0, 100)}`);
                }
                
                resolve();
            }
        }, 200);
        
        // Timeout after 10 seconds
        setTimeout(() => {
            if (!resolved) {
                resolved = true;
                clearInterval(statusMonitor);
                logResult(`Invalid key rejected: ${testCase.description}`, 'warning', 
                    'Test timed out - no clear result');
                resolve();
            }
        }, 10000);
        
        // Click the test button
        testButton.click();
    });
}

// Run all tests
async function runInvalidKeyTests() {
    if (!checkApiSettingsPage()) {
        return;
    }
    
    console.log('\n🚀 Starting Invalid API Key Tests...');
    console.log(`📊 Testing ${INVALID_API_KEYS.length} invalid key formats`);
    
    // Test each invalid key
    for (let i = 0; i < INVALID_API_KEYS.length; i++) {
        const testCase = INVALID_API_KEYS[i];
        console.log(`\n📋 Test ${i + 1}/${INVALID_API_KEYS.length}:`);
        
        await testApiKey(testCase);
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Test summary
    console.log('\n=== Invalid API Key Test Summary ===');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⚠️ Warnings: ${testResults.warnings}`);
    
    const totalTests = testResults.passed + testResults.failed + testResults.warnings;
    const successRate = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
    
    console.log(`\n📊 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 All tests passed! Invalid API keys are properly rejected.');
    } else {
        console.log('\n⚠️ Some invalid keys were incorrectly accepted. This is a security issue!');
        console.log('\n🔧 Issues found:');
        console.log('- API validation is not strict enough');
        console.log('- Fallback API key may be used instead of user input');
        console.log('- Format validation may be bypassed');
    }
    
    console.log('\n📋 Expected Behavior:');
    console.log('✅ All invalid keys should be rejected with clear error messages');
    console.log('✅ Only valid 32-character hexadecimal API keys should pass');
    console.log('✅ No fallback or development keys should be used during validation');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. If tests failed, check ApiValidator::validate_api_key() method');
    console.log('2. Ensure WheelSizeApi does not use fallback keys during validation');
    console.log('3. Add proper format validation for API keys');
    console.log('4. Test with a real valid API key to ensure it still works');
}

// Manual test function for specific key
window.testSpecificApiKey = function(apiKey, description = 'Manual test') {
    if (!checkApiSettingsPage()) {
        return;
    }
    
    console.log('\n🔧 Manual API Key Test:');
    testApiKey({ key: apiKey, description: description });
};

// Auto-run the tests
console.log('\n💡 Starting automated invalid API key tests...');
console.log('📋 This will test various invalid API key formats');
console.log('⏰ Each test may take up to 10 seconds');

setTimeout(() => {
    runInvalidKeyTests();
}, 1000);

// Provide manual test function
console.log('\n💡 Manual testing available:');
console.log('   testSpecificApiKey("your-key-here", "description")');

// Test deactivation/reactivation behavior
console.log('\n🔄 Deactivation/Reactivation Test Instructions:');
console.log('1. After running these tests, note the current plugin status');
console.log('2. Go to WordPress Admin → Plugins');
console.log('3. Deactivate the "My Tyre Finder" plugin');
console.log('4. Reactivate the plugin');
console.log('5. Return to Wheel-Size → API Settings');
console.log('6. Check if plugin status reset to "not configured"');
console.log('7. Verify that you need to re-validate your API key');
