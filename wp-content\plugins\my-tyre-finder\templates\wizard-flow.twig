{# Wizard Flow Template - используется FormRenderer для wizard layout #}
<div id="wheel-fit-wizard" class="wheel-fit-widget max-w-4xl mx-auto p-4 md:p-6 bg-wsf-bg wsf-root-font rounded-lg shadow-lg{% if garage_enabled %} garage-enabled{% endif %}">

    {# Widget Title - в самом верху формы #}
    <div class="wsf-widget__header mb-6">
        <h1 class="wsf-widget__title text-center text-2xl md:text-3xl font-extrabold tracking-tight wsf-text-primary m-0" data-i18n="widget_title">{{ widget_title|e }}</h1>
    </div>

    <!-- Header / Progress Bar - стадии под заголовком -->
    <div id="wizard-header" class="mb-6">
        <div class="flex items-center justify-between text-sm font-semibold text-gray-400">
            <div id="wizard-step-name-1" class="wizard-step-name wsf-text-primary" data-i18n="label_make">Make</div>
            <div id="wizard-step-name-2" class="wizard-step-name" data-i18n="label_model">Model</div>
            <div id="wizard-step-name-3" class="wizard-step-name" data-i18n="label_year">Year</div>
            <div id="wizard-step-name-4" class="wizard-step-name" data-i18n="label_mods">Modification</div>
            <div id="wizard-step-name-5" class="wizard-step-name" data-i18n="label_wheel_options">Wheel Options</div>
        </div>
        <div class="mt-2 bg-wsf-surface rounded-full h-1.5">
            <div id="wizard-progress-bar" class="bg-[color:var(--wsf-primary)] h-1.5 rounded-full transition-all duration-500" style="width: 12.5%"></div>
        </div>

        <!-- Selection Summary -->
        <div id="wizard-selection-summary" class="mt-4 text-sm text-gray-600 hidden">
            <span id="wizard-selected-make"></span>
            <span id="wizard-selected-model"></span>
            <span id="wizard-selected-year"></span>
        </div>
    </div>

    <!-- Step Content -->
    <div class="wsf-form-wrapper">
        <div class="relative">
        <!-- Step 1: Makes -->
        <div id="wizard-step-1" class="wizard-step">
            <h2 class="text-2xl font-bold wsf-text-primary mb-4" data-i18n="select_make_placeholder">Select Manufacturer</h2>
            <div id="wizard-makes-grid" class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-10 gap-4">
                <!-- Loader -->
                <div id="wizard-makes-loader" class="col-span-full text-center p-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b border-wsf-border border-wsf-border border-wsf-border border-wsf-border border-wsf-border-2 border-wsf-primary mx-auto"></div>
                    <p class="mt-2 text-gray-500">Loading makes...</p>
                </div>
            </div>
        </div>

        <!-- Step 2: Models -->
        <div id="wizard-step-2" class="wizard-step hidden opacity-0">
            <div class="bg-white rounded-lg border border-wsf-border p-6 shadow-sm">
                <!-- 1. Header with title and count -->
                <div class="flex justify-between items-start mb-3">
                    <h2 class="text-xl font-semibold wsf-text-primary" data-i18n="select_model_placeholder">Choose a model</h2>
                    <p id="wizard-models-count" class="text-sm text-gray-500 hidden"></p>
                </div>

                <!-- 2. Breadcrumbs -->
                <nav id="wizard-breadcrumbs-2" class="text-sm text-gray-500 mb-3"></nav>

                <!-- 4. Search input will be inserted here by JavaScript -->

                <!-- 5. Models grid - Main content -->
                <div id="wizard-models-list" class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-4 mb-6"></div>
            </div>
        </div>

        <!-- Step 3: Years -->
        <div id="wizard-step-3" class="wizard-step hidden opacity-0">
            <div class="bg-white rounded-lg border border-wsf-border p-6 shadow-sm">
                <!-- 1. Step Title - Top level heading -->
                <h2 class="text-2xl font-bold wsf-text-primary mb-4" data-i18n="select_year_placeholder">Select Year</h2>

                <!-- 2. Breadcrumbs - Show selected make > model -->
                <nav id="wizard-breadcrumbs-3" class="text-sm text-gray-500 mb-3"></nav>

                <!-- 3. Years count - Secondary info -->
                <p id="wizard-years-count" class="text-sm text-gray-500 mb-6 hidden"></p>

                <!-- 4. Years grid - Main content -->
                <div id="wizard-years-list" class="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-8 gap-4 mb-6"></div>
            </div>
        </div>

        <!-- Step 4: Modifications -->
        <div id="wizard-step-4" class="wizard-step hidden opacity-0">
            <div class="bg-white rounded-lg border border-wsf-border p-6 shadow-sm">
                <!-- 1. Step Title - Top level heading -->
                <h2 class="text-2xl font-bold wsf-text-primary mb-4" data-i18n="select_mods_placeholder">Select Modification</h2>

                <!-- 2. Breadcrumbs - Show selected make > model > year -->
                <nav id="wizard-breadcrumbs-4" class="text-sm text-gray-500 mb-3"></nav>

                <!-- 3. Modifications count - Secondary info -->
                <p id="wizard-modifications-count" class="text-sm text-gray-500 mb-6 hidden"></p>

                <!-- 4. Modifications grid - Main content -->
                <div id="wizard-modifications-list" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6"></div>
            </div>
        </div>
        
        <!-- Step 5: Results -->
        <div id="wizard-results" class="wizard-step hidden opacity-0">
             <section id="search-results" class="bg-wsf-bg transform transition-all duration-300 ease-in-out">
                <h2 class="text-lg font-semibold text-gray-900 mb-1" data-i18n="section_results">Search Results</h2>
                <p id="vehicle-label" class="text-base font-medium text-gray-900"></p>
                <div id="selected-modification-info" class="mt-1"></div>
                
                <div class="border-t border-wsf-border my-4"></div>
                
                {# Factory block #}
                <div id="factory-section" class="mb-8 hidden">
                    <h3 class="text-sm font-bold text-gray-900 mb-3" data-i18n="section_factory">Factory Sizes</h3>
                    <div id="factory-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
                </div>

                {# Optional block #}
                <div id="optional-section" class="mb-8 hidden">
                    <h3 class="text-sm font-bold text-gray-900 mb-3" data-i18n="section_optional">Optional Sizes</h3>
                    <div id="optional-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
                </div>

                {# No Results Message #}
                <div id="no-results" class="hidden text-center py-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-2" data-i18n="text_no_results_header">Sizes not found</h3>
                    <p class="text-wsf-muted" data-i18n="text_no_results_body">Try selecting another modification or check your selection.</p>
                </div>
            </section>
        </div>
        </div>

        <!-- Navigation Buttons -->
        <div id="wizard-nav" class="flex justify-between mt-8">
            <button id="wizard-back-btn" class="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors hidden" data-i18n="button_back">Back</button>
            <button id="wizard-next-btn" class="px-6 py-2 bg-wsf-primary text-white rounded-lg hover:bg-blue-700 transition-colors ml-auto hidden" data-i18n="button_next">Next</button>
        </div>
    </div>

    <!-- Garage Section -->
    {% if garage_enabled %}
    <div id="garage-section" class="mt-8 p-4 bg-wsf-surface rounded-lg border border-wsf-border">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold wsf-text-primary" data-i18n="garage_title">My Garage</h3>
            <button id="garage-clear-all" class="text-sm text-red-600 hover:text-red-800" data-i18n="garage_clear_all">Clear all</button>
        </div>
        <div id="garage-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3"></div>
        <div id="garage-empty" class="text-center py-8 text-gray-500">
            <p data-i18n="garage_empty">No vehicles saved yet</p>
        </div>
    </div>
    {% endif %}

</div>



{# Стили для wizard #}
<style>
.animate-fadeIn { animation: fadeIn 0.5s ease-out forwards; }
@keyframes fadeIn { from { opacity:0; transform: translateY(10px);} to { opacity:1; transform: translateY(0);} }

/* Brand tiles background overrides */
#wheel-fit-wizard #wizard-makes-grid > button {
  background-color: transparent !important;
  background-image: none !important;
}
#wheel-fit-wizard #wizard-makes-grid > button:hover {
  background-color: var(--wsf-surface) !important;
}
#wheel-fit-wizard #wizard-makes-grid > button:focus-visible {
  outline: 2px solid var(--wsf-primary);
  outline-offset: 2px;
}

/* Wizard step transitions */
.wizard-step {
    transition: opacity 0.3s ease-in-out;
    min-height: auto !important;
    height: auto !important;
}

/* Ensure wizard steps don't have excessive height */
#wheel-fit-wizard .wizard-step {
    min-height: auto !important;
    max-height: none !important;
}

/* Fix wizard container height issues */
#wheel-fit-wizard .wsf-form-wrapper {
    min-height: auto !important;
    height: auto !important;
}

#wheel-fit-wizard .relative {
    min-height: auto !important;
    height: auto !important;
}

/* Progress bar styling */
#wizard-progress-bar {
    transition: width 0.5s ease-in-out;
}

/* Step name highlighting */
.wizard-step-name {
    transition: color 0.3s ease;
}
.wizard-step-name.wsf-text-primary {
    color: var(--wsf-primary);
    font-weight: 600;
}

/* Ensure proper spacing and no excessive heights */
#wheel-fit-wizard {
    max-height: none !important;
    height: auto !important;
}

/* Fix models list layout */
#wizard-models-list {
    min-height: auto !important;
    height: auto !important;
}

/* Fix years list layout */
#wizard-years-list {
    min-height: auto !important;
    height: auto !important;
}

/* Fix modifications list layout */
#wizard-modifications-list {
    min-height: auto !important;
    height: auto !important;
}

/* AGGRESSIVE HEIGHT RESET - Force all wizard elements to auto height */
#wheel-fit-wizard,
#wheel-fit-wizard *,
#wheel-fit-wizard .wizard-step,
#wheel-fit-wizard .wsf-form-wrapper,
#wheel-fit-wizard .relative,
#wizard-step-1,
#wizard-step-2,
#wizard-step-3,
#wizard-step-4,
#wizard-results {
    min-height: auto !important;
    height: auto !important;
    max-height: none !important;
}

/* Remove any padding/margin that might create excessive space */
#wheel-fit-wizard .wizard-step {
    padding-top: 0 !important;
    padding-bottom: 20px !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* Ensure hidden steps take no space */
#wheel-fit-wizard .wizard-step.hidden {
    display: none !important;
    height: 0 !important;
    min-height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
}

/* Improved wizard step containers */
#wheel-fit-wizard .wizard-step > div {
    background: white;
    border-radius: 8px;
    border: 1px solid var(--wsf-border, #e5e7eb);
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* VERTICAL HIERARCHY: Proper spacing for logical flow */
#wheel-fit-wizard .wizard-step h2 {
    margin-bottom: 20px !important;
    line-height: 1.2 !important;
    font-size: 28px !important;
    font-weight: 700 !important;
}

/* Breadcrumbs - tight spacing under title */
#wheel-fit-wizard .wizard-step nav {
    margin-bottom: 12px !important;
    font-size: 14px !important;
    color: var(--wsf-text-muted, #6b7280) !important;
    font-weight: 500 !important;
}

/* Count labels - secondary info with moderate spacing */
#wheel-fit-wizard .wizard-step p[id*="-count"] {
    margin-bottom: 24px !important;
    font-size: 13px !important;
    color: var(--wsf-text-muted, #9ca3af) !important;
    font-weight: 400 !important;
}

/* Improved search input styling */
#wheel-fit-wizard .wizard-search-wrapper {
    margin-bottom: 24px !important;
    max-width: 400px !important;
}

#wheel-fit-wizard .wizard-search-wrapper input {
    height: 48px !important;
    padding: 12px 48px 12px 16px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
    border: 2px solid #e5e7eb !important;
    transition: all 0.2s ease !important;
}

#wheel-fit-wizard .wizard-search-wrapper input:focus {
    border-color: var(--wsf-primary, #2563eb) !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* Better list item styling for readability */
#wheel-fit-wizard .list-item {
    min-width: 80px !important;
    min-height: 48px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    word-break: break-word !important;
    hyphens: auto !important;
    line-height: 1.3 !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    transition: all 0.2s ease !important;
}

/* Ensure text in list items is readable */
#wheel-fit-wizard .list-item span {
    white-space: normal !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    line-height: 1.3 !important;
    display: block !important;
}

/* Grid improvements for better spacing */
#wizard-models-list,
#wizard-years-list {
    gap: 16px !important;
}

#wizard-modifications-list {
    gap: 20px !important;
}

/* Navigation buttons improvements */
#wheel-fit-wizard #wizard-next-btn,
#wheel-fit-wizard #wizard-back-btn {
    min-height: 44px !important;
    padding: 12px 24px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    white-space: nowrap !important;
    transition: all 0.2s ease !important;
}

/* Garage button improvements */
#wheel-fit-wizard [data-garage-trigger] {
    padding: 8px 12px !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
}

#wheel-fit-wizard .wsf-garage-count-badge {
    margin-left: 6px !important;
    min-width: 20px !important;
    height: 20px !important;
    line-height: 18px !important;
    font-size: 11px !important;
    font-weight: 700 !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    #wizard-models-list {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 12px !important;
    }

    #wizard-years-list {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 12px !important;
    }

    #wizard-modifications-list {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }

    #wheel-fit-wizard .wizard-search-wrapper {
        max-width: 100% !important;
    }

    #wheel-fit-wizard .wizard-search-wrapper input {
        font-size: 16px !important; /* Prevent zoom on iOS */
    }
}
</style>
