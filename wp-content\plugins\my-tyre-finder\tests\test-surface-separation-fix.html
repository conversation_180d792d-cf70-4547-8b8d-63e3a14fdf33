<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surface Separation Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .problem-demo {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .solution-demo {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .demo-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .demo-label {
            color: var(--wsf-text, #1f2937);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        
        .demo-select {
            width: 100%;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid var(--wsf-input-border, #e5e7eb);
            background: var(--wsf-input-bg, #ffffff);
            color: var(--wsf-input-text, #1f2937);
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .demo-results {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .fuel-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 8px;
        }
        
        .fuel-badge.old {
            background: var(--wsf-surface, #f9fafb);
            color: var(--wsf-text-secondary, #6b7280);
        }
        
        .fuel-badge.new {
            background: #f3f4f6;
            color: #374151;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .color-controls {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .color-field {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .color-input {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .color-label {
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Surface Separation Fix</h1>
        <p>Исправление проблемы, когда настройка "Surface" влияла на fuel badges в результатах поиска.</p>
        
        <!-- Problem Description -->
        <div class="test-section">
            <h3>❌ Проблема</h3>
            <div class="problem-demo">
                <h4>ДО исправления:</h4>
                <p>При изменении цвета <strong>"Surface"</strong> в Theme Presets менялся фон fuel badges (NATURAL GAS, DIESEL, etc.) в результатах поиска.</p>
                
                <p><strong>Причина:</strong> Fuel badges использовали класс <code>wsf-bg-surface</code>, который зависит от CSS переменной <code>--wsf-surface</code>.</p>
                
                <div class="code-block">
// ДО (проблемный код):
badges.push(`&lt;span class="px-2 py-1 wsf-bg-surface wsf-text-secondary"&gt;${fuel}&lt;/span&gt;`);

// CSS:
.wsf-bg-surface { background-color: var(--wsf-surface); }
                </div>
            </div>
        </div>
        
        <!-- Solution Description -->
        <div class="test-section">
            <h3>✅ Решение</h3>
            <div class="solution-demo">
                <h4>ПОСЛЕ исправления:</h4>
                <p>Fuel badges теперь используют фиксированные цвета и не зависят от настроек Surface или Input Background.</p>
                
                <div class="code-block">
// ПОСЛЕ (исправленный код):
badges.push(`&lt;span class="px-2 py-1 bg-gray-100 text-gray-700"&gt;${fuel}&lt;/span&gt;`);

// Фиксированные Tailwind классы, не зависят от CSS переменных
                </div>
                
                <h4>Дополнительные исправления:</h4>
                <ul>
                    <li><code>--wsf-input-bg</code> больше не зависит от <code>--wsf-surface</code></li>
                    <li>Loading сообщения используют <code>bg-gray-50</code> вместо <code>wsf-bg-surface</code></li>
                    <li>Model items используют <code>bg-gray-50</code> вместо <code>wsf-bg-surface</code></li>
                    <li>Region badges используют <code>bg-gray-100</code> вместо <code>wsf-bg-surface</code></li>
                </ul>
            </div>
        </div>
        
        <!-- Live Demo -->
        <div class="test-section">
            <h3>🎨 Демонстрация исправления</h3>
            <p>Измените цвета ниже и убедитесь, что fuel badges не меняются:</p>
            
            <div class="color-controls">
                <div class="color-field">
                    <input type="color" class="color-input" value="#f9fafb" data-token="--wsf-surface">
                    <label class="color-label">Surface (для карточек и контейнеров)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#ffffff" data-token="--wsf-input-bg">
                    <label class="color-label">Input Background (для селекторов)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#1f2937" data-token="--wsf-input-text">
                    <label class="color-label">Input Text (текст в селекторах)</label>
                </div>
            </div>
            
            <div class="demo-widget" id="demo-widget">
                <label class="demo-label">Make</label>
                <select class="demo-select">
                    <option value="">Select a make...</option>
                    <option value="audi">Audi</option>
                    <option value="bmw">BMW</option>
                </select>
                
                <div class="demo-results">
                    <h4>Результаты поиска:</h4>
                    <p><strong>BMW 320i</strong> (2020)</p>
                    <div>
                        <span class="fuel-badge new">PETROL</span>
                        <span class="fuel-badge new">184 HP</span>
                        <span class="fuel-badge new">2.0L</span>
                    </div>
                    <p style="margin-top: 10px; font-size: 12px; color: #6b7280;">
                        ↑ Эти badges теперь НЕ меняются при изменении Surface или Input Background
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>📊 Сравнение До/После</h3>
            <div class="comparison-grid">
                <div class="problem-demo">
                    <h4>❌ ДО</h4>
                    <p><strong>Проблема:</strong></p>
                    <ul>
                        <li>Surface влияет на fuel badges</li>
                        <li>Input Background зависит от Surface</li>
                        <li>Нет независимого контроля</li>
                    </ul>
                    
                    <div style="margin-top: 15px;">
                        <span class="fuel-badge old">NATURAL GAS</span>
                        <p style="font-size: 11px; margin-top: 5px;">Меняется при изменении Surface</p>
                    </div>
                </div>
                
                <div class="solution-demo">
                    <h4>✅ ПОСЛЕ</h4>
                    <p><strong>Решение:</strong></p>
                    <ul>
                        <li>Surface только для карточек/контейнеров</li>
                        <li>Input Background независим</li>
                        <li>Fuel badges фиксированные</li>
                    </ul>
                    
                    <div style="margin-top: 15px;">
                        <span class="fuel-badge new">NATURAL GAS</span>
                        <p style="font-size: 11px; margin-top: 5px;">Всегда стабильный цвет</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Технические детали</h3>
            
            <h4>Исправленные файлы:</h4>
            <ul>
                <li><code>assets/js/finder.js</code> - fuel badges, loading messages, model items</li>
                <li><code>assets/js/wizard.js</code> - fuel badges в wizard</li>
                <li><code>assets/css/wheel-fit-shared.src.css</code> - независимый --wsf-input-bg</li>
                <li><code>src/includes/ThemeManager.php</code> - обновлены дефолтные значения</li>
                <li><code>assets/js/admin-theme-panel.js</code> - обновлен createNewTheme</li>
            </ul>
            
            <h4>Изменения в CSS переменных:</h4>
            <div class="code-block">
/* ДО */
--wsf-input-bg: var(--wsf-surface);  /* Зависел от surface */

/* ПОСЛЕ */
--wsf-input-bg: #ffffff;             /* Независимое значение */
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить исправление</h4>
            <ol>
                <li><strong>Очистите кэш</strong> WordPress и браузера</li>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>Создайте новую тему или отредактируйте существующую</li>
                <li>Измените цвет <strong>"Surface"</strong> на яркий (например, красный)</li>
                <li>Сохраните и примените тему</li>
                <li>Перейдите на страницу с виджетом и выполните поиск</li>
                <li>Убедитесь, что fuel badges (PETROL, DIESEL, NATURAL GAS) <strong>НЕ изменили цвет</strong></li>
                <li>Измените <strong>"Input Background"</strong> и убедитесь, что селекторы изменились</li>
            </ol>
            
            <h4>🎯 Ожидаемый результат</h4>
            <ul>
                <li>✅ Surface влияет только на карточки и контейнеры</li>
                <li>✅ Input Background влияет только на селекторы</li>
                <li>✅ Fuel badges всегда имеют стабильный серый цвет</li>
                <li>✅ Независимый контроль всех элементов</li>
            </ul>
        </div>
    </div>

    <script>
        // Color picker functionality
        document.querySelectorAll('.color-input').forEach(input => {
            input.addEventListener('change', function() {
                const token = this.dataset.token;
                const value = this.value;
                const demoWidget = document.getElementById('demo-widget');
                
                demoWidget.style.setProperty(token, value);
                console.log(`Updated ${token} to ${value}`);
            });
        });
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            const demoWidget = document.getElementById('demo-widget');
            
            // Set default values
            demoWidget.style.setProperty('--wsf-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-border', '#e5e7eb');
            demoWidget.style.setProperty('--wsf-surface', '#f9fafb');
            demoWidget.style.setProperty('--wsf-input-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-input-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-input-border', '#e5e7eb');
            
            console.log('Surface separation fix demo initialized');
        });
    </script>
</body>
</html>
