/**
 * Wheel Size Finder - Theme Listener for Live Preview
 * Handles theme updates via postMessage communication
 */

(function() {
    'use strict';

    /**
     * Apply theme variables to the preview container
     * @param {Object} themeVars - Theme CSS custom properties
     */
    function applyThemeVariables(themeVars) {
        const previewContainer = document.getElementById('widget-preview');
        if (!previewContainer) {
            console.warn('[Theme Listener] Preview container not found');
            return;
        }

        // Find the widget element within the preview
        const widget = previewContainer.querySelector('.wheel-fit-widget, .wsf-finder-widget, [data-wsf-theme]');
        if (!widget) {
            console.warn('[Theme Listener] Widget element not found in preview');
            return;
        }

        // Apply theme variables as CSS custom properties
        Object.entries(themeVars).forEach(([property, value]) => {
            if (property !== 'name' && typeof value === 'string') {
                // Convert property name to CSS custom property format
                const cssProperty = property.startsWith('--') ? property : `--${property}`;
                widget.style.setProperty(cssProperty, value);
            }
        });

        console.log('[Theme Listener] Applied theme variables:', themeVars);
    }

    /**
     * Set theme attribute on widget element
     * @param {string} themeSlug - Theme slug identifier
     */
    function setThemeAttribute(themeSlug) {
        const previewContainer = document.getElementById('widget-preview');
        if (!previewContainer) return;

        const widget = previewContainer.querySelector('.wheel-fit-widget, .wsf-finder-widget, [data-wsf-theme]');
        if (widget) {
            widget.setAttribute('data-wsf-theme', themeSlug);
            console.log('[Theme Listener] Set theme attribute:', themeSlug);
        }
    }

    /**
     * Handle theme update messages
     * @param {MessageEvent} event - PostMessage event
     */
    function handleThemeUpdate(event) {
        // Verify origin for security (same-origin only)
        if (event.origin !== window.location.origin) {
            return;
        }

        const data = event.data;
        if (data && data.type === 'wsf-theme-update') {
            console.log('[Theme Listener] Received theme update:', data);
            
            if (data.slug) {
                setThemeAttribute(data.slug);
            }
            
            if (data.vars && typeof data.vars === 'object') {
                applyThemeVariables(data.vars);
            }
        }
    }

    /**
     * Initialize theme listener
     */
    function init() {
        // Listen for postMessage events
        window.addEventListener('message', handleThemeUpdate, false);
        
        // Apply initial theme if available
        if (window.wsfActiveTheme && window.wsfActiveTheme.properties) {
            applyThemeVariables(window.wsfActiveTheme.properties);
            if (window.wsfActiveTheme.slug) {
                setThemeAttribute(window.wsfActiveTheme.slug);
            }
        }

        console.log('[Theme Listener] Initialized');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
