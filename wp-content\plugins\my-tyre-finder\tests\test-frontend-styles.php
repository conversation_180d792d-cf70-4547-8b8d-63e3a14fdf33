<?php
/**
 * Test page to verify frontend button styles
 * 
 * Usage: Add this to your WordPress theme or create a custom page template
 * Or visit: /wp-admin/admin.php?page=wheel-size-appearance to see the preview
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header(); ?>

<div style="padding: 2rem; max-width: 800px; margin: 0 auto;">
    <h1>🧪 Test Frontend Button Styles</h1>
    
    <div style="background: #f0f0f1; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
        <h2>Widget Preview</h2>
        <p>This should show the widget with properly styled buttons:</p>
        
        <!-- WordPress shortcode for the widget -->
        <?php echo do_shortcode('[wheel_fit]'); ?>
    </div>
    
    <div style="background: #fff3cd; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border: 1px solid #ffeaa7;">
        <h2>🔍 Manual Button Test</h2>
        <p>These buttons should have the same styling as in the widget:</p>
        
        <button class="btn-secondary" style="margin: 0.5rem;">
            <i data-lucide="trash-2"></i>
            <span>Clear all (class)</span>
        </button>
        
        <button id="garage-clear-all" style="margin: 0.5rem;">
            <i data-lucide="trash-2"></i>
            <span>Clear all (ID)</span>
        </button>
        
        <button style="margin: 0.5rem;">
            <i data-lucide="trash-2"></i>
            <span>Unstyled button</span>
        </button>
    </div>
    
    <div style="background: #e0f2fe; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
        <h2>✅ Expected Result</h2>
        <ul>
            <li><strong>Styled buttons</strong>: Transparent background, red text (#ef4444), light border</li>
            <li><strong>Hover effect</strong>: Light gray background, red border</li>
            <li><strong>Unstyled button</strong>: Default browser styling</li>
        </ul>
    </div>
    
    <div style="background: #ffebee; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
        <h2>🔧 Debug Info</h2>
        <p><strong>Current context:</strong> <?php echo is_admin() ? 'Admin' : 'Frontend'; ?></p>
        <p><strong>Critical styles loaded:</strong> 
            <span id="critical-styles-check">Checking...</span>
        </p>
        <p><strong>Widget styles loaded:</strong> 
            <span id="widget-styles-check">Checking...</span>
        </p>
    </div>
</div>

<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
<script>
    // Initialize Lucide icons
    lucide.createIcons();
    
    // Check if critical styles are loaded
    const criticalStylesElement = document.getElementById('wheel-fit-critical-button-styles');
    document.getElementById('critical-styles-check').textContent = 
        criticalStylesElement ? '✅ Found' : '❌ Missing';
    
    // Check if widget styles are loaded
    const widgetStylesElement = document.querySelector('link[href*="wheel-fit-shared"]');
    document.getElementById('widget-styles-check').textContent = 
        widgetStylesElement ? '✅ Found' : '❌ Missing';
    
    // Log computed styles for debugging
    setTimeout(() => {
        const styledButton = document.querySelector('.btn-secondary');
        if (styledButton) {
            const computed = window.getComputedStyle(styledButton);
            console.log('Button styles:', {
                backgroundColor: computed.backgroundColor,
                color: computed.color,
                border: computed.border,
                fontSize: computed.fontSize,
                padding: computed.padding
            });
        }
    }, 100);
</script>

<?php get_footer(); ?>
