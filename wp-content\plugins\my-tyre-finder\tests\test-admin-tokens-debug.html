<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Tokens Debug Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .token-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .token-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
        }
        
        .token-property {
            font-family: monospace;
            font-size: 12px;
            color: #7c3aed;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .token-label {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .token-help {
            font-size: 12px;
            color: #64748b;
            font-style: italic;
        }
        
        .status-pass {
            color: #059669;
            font-weight: bold;
        }
        
        .status-fail {
            color: #dc2626;
            font-weight: bold;
        }
        
        .status-warning {
            color: #d97706;
            font-weight: bold;
        }
        
        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .debug-info {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .debug-info h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px 10px 0;
        }
        
        .test-button:hover {
            background: #1d4ed8;
        }
        
        .results-area {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            min-height: 100px;
        }
        
        .mock-theme-editor {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .color-field {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .color-input {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .color-label {
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Admin Tokens Debug Test</h1>
        <p>Этот тест проверяет передачу input-токенов в WordPress admin Theme Presets интерфейс.</p>
        
        <!-- Test 1: Check JavaScript Variables -->
        <div class="test-section">
            <h3>1. Проверка JavaScript переменных</h3>
            <button class="test-button" onclick="checkJavaScriptTokens()">🔍 Проверить wsfColorTokens</button>
            <div class="results-area" id="js-tokens-results">
                <p>Нажмите кнопку для проверки...</p>
            </div>
        </div>
        
        <!-- Test 2: Simulate Theme Editor -->
        <div class="test-section">
            <h3>2. Симуляция Theme Editor</h3>
            <button class="test-button" onclick="simulateThemeEditor()">🎨 Симулировать редактор тем</button>
            <div class="mock-theme-editor" id="mock-editor" style="display: none;">
                <h4>Симуляция createColorFields()</h4>
                <div id="simulated-fields"></div>
            </div>
        </div>
        
        <!-- Test 3: Check Fallback Labels -->
        <div class="test-section">
            <h3>3. Проверка Fallback Labels</h3>
            <button class="test-button" onclick="checkFallbackLabels()">📋 Проверить fallback</button>
            <div class="results-area" id="fallback-results">
                <p>Нажмите кнопку для проверки...</p>
            </div>
        </div>
        
        <!-- Test 4: Debug Information -->
        <div class="test-section">
            <h3>4. Отладочная информация</h3>
            <div class="debug-info">
                <h4>📋 Что проверить в WordPress Admin:</h4>
                <ol>
                    <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                    <li>Откройте <strong>Developer Tools (F12)</strong> в браузере</li>
                    <li>В консоли выполните: <code>console.log(window.wsfColorTokens)</code></li>
                    <li>Проверьте, что объект содержит все input-токены</li>
                    <li>Нажмите <strong>"Add New Theme"</strong> в Theme Presets панели</li>
                    <li>Убедитесь, что в редакторе есть поля для input-токенов</li>
                </ol>
            </div>
            
            <button class="test-button" onclick="generateDebugCode()">🛠️ Генерировать код для консоли</button>
            <div class="code-block" id="debug-code" style="display: none;"></div>
        </div>
        
        <!-- Expected Tokens List -->
        <div class="test-section">
            <h3>5. Ожидаемые токены</h3>
            <p>Эти токены должны быть доступны в Theme Presets:</p>
            <div class="token-list" id="expected-tokens">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // Expected input tokens
        const expectedInputTokens = {
            '--wsf-surface': {
                label: 'Surface',
                help: 'Background for cards, inputs & form elements',
                order: 9
            },
            '--wsf-input-bg': {
                label: 'Input Background',
                help: 'Background color for select elements & inputs',
                order: 10
            },
            '--wsf-input-text': {
                label: 'Input Text',
                help: 'Text color inside select elements & inputs',
                order: 11
            },
            '--wsf-input-border': {
                label: 'Input Border',
                help: 'Border color for select elements & inputs',
                order: 12
            },
            '--wsf-input-placeholder': {
                label: 'Input Placeholder',
                help: 'Placeholder text color in select elements',
                order: 13
            },
            '--wsf-input-focus': {
                label: 'Input Focus',
                help: 'Focus ring color for select elements & inputs',
                order: 14
            }
        };
        
        // Fallback labels (should match admin-theme-panel.js)
        const fallbackLabels = {
            '--wsf-primary': 'Primary Color',
            '--wsf-bg': 'Background',
            '--wsf-text': 'Text Color',
            '--wsf-border': 'Border Color',
            '--wsf-hover': 'Hover State',
            '--wsf-secondary': 'Secondary',
            '--wsf-accent': 'Accent',
            '--wsf-muted': 'Muted',
            '--wsf-surface': 'Surface',
            '--wsf-input-bg': 'Input Background',
            '--wsf-input-text': 'Input Text',
            '--wsf-input-border': 'Input Border',
            '--wsf-input-placeholder': 'Input Placeholder',
            '--wsf-input-focus': 'Input Focus',
            '--wsf-success': 'Success',
            '--wsf-warning': 'Warning',
            '--wsf-error': 'Error'
        };
        
        function checkJavaScriptTokens() {
            const resultsDiv = document.getElementById('js-tokens-results');
            let html = '';
            
            // Check if wsfColorTokens exists
            if (typeof window.wsfColorTokens !== 'undefined') {
                html += '<div class="status-pass">✅ window.wsfColorTokens найден</div>';
                
                const tokens = window.wsfColorTokens.tokens || {};
                html += `<div>Найдено токенов: ${Object.keys(tokens).length}</div>`;
                
                // Check each expected input token
                Object.keys(expectedInputTokens).forEach(property => {
                    if (tokens[property]) {
                        html += `<div class="status-pass">✅ ${property}: "${tokens[property].label}"</div>`;
                    } else {
                        html += `<div class="status-fail">❌ ${property}: Отсутствует</div>`;
                    }
                });
                
                // Show full object
                html += '<h4>Полный объект wsfColorTokens:</h4>';
                html += `<div class="code-block">${JSON.stringify(window.wsfColorTokens, null, 2)}</div>`;
                
            } else {
                html += '<div class="status-fail">❌ window.wsfColorTokens не найден</div>';
                html += '<div class="status-warning">⚠️ Это нормально, если тест запущен вне WordPress admin</div>';
            }
            
            resultsDiv.innerHTML = html;
        }
        
        function simulateThemeEditor() {
            const mockEditor = document.getElementById('mock-editor');
            const fieldsDiv = document.getElementById('simulated-fields');
            
            // Simulate the createColorFields function
            const colorTokens = window.wsfColorTokens?.tokens || {};
            const fieldsToRender = Object.keys(colorTokens).length > 0 ? colorTokens : fallbackLabels;
            
            let html = '';
            Object.entries(fieldsToRender).forEach(([property, tokenData]) => {
                const label = typeof tokenData === 'object' ? tokenData.label : tokenData;
                const help = typeof tokenData === 'object' ? tokenData.help : '';
                
                html += `
                    <div class="color-field">
                        <input type="color" class="color-input" value="#2563eb">
                        <div class="color-label">
                            <div><strong>${label}</strong></div>
                            <div style="font-size: 11px; color: #6b7280;">${property}</div>
                            ${help ? `<div style="font-size: 10px; color: #9ca3af; font-style: italic;">${help}</div>` : ''}
                        </div>
                    </div>
                `;
            });
            
            fieldsDiv.innerHTML = html;
            mockEditor.style.display = 'block';
        }
        
        function checkFallbackLabels() {
            const resultsDiv = document.getElementById('fallback-results');
            let html = '<h4>Проверка fallback labels:</h4>';
            
            Object.keys(expectedInputTokens).forEach(property => {
                if (fallbackLabels[property]) {
                    html += `<div class="status-pass">✅ ${property}: "${fallbackLabels[property]}"</div>`;
                } else {
                    html += `<div class="status-fail">❌ ${property}: Отсутствует в fallback</div>`;
                }
            });
            
            html += '<h4>Все fallback labels:</h4>';
            html += '<div class="code-block">' + JSON.stringify(fallbackLabels, null, 2) + '</div>';
            
            resultsDiv.innerHTML = html;
        }
        
        function generateDebugCode() {
            const codeDiv = document.getElementById('debug-code');
            const debugCode = `
// Выполните этот код в консоли WordPress admin
console.log('=== Theme Tokens Debug ===');
console.log('wsfColorTokens:', window.wsfColorTokens);

if (window.wsfColorTokens && window.wsfColorTokens.tokens) {
    const tokens = window.wsfColorTokens.tokens;
    const inputTokens = [
        '--wsf-surface',
        '--wsf-input-bg',
        '--wsf-input-text', 
        '--wsf-input-border',
        '--wsf-input-placeholder',
        '--wsf-input-focus'
    ];
    
    console.log('=== Input Tokens Check ===');
    inputTokens.forEach(token => {
        if (tokens[token]) {
            console.log('✅', token, ':', tokens[token]);
        } else {
            console.log('❌', token, ': Missing');
        }
    });
} else {
    console.log('❌ wsfColorTokens not available');
}

// Check if ThemePresetsPanel exists
if (window.ThemePresetsPanel) {
    console.log('✅ ThemePresetsPanel available');
} else {
    console.log('❌ ThemePresetsPanel not available');
}
            `;
            
            codeDiv.textContent = debugCode.trim();
            codeDiv.style.display = 'block';
        }
        
        function renderExpectedTokens() {
            const tokensDiv = document.getElementById('expected-tokens');
            let html = '';
            
            Object.entries(expectedInputTokens).forEach(([property, token]) => {
                html += `
                    <div class="token-item">
                        <div class="token-property">${property}</div>
                        <div class="token-label">${token.label}</div>
                        <div class="token-help">${token.help}</div>
                    </div>
                `;
            });
            
            tokensDiv.innerHTML = html;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderExpectedTokens();
            
            // Auto-check if we're in WordPress admin context
            if (typeof window.wsfColorTokens !== 'undefined') {
                setTimeout(checkJavaScriptTokens, 500);
            }
        });
    </script>
</body>
</html>
