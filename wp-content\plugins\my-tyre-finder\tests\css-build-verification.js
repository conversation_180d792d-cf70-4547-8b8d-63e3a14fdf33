/**
 * CSS Build Verification Script
 * Run this in browser console to verify Tailwind CSS build is working correctly
 */

(function() {
    'use strict';

    console.log('🔍 === CSS BUILD VERIFICATION ===');

    // Check if CSS file is loaded and contains Tailwind utilities
    function checkTailwindUtilities() {
        console.log('\n1️⃣ Checking Tailwind Utilities...');
        
        const stylesheets = Array.from(document.styleSheets);
        const themePanel = stylesheets.find(sheet => {
            try {
                return sheet.href && sheet.href.includes('admin-theme-panel.css');
            } catch (e) {
                return false;
            }
        });

        if (!themePanel) {
            console.error('❌ admin-theme-panel.css not found');
            return false;
        }

        console.log('✅ CSS file found:', themePanel.href);

        // Check for specific Tailwind utilities
        const requiredUtilities = [
            '.wsf-flex',
            '.wsf-flex-col',
            '.wsf-bg-white',
            '.wsf-border',
            '.wsf-rounded-md',
            '.wsf-p-4',
            '.wsf-gap-3',
            '.wsf-text-xs',
            '.wsf-font-semibold'
        ];

        let foundUtilities = 0;
        
        try {
            const rules = themePanel.cssRules || themePanel.rules;
            const ruleTexts = Array.from(rules).map(rule => rule.cssText || '').join(' ');
            
            requiredUtilities.forEach(utility => {
                if (ruleTexts.includes(utility)) {
                    console.log(`✅ Found utility: ${utility}`);
                    foundUtilities++;
                } else {
                    console.warn(`⚠️ Missing utility: ${utility}`);
                }
            });
            
            console.log(`📊 Utilities found: ${foundUtilities}/${requiredUtilities.length}`);
            return foundUtilities >= requiredUtilities.length * 0.8; // 80% threshold
            
        } catch (e) {
            console.warn('⚠️ Cannot access CSS rules (CORS):', e.message);
            return true; // Assume OK if we can't check
        }
    }

    // Check theme panel styling
    function checkThemePanelStyling() {
        console.log('\n2️⃣ Checking Theme Panel Styling...');
        
        const themePanel = document.querySelector('.wsf-theme-panel');
        if (!themePanel) {
            console.error('❌ Theme panel element not found');
            return false;
        }

        const computedStyle = getComputedStyle(themePanel);
        const checks = [
            {
                property: 'display',
                expected: 'flex',
                actual: computedStyle.display
            },
            {
                property: 'flex-direction',
                expected: 'column',
                actual: computedStyle.flexDirection
            },
            {
                property: 'background-color',
                expected: 'rgb(255, 255, 255)',
                actual: computedStyle.backgroundColor
            },
            {
                property: 'border-width',
                expected: '1px',
                actual: computedStyle.borderWidth
            },
            {
                property: 'border-radius',
                expected: '6px', // 0.375rem
                actual: computedStyle.borderRadius
            }
        ];

        let passedChecks = 0;
        checks.forEach(check => {
            if (check.actual === check.expected || 
                (check.property === 'border-radius' && parseFloat(check.actual) >= 5)) {
                console.log(`✅ ${check.property}: ${check.actual}`);
                passedChecks++;
            } else {
                console.warn(`⚠️ ${check.property}: expected ${check.expected}, got ${check.actual}`);
            }
        });

        console.log(`📊 Style checks passed: ${passedChecks}/${checks.length}`);
        return passedChecks >= checks.length * 0.8;
    }

    // Check admin grid layout
    function checkAdminGrid() {
        console.log('\n3️⃣ Checking Admin Grid Layout...');
        
        const adminGrid = document.querySelector('.wsf-admin-grid');
        if (!adminGrid) {
            console.error('❌ Admin grid element not found');
            return false;
        }

        const computedStyle = getComputedStyle(adminGrid);
        const checks = [
            {
                property: 'display',
                expected: 'grid',
                actual: computedStyle.display
            },
            {
                property: 'grid-template-columns',
                expected: /1fr.*280px/,
                actual: computedStyle.gridTemplateColumns
            },
            {
                property: 'gap',
                expected: '32px', // 2rem
                actual: computedStyle.gap
            }
        ];

        let passedChecks = 0;
        checks.forEach(check => {
            const matches = check.expected instanceof RegExp ? 
                check.expected.test(check.actual) : 
                check.actual === check.expected;
                
            if (matches) {
                console.log(`✅ ${check.property}: ${check.actual}`);
                passedChecks++;
            } else {
                console.warn(`⚠️ ${check.property}: expected ${check.expected}, got ${check.actual}`);
            }
        });

        console.log(`📊 Grid checks passed: ${passedChecks}/${checks.length}`);
        return passedChecks >= checks.length * 0.8;
    }

    // Check responsive behavior
    function checkResponsive() {
        console.log('\n4️⃣ Checking Responsive Behavior...');
        
        const originalWidth = window.innerWidth;
        const isMobile = originalWidth <= 1100;
        
        console.log(`📱 Current viewport: ${originalWidth}px (${isMobile ? 'Mobile' : 'Desktop'})`);
        
        const adminGrid = document.querySelector('.wsf-admin-grid');
        if (!adminGrid) return false;

        const computedStyle = getComputedStyle(adminGrid);
        
        if (isMobile) {
            // On mobile, should be single column
            const isResponsive = computedStyle.gridTemplateColumns === '1fr' ||
                                computedStyle.gridTemplateColumns === 'none';
            if (isResponsive) {
                console.log('✅ Mobile layout: single column grid');
            } else {
                console.warn('⚠️ Mobile layout: expected single column, got', computedStyle.gridTemplateColumns);
            }
            return isResponsive;
        } else {
            // On desktop, should be two columns
            const isResponsive = computedStyle.gridTemplateColumns.includes('1fr') &&
                                computedStyle.gridTemplateColumns.includes('280px');
            if (isResponsive) {
                console.log('✅ Desktop layout: two column grid');
            } else {
                console.warn('⚠️ Desktop layout: expected two columns, got', computedStyle.gridTemplateColumns);
            }
            return isResponsive;
        }
    }

    // Check file size and content
    function checkFileSize() {
        console.log('\n5️⃣ Checking File Size and Content...');
        
        const stylesheets = Array.from(document.styleSheets);
        const themePanel = stylesheets.find(sheet => {
            try {
                return sheet.href && sheet.href.includes('admin-theme-panel.css');
            } catch (e) {
                return false;
            }
        });

        if (!themePanel) return false;

        try {
            const rules = themePanel.cssRules || themePanel.rules;
            const ruleCount = rules.length;
            
            console.log(`📊 CSS rules count: ${ruleCount}`);
            
            if (ruleCount > 50) {
                console.log('✅ CSS file appears to contain Tailwind utilities (good rule count)');
                return true;
            } else {
                console.warn('⚠️ CSS file has few rules - utilities may be missing');
                return false;
            }
        } catch (e) {
            console.warn('⚠️ Cannot count CSS rules:', e.message);
            return true; // Assume OK if we can't check
        }
    }

    // Generate final report
    function generateReport(results) {
        console.log('\n📋 === VERIFICATION REPORT ===');
        
        const tests = [
            { name: 'Tailwind Utilities', result: results.utilities },
            { name: 'Theme Panel Styling', result: results.styling },
            { name: 'Admin Grid Layout', result: results.grid },
            { name: 'Responsive Behavior', result: results.responsive },
            { name: 'File Size/Content', result: results.fileSize }
        ];

        const passedTests = tests.filter(test => test.result).length;
        const totalTests = tests.length;
        const successRate = (passedTests / totalTests * 100).toFixed(1);

        console.log(`\n🎯 Overall Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
        
        tests.forEach(test => {
            console.log(`   ${test.result ? '✅' : '❌'} ${test.name}`);
        });

        if (successRate >= 80) {
            console.log('\n🎉 CSS build verification PASSED! Tailwind utilities are working.');
        } else {
            console.log('\n⚠️ CSS build verification FAILED. Check the issues above.');
        }

        console.log('\n💡 Next Steps:');
        if (successRate >= 80) {
            console.log('• Theme panel should now display with proper styling');
            console.log('• Test theme switching functionality');
            console.log('• Verify responsive behavior on different screen sizes');
        } else {
            console.log('• Rebuild CSS with proper Tailwind configuration');
            console.log('• Check safelist configuration in tailwind.config.js');
            console.log('• Verify content paths include all relevant files');
        }

        return { successRate, passedTests, totalTests };
    }

    // Run all verifications
    function runVerification() {
        console.log('Starting CSS build verification...\n');

        const results = {
            utilities: checkTailwindUtilities(),
            styling: checkThemePanelStyling(),
            grid: checkAdminGrid(),
            responsive: checkResponsive(),
            fileSize: checkFileSize()
        };

        return generateReport(results);
    }

    // Export to window for manual access
    window.cssBuildVerification = {
        run: runVerification,
        individual: {
            checkTailwindUtilities,
            checkThemePanelStyling,
            checkAdminGrid,
            checkResponsive,
            checkFileSize
        }
    };

    // Auto-run verification
    runVerification();

})();
