<?php

declare(strict_types=1);

namespace MyTyreFinder;

use League\Container\Container;

/**
 * Main plugin orchestrator.
 */
final class Plugin
{
    /** Singleton instance */
    private static ?self $instance = null;

    private Loader $loader;

    private function __construct(private Container $container)
    {
        $this->loader = new Loader();
        $this->registerServices();

        // Frontend будет зарегистрирован через DI контейнер в registerServices()
    }

    /**
     * Retrieve the shared instance, creating it when it does not yet exist.
     */
    public static function instance(?Container $container = null): self
    {
        if (null === self::$instance) {
            $container = $container ?? new Container();
            self::$instance = new self($container);
        }

        return self::$instance;
    }

    /**
     * Expose the Loader for external use.
     */
    public function loader(): Loader
    {
        return $this->loader;
    }

    /**
     * Run the loader to register all queued hooks with WordPress.
     */
    public function init(): void
    {
        $this->loader->run();
    }

    /* Convenience wrappers */
    public function add_action(string $hook, callable $callback, int $priority = 10, int $accepted_args = 1): void
    {
        $this->loader->add_action($hook, $callback, $priority, $accepted_args);
    }

    public function add_filter(string $hook, callable $callback, int $priority = 10, int $accepted_args = 1): void
    {
        $this->loader->add_filter($hook, $callback, $priority, $accepted_args);
    }

    /**
     * Register services within the DI container and call their register() method so
     * they may enqueue hooks via the shared Loader instance.
     */
    private function registerServices(): void
    {
        $services = [
            \MyTyreFinder\Admin\Admin::class,
            \MyTyreFinder\Public\Frontend::class,
            \MyTyreFinder\Ajax\AjaxController::class,
            // \MyTyreFinder\Rest\FitmentController::class,
            \MyTyreFinder\Rest\ThemeController::class,
            \MyTyreFinder\Services\WheelSizeApi::class,
        ];

        // Share the loader instance.
        $this->container->add(Loader::class, $this->loader);

        foreach ($services as $service) {
            $this->container->add($service);
        }

        foreach ($services as $service) {
            $instance = $this->container->get($service);
            if (method_exists($instance, 'register')) {
                $instance->register();
            }
        }

        // Ensure analytics table exists on every load
        if (class_exists('MyTyreFinder\Installer')) {
            \MyTyreFinder\Installer::maybe_create_stats_table();
        }

        // Initialize default themes if needed (after activation)
        $this->init_default_themes();
    }

    /**
     * Initialize default themes after plugin activation
     */
    private function init_default_themes(): void
    {
        // Always ensure themes are initialized, not just on activation
        if (class_exists('MyTyreFinder\Includes\ThemeManager')) {
            $themes = \MyTyreFinder\Includes\ThemeManager::get_themes(); // This will create default themes if they don't exist

            // Verify that both light and dark themes exist
            if (!isset($themes['light']) || !isset($themes['dark'])) {
                // Force re-initialization if default themes are missing
                delete_option('wsf_theme_presets');
                \MyTyreFinder\Includes\ThemeManager::get_themes();
            }
        }

        // Handle the activation flag if it exists
        if (get_option('wheel_size_init_themes', false)) {
            delete_option('wheel_size_init_themes');
        }
    }
} 