/**
 * Wheel Size Finder - Theme Presets Panel JavaScript
 * Handles CRUD operations and live preview functionality
 */

(function($) {
    'use strict';

    class ThemePresetsPanel {
        constructor() {
            this.apiBase = wpApiSettings.root + 'wheel-size/v1/themes';
            this.activeTheme = null;
            this.themes = {};
            this.isLoading = false;
            this.editingTheme = null;

            // Configuration options
            this.config = {
                showActivationNotifications: true, // Set to false to disable activation notifications
                notificationDuration: 3000 // Duration in milliseconds
            };

            this.init();
        }

        init() {
            this.bindEvents();
            this.loadThemes();
        }

        bindEvents() {
            // Theme card clicks
            $(document).on('click', '.wsf-theme-card', (e) => {
                if (!$(e.target).closest('.wsf-theme-card__actions').length) {
                    const slug = $(e.currentTarget).data('theme-slug');
                    this.activateTheme(slug);
                }
            });

            // Action buttons
            $(document).on('click', '.wsf-theme-card__action--edit', (e) => {
                e.stopPropagation();
                const slug = $(e.currentTarget).closest('.wsf-theme-card').data('theme-slug');
                this.editTheme(slug);
            });

            $(document).on('click', '.wsf-theme-card__action--duplicate', (e) => {
                e.stopPropagation();
                const slug = $(e.currentTarget).closest('.wsf-theme-card').data('theme-slug');
                this.duplicateTheme(slug);
            });

            $(document).on('click', '.wsf-theme-card__action--delete', (e) => {
                e.stopPropagation();
                const slug = $(e.currentTarget).closest('.wsf-theme-card').data('theme-slug');
                this.deleteTheme(slug);
            });

            // Add new theme button
            $(document).on('click', '.wsf-theme-add', () => {
                this.createNewTheme();
            });

            // Editor modal events
            $(document).on('click', '.wsf-theme-editor__button--save', () => {
                this.saveThemeFromEditor();
            });

            $(document).on('click', '.wsf-theme-editor__button--cancel, .wsf-theme-overlay', () => {
                this.closeEditor();
            });

            // Color input changes for live preview
            $(document).on('input', '.wsf-theme-editor__color-input', (e) => {
                this.previewColorChange(e.target);
            });

            // Cheat sheet toggle
            $(document).on('click', '.wsf-cheat-sheet__toggle', (e) => {
                e.preventDefault();
                const content = $(e.currentTarget).closest('.wsf-cheat-sheet').find('.wsf-cheat-sheet__content');
                const toggleText = $(e.currentTarget).find('.wsf-cheat-sheet__toggle-text');
                const toggleIcon = $(e.currentTarget).find('.wsf-cheat-sheet__toggle-icon');

                if (content.is(':visible')) {
                    content.slideUp(200);
                    toggleText.text('Show Details');
                    toggleIcon.css('transform', 'rotate(0deg)');
                    this.setCheatSheetState(false);
                } else {
                    content.slideDown(200);
                    toggleText.text('Hide Details');
                    toggleIcon.css('transform', 'rotate(180deg)');
                    this.setCheatSheetState(true);
                }
            });

            // Copy CSS variables
            $(document).on('click', '.wsf-cheat-sheet__copy', (e) => {
                e.preventDefault();
                const textToCopy = $(e.currentTarget).data('copy');

                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(textToCopy).then(() => {
                        this.showCopyFeedback(e.currentTarget);
                    });
                } else {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = textToCopy;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    this.showCopyFeedback(e.currentTarget);
                }
            });

            // Prevent closing modal when clicking inside
            $(document).on('click', '.wsf-theme-editor', (e) => {
                e.stopPropagation();
            });
        }

        async loadThemes() {
            this.setLoading(true);

            try {
                const response = await $.ajax({
                    url: wpApiSettings.root + 'wheel-size/v1/themes',
                    method: 'GET',
                    beforeSend: function(xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', wpApiSettings.nonce);
                    }
                });

                this.themes = response.themes;
                this.activeTheme = response.active_theme;
                this.renderThemes();

                // Apply active theme to preview and widgets on load
                if (this.activeTheme && this.themes[this.activeTheme]) {
                    this.applyThemeToPreview(this.themes[this.activeTheme]);
                    this.applyThemeToCurrentPageWidgets(this.themes[this.activeTheme]);
                }
            } catch (error) {
                console.error('Failed to load themes:', error);
                this.showNotification('Failed to load themes', 'error');
            } finally {
                this.setLoading(false);
            }
        }

        renderThemes() {
            const container = $('.wsf-theme-panel__content');
            container.empty();

            // Render theme cards
            Object.entries(this.themes).forEach(([slug, theme]) => {
                const card = this.createThemeCard(slug, theme);
                container.append(card);
            });

            // Add "Create New Theme" button
            container.append(`
                <button type="button" class="wsf-theme-add">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    ${wp.i18n.__('Add New Theme', 'wheel-size')}
                </button>
            `);
        }

        /**
         * Get theme color with improved fallback values for better contrast
         * @param {Object} theme - Theme object
         * @param {string} property - CSS property name
         * @param {string} slug - Theme slug for context
         * @returns {string} Color value
         */
        getThemeColorWithFallback(theme, property, slug) {
            // Return actual theme color if available
            if (theme[property]) {
                return theme[property];
            }

            // Improved fallback values based on theme type and property
            const fallbacks = {
                'dark': {
                    '--wsf-bg': '#1E293B',      // slate-800 instead of white
                    '--wsf-text': '#F1F5F9',    // slate-100 for dark theme text
                    '--wsf-primary': '#3B82F6'  // blue-500 for dark theme
                },
                'light': {
                    '--wsf-bg': '#F8FAFC',      // slate-50 instead of pure white
                    '--wsf-text': '#1E293B',    // slate-800
                    '--wsf-primary': '#2563EB'  // blue-600
                },
                'green': {
                    '--wsf-bg': '#F0FDF4',      // green-50 tinted background
                    '--wsf-text': '#14532D',    // green-900
                    '--wsf-primary': '#16A34A'  // green-600
                },
                'blue': {
                    '--wsf-bg': '#EFF6FF',      // blue-50 tinted background
                    '--wsf-text': '#1E3A8A',    // blue-900
                    '--wsf-primary': '#2563EB'  // blue-600
                }
            };

            // Default fallbacks for unknown themes
            const defaultFallbacks = {
                '--wsf-bg': '#F3F4F6',      // gray-100 - visible but not pure white
                '--wsf-text': '#1F2937',    // gray-800
                '--wsf-primary': '#6366F1'  // indigo-500
            };

            // Use theme-specific fallback if available, otherwise use default
            const themeFallbacks = fallbacks[slug] || defaultFallbacks;
            return themeFallbacks[property] || defaultFallbacks[property];
        }

        /**
         * Escape HTML characters to prevent XSS
         * @param {string} text - Text to escape
         * @returns {string} Escaped text
         */
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        createThemeCard(slug, theme) {
            const isActive = slug === this.activeTheme;
            const isDefault = ['light', 'dark'].includes(slug);

            return `
                <div class="wsf-theme-card ${isActive ? 'wsf-theme-card--active' : ''}" data-theme-id="${this.escapeHtml(slug)}" data-theme-slug="${this.escapeHtml(slug)}">
                    ${isActive ? '<span class="wsf-theme-card__badge">Active</span>' : ''}
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">${theme.name}</h3>
                        <div class="wsf-theme-card__actions">
                            <button type="button" class="wsf-theme-card__action wsf-theme-card__action--edit" title="${wp.i18n.__('Edit', 'wheel-size')}">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                </svg>
                            </button>
                            <button type="button" class="wsf-theme-card__action wsf-theme-card__action--duplicate" title="${wp.i18n.__('Duplicate', 'wheel-size')}">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                            </button>
                            ${!isDefault ? `
                                <button type="button" class="wsf-theme-card__action wsf-theme-card__action--delete" title="${wp.i18n.__('Delete', 'wheel-size')}">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="3 6 5 6 21 6"></polyline>
                                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                    </svg>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                    <div class="wsf-theme-card__colors">
                        <div class="wsf-theme-card__swatch"
                             style="background-color: ${this.getThemeColorWithFallback(theme, '--wsf-bg', slug)}"
                             data-label="Bg"
                             data-label-full="Background"
                             title="Background: ${this.getThemeColorWithFallback(theme, '--wsf-bg', slug)}"></div>
                        <div class="wsf-theme-card__swatch"
                             style="background-color: ${this.getThemeColorWithFallback(theme, '--wsf-text', slug)}"
                             data-label="Txt"
                             data-label-full="Text"
                             title="Text: ${this.getThemeColorWithFallback(theme, '--wsf-text', slug)}"></div>
                        <div class="wsf-theme-card__swatch"
                             style="background-color: ${this.getThemeColorWithFallback(theme, '--wsf-primary', slug)}"
                             data-label="Prm"
                             data-label-full="Primary"
                             title="Primary: ${this.getThemeColorWithFallback(theme, '--wsf-primary', slug)}"></div>
                    </div>
                </div>
            `;
        }

        async activateTheme(slug) {
            if (this.isLoading || slug === this.activeTheme) return;

            this.setLoading(true);

            try {
                const response = await fetch(wpApiSettings.root + 'wheel-size/v1/themes/active', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': wpApiSettings.nonce
                    },
                    body: JSON.stringify({ slug })
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
                    throw new Error(errorData.message || `HTTP ${response.status}`);
                }

                // Theme activation successful - update UI
                this.activeTheme = slug;
                this.renderThemes();

                // Show notification only if enabled in config
                // Since we have ACTIVE badge, notification is optional but helpful for confirmation
                if (this.config.showActivationNotifications) {
                    this.showNotification(wp.i18n.__('Theme activated successfully', 'wheel-size'), 'success');
                }

                // Apply theme to preview and current page widgets
                this.applyThemeToPreview(this.themes[slug]);
                this.applyThemeToCurrentPageWidgets(this.themes[slug]);

                // Apply theme to preview (non-blocking - don't let preview errors break activation)
                this.waitForPreviewIframe(() => {
                    try {
                        this.applyThemeToPreview(this.themes[slug]);
                    } catch (previewError) {
                        console.warn('Theme activated successfully, but preview update failed:', previewError);
                        // Don't show error notification for preview failures
                    }
                });

            } catch (error) {
                console.error('Failed to activate theme:', error);
                console.log('Available themes:', Object.keys(this.themes));
                console.log('Requested theme slug:', slug);

                // More specific error message
                let errorMessage = wp.i18n.__('Failed to activate theme', 'wheel-size');
                if (error.message.includes('not found')) {
                    errorMessage = wp.i18n.__('Theme not found. Please refresh the page and try again.', 'wheel-size');
                } else if (error.message.includes('HTTP 403') || error.message.includes('HTTP 401')) {
                    errorMessage = wp.i18n.__('Permission denied. Please refresh the page and try again.', 'wheel-size');
                } else if (error.message.includes('HTTP 500')) {
                    errorMessage = wp.i18n.__('Server error. Please check the error logs.', 'wheel-size');
                }

                this.showNotification(errorMessage, 'error');
            } finally {
                this.setLoading(false);
            }
        }

        editTheme(slug) {
            const theme = this.themes[slug];
            if (!theme) return;

            this.editingTheme = { slug, ...theme };
            this.openEditor(theme.name, theme);
        }

        async duplicateTheme(slug) {
            const theme = this.themes[slug];
            if (!theme) return;

            const newName = await this.showPromptDialog(
                wp.i18n.__('Enter name for the duplicated theme:', 'wheel-size'),
                `${theme.name} Copy`,
                wp.i18n.__('Duplicate Theme', 'wheel-size')
            );
            if (!newName) return;

            this.setLoading(true);

            try {
                const properties = { ...theme };
                delete properties.name;

                const response = await $.ajax({
                    url: wpApiSettings.root + 'wheel-size/v1/themes',
                    method: 'POST',
                    data: JSON.stringify({ name: newName, properties }),
                    contentType: 'application/json',
                    beforeSend: function(xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', wpApiSettings.nonce);
                    }
                });

                this.themes[response.slug] = response.theme;
                this.renderThemes();
                this.showNotification(wp.i18n.__('Theme duplicated successfully', 'wheel-size'), 'success');
            } catch (error) {
                console.error('Failed to duplicate theme:', error);
                this.showNotification(wp.i18n.__('Failed to duplicate theme', 'wheel-size'), 'error');
            } finally {
                this.setLoading(false);
            }
        }

        async deleteTheme(slug) {
            const confirmed = await this.showConfirmDialog(
                wp.i18n.__('Are you sure you want to delete this theme?', 'wheel-size'),
                wp.i18n.__('Delete Theme', 'wheel-size')
            );
            if (!confirmed) return;

            this.setLoading(true);

            try {
                await $.ajax({
                    url: wpApiSettings.root + 'wheel-size/v1/themes/' + encodeURIComponent(slug),
                    method: 'DELETE',
                    beforeSend: function(xhr) {
                        xhr.setRequestHeader('X-WP-Nonce', wpApiSettings.nonce);
                    }
                });

                delete this.themes[slug];
                this.renderThemes();
                this.showNotification(wp.i18n.__('Theme deleted successfully', 'wheel-size'), 'success');
            } catch (error) {
                console.error('Failed to delete theme:', error);
                this.showNotification(wp.i18n.__('Failed to delete theme', 'wheel-size'), 'error');
            } finally {
                this.setLoading(false);
            }
        }

        createNewTheme() {
            this.editingTheme = null;
            this.openEditor('', {
                '--wsf-primary': '#2563eb',
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#1f2937',
                '--wsf-border': '#e5e7eb',
                '--wsf-hover': '#1d4ed8',
                '--wsf-secondary': '#6b7280',
                '--wsf-accent': '#3b82f6',
                '--wsf-muted': '#9ca3af',
                '--wsf-surface': '#f9fafb',
                '--wsf-input-bg': '#ffffff',
                '--wsf-input-text': '#1f2937',
                '--wsf-input-border': '#e5e7eb',
                '--wsf-success': '#10b981',
                '--wsf-warning': '#f59e0b',
                '--wsf-error': '#ef4444'
            });
        }

        openEditor(name, properties) {
            const isEditing = !!this.editingTheme;
            const title = isEditing ? wp.i18n.__('Edit Theme', 'wheel-size') : wp.i18n.__('Create New Theme', 'wheel-size');

            const modal = `
                <div class="wsf-theme-overlay wsf-theme-overlay--active"></div>
                <div class="wsf-theme-editor wsf-theme-editor--active">
                    <div class="wsf-theme-editor__header">
                        <h2 class="wsf-theme-editor__title">${title}</h2>
                    </div>
                    <div class="wsf-theme-editor__content">
                        <div class="wsf-theme-editor__field">
                            <label class="wsf-theme-editor__label" for="theme-name">
                                ${wp.i18n.__('Theme Name', 'wheel-size')}
                            </label>
                            <input type="text" id="theme-name" class="wsf-theme-editor__input" value="${name}" placeholder="${wp.i18n.__('My Custom Theme', 'wheel-size')}">
                        </div>
                        <div class="wsf-theme-editor__field">
                            <label class="wsf-theme-editor__label">
                                Colors
                            </label>
                            <div class="wsf-theme-editor__color-grid">
                                ${this.createColorFields(properties)}
                            </div>
                        </div>
                        <div class="wsf-theme-editor__field">
                            ${this.createColorCheatSheet()}
                        </div>
                    </div>
                    <div class="wsf-theme-editor__footer">
                        <button type="button" class="wsf-theme-editor__button wsf-theme-editor__button--cancel">
                            ${wp.i18n.__('Cancel', 'wheel-size')}
                        </button>
                        <button type="button" class="wsf-theme-editor__button wsf-theme-editor__button--primary wsf-theme-editor__button--save">
                            ${isEditing ? wp.i18n.__('Save Changes', 'wheel-size') : wp.i18n.__('Create Theme', 'wheel-size')}
                        </button>
                    </div>
                </div>
            `;

            $('body').append(modal);
        }

        createColorFields(properties) {
            // Use color tokens metadata from PHP
            const colorTokens = window.wsfColorTokens?.tokens || {};

            // Fallback to hardcoded labels if metadata is not available
            const fallbackLabels = {
                '--wsf-primary': 'Primary Color',
                '--wsf-bg': 'Background',
                '--wsf-text': 'Text Color',
                '--wsf-border': 'Border Color',
                '--wsf-hover': 'Hover State',
                '--wsf-secondary': 'Secondary',
                '--wsf-accent': 'Accent',
                '--wsf-muted': 'Muted',
                // Surface & Input tokens
                '--wsf-surface': 'Surface',
                '--wsf-input-bg': 'Input Background',
                '--wsf-input-text': 'Input Text',
                '--wsf-input-border': 'Input Border',
                // State colors
                '--wsf-success': 'Success',
                '--wsf-warning': 'Warning',
                '--wsf-error': 'Error'
            };

            const fieldsToRender = Object.keys(colorTokens).length > 0 ? colorTokens : fallbackLabels;

            return Object.entries(fieldsToRender).map(([property, tokenData]) => {
                const label = typeof tokenData === 'object' ? tokenData.label : tokenData;
                const help = typeof tokenData === 'object' ? tokenData.help : '';
                const currentValue = properties[property] || '#000000';

                // Add contrast indicator for Primary color
                let contrastIndicator = '';
                if (property === '--wsf-primary' && properties['--wsf-bg']) {
                    contrastIndicator = this.createContrastIndicator(currentValue, properties['--wsf-bg']);
                }

                return `
                    <div class="wsf-theme-editor__color-field">
                        <input type="color"
                               class="wsf-theme-editor__color-input"
                               data-property="${property}"
                               value="${currentValue}">
                        <div class="wsf-theme-editor__color-label-wrapper">
                            <label class="wsf-theme-editor__color-label">${label}</label>
                            ${help ? `
                                <span class="wsf-theme-editor__help-icon" title="${help}">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                    </svg>
                                </span>
                            ` : ''}
                            ${contrastIndicator}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Color contrast calculation functions
        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        getLuminance(r, g, b) {
            const [rs, gs, bs] = [r, g, b].map(c => {
                c = c / 255;
                return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
            });
            return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
        }

        getContrastRatio(color1, color2) {
            const rgb1 = this.hexToRgb(color1);
            const rgb2 = this.hexToRgb(color2);

            if (!rgb1 || !rgb2) return 1;

            const lum1 = this.getLuminance(rgb1.r, rgb1.g, rgb1.b);
            const lum2 = this.getLuminance(rgb2.r, rgb2.g, rgb2.b);

            const brightest = Math.max(lum1, lum2);
            const darkest = Math.min(lum1, lum2);

            return (brightest + 0.05) / (darkest + 0.05);
        }

        createContrastIndicator(primaryColor, backgroundColor) {
            const ratio = this.getContrastRatio(primaryColor, backgroundColor);
            const isGood = ratio >= 4.5;
            const level = ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : 'Fail';

            return `
                <div class="wsf-contrast-indicator ${isGood ? 'wsf-contrast-indicator--good' : 'wsf-contrast-indicator--poor'}"
                     title="Contrast ratio: ${ratio.toFixed(2)}:1 (WCAG ${level})">
                    <span class="wsf-contrast-indicator__icon">${isGood ? '✓' : '⚠'}</span>
                    <span class="wsf-contrast-indicator__text">${level}</span>
                </div>
            `;
        }

        getCheatSheetState() {
            // Always start collapsed when opening editor
            return false;
        }

        setCheatSheetState(expanded) {
            // Don't persist state - always start collapsed on new editor open
            // This ensures Color Tokens Reference is always closed by default
        }

        createColorCheatSheet() {
            const colorTokens = window.wsfColorTokens?.tokens || {};
            const examples = window.wsfColorTokens?.examples || {};

            if (Object.keys(colorTokens).length === 0) {
                return '<p class="wsf-cheat-sheet__empty">Color tokens metadata not available</p>';
            }

            const rows = Object.entries(colorTokens).map(([property, tokenData]) => {
                const vars = tokenData.vars || [property];
                const example = examples[tokenData.key] || 'Various elements';

                // Create individual copy buttons for each variable
                const varButtons = vars.map(varName => `
                    <div class="wsf-cheat-sheet__var-item">
                        <code class="wsf-cheat-sheet__code">${varName}</code>
                        <button type="button" class="wsf-cheat-sheet__copy" data-copy="${varName}" title="Copy ${varName}">
                            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                        </button>
                    </div>
                `).join('');

                return `
                    <tr class="wsf-cheat-sheet__row">
                        <td class="wsf-cheat-sheet__token">${tokenData.label}</td>
                        <td class="wsf-cheat-sheet__description">${tokenData.help}</td>
                        <td class="wsf-cheat-sheet__vars">
                            ${varButtons}
                        </td>
                        <td class="wsf-cheat-sheet__example">${example}</td>
                    </tr>
                `;
            }).join('');

            const isExpanded = this.getCheatSheetState();
            const toggleText = isExpanded ? 'Hide Details' : 'Show Details';
            const contentStyle = isExpanded ? 'display: block;' : 'display: none;';
            const iconTransform = isExpanded ? 'transform: rotate(180deg);' : 'transform: rotate(0deg);';

            return `
                <div class="wsf-cheat-sheet">
                    <div class="wsf-cheat-sheet__header">
                        <h4 class="wsf-cheat-sheet__title">🎨 Color Tokens Reference</h4>
                        <button type="button" class="wsf-cheat-sheet__toggle" data-toggle="cheat-sheet">
                            <span class="wsf-cheat-sheet__toggle-text">${toggleText}</span>
                            <svg class="wsf-cheat-sheet__toggle-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="${iconTransform}">
                                <polyline points="6,9 12,15 18,9"></polyline>
                            </svg>
                        </button>
                    </div>
                    <div class="wsf-cheat-sheet__content" style="${contentStyle}">
                        <div class="wsf-cheat-sheet__table-wrapper">
                            <table class="wsf-cheat-sheet__table">
                                <thead>
                                    <tr>
                                        <th>Token</th>
                                        <th>Description</th>
                                        <th>CSS Variables</th>
                                        <th>Examples</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${rows}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        showCopyFeedback(button) {
            const originalTitle = button.title;
            button.title = 'Copied!';
            $(button).addClass('wsf-cheat-sheet__copy--copied');

            setTimeout(() => {
                button.title = originalTitle;
                $(button).removeClass('wsf-cheat-sheet__copy--copied');
            }, 1500);
        }

        closeEditor() {
            $('.wsf-theme-overlay, .wsf-theme-editor').remove();
            // Reset preview if needed
            if (this.activeTheme) {
                try {
                    this.applyThemeToPreview(this.themes[this.activeTheme]);
                } catch (error) {
                    console.warn('Failed to reset preview after closing editor:', error);
                }
            }
        }

        async saveThemeFromEditor() {
            const name = $('#theme-name').val().trim();
            if (!name) {
                alert(wp.i18n.__('Please enter a theme name', 'wheel-size'));
                return;
            }

            const properties = {};
            $('.wsf-theme-editor__color-input').each(function() {
                properties[$(this).data('property')] = $(this).val();
            });

            this.setLoading(true);

            try {
                if (this.editingTheme) {
                    // Update existing theme
                    const response = await fetch(wpApiSettings.root + 'wheel-size/v1/themes/' + encodeURIComponent(this.editingTheme.slug), {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': wpApiSettings.nonce
                        },
                        body: JSON.stringify({ name, properties })
                    });

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
                        throw new Error(errorData.message || `HTTP ${response.status}`);
                    }

                    this.themes[this.editingTheme.slug] = { name, ...properties };
                    this.showNotification(wp.i18n.__('Theme updated successfully', 'wheel-size'), 'success');
                } else {
                    // Create new theme with optional slug suggestion
                    const suggestedSlug = this.generateSlugSuggestion(name);
                    const requestData = { name, properties };

                    // Add suggested slug if it's different from name and looks valid
                    if (suggestedSlug && suggestedSlug !== name && this.isValidSlug(suggestedSlug)) {
                        requestData.suggested_slug = suggestedSlug;
                    }

                    const response = await $.ajax({
                        url: wpApiSettings.root + 'wheel-size/v1/themes',
                        method: 'POST',
                        data: JSON.stringify(requestData),
                        contentType: 'application/json',
                        beforeSend: function(xhr) {
                            xhr.setRequestHeader('X-WP-Nonce', wpApiSettings.nonce);
                        }
                    });

                    this.themes[response.slug] = response.theme;
                    this.showNotification(wp.i18n.__('Theme created successfully', 'wheel-size'), 'success');
                }

                this.renderThemes();
                this.closeEditor();
            } catch (error) {
                console.error('Failed to save theme:', error);
                this.showNotification(wp.i18n.__('Failed to save theme', 'wheel-size'), 'error');
            } finally {
                this.setLoading(false);
            }
        }

        /**
         * Generate a slug suggestion from theme name (client-side)
         * This is optional and the server will always validate/regenerate if needed
         */
        generateSlugSuggestion(name) {
            if (!name || typeof name !== 'string') {
                return '';
            }

            let slug = name.toLowerCase().trim();

            // Basic transliteration for common characters
            const transliterations = {
                // Cyrillic
                'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd',
                'е': 'e', 'ё': 'yo', 'ж': 'zh', 'з': 'z', 'и': 'i',
                'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n',
                'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't',
                'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts', 'ч': 'ch',
                'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '',
                'э': 'e', 'ю': 'yu', 'я': 'ya',
                // German
                'ä': 'ae', 'ö': 'oe', 'ü': 'ue', 'ß': 'ss',
                // French/Spanish
                'à': 'a', 'á': 'a', 'â': 'a', 'ã': 'a', 'ç': 'c',
                'è': 'e', 'é': 'e', 'ê': 'e', 'ë': 'e',
                'ì': 'i', 'í': 'i', 'î': 'i', 'ï': 'i',
                'ñ': 'n', 'ò': 'o', 'ó': 'o', 'ô': 'o', 'õ': 'o',
                'ù': 'u', 'ú': 'u', 'û': 'u', 'ü': 'u',
                'ý': 'y', 'ÿ': 'y'
            };

            // Apply transliterations
            for (const [char, replacement] of Object.entries(transliterations)) {
                slug = slug.replace(new RegExp(char, 'g'), replacement);
            }

            // Normalize unicode and remove diacritics
            slug = slug.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

            // Replace non-alphanumeric characters with hyphens
            slug = slug.replace(/[^a-z0-9]+/g, '-');

            // Remove leading/trailing hyphens and multiple consecutive hyphens
            slug = slug.replace(/^-+|-+$/g, '').replace(/-+/g, '-');

            return slug || 'theme';
        }

        /**
         * Validate if a slug looks valid (client-side check)
         */
        isValidSlug(slug) {
            return typeof slug === 'string' &&
                   slug.length > 0 &&
                   slug.length <= 100 &&
                   /^[a-zA-Z0-9\-]+$/.test(slug) &&
                   !slug.startsWith('-') &&
                   !slug.endsWith('-') &&
                   !slug.includes('--');
        }

        previewColorChange(input) {
            const property = $(input).data('property');
            const value = $(input).val();

            // Apply to preview iframe
            const previewFrame = document.getElementById('widget-preview');
            if (previewFrame) {
                const previewDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                previewDoc.documentElement.style.setProperty(property, value);
            }

            // Update contrast indicator if this is primary or background color
            this.updateContrastIndicator(property, value);
        }

        updateContrastIndicator(changedProperty, newValue) {
            const primaryInput = document.querySelector('[data-property="--wsf-primary"]');
            const backgroundInput = document.querySelector('[data-property="--wsf-bg"]');

            if (!primaryInput || !backgroundInput) return;

            const primaryColor = changedProperty === '--wsf-primary' ? newValue : primaryInput.value;
            const backgroundColor = changedProperty === '--wsf-bg' ? newValue : backgroundInput.value;

            // Find and update the contrast indicator
            const primaryField = primaryInput.closest('.wsf-theme-editor__color-field');
            const existingIndicator = primaryField.querySelector('.wsf-contrast-indicator');

            if (existingIndicator) {
                const newIndicator = this.createContrastIndicator(primaryColor, backgroundColor);
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newIndicator;
                existingIndicator.replaceWith(tempDiv.firstElementChild);
            }
        }

        applyThemeToPreview(theme) {
            try {
                // Look for preview container (div, not iframe in our case)
                const previewContainer = document.getElementById('widget-preview');
                if (!previewContainer) {
                    console.warn('[Theme Panel] Preview container not found - skipping preview update');
                    return;
                }

                // Check if it's an iframe or direct container
                if (previewContainer.tagName.toLowerCase() === 'iframe') {
                    // Handle iframe case
                    this.applyThemeToIframe(previewContainer, theme);
                } else {
                    // Handle direct container case (our current setup)
                    this.applyThemeToContainer(previewContainer, theme);
                }

                // Additional fallback: try to apply theme to any widget in the current page
                this.applyThemeToCurrentPageWidgets(theme);

            } catch (error) {
                console.error('[Theme Panel] Error in applyThemeToPreview:', error);
                // Don't throw the error - preview failure shouldn't break theme activation
            }
        }

        applyThemeToContainer(container, theme) {
            try {
                // Apply theme CSS variables directly to the container
                Object.entries(theme).forEach(([property, value]) => {
                    if (property.startsWith('--wsf-')) {
                        container.style.setProperty(property, value);
                    }
                });
                console.log('[Theme Panel] Applied theme properties to preview container');
            } catch (error) {
                console.warn('[Theme Panel] Failed to apply theme to container:', error.message);
            }
        }

        applyThemeToIframe(iframe, theme) {
            try {
                // Check if iframe is loaded and accessible
                let previewDoc = null;
                try {
                    previewDoc = iframe.contentDocument ||
                                (iframe.contentWindow && iframe.contentWindow.document);
                } catch (e) {
                    console.warn('[Theme Panel] Cannot access iframe document (cross-origin or not loaded):', e.message);
                }

                // Apply theme properties to iframe document if accessible
                if (previewDoc && previewDoc.documentElement) {
                    try {
                        Object.entries(theme).forEach(([property, value]) => {
                            if (property.startsWith('--wsf-')) {
                                previewDoc.documentElement.style.setProperty(property, value);
                            }
                        });
                        console.log('[Theme Panel] Applied theme properties to preview iframe');
                    } catch (e) {
                        console.warn('[Theme Panel] Failed to apply CSS properties to iframe:', e.message);
                    }
                }

                // Try postMessage as fallback
                if (iframe.contentWindow) {
                    try {
                        iframe.contentWindow.postMessage({
                            type: 'theme-update',
                            theme: theme
                        }, '*');
                        console.log('[Theme Panel] Sent theme update via postMessage');
                    } catch (e) {
                        console.warn('[Theme Panel] Failed to send postMessage:', e.message);
                    }
                }
            } catch (error) {
                console.warn('[Theme Panel] Failed to apply theme to iframe:', error.message);
            }
        }

        applyThemeToCurrentPageWidgets(theme) {
            try {
                // Find any widgets on the current page and apply theme
                const widgets = document.querySelectorAll('.wsf-finder-widget, .wheel-fit-widget');
                if (widgets.length > 0) {
                    widgets.forEach(widget => {
                        Object.entries(theme).forEach(([property, value]) => {
                            if (property.startsWith('--wsf-')) {
                                widget.style.setProperty(property, value);
                            }
                        });
                    });
                    console.log(`[Theme Panel] Applied theme to ${widgets.length} widget(s) on current page`);
                }
            } catch (error) {
                console.warn('[Theme Panel] Failed to apply theme to current page widgets:', error.message);
            }
        }

        applyThemeToAdminPanel(theme) {
            try {
                // Apply theme background to admin panel (minimal integration)
                if (theme && theme['--wsf-bg']) {
                    document.documentElement.style.setProperty('--wsf-admin-panel-bg', theme['--wsf-bg']);
                    console.log('[Theme Panel] Applied theme background to admin panel:', theme['--wsf-bg']);
                }
            } catch (error) {
                console.warn('[Theme Panel] Failed to apply theme to admin panel:', error.message);
            }
        }

        isPreviewIframeReady() {
            try {
                const previewFrame = document.getElementById('widget-preview');
                if (!previewFrame) {
                    return false;
                }

                // Check if iframe is loaded
                if (previewFrame.contentDocument === null && previewFrame.contentWindow === null) {
                    return false;
                }

                // Check if we can access the document
                const doc = previewFrame.contentDocument ||
                           (previewFrame.contentWindow && previewFrame.contentWindow.document);

                if (!doc || !doc.documentElement) {
                    return false;
                }

                // Check if document is fully loaded
                if (doc.readyState !== 'complete') {
                    return false;
                }

                return true;
            } catch (error) {
                console.warn('[Theme Panel] Cannot determine iframe readiness:', error.message);
                return false;
            }
        }

        waitForPreviewIframe(callback, timeout = 5000) {
            const startTime = Date.now();

            const checkReady = () => {
                if (this.isPreviewIframeReady()) {
                    callback();
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    console.warn('[Theme Panel] Timeout waiting for preview iframe to be ready');
                    callback(); // Call anyway, let applyThemeToPreview handle the error
                    return;
                }

                setTimeout(checkReady, 100);
            };

            checkReady();
        }

        setLoading(loading) {
            this.isLoading = loading;
            $('.wsf-theme-panel').toggleClass('wsf-theme-panel--loading', loading);
        }

        showNotification(message, type = 'info') {
            // Use WordPress admin notices if available
            if (window.wp && window.wp.data && window.wp.data.dispatch) {
                window.wp.data.dispatch('core/notices').createNotice(
                    type === 'error' ? 'error' : 'success',
                    message,
                    {
                        isDismissible: true,
                        type: 'snackbar'
                    }
                );
            } else {
                // Fallback to modern styled notification instead of alert
                this.showStyledNotification(message, type);
            }
        }

        showStyledNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `wsf-notification wsf-notification--${type}`;
            notification.textContent = message;

            // Add to DOM
            document.body.appendChild(notification);

            // Show notification with animation
            requestAnimationFrame(() => {
                notification.classList.add('wsf-notification--show');
            });

            // Auto-hide after configured duration
            setTimeout(() => {
                notification.classList.remove('wsf-notification--show');

                // Remove from DOM after animation completes
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300); // Match CSS transition duration
            }, this.config.notificationDuration);
        }

        // Modern modal dialogs to replace alert/confirm/prompt
        showConfirmDialog(message, title = 'Confirm') {
            return new Promise((resolve) => {
                const overlay = document.createElement('div');
                overlay.className = 'wsf-overlay wsf-overlay--active';

                const modal = document.createElement('div');
                modal.className = 'wsf-modal wsf-modal--confirm';
                modal.innerHTML = `
                    <div class="wsf-modal__header">
                        <h3 class="wsf-modal__title">${title}</h3>
                    </div>
                    <div class="wsf-modal__body">
                        <p class="wsf-modal__message">${message}</p>
                    </div>
                    <div class="wsf-modal__footer">
                        <button class="wsf-modal__button wsf-modal__button--secondary" data-action="cancel">Cancel</button>
                        <button class="wsf-modal__button wsf-modal__button--danger" data-action="confirm">Delete</button>
                    </div>
                `;

                overlay.appendChild(modal);
                document.body.appendChild(overlay);

                // Handle button clicks
                modal.addEventListener('click', (e) => {
                    const btn = e.target.closest('[data-action]'); // Ищем ближайший элемент с data-action
                    if (!btn) return;

                    const action = btn.dataset.action;
                    if (action === 'confirm') {
                        resolve(true);
                        this.closeModal(overlay);
                    } else if (action === 'cancel') {
                        resolve(false);
                        this.closeModal(overlay);
                    }
                });

                // Close on overlay click
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        resolve(false);
                        this.closeModal(overlay);
                    }
                });

                // Close on Escape key
                const handleEscape = (e) => {
                    if (e.key === 'Escape') {
                        resolve(false);
                        this.closeModal(overlay);
                        document.removeEventListener('keydown', handleEscape);
                    }
                };
                document.addEventListener('keydown', handleEscape);
            });
        }

        showPromptDialog(message, defaultValue = '', title = 'Enter Value') {
            return new Promise((resolve) => {
                const overlay = document.createElement('div');
                overlay.className = 'wsf-overlay wsf-overlay--active';

                const modal = document.createElement('div');
                modal.className = 'wsf-modal wsf-modal--prompt';
                modal.innerHTML = `
                    <div class="wsf-modal__header">
                        <h3 class="wsf-modal__title">${title}</h3>
                    </div>
                    <div class="wsf-modal__body">
                        <p class="wsf-modal__message">${message}</p>
                        <input type="text" class="wsf-modal__input" value="${defaultValue}" placeholder="Enter name...">
                    </div>
                    <div class="wsf-modal__footer">
                        <button class="wsf-modal__button wsf-modal__button--secondary" data-action="cancel">Cancel</button>
                        <button class="wsf-modal__button wsf-modal__button--primary" data-action="confirm">Create</button>
                    </div>
                `;

                overlay.appendChild(modal);
                document.body.appendChild(overlay);

                const input = modal.querySelector('.wsf-modal__input');
                input.focus();
                input.select();

                // Handle button clicks
                modal.addEventListener('click', (e) => {
                    const btn = e.target.closest('[data-action]'); // Ищем ближайший элемент с data-action
                    if (!btn) return;

                    const action = btn.dataset.action;
                    if (action === 'confirm') {
                        const value = input.value.trim();
                        if (value) {
                            resolve(value);
                            this.closeModal(overlay);
                        } else {
                            input.focus();
                        }
                    } else if (action === 'cancel') {
                        resolve(null);
                        this.closeModal(overlay);
                    }
                });

                // Handle Enter key
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        const value = input.value.trim();
                        if (value) {
                            resolve(value);
                            this.closeModal(overlay);
                        }
                    }
                });

                // Close on overlay click
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        resolve(null);
                        this.closeModal(overlay);
                    }
                });

                // Close on Escape key
                const handleEscape = (e) => {
                    if (e.key === 'Escape') {
                        resolve(null);
                        this.closeModal(overlay);
                        document.removeEventListener('keydown', handleEscape);
                    }
                };
                document.addEventListener('keydown', handleEscape);
            });
        }

        closeModal(overlay) {
            overlay.classList.remove('wsf-overlay--active');
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }
    }

    // Initialize when DOM is ready
    $(document).ready(() => {
        // Check if feature flag is enabled
        if (typeof WSF_THEME_PRESETS !== 'undefined' && WSF_THEME_PRESETS) {
            new ThemePresetsPanel();
        }
    });

})(jQuery); 