/**
 * Тест правильного порядка инициализации и применения переводов
 */

(function() {
    'use strict';

    console.log('[Order Test] Инициализация теста правильного порядка...');

    // Тестовые переводы
    const testTranslations = {
        ru: {
            'select_make_placeholder': 'Выберите бренд',
            'select_model_placeholder': 'Выберите модель',
            'select_year_placeholder': 'Выберите год',
            'select_mods_placeholder': 'Выберите модификацию',
            'select_gen_placeholder': 'Выберите поколение',
            'loading_makes': 'Загрузка брендов...',
            'loading_models': 'Загрузка моделей...',
            'label_make': 'Марка',
            'label_model': 'Модель',
            'button_search': 'Подобрать размеры'
        }
    };

    // Функция мониторинга изменений
    function monitorChanges() {
        console.log('[Order Test] === Запуск мониторинга изменений ===');
        
        const events = [];
        
        // Мониторим изменения в селекторах
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.target.tagName === 'SELECT') {
                    const select = mutation.target;
                    const placeholderOption = select.querySelector('option[value=""]');
                    if (placeholderOption) {
                        events.push({
                            timestamp: Date.now(),
                            type: 'selector_change',
                            selectId: select.id,
                            text: placeholderOption.textContent.trim(),
                            dataI18n: placeholderOption.getAttribute('data-i18n'),
                            source: 'mutation_observer'
                        });
                        console.log(`[Order Test] Селектор изменен: ${select.id} → "${placeholderOption.textContent.trim()}" (data-i18n: ${placeholderOption.getAttribute('data-i18n')})`);
                    }
                }
            });
        });
        
        // Наблюдаем за изменениями в контейнере виджета
        const container = document.getElementById('widget-preview');
        if (container) {
            observer.observe(container, {
                childList: true,
                subtree: true
            });
            console.log('[Order Test] Мониторинг запущен для контейнера виджета');
        }
        
        return { events, stop: () => observer.disconnect() };
    }

    // Функция проверки текущего состояния
    function checkCurrentState() {
        console.log('[Order Test] === Проверка текущего состояния ===');
        
        const selectors = ['wf-make', 'wf-model', 'wf-year', 'wf-modification', 'wf-generation'];
        const state = {};
        
        selectors.forEach(id => {
            const select = document.getElementById(id);
            if (select) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    state[id] = {
                        text: placeholderOption.textContent.trim(),
                        dataI18n: placeholderOption.getAttribute('data-i18n'),
                        isRussian: placeholderOption.textContent.includes('Выберите') || placeholderOption.textContent.includes('Загрузка'),
                        isEnglish: placeholderOption.textContent.includes('Choose') || placeholderOption.textContent.includes('Loading')
                    };
                    
                    const status = state[id].isRussian ? '✅ RU' : state[id].isEnglish ? '❌ EN' : '⚠️ OTHER';
                    console.log(`[Order Test] ${id}: ${status} "${state[id].text}" (data-i18n: ${state[id].dataI18n})`);
                }
            }
        });
        
        return state;
    }

    // Функция симуляции изменения настроек
    function simulateSettingsChange() {
        console.log('[Order Test] === Симуляция изменения настроек ===');
        
        // Устанавливаем русские переводы
        window.WheelFitI18n = testTranslations.ru;
        console.log('[Order Test] Установлены русские переводы');
        
        // Проверяем начальное состояние
        console.log('[Order Test] Начальное состояние:');
        const initialState = checkCurrentState();
        
        // Симулируем изменение Search Flow
        const searchFlowSelect = document.getElementById('search_flow');
        if (searchFlowSelect) {
            console.log('[Order Test] Симулируем изменение Search Flow...');
            const originalValue = searchFlowSelect.value;
            const options = Array.from(searchFlowSelect.options).map(opt => opt.value).filter(val => val !== originalValue);
            
            if (options.length > 0) {
                searchFlowSelect.value = options[0];
                searchFlowSelect.dispatchEvent(new Event('change', { bubbles: true }));
                
                // Проверяем состояние через 3 секунды
                setTimeout(() => {
                    console.log('[Order Test] Состояние после изменения Search Flow:');
                    const afterState = checkCurrentState();
                    
                    // Анализируем изменения
                    let russianCount = 0;
                    let englishCount = 0;
                    
                    Object.values(afterState).forEach(state => {
                        if (state.isRussian) russianCount++;
                        if (state.isEnglish) englishCount++;
                    });
                    
                    console.log(`[Order Test] Результат: ${russianCount} русских, ${englishCount} английских`);
                    
                    if (russianCount > englishCount) {
                        console.log('✅ [Order Test] УСПЕХ: Большинство селекторов на русском');
                    } else if (englishCount > russianCount) {
                        console.error('❌ [Order Test] ОШИБКА: Большинство селекторов на английском');
                    } else {
                        console.warn('⚠️ [Order Test] СМЕШАННЫЙ РЕЗУЛЬТАТ');
                    }
                    
                    // Возвращаем исходное значение
                    searchFlowSelect.value = originalValue;
                    searchFlowSelect.dispatchEvent(new Event('change', { bubbles: true }));
                }, 3000);
            }
        }
    }

    // Функция проверки порядка событий
    function checkEventOrder() {
        console.log('[Order Test] === Проверка порядка событий ===');
        
        // Перехватываем ключевые функции
        const originalLog = console.log;
        const events = [];
        
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('[Admin Preview]') || message.includes('WheelFitWidget') || message.includes('Translation')) {
                events.push({
                    timestamp: Date.now(),
                    message: message,
                    type: 'log'
                });
            }
            originalLog.apply(console, args);
        };
        
        // Восстанавливаем через 10 секунд
        setTimeout(() => {
            console.log = originalLog;
            console.log('[Order Test] События в порядке выполнения:');
            events.forEach((event, index) => {
                console.log(`${index + 1}. ${event.message}`);
            });
        }, 10000);
    }

    // Основная функция тестирования
    function runOrderTest() {
        console.log('[Order Test] 🚀 ЗАПУСК ТЕСТА ПРАВИЛЬНОГО ПОРЯДКА');
        
        // 1. Запускаем мониторинг
        console.log('[Order Test] 1. Запуск мониторинга...');
        const monitor = monitorChanges();
        
        // 2. Проверяем текущее состояние
        console.log('[Order Test] 2. Проверка текущего состояния...');
        checkCurrentState();
        
        // 3. Проверяем порядок событий
        console.log('[Order Test] 3. Проверка порядка событий...');
        checkEventOrder();
        
        // 4. Симулируем изменения
        setTimeout(() => {
            console.log('[Order Test] 4. Симуляция изменений...');
            simulateSettingsChange();
        }, 2000);
        
        // 5. Финальная проверка
        setTimeout(() => {
            console.log('[Order Test] 5. Финальная проверка...');
            checkCurrentState();
            monitor.stop();
            console.log('[Order Test] ✅ ТЕСТ ЗАВЕРШЕН');
        }, 15000);
    }

    // Глобальные функции для ручного тестирования
    window.testCorrectOrder = {
        runTest: runOrderTest,
        checkState: checkCurrentState,
        simulate: simulateSettingsChange,
        monitor: monitorChanges
    };

    // Автоматический запуск через 2 секунды
    setTimeout(() => {
        console.log('[Order Test] Автоматический запуск теста...');
        runOrderTest();
    }, 2000);

    console.log('[Order Test] Тест загружен. Доступные функции:');
    console.log('- testCorrectOrder.runTest() - полный тест');
    console.log('- testCorrectOrder.checkState() - проверка состояния');
    console.log('- testCorrectOrder.simulate() - симуляция изменений');

})();
