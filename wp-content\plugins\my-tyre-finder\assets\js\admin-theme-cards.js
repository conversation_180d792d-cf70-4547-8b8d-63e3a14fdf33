/**
 * Advanced Theme Cards Interactive System
 * Features: Preview, AJAX, Animations, Drag & Drop
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        ANIMATION_DURATION: 300,
        PREVIEW_DELAY: 500,
        NOTIFICATION_DURATION: 3000,
        AJAX_TIMEOUT: 10000
    };

    // State management
    const state = {
        activeTheme: null,
        previewTimeout: null,
        draggedCard: null,
        notifications: []
    };

    /**
     * Calculate optimal icon offset based on theme name length
     * @param {string} themeName - The theme name text
     * @returns {number} Offset in pixels (0 or 16)
     */
    function calculateIconOffset(themeName) {
        return themeName.length > 14 ? 16 : 0;
    }

    /**
     * Apply dynamic positioning to theme card actions
     * @param {HTMLElement} card - Theme card element
     */
    function adjustCardActions(card) {
        const nameElement = card.querySelector('.wsf-theme-card__name');
        const actionsElement = card.querySelector('.wsf-theme-card__actions');

        if (!nameElement || !actionsElement) return;

        const themeName = nameElement.textContent.trim();
        const offset = calculateIconOffset(themeName);

        if (offset > 0) {
            actionsElement.style.right = `calc(var(--wsf-card-padding) + ${offset}px)`;
            actionsElement.setAttribute('data-offset', offset);
        } else {
            actionsElement.style.right = 'var(--wsf-card-padding)';
            actionsElement.removeAttribute('data-offset');
        }
    }

    /**
     * Show theme preview modal
     * @param {HTMLElement} card - Theme card element
     */
    function showPreview(card) {
        const themeId = card.dataset.themeId;
        const themeName = card.querySelector('.wsf-theme-card__name').textContent;

        // Create preview modal
        const modal = createPreviewModal(themeId, themeName);
        document.body.appendChild(modal);

        // Show with animation
        requestAnimationFrame(() => {
            modal.classList.add('wsf-theme-preview--active');
        });

        // Load preview content
        loadPreviewContent(modal, themeId);
    }

    /**
     * Create preview modal element
     * @param {string} themeId - Theme ID
     * @param {string} themeName - Theme name
     * @returns {HTMLElement} Modal element
     */
    function createPreviewModal(themeId, themeName) {
        const modal = document.createElement('div');
        modal.className = 'wsf-theme-preview';
        modal.innerHTML = `
            <div class="wsf-theme-preview__header">
                <h3 class="wsf-theme-preview__title">Preview: ${themeName}</h3>
                <button class="wsf-theme-preview__close" data-action="close-preview">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="wsf-theme-preview__content">
                <div class="wsf-theme-preview__widget">
                    <div class="wsf-loading">Loading preview...</div>
                </div>
            </div>
            <div class="wsf-theme-preview__actions">
                <button class="wsf-theme-preview__button wsf-theme-preview__button--secondary" data-action="close-preview">
                    Cancel
                </button>
                <button class="wsf-theme-preview__button wsf-theme-preview__button--primary" data-action="apply-theme" data-theme-id="${themeId}">
                    Apply Theme
                </button>
            </div>
        `;

        return modal;
    }

    /**
     * Load preview content via AJAX
     * @param {HTMLElement} modal - Modal element
     * @param {string} themeId - Theme ID
     */
    function loadPreviewContent(modal, themeId) {
        const contentArea = modal.querySelector('.wsf-theme-preview__widget');

        // Simulate AJAX call (replace with actual endpoint)
        setTimeout(() => {
            contentArea.innerHTML = `
                <div style="padding: 20px; text-align: left;">
                    <div style="margin-bottom: 12px; font-weight: 600;">Widget Preview</div>
                    <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                        <div style="width: 20px; height: 20px; background: #3b82f6; border-radius: 4px;"></div>
                        <div style="width: 20px; height: 20px; background: #10b981; border-radius: 4px;"></div>
                        <div style="width: 20px; height: 20px; background: #f59e0b; border-radius: 4px;"></div>
                    </div>
                    <div style="font-size: 12px; color: #6b7280;">
                        This is how your widget will look with the "${themeId}" theme applied.
                    </div>
                </div>
            `;
        }, 800);
    }

    /**
     * Handle dropdown menu interactions
     * @param {HTMLElement} dropdown - Dropdown element
     */
    function initDropdown(dropdown) {
        const toggle = dropdown.querySelector('.wsf-theme-card__dropdown-toggle');
        const menu = dropdown.querySelector('.wsf-theme-card__dropdown-menu');

        if (!toggle || !menu) return;

        toggle.addEventListener('click', (e) => {
            e.stopPropagation();
            closeAllDropdowns();
            dropdown.classList.toggle('wsf-theme-card__dropdown--open');
        });

        // Handle menu item clicks
        menu.addEventListener('click', (e) => {
            const item = e.target.closest('.wsf-theme-card__dropdown-item');
            if (!item) return;

            const action = item.dataset.action;
            const card = dropdown.closest('.wsf-theme-card');

            handleDropdownAction(action, card);
            closeAllDropdowns();
        });
    }

    /**
     * Close all open dropdowns
     */
    function closeAllDropdowns() {
        document.querySelectorAll('.wsf-theme-card__dropdown--open').forEach(dropdown => {
            dropdown.classList.remove('wsf-theme-card__dropdown--open');
        });
    }

    /**
     * Handle dropdown action
     * @param {string} action - Action type
     * @param {HTMLElement} card - Theme card element
     */
    function handleDropdownAction(action, card) {
        const themeId = card.dataset.themeId;

        switch (action) {
            case 'edit':
                editTheme(themeId);
                break;
            case 'duplicate':
                duplicateTheme(themeId);
                break;
            case 'delete':
                deleteTheme(themeId, card);
                break;
        }
    }

    /**
     * Apply theme via AJAX
     * @param {string} themeId - Theme ID to apply
     */
    function applyTheme(themeId) {
        const card = document.querySelector(`[data-theme-id="${themeId}"]`);
        if (!card) return;

        // Add loading state
        card.classList.add('wsf-loading');

        // Simulate AJAX call
        const formData = new FormData();
        formData.append('action', 'wsf_apply_theme');
        formData.append('theme_id', themeId);
        formData.append('nonce', window.wsfAdmin?.nonce || '');

        fetch(window.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update active state
                updateActiveTheme(themeId);
                showNotification('Theme applied successfully!', 'success');

                // Trigger selection animation
                card.classList.add('wsf-theme-card--selecting');
                setTimeout(() => {
                    card.classList.remove('wsf-theme-card--selecting');
                }, CONFIG.ANIMATION_DURATION);
            } else {
                showNotification(data.data || 'Failed to apply theme', 'error');
            }
        })
        .catch(error => {
            console.error('Theme application error:', error);
            showNotification('Network error occurred', 'error');
        })
        .finally(() => {
            card.classList.remove('wsf-loading');
        });
    }

    /**
     * Update active theme state
     * @param {string} newThemeId - New active theme ID
     */
    function updateActiveTheme(newThemeId) {
        // Remove active state from all cards
        document.querySelectorAll('.wsf-theme-card--active').forEach(card => {
            card.classList.remove('wsf-theme-card--active');
        });

        // Add active state to new theme
        const newActiveCard = document.querySelector(`[data-theme-id="${newThemeId}"]`);
        if (newActiveCard) {
            newActiveCard.classList.add('wsf-theme-card--active');
            state.activeTheme = newThemeId;
        }
    }

    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, warning, error)
     */
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `wsf-notification wsf-notification--${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Show notification
        requestAnimationFrame(() => {
            notification.classList.add('wsf-notification--show');
        });

        // Auto-hide after delay
        setTimeout(() => {
            notification.classList.remove('wsf-notification--show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, CONFIG.ANIMATION_DURATION);
        }, CONFIG.NOTIFICATION_DURATION);

        state.notifications.push(notification);
    }

    /**
     * Edit theme
     * @param {string} themeId - Theme ID to edit
     */
    function editTheme(themeId) {
        showNotification(`Opening editor for theme: ${themeId}`, 'success');
        // TODO: Implement theme editor modal
    }

    /**
     * Duplicate theme
     * @param {string} themeId - Theme ID to duplicate
     */
    function duplicateTheme(themeId) {
        showNotification(`Duplicating theme: ${themeId}`, 'success');
        // TODO: Implement theme duplication
    }

    /**
     * Delete theme
     * @param {string} themeId - Theme ID to delete
     * @param {HTMLElement} card - Theme card element
     */
    function deleteTheme(themeId, card) {
        if (!confirm('Are you sure you want to delete this theme?')) {
            return;
        }

        card.classList.add('wsf-loading');

        // Simulate AJAX delete
        setTimeout(() => {
            card.style.transform = 'scale(0)';
            card.style.opacity = '0';

            setTimeout(() => {
                if (card.parentNode) {
                    card.parentNode.removeChild(card);
                }
                showNotification('Theme deleted successfully', 'success');
            }, CONFIG.ANIMATION_DURATION);
        }, 500);
    }

    /**
     * Initialize all theme card functionality
     */
    function initThemeCards() {
        const cards = document.querySelectorAll('.wsf-theme-card');
        cards.forEach(card => {
            adjustCardActions(card);
            initCardInteractions(card);
        });

        // Initialize dropdowns
        document.querySelectorAll('.wsf-theme-card__dropdown').forEach(initDropdown);

        // Initialize global event listeners
        initGlobalEvents();
    }

    /**
     * Initialize card interactions
     * @param {HTMLElement} card - Theme card element
     */
    function initCardInteractions(card) {
        // Preview on hover
        let previewTimeout;

        card.addEventListener('mouseenter', () => {
            previewTimeout = setTimeout(() => {
                if (!card.classList.contains('wsf-theme-card--active')) {
                    // Could show mini preview here
                }
            }, CONFIG.PREVIEW_DELAY);
        });

        card.addEventListener('mouseleave', () => {
            if (previewTimeout) {
                clearTimeout(previewTimeout);
            }
        });

        // Click to apply theme
        card.addEventListener('click', (e) => {
            // Don't trigger if clicking on dropdown or actions
            if (e.target.closest('.wsf-theme-card__dropdown') ||
                e.target.closest('.wsf-theme-card__actions')) {
                return;
            }

            const themeId = card.dataset.themeId;
            if (themeId && !card.classList.contains('wsf-theme-card--active')) {
                applyTheme(themeId);
            }
        });

        // Double-click for preview
        card.addEventListener('dblclick', (e) => {
            e.preventDefault();
            showPreview(card);
        });
    }

    /**
     * Initialize global event listeners
     */
    function initGlobalEvents() {
        // Close dropdowns on outside click
        document.addEventListener('click', closeAllDropdowns);

        // Handle modal events
        document.addEventListener('click', (e) => {
            const action = e.target.dataset.action;

            switch (action) {
                case 'close-preview':
                    closePreview();
                    break;
                case 'apply-theme':
                    const themeId = e.target.dataset.themeId;
                    if (themeId) {
                        applyTheme(themeId);
                        closePreview();
                    }
                    break;
                case 'add-theme':
                    showAddThemeModal();
                    break;
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closePreview();
                closeAllDropdowns();
            }
        });
    }

    /**
     * Close preview modal
     */
    function closePreview() {
        const modal = document.querySelector('.wsf-theme-preview');
        if (modal) {
            modal.classList.remove('wsf-theme-preview--active');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, CONFIG.ANIMATION_DURATION);
        }
    }

    /**
     * Show add theme modal
     */
    function showAddThemeModal() {
        showNotification('Add Theme feature coming soon!', 'warning');
        // TODO: Implement add theme modal
    }

    /**
     * Handle dynamic card additions
     * @param {HTMLElement} newCard - Newly added card
     */
    function handleNewCard(newCard) {
        if (newCard.classList.contains('wsf-theme-card')) {
            adjustCardActions(newCard);
            initCardInteractions(newCard);
        }
    }

    // Initialize on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initThemeCards);
    } else {
        initThemeCards();
    }

    // Watch for dynamic card additions
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    handleNewCard(node);
                    // Also check children
                    const childCards = node.querySelectorAll('.wsf-theme-card');
                    childCards.forEach(card => {
                        adjustCardActions(card);
                        initCardInteractions(card);
                    });
                }
            });
        });
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Export for testing and external access
    window.WSFThemeCards = {
        calculateIconOffset,
        adjustCardActions,
        initThemeCards,
        showPreview,
        applyTheme,
        showNotification,
        state
    };

})();
