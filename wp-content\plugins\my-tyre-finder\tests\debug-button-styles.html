<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug <PERSON><PERSON> Styles</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 2rem;
            background: #f0f0f1;
            margin: 0;
        }
        
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        /* Inline styles exactly as they appear in Frontend.php */
        .btn-secondary,
        button.btn-secondary,
        #garage-clear-all {
            background-color: transparent !important;
            background-image: none !important;
            color: #ef4444 !important;
            font-weight: 500 !important;
            font-size: 14px !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            padding: 8px 12px !important;
            border-radius: 6px !important;
            border: 1px solid #f3f4f6 !important;
            transition: all 0.15s ease !important;
            cursor: pointer !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 6px !important;
            text-decoration: none !important;
            box-shadow: none !important;
            outline: none !important;
            vertical-align: middle !important;
            line-height: 1.4 !important;
            white-space: nowrap !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            word-spacing: normal !important;
            text-indent: 0 !important;
            text-shadow: none !important;
            text-align: center !important;
        }

        .btn-secondary:hover:not(:disabled),
        button.btn-secondary:hover:not(:disabled),
        #garage-clear-all:hover:not(:disabled) {
            background-color: #f3f4f6 !important;
            border-color: #ef4444 !important;
            color: #dc2626 !important;
            transform: none !important;
        }

        .btn-secondary:focus,
        button.btn-secondary:focus,
        #garage-clear-all:focus {
            outline: none !important;
            box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
        }

        .btn-secondary:disabled,
        button.btn-secondary:disabled,
        #garage-clear-all:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .btn-secondary i,
        button.btn-secondary i,
        #garage-clear-all i {
            width: 16px !important;
            height: 16px !important;
            flex-shrink: 0 !important;
            margin: 0 !important;
        }

        /* Responsive styles for mobile devices */
        @media (max-width: 640px) {
            .btn-secondary,
            button.btn-secondary,
            #garage-clear-all {
                padding: 8px !important;
                min-width: 40px !important;
            }
            
            .btn-secondary span,
            button.btn-secondary span,
            #garage-clear-all span {
                display: none !important;
            }
            
            .btn-secondary i,
            button.btn-secondary i,
            #garage-clear-all i {
                margin: 0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Диагностика стилей кнопки</h1>
        
        <div class="test-section">
            <h2>Тест 1: Кнопка с классом btn-secondary</h2>
            <button class="btn-secondary">
                <i data-lucide="trash-2"></i>
                <span>Clear all</span>
            </button>
            <div class="debug-info" id="debug1"></div>
        </div>

        <div class="test-section">
            <h2>Тест 2: Кнопка с ID garage-clear-all</h2>
            <button id="garage-clear-all">
                <i data-lucide="trash-2"></i>
                <span>Clear all</span>
            </button>
            <div class="debug-info" id="debug2"></div>
        </div>

        <div class="test-section">
            <h2>Тест 3: Кнопка с обоими селекторами</h2>
            <button class="btn-secondary" id="garage-clear-all-2">
                <i data-lucide="trash-2"></i>
                <span>Clear all</span>
            </button>
            <div class="debug-info" id="debug3"></div>
        </div>

        <div class="test-section">
            <h2>Тест 4: Обычная кнопка (для сравнения)</h2>
            <button>
                <i data-lucide="trash-2"></i>
                <span>Clear all</span>
            </button>
            <div class="debug-info" id="debug4"></div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        function getComputedStyles(element, properties) {
            const computed = window.getComputedStyle(element);
            const result = {};
            properties.forEach(prop => {
                result[prop] = computed.getPropertyValue(prop);
            });
            return result;
        }
        
        function debugButton(buttonSelector, debugElementId) {
            const button = document.querySelector(buttonSelector);
            const debugElement = document.getElementById(debugElementId);
            
            if (!button) {
                debugElement.textContent = 'Кнопка не найдена!';
                return;
            }
            
            const properties = [
                'background-color', 'color', 'font-size', 'font-weight',
                'padding', 'border', 'border-radius', 'display',
                'align-items', 'gap', 'cursor'
            ];
            
            const styles = getComputedStyles(button, properties);
            
            let debugText = `Селектор: ${buttonSelector}\n`;
            debugText += `Классы: ${button.className}\n`;
            debugText += `ID: ${button.id}\n\n`;
            debugText += 'Computed styles:\n';
            
            Object.entries(styles).forEach(([prop, value]) => {
                debugText += `${prop}: ${value}\n`;
            });
            
            debugElement.textContent = debugText;
        }
        
        // Debug all buttons
        setTimeout(() => {
            debugButton('.btn-secondary', 'debug1');
            debugButton('#garage-clear-all', 'debug2');
            debugButton('#garage-clear-all-2', 'debug3');
            debugButton('button:not([class]):not([id])', 'debug4');
        }, 100);
    </script>
</body>
</html>
