/**
 * Тест проверки исправления селекторов
 * Проверяет, что все селекторы теперь используют data-i18n атрибуты
 */

(function() {
    'use strict';

    console.log('[Selector Fix Test] Инициализация теста исправления селекторов...');

    // Тестовые переводы
    const testTranslations = {
        ru: {
            'select_make_placeholder': 'Выберите бренд',
            'select_model_placeholder': 'Выберите модель',
            'select_year_placeholder': 'Выберите год',
            'select_mods_placeholder': 'Выберите модификацию',
            'select_gen_placeholder': 'Выберите поколение'
        },
        en: {
            'select_make_placeholder': 'Choose a make',
            'select_model_placeholder': 'Choose a model',
            'select_year_placeholder': 'Choose a year',
            'select_mods_placeholder': 'Choose a modification',
            'select_gen_placeholder': 'Choose a generation'
        }
    };

    // Функция тестирования populateSelect функций
    function testPopulateFunctions() {
        console.log('[Selector Fix Test] === Тестирование populate функций ===');
        
        // Устанавливаем русские переводы
        window.WheelFitI18n = testTranslations.ru;
        console.log('[Selector Fix Test] Установлены русские переводы');
        
        // Проверяем, доступен ли виджет
        if (typeof window.wheelFitWidget === 'undefined' || !window.wheelFitWidget) {
            console.warn('[Selector Fix Test] wheelFitWidget недоступен, создаем тестовые функции');
            createTestFunctions();
            return;
        }
        
        const widget = window.wheelFitWidget;
        
        // Тестируем populateMakes
        if (typeof widget.populateMakes === 'function') {
            console.log('[Selector Fix Test] Тестируем populateMakes...');
            widget.populateMakes([
                { name: 'BMW', slug: 'bmw' },
                { name: 'Mercedes', slug: 'mercedes' },
                { name: 'Audi', slug: 'audi' }
            ]);
            checkSelector('wf-make', 'select_make_placeholder');
        }
        
        // Тестируем populateModels
        if (typeof widget.populateModels === 'function') {
            console.log('[Selector Fix Test] Тестируем populateModels...');
            widget.populateModels([
                { name: 'X5', slug: 'x5' },
                { name: 'X3', slug: 'x3' }
            ]);
            checkSelector('wf-model', 'select_model_placeholder');
        }
        
        // Тестируем populateYears
        if (typeof widget.populateYears === 'function') {
            console.log('[Selector Fix Test] Тестируем populateYears...');
            widget.populateYears([
                { name: '2023', slug: '2023' },
                { name: '2022', slug: '2022' }
            ]);
            checkSelector('wf-year', 'select_year_placeholder');
        }
    }

    // Функция проверки отдельного селектора
    function checkSelector(selectId, expectedKey) {
        const select = document.getElementById(selectId);
        if (!select) {
            console.warn(`[Selector Fix Test] ❌ Селектор ${selectId} не найден`);
            return false;
        }
        
        const placeholderOption = select.querySelector('option[value=""]');
        if (!placeholderOption) {
            console.warn(`[Selector Fix Test] ❌ ${selectId}: placeholder option не найден`);
            return false;
        }
        
        const dataI18n = placeholderOption.getAttribute('data-i18n');
        const currentText = placeholderOption.textContent.trim();
        const expectedText = window.WheelFitI18n && window.WheelFitI18n[expectedKey] 
            ? window.WheelFitI18n[expectedKey] 
            : 'НЕТ ПЕРЕВОДА';
        
        const hasCorrectAttribute = dataI18n === expectedKey;
        const hasCorrectText = currentText === expectedText;
        
        console.log(`[Selector Fix Test] ${selectId}:`);
        console.log(`  data-i18n: "${dataI18n}" (ожидался: "${expectedKey}") ${hasCorrectAttribute ? '✅' : '❌'}`);
        console.log(`  текст: "${currentText}" (ожидался: "${expectedText}") ${hasCorrectText ? '✅' : '❌'}`);
        
        if (hasCorrectAttribute && hasCorrectText) {
            console.log(`[Selector Fix Test] ✅ ${selectId}: ИСПРАВЛЕН ПРАВИЛЬНО`);
            return true;
        } else {
            console.log(`[Selector Fix Test] ❌ ${selectId}: ТРЕБУЕТ ИСПРАВЛЕНИЯ`);
            return false;
        }
    }

    // Функция создания тестовых функций (если виджет недоступен)
    function createTestFunctions() {
        console.log('[Selector Fix Test] Создаем тестовые функции...');
        
        // Создаем тестовые селекторы, если их нет
        const selectors = ['wf-make', 'wf-model', 'wf-year', 'wf-modification', 'wf-generation'];
        selectors.forEach(id => {
            if (!document.getElementById(id)) {
                const select = document.createElement('select');
                select.id = id;
                select.innerHTML = '<option value="">Loading...</option>';
                document.body.appendChild(select);
                console.log(`[Selector Fix Test] Создан тестовый селектор ${id}`);
            }
        });
        
        // Тестовая функция populateMakes
        window.testPopulateMakes = function(makes) {
            const select = document.getElementById('wf-make');
            if (!select) return;
            
            // Создаем placeholder option с правильными атрибутами
            const placeholderOption = document.createElement('option');
            placeholderOption.value = '';
            placeholderOption.setAttribute('data-i18n', 'select_make_placeholder');
            placeholderOption.textContent = window.WheelFitI18n && window.WheelFitI18n['select_make_placeholder'] 
                ? window.WheelFitI18n['select_make_placeholder'] 
                : 'Choose a make';
            
            // Очищаем и добавляем placeholder
            select.innerHTML = '';
            select.appendChild(placeholderOption);
            
            // Добавляем опции
            makes.forEach(make => {
                const option = document.createElement('option');
                option.value = make.slug || make.name;
                option.textContent = make.name;
                select.appendChild(option);
            });
            
            console.log('[Selector Fix Test] testPopulateMakes выполнена');
        };
        
        // Тестируем
        window.testPopulateMakes([
            { name: 'BMW', slug: 'bmw' },
            { name: 'Mercedes', slug: 'mercedes' }
        ]);
        
        checkSelector('wf-make', 'select_make_placeholder');
    }

    // Функция проверки всех селекторов
    function checkAllSelectors() {
        console.log('[Selector Fix Test] === Проверка всех селекторов ===');
        
        const selectorsToCheck = [
            { id: 'wf-make', key: 'select_make_placeholder' },
            { id: 'wf-model', key: 'select_model_placeholder' },
            { id: 'wf-year', key: 'select_year_placeholder' },
            { id: 'wf-modification', key: 'select_mods_placeholder' },
            { id: 'wf-generation', key: 'select_gen_placeholder' }
        ];
        
        let passed = 0;
        let total = 0;
        
        selectorsToCheck.forEach(({ id, key }) => {
            const select = document.getElementById(id);
            if (select) {
                total++;
                if (checkSelector(id, key)) {
                    passed++;
                }
            }
        });
        
        console.log(`[Selector Fix Test] Результат: ${passed}/${total} селекторов исправлены правильно`);
        
        if (passed === total && total > 0) {
            console.log('🎉 [Selector Fix Test] ВСЕ СЕЛЕКТОРЫ ИСПРАВЛЕНЫ ПРАВИЛЬНО!');
        } else if (passed > 0) {
            console.log('⚠️ [Selector Fix Test] Некоторые селекторы требуют доработки');
        } else {
            console.log('❌ [Selector Fix Test] Селекторы не исправлены');
        }
        
        return { passed, total };
    }

    // Функция тестирования с разными языками
    function testMultipleLanguages() {
        console.log('[Selector Fix Test] === Тестирование с разными языками ===');
        
        const languages = ['ru', 'en'];
        
        languages.forEach(lang => {
            console.log(`[Selector Fix Test] Тестируем язык: ${lang}`);
            window.WheelFitI18n = testTranslations[lang];
            
            // Применяем переводы
            if (typeof applyStaticTranslations === 'function') {
                applyStaticTranslations();
            } else if (window.translationManager && typeof window.translationManager.applyTranslations === 'function') {
                window.translationManager.applyTranslations();
            }
            
            // Проверяем результат
            const result = checkAllSelectors();
            console.log(`[Selector Fix Test] ${lang}: ${result.passed}/${result.total} правильно`);
        });
    }

    // Основная функция тестирования
    function runComprehensiveTest() {
        console.log('[Selector Fix Test] 🚀 ЗАПУСК КОМПЛЕКСНОГО ТЕСТА');
        
        // Проверяем начальное состояние
        console.log('[Selector Fix Test] 1. Проверка начального состояния...');
        checkAllSelectors();
        
        // Тестируем populate функции
        console.log('[Selector Fix Test] 2. Тестирование populate функций...');
        testPopulateFunctions();
        
        // Тестируем с разными языками
        console.log('[Selector Fix Test] 3. Тестирование с разными языками...');
        testMultipleLanguages();
        
        console.log('[Selector Fix Test] ✅ КОМПЛЕКСНЫЙ ТЕСТ ЗАВЕРШЕН');
    }

    // Глобальные функции для ручного тестирования
    window.testSelectorFix = {
        runTest: runComprehensiveTest,
        checkAll: checkAllSelectors,
        testPopulate: testPopulateFunctions,
        testLanguages: testMultipleLanguages
    };

    // Автоматический запуск через 3 секунды
    setTimeout(() => {
        console.log('[Selector Fix Test] Автоматический запуск теста...');
        runComprehensiveTest();
    }, 3000);

    console.log('[Selector Fix Test] Тест загружен. Доступные функции:');
    console.log('- testSelectorFix.runTest() - полный тест');
    console.log('- testSelectorFix.checkAll() - проверка всех селекторов');
    console.log('- testSelectorFix.testPopulate() - тест populate функций');

})();
