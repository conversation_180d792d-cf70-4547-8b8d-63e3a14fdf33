/**
 * Test script to verify theme activation and button positioning fixes
 * Run this in browser console on the WordPress admin Appearance page
 */

(function() {
    'use strict';

    console.log('🔍 === THEME FIXES VERIFICATION ===');

    // Test 1: Verify theme activation fix with specific dark theme test
    async function testThemeActivation() {
        console.log('\n1️⃣ Testing Theme Activation Fix...');

        if (typeof wpApiSettings === 'undefined') {
            console.warn('⚠️ wpApiSettings not available - run this on the admin Appearance page');
            return false;
        }

        try {
            // First get available themes
            const themesResponse = await fetch(wpApiSettings.root + 'wheel-size/v1/themes', {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (!themesResponse.ok) {
                console.error('❌ Failed to fetch themes:', themesResponse.status);
                return false;
            }

            const themesData = await themesResponse.json();
            console.log('✅ Successfully fetched themes:', Object.keys(themesData.themes));

            // Check if dark theme exists
            if (!themesData.themes.dark) {
                console.error('❌ Dark theme not found in available themes');
                console.log('Available themes:', Object.keys(themesData.themes));
                return false;
            }
            console.log('✅ Dark theme found in available themes');

            // Get current active theme
            const activeResponse = await fetch(wpApiSettings.root + 'wheel-size/v1/themes/active', {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (!activeResponse.ok) {
                console.error('❌ Failed to get active theme:', activeResponse.status);
                return false;
            }

            const activeData = await activeResponse.json();
            console.log('✅ Current active theme:', activeData.slug);

            // Test light theme first (should work)
            console.log('🎯 Testing light theme activation...');
            const lightResponse = await fetch(wpApiSettings.root + 'wheel-size/v1/themes/active', {
                method: 'PUT',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ slug: 'light' })
            });

            if (lightResponse.ok) {
                console.log('✅ Light theme activation successful');
            } else {
                const lightError = await lightResponse.text();
                console.error('❌ Light theme activation failed:', lightResponse.status, lightError);
            }

            // Now test dark theme specifically
            console.log('🌙 Testing dark theme activation...');
            const darkResponse = await fetch(wpApiSettings.root + 'wheel-size/v1/themes/active', {
                method: 'PUT',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ slug: 'dark' })
            });

            if (darkResponse.ok) {
                const darkData = await darkResponse.json();
                console.log('✅ Dark theme activation successful:', darkData);
                return true;
            } else {
                const darkError = await darkResponse.text();
                console.error('❌ Dark theme activation failed:', darkResponse.status, darkError);

                // Try to parse error details
                try {
                    const errorData = JSON.parse(darkError);
                    console.error('Dark theme error details:', errorData);
                } catch (e) {
                    console.error('Raw dark theme error:', darkError);
                }
                return false;
            }

        } catch (error) {
            console.error('❌ Theme activation test error:', error);
            return false;
        }
    }

    // Test 2: Verify button positioning
    function testButtonPositioning() {
        console.log('\n2️⃣ Testing Button Positioning Fix...');
        
        const themeCards = document.querySelectorAll('.wsf-theme-card');
        if (themeCards.length === 0) {
            console.warn('⚠️ No theme cards found - make sure you\'re on the Appearance page with theme presets enabled');
            return false;
        }

        console.log(`Found ${themeCards.length} theme card(s)`);

        let positioningCorrect = true;
        themeCards.forEach((card, index) => {
            const actions = card.querySelector('.wsf-theme-card__actions');
            if (actions) {
                const styles = window.getComputedStyle(actions);
                const top = styles.top;
                const right = styles.right;
                
                console.log(`Card ${index + 1} actions positioning:`, { top, right });
                
                // Check if the positioning includes our adjustments
                // We added +4px to top and -4px to right (which means more to the right)
                if (top.includes('calc') && right.includes('calc')) {
                    console.log(`✅ Card ${index + 1} has updated positioning`);
                } else {
                    console.log(`⚠️ Card ${index + 1} may not have updated positioning`);
                    positioningCorrect = false;
                }
            } else {
                console.log(`⚠️ Card ${index + 1} has no actions element`);
                positioningCorrect = false;
            }
        });

        return positioningCorrect;
    }

    // Test 3: Visual verification helper
    function highlightButtons() {
        console.log('\n3️⃣ Highlighting buttons for visual verification...');
        
        const actions = document.querySelectorAll('.wsf-theme-card__actions');
        actions.forEach((action, index) => {
            // Add a temporary highlight
            action.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
            action.style.border = '2px solid red';
            action.style.opacity = '1';
            
            setTimeout(() => {
                action.style.backgroundColor = '';
                action.style.border = '';
                action.style.opacity = '';
            }, 3000);
        });
        
        console.log(`✅ Highlighted ${actions.length} action button groups for 3 seconds`);
    }

    // Run all tests
    async function runAllTests() {
        console.log('🚀 Starting verification tests...\n');
        
        const activationTest = await testThemeActivation();
        const positioningTest = testButtonPositioning();
        
        console.log('\n📊 === TEST RESULTS ===');
        console.log(`Theme Activation Fix: ${activationTest ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`Button Positioning Fix: ${positioningTest ? '✅ PASS' : '❌ FAIL'}`);
        
        if (activationTest && positioningTest) {
            console.log('\n🎉 All fixes verified successfully!');
        } else {
            console.log('\n⚠️ Some issues may still exist. Check the details above.');
        }
        
        // Offer visual verification
        if (document.querySelectorAll('.wsf-theme-card').length > 0) {
            console.log('\n💡 Run highlightButtons() to visually verify button positioning');
            window.highlightButtons = highlightButtons;
        }
    }

    // Start the tests
    runAllTests();

})();
