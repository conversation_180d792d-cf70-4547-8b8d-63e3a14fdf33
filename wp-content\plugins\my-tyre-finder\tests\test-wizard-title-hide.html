<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wizard Title Hide Test</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Plugin CSS -->
    <link rel="stylesheet" href="../assets/css/live-preview-width-fix.css">
    
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f3f4f6;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        /* Simulate widget preview container */
        #widget-preview {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            background: white;
            max-width: 56rem;
            margin: 1rem auto;
        }
        
        .test-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.25rem;
        }
        
        .test-btn:hover {
            background: #1d4ed8;
        }
        
        .debug-info {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; margin-bottom: 2rem; color: #1f2937;">
            🧙‍♂️ Wizard Title Hide Test
        </h1>
        
        <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid #e5e7eb; border-radius: 6px;">
            <h3 style="margin: 0 0 1rem 0; color: #1f2937; font-weight: 600;">📋 Задача</h3>
            <p>Убрать заголовок "Select Manufacturer" из wizard layout в Live Preview, так как он ломает отображение.</p>
            
            <h4>Что должно быть скрыто:</h4>
            <ul>
                <li>✅ Заголовок h2 "Select Manufacturer" в первом шаге wizard</li>
                <li>✅ Только в Live Preview (#widget-preview)</li>
                <li>✅ Не затрагивать другие заголовки в wizard</li>
            </ul>
        </div>
        
        <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid #e5e7eb; border-radius: 6px;">
            <h3 style="margin: 0 0 1rem 0; color: #1f2937; font-weight: 600;">🎮 Test Controls</h3>
            <button class="test-btn" onclick="checkTitleVisibility()">Check Title Visibility</button>
            <button class="test-btn" onclick="toggleDebugMode()">Toggle Debug Mode</button>
        </div>
        
        <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid #e5e7eb; border-radius: 6px;">
            <h3 style="margin: 0 0 1rem 0; color: #1f2937; font-weight: 600;">🖼️ Widget Preview Simulation</h3>
            <div id="widget-preview">
                <!-- Wizard Step 1 -->
                <div id="wizard-step-1" class="wizard-step">
                    <h2 class="text-2xl font-bold mb-4" style="color: #2563eb;">Select Manufacturer</h2>
                    <div id="wizard-makes-grid" class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-10 gap-4">
                        <div class="bg-gray-100 p-4 rounded text-center">Brand 1</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Brand 2</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Brand 3</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Brand 4</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Brand 5</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Brand 6</div>
                    </div>
                </div>
                
                <hr style="margin: 2rem 0;">
                
                <!-- Wizard Step 2 (should remain visible) -->
                <div id="wizard-step-2" class="wizard-step">
                    <h2 class="text-2xl font-bold mb-4" style="color: #2563eb;">Select Model</h2>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                        <div class="bg-gray-100 p-4 rounded text-center">Model 1</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Model 2</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Model 3</div>
                    </div>
                </div>
                
                <hr style="margin: 2rem 0;">
                
                <!-- Alternative step-make structure -->
                <div id="step-make">
                    <h2 class="text-2xl font-bold mb-4" style="color: #2563eb;">Select Manufacturer (step-make)</h2>
                    <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <div class="bg-gray-100 p-4 rounded text-center">Brand A</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Brand B</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Brand C</div>
                        <div class="bg-gray-100 p-4 rounded text-center">Brand D</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid #e5e7eb; border-radius: 6px;">
            <h3 style="margin: 0 0 1rem 0; color: #1f2937; font-weight: 600;">📊 Test Results</h3>
            <div id="test-results" class="debug-info">
                Click "Check Title Visibility" to see results...
            </div>
        </div>
        
        <!-- Outside widget-preview (should remain visible) -->
        <div style="margin-top: 2rem; padding: 1rem; border: 2px solid #10b981; border-radius: 6px;">
            <h3 style="margin: 0 0 1rem 0; color: #1f2937; font-weight: 600;">✅ Outside Preview (Should Remain Visible)</h3>
            <div id="wizard-step-1" class="wizard-step">
                <h2 class="text-2xl font-bold mb-4" style="color: #10b981;">Select Manufacturer (Outside Preview)</h2>
                <p>This title should remain visible because it's outside #widget-preview</p>
            </div>
        </div>
    </div>
    
    <script>
        let debugMode = false;
        
        function checkTitleVisibility() {
            const resultsDiv = document.getElementById('test-results');
            let output = '🔍 TITLE VISIBILITY TEST RESULTS:\n';
            output += '=====================================\n\n';
            
            // Test titles inside widget-preview
            const previewTitles = [
                { selector: '#widget-preview #wizard-step-1 > h2', name: 'Wizard Step 1 Title (should be HIDDEN)' },
                { selector: '#widget-preview #wizard-step-2 > h2', name: 'Wizard Step 2 Title (should be VISIBLE)' },
                { selector: '#widget-preview #step-make > h2', name: 'Step Make Title (should be HIDDEN)' }
            ];
            
            previewTitles.forEach(test => {
                const element = document.querySelector(test.selector);
                if (element) {
                    const computedStyle = getComputedStyle(element);
                    const isVisible = computedStyle.display !== 'none';
                    const status = test.name.includes('should be HIDDEN') 
                        ? (isVisible ? '❌ FAIL' : '✅ PASS') 
                        : (isVisible ? '✅ PASS' : '❌ FAIL');
                    
                    output += `${status} ${test.name}\n`;
                    output += `    Display: ${computedStyle.display}\n`;
                    output += `    Visibility: ${computedStyle.visibility}\n`;
                    output += `    Text: "${element.textContent.trim()}"\n\n`;
                } else {
                    output += `❌ FAIL ${test.name} - Element not found\n\n`;
                }
            });
            
            // Test title outside widget-preview
            const outsideTitle = document.querySelector('body > div:last-child #wizard-step-1 > h2');
            if (outsideTitle) {
                const computedStyle = getComputedStyle(outsideTitle);
                const isVisible = computedStyle.display !== 'none';
                const status = isVisible ? '✅ PASS' : '❌ FAIL';
                output += `${status} Outside Preview Title (should be VISIBLE)\n`;
                output += `    Display: ${computedStyle.display}\n`;
                output += `    Text: "${outsideTitle.textContent.trim()}"\n\n`;
            }
            
            // Summary
            const passCount = (output.match(/✅ PASS/g) || []).length;
            const failCount = (output.match(/❌ FAIL/g) || []).length;
            
            output += `📊 SUMMARY: ${passCount} passed, ${failCount} failed\n`;
            
            if (failCount === 0) {
                output += '🎉 All tests passed! Title hiding is working correctly.';
            } else {
                output += '⚠️ Some tests failed. Check the CSS rules.';
            }
            
            resultsDiv.textContent = output;
            console.log(output);
        }
        
        function toggleDebugMode() {
            debugMode = !debugMode;
            
            const titles = document.querySelectorAll('#widget-preview h2');
            
            if (debugMode) {
                titles.forEach(title => {
                    title.style.border = '2px solid red';
                    title.style.backgroundColor = '#fef2f2';
                    title.style.padding = '0.5rem';
                });
                console.log('🐛 Debug mode ON - titles highlighted');
            } else {
                titles.forEach(title => {
                    title.style.border = '';
                    title.style.backgroundColor = '';
                    title.style.padding = '';
                });
                console.log('🐛 Debug mode OFF');
            }
        }
        
        // Auto-run test when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkTitleVisibility, 500);
        });
    </script>
</body>
</html>
