<?php
/**
 * Debug Slug Generation
 * Test script to verify slug generation works correctly for international characters
 * 
 * Usage: Run this script in WordPress admin or via WP-CLI
 */

// Ensure this is run in WordPress context
if (!defined('ABSPATH')) {
    die('This script must be run in WordPress context');
}

// Include the ThemeManager class
require_once plugin_dir_path(__FILE__) . '../src/includes/ThemeManager.php';

echo "<h2>Slug Generation Test</h2>\n";
echo "<style>
    body { font-family: monospace; }
    .test-case { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
    .success { background: #d4edda; }
    .warning { background: #fff3cd; }
    .error { background: #f8d7da; }
</style>\n";

// Test cases for slug generation
$testCases = [
    '123' => 'Pure numeric',
    '456789' => 'Longer numeric',
    'Тема' => 'Cyrillic (Russian)',
    '主题' => 'Chinese characters',
    'テーマ' => 'Japanese characters',
    'موضوع' => 'Arabic characters',
    'Thème' => 'French with accents',
    'Téma' => 'Spanish with accents',
    'Thema123' => 'Mixed English + numbers',
    'Тема2024' => 'Mixed Cyrillic + numbers',
    'Theme-Test' => 'English with hyphen',
    'Theme_Test' => 'English with underscore',
    'Ελληνικό' => 'Greek characters',
    'हिंदी' => 'Hindi characters',
    '한국어' => 'Korean characters',
    'Ñoño' => 'Spanish with ñ',
    'Café' => 'French with é',
    'Naïve' => 'French with ï',
    'Résumé' => 'French with é and é',
    'Björk' => 'Icelandic with ö',
    'Zürich' => 'German with ü',
    'São Paulo' => 'Portuguese with ã and tilde',
    '' => 'Empty string',
    '   ' => 'Whitespace only',
    '!@#$%' => 'Special characters only',
    'Test Theme 2024!' => 'Mixed with spaces and punctuation'
];

echo "<h3>Testing Slug Generation</h3>\n";

foreach ($testCases as $input => $description) {
    echo "<div class='test-case'>\n";
    echo "<strong>Input:</strong> '" . htmlspecialchars($input) . "' <em>({$description})</em><br>\n";
    
    try {
        // Use reflection to access the private method
        $reflection = new ReflectionClass('WheelSize\\ThemeManager');
        $method = $reflection->getMethod('generate_slug');
        $method->setAccessible(true);
        
        $slug = $method->invoke(null, $input);
        
        echo "<strong>Generated Slug:</strong> '" . htmlspecialchars($slug) . "'<br>\n";
        
        // Validate the slug
        if (empty($slug)) {
            echo "<span style='color: red;'>❌ Empty slug generated</span><br>\n";
            echo "</div>\n";
            continue;
        }
        
        // Test if slug would pass validation
        $isValid = true;
        $validationMessage = '';
        
        // Check length
        if (strlen($slug) > 100) {
            $isValid = false;
            $validationMessage .= 'Too long (>100 chars); ';
        }
        
        // Check for pure numeric
        if (preg_match('/^\d+$/', $slug)) {
            $validationMessage .= 'Pure numeric (should be valid); ';
        }
        // Check for ASCII alphanumeric
        elseif (preg_match('/^[a-zA-Z0-9\-_]+$/', $slug)) {
            $validationMessage .= 'ASCII alphanumeric (should be valid); ';
        }
        // Check for Unicode letters
        elseif (preg_match('/^[\p{L}\p{N}\-_]+$/u', $slug)) {
            $validationMessage .= 'Unicode letters/numbers (should be valid); ';
        }
        // Check for mixed content
        elseif (preg_match('/^[\p{L}\p{N}\-_.]+$/u', $slug)) {
            $validationMessage .= 'Mixed Unicode content (should be valid); ';
        }
        else {
            $isValid = false;
            $validationMessage .= 'Contains invalid characters; ';
        }
        
        $class = $isValid ? 'success' : 'error';
        $icon = $isValid ? '✅' : '❌';
        
        echo "<span class='{$class}'>{$icon} Validation: {$validationMessage}</span><br>\n";
        
    } catch (Exception $e) {
        echo "<span style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</span><br>\n";
    }
    
    echo "</div>\n";
}

echo "<h3>Testing WordPress sanitize_title Function</h3>\n";
echo "<p>Comparing our slug generation with WordPress built-in sanitize_title:</p>\n";

foreach (array_slice($testCases, 0, 10, true) as $input => $description) {
    echo "<div class='test-case'>\n";
    echo "<strong>Input:</strong> '" . htmlspecialchars($input) . "'<br>\n";
    
    $wpSlug = sanitize_title($input);
    echo "<strong>WordPress sanitize_title:</strong> '" . htmlspecialchars($wpSlug) . "'<br>\n";
    
    try {
        $reflection = new ReflectionClass('WheelSize\\ThemeManager');
        $method = $reflection->getMethod('generate_slug');
        $method->setAccessible(true);
        $ourSlug = $method->invoke(null, $input);
        echo "<strong>Our generate_slug:</strong> '" . htmlspecialchars($ourSlug) . "'<br>\n";
        
        if ($wpSlug === $ourSlug) {
            echo "<span class='success'>✅ Results match</span><br>\n";
        } else {
            echo "<span class='warning'>⚠️ Results differ</span><br>\n";
        }
    } catch (Exception $e) {
        echo "<span style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</span><br>\n";
    }
    
    echo "</div>\n";
}

echo "<h3>Summary</h3>\n";
echo "<p>This test verifies that:</p>\n";
echo "<ul>\n";
echo "<li>✅ Numeric theme names generate valid slugs</li>\n";
echo "<li>✅ International characters are handled properly</li>\n";
echo "<li>✅ Generated slugs pass validation rules</li>\n";
echo "<li>✅ Edge cases (empty strings, special chars) are handled</li>\n";
echo "</ul>\n";

echo "<p><strong>Next steps:</strong></p>\n";
echo "<ol>\n";
echo "<li>Run the HTML test file in WordPress admin to test full API integration</li>\n";
echo "<li>Create themes with problematic names and verify activation works</li>\n";
echo "<li>Check that existing themes still work after the changes</li>\n";
echo "</ol>\n";
?>
