<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surface Tokens Implementation Test</title>
    <link rel="stylesheet" href="../assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .theme-controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .theme-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .theme-btn:hover {
            background: #f3f4f6;
        }
        
        .theme-btn.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .comparison-item {
            padding: 15px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
        }
        
        .comparison-item h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #374151;
        }
        
        .widget-demo {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
        }
        
        .tokens-display {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 15px;
        }
        
        .token-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .token-name {
            color: #7c3aed;
            font-weight: bold;
        }
        
        .token-value {
            color: #059669;
        }
        
        .color-swatch {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 3px;
            border: 1px solid #ccc;
            margin-left: 8px;
            vertical-align: middle;
        }
        
        .test-results {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .test-results h4 {
            margin-top: 0;
            color: #0369a1;
        }
        
        .result-item {
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e0f2fe;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .status-pass {
            color: #059669;
            font-weight: bold;
        }
        
        .status-fail {
            color: #dc2626;
            font-weight: bold;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before-after > div {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Surface Tokens Implementation Test</h1>
        <p>Тест проверяет правильную реализацию системы токенов Surface для разделения фона виджета и фона селекторов.</p>
        
        <!-- Theme Controls -->
        <div class="theme-controls">
            <button class="theme-btn active" onclick="applyTheme('light')">Light Theme</button>
            <button class="theme-btn" onclick="applyTheme('dark')">Dark Theme</button>
            <button class="theme-btn" onclick="applyTheme('custom')">Custom Theme</button>
            <button class="theme-btn" onclick="runTests()">🧪 Run Tests</button>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>1. До и После: Проблема и Решение</h3>
            <div class="before-after">
                <div class="before">
                    <h4>❌ ДО: Проблема</h4>
                    <p><strong>Селекторы использовали --wsf-bg:</strong></p>
                    <ul>
                        <li>Фон виджета: <code>--wsf-bg</code></li>
                        <li>Фон селектов: <code>--wsf-bg</code> (та же переменная!)</li>
                        <li>Результат: селекты сливаются с фоном</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>✅ ПОСЛЕ: Решение</h4>
                    <p><strong>Селекторы используют --wsf-surface:</strong></p>
                    <ul>
                        <li>Фон виджета: <code>--wsf-bg</code></li>
                        <li>Фон селектов: <code>--wsf-input-bg</code> → <code>var(--wsf-surface)</code></li>
                        <li>Результат: селекты контрастируют с фоном</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Widget Demo -->
        <div class="test-section">
            <h3>2. Демонстрация виджета с новой системой токенов</h3>
            <div class="widget-demo wsf-finder-widget" id="test-widget">
                <div class="wsf-form-wrapper">
                    <div class="step-container">
                        <label for="test-make" class="block text-sm font-semibold text-wsf-text uppercase tracking-wide mb-2">Make</label>
                        <select id="test-make" name="make" class="wsf-input block w-full">
                            <option value="">Select a make...</option>
                            <option value="audi">Audi</option>
                            <option value="bmw">BMW</option>
                            <option value="mercedes">Mercedes-Benz</option>
                        </select>
                    </div>
                    
                    <div class="step-container">
                        <label for="test-model" class="block text-sm font-semibold text-wsf-text uppercase tracking-wide mb-2">Model</label>
                        <select id="test-model" name="model" class="wsf-input block w-full" disabled>
                            <option value="">Select make first</option>
                        </select>
                    </div>
                    
                    <button class="btn-primary w-full mt-4">Find Sizes</button>
                </div>
            </div>
            
            <!-- Tokens Display -->
            <div class="tokens-display" id="tokens-display">
                <strong>Текущие CSS токены:</strong>
                <div id="tokens-list"></div>
            </div>
        </div>
        
        <!-- Template Comparison -->
        <div class="test-section">
            <h3>3. Сравнение шаблонов: Исправленные жестко заданные цвета</h3>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>finder-form-inline.twig</h4>
                    <p><strong>ДО:</strong></p>
                    <code>border: 1px solid #d1d5db;</code><br>
                    <code>color: rgb(100 116 139);</code>
                    
                    <p><strong>ПОСЛЕ:</strong></p>
                    <code>border: 1px solid var(--wsf-input-border);</code><br>
                    <code>color: var(--wsf-input-placeholder);</code>
                </div>
                
                <div class="comparison-item">
                    <h4>finder-form-flow.twig</h4>
                    <p><strong>ДО:</strong></p>
                    <code>class="border-wsf-border"</code><br>
                    <small>(без фона)</small>
                    
                    <p><strong>ПОСЛЕ:</strong></p>
                    <code>class="wsf-input"</code><br>
                    <small>(с полным набором стилей)</small>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="test-results" id="test-results" style="display: none;">
            <h4>🧪 Результаты тестов</h4>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        // Theme definitions with Surface tokens
        const themes = {
            light: {
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#1f2937',
                '--wsf-primary': '#2563eb',
                '--wsf-border': '#e5e7eb',
                '--wsf-hover': '#1d4ed8',
                '--wsf-surface': '#f9fafb',
                '--wsf-input-bg': '#f9fafb',
                '--wsf-input-text': '#1f2937',
                '--wsf-input-border': '#e5e7eb',
                '--wsf-input-placeholder': '#9ca3af',
                '--wsf-input-focus': '#2563eb'
            },
            dark: {
                '--wsf-bg': '#1e1e1e',
                '--wsf-text': '#f3f4f6',
                '--wsf-primary': '#7dd3fc',
                '--wsf-border': '#374151',
                '--wsf-hover': '#0ea5e9',
                '--wsf-surface': '#2d2d2d',
                '--wsf-input-bg': '#2d2d2d',
                '--wsf-input-text': '#f3f4f6',
                '--wsf-input-border': '#374151',
                '--wsf-input-placeholder': '#6b7280',
                '--wsf-input-focus': '#7dd3fc'
            },
            custom: {
                '--wsf-bg': '#fef3c7',
                '--wsf-text': '#92400e',
                '--wsf-primary': '#d97706',
                '--wsf-border': '#fbbf24',
                '--wsf-hover': '#b45309',
                '--wsf-surface': '#fffbeb',
                '--wsf-input-bg': '#fffbeb',
                '--wsf-input-text': '#92400e',
                '--wsf-input-border': '#fbbf24',
                '--wsf-input-placeholder': '#a16207',
                '--wsf-input-focus': '#d97706'
            }
        };
        
        let currentTheme = 'light';
        
        function applyTheme(themeName) {
            const theme = themes[themeName];
            const widget = document.getElementById('test-widget');
            
            // Remove previous theme classes
            widget.classList.remove('wsf-theme-light', 'wsf-theme-dark', 'wsf-theme-custom');
            
            // Add new theme class
            widget.classList.add(`wsf-theme-${themeName}`);
            
            // Apply CSS custom properties
            Object.entries(theme).forEach(([property, value]) => {
                widget.style.setProperty(property, value);
            });
            
            // Update active button
            document.querySelectorAll('.theme-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            currentTheme = themeName;
            updateTokensDisplay();
            
            console.log(`Applied ${themeName} theme`);
        }
        
        function updateTokensDisplay() {
            const widget = document.getElementById('test-widget');
            const computedStyle = getComputedStyle(widget);
            const tokensList = document.getElementById('tokens-list');
            
            const importantTokens = [
                '--wsf-bg',
                '--wsf-surface',
                '--wsf-input-bg',
                '--wsf-input-text',
                '--wsf-input-border',
                '--wsf-input-focus'
            ];
            
            tokensList.innerHTML = importantTokens.map(token => {
                const value = computedStyle.getPropertyValue(token).trim();
                return `
                    <div class="token-row">
                        <span class="token-name">${token}:</span>
                        <span class="token-value">${value || 'not set'}
                            ${value ? `<span class="color-swatch" style="background-color: ${value}"></span>` : ''}
                        </span>
                    </div>
                `;
            }).join('');
        }
        
        function runTests() {
            console.log('🧪 Running Surface tokens implementation tests...');
            
            const widget = document.getElementById('test-widget');
            const computedStyle = getComputedStyle(widget);
            const selects = widget.querySelectorAll('select');
            
            const results = [];
            
            // Test 1: Surface tokens are defined
            const surfaceTokens = ['--wsf-bg', '--wsf-surface', '--wsf-input-bg'];
            surfaceTokens.forEach(token => {
                const value = computedStyle.getPropertyValue(token).trim();
                results.push({
                    test: `Surface Token ${token}`,
                    status: value ? 'PASS' : 'FAIL',
                    details: value ? `Value: ${value}` : 'Token not found'
                });
            });
            
            // Test 2: Background separation
            const bgValue = computedStyle.getPropertyValue('--wsf-bg').trim();
            const surfaceValue = computedStyle.getPropertyValue('--wsf-surface').trim();
            const inputBgValue = computedStyle.getPropertyValue('--wsf-input-bg').trim();
            
            results.push({
                test: 'Background Separation',
                status: (bgValue !== surfaceValue && surfaceValue === inputBgValue) ? 'PASS' : 'FAIL',
                details: `Widget bg: ${bgValue}, Surface: ${surfaceValue}, Input bg: ${inputBgValue}`
            });
            
            // Test 3: Select elements use correct background
            selects.forEach((select, index) => {
                const selectStyle = getComputedStyle(select);
                const selectBg = selectStyle.backgroundColor;
                
                results.push({
                    test: `Select ${index + 1} Background`,
                    status: selectBg !== 'rgba(0, 0, 0, 0)' ? 'PASS' : 'FAIL',
                    details: `Background: ${selectBg}`
                });
            });
            
            // Test 4: Theme switching works
            const originalTheme = currentTheme;
            const testTheme = originalTheme === 'light' ? 'dark' : 'light';
            
            // Temporarily switch theme
            applyTheme(testTheme);
            setTimeout(() => {
                const newSurface = getComputedStyle(widget).getPropertyValue('--wsf-surface').trim();
                const originalSurface = themes[originalTheme]['--wsf-surface'];
                
                results.push({
                    test: 'Theme Switching (Surface)',
                    status: newSurface !== originalSurface ? 'PASS' : 'FAIL',
                    details: `Surface changed from ${originalSurface} to ${newSurface}`
                });
                
                // Switch back
                applyTheme(originalTheme);
                
                // Display results
                displayResults(results);
            }, 100);
        }
        
        function displayResults(results) {
            const resultsContainer = document.getElementById('test-results');
            const resultsContent = document.getElementById('results-content');
            
            resultsContent.innerHTML = results.map(result => `
                <div class="result-item">
                    <strong>${result.test}:</strong> 
                    <span class="status-${result.status.toLowerCase()}">${result.status}</span>
                    <br>
                    <small>${result.details}</small>
                </div>
            `).join('');
            
            resultsContainer.style.display = 'block';
            
            console.log('Test results:', results);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            applyTheme('light');
            updateTokensDisplay();
            
            // Add focus event listeners
            document.querySelectorAll('select').forEach(select => {
                select.addEventListener('focus', () => {
                    console.log('Select focused with focus color:', getComputedStyle(select).getPropertyValue('border-color'));
                });
            });
        });
    </script>
</body>
</html>
