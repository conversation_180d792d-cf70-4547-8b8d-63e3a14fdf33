/**
 * Quick Inline Button Height Check
 * 
 * Copy and paste this entire script into browser console on:
 * /wp-admin/admin.php?page=wheel-size-appearance
 * 
 * Make sure to select "Inline (1×4)" layout first!
 */

console.log('📏 Quick Inline Button Height Check...');

// Check if we're in inline layout
const inlineForm = document.querySelector('#tab-by-car.grid-2x2');
const submitButton = document.querySelector('.ws-submit .btn-primary');

if (!inlineForm || !submitButton) {
    console.error('❌ Inline layout not detected');
    console.log('💡 Make sure to:');
    console.log('   1. Select "Inline (1×4)" in Form Layout dropdown');
    console.log('   2. Refresh the Live Preview');
    console.log('   3. Run this test again');
} else {
    console.log('✅ Inline layout detected');
    
    // Find all form elements
    const selects = document.querySelectorAll('select');
    
    if (selects.length === 0) {
        console.error('❌ No select fields found');
    } else {
        console.log(`✅ Found ${selects.length} select fields and 1 submit button`);
        
        console.log('\n📊 HEIGHT ALIGNMENT CHECK:');
        
        // Get heights
        const buttonRect = submitButton.getBoundingClientRect();
        const buttonHeight = buttonRect.height;
        
        console.log(`Button height: ${buttonHeight.toFixed(1)}px`);
        
        // Check each select
        let allAligned = true;
        const expectedHeight = 44; // --ws-control-height
        const tolerance = 2;
        
        selects.forEach((select, index) => {
            const selectRect = select.getBoundingClientRect();
            const selectHeight = selectRect.height;
            
            console.log(`Select ${index + 1} height: ${selectHeight.toFixed(1)}px`);
            
            const heightDiff = Math.abs(buttonHeight - selectHeight);
            const isAligned = heightDiff <= tolerance;
            
            if (!isAligned) {
                allAligned = false;
            }
        });
        
        // Overall alignment check
        const firstSelectHeight = selects[0].getBoundingClientRect().height;
        const heightDiff = Math.abs(buttonHeight - firstSelectHeight);
        const heightsMatch = heightDiff <= tolerance;
        
        console.log(`\nHeight difference (button vs select): ${heightDiff.toFixed(1)}px`);
        
        // Check if heights match expected value
        const buttonCorrect = Math.abs(buttonHeight - expectedHeight) <= tolerance;
        const selectCorrect = Math.abs(firstSelectHeight - expectedHeight) <= tolerance;
        
        console.log(`\n📊 ALIGNMENT RESULTS:`);
        console.log(`1. Button height correct (44px): ${buttonCorrect ? '✅' : '❌'}`);
        console.log(`2. Select height correct (44px): ${selectCorrect ? '✅' : '❌'}`);
        console.log(`3. Heights match each other: ${heightsMatch ? '✅' : '❌'}`);
        
        // Visual alignment check
        const buttonTop = buttonRect.top;
        const selectTop = selects[0].getBoundingClientRect().top;
        const topDiff = Math.abs(buttonTop - selectTop);
        const visuallyAligned = topDiff <= 5; // 5px tolerance
        
        console.log(`4. Visually aligned: ${visuallyAligned ? '✅' : '❌'} (top difference: ${topDiff.toFixed(1)}px)`);
        
        // Overall status
        const perfectAlignment = buttonCorrect && selectCorrect && heightsMatch && visuallyAligned;
        
        console.log(`\n🎯 OVERALL STATUS: ${perfectAlignment ? '✅ PERFECT ALIGNMENT' : '❌ NEEDS FIXING'}`);
        
        if (!perfectAlignment) {
            console.log('\n💡 ISSUES TO FIX:');
            if (!buttonCorrect) {
                console.log(`   - Button height should be 44px, got ${buttonHeight.toFixed(1)}px`);
            }
            if (!selectCorrect) {
                console.log(`   - Select height should be 44px, got ${firstSelectHeight.toFixed(1)}px`);
            }
            if (!heightsMatch) {
                console.log(`   - Button and select heights don't match (${heightDiff.toFixed(1)}px difference)`);
            }
            if (!visuallyAligned) {
                console.log(`   - Elements are not visually aligned (${topDiff.toFixed(1)}px top difference)`);
            }
        }
        
        // Check button styling
        console.log('\n🎨 BUTTON STYLING CHECK:');
        const buttonStyle = window.getComputedStyle(submitButton);
        
        const hasBackground = buttonStyle.backgroundColor !== 'rgba(0, 0, 0, 0)';
        const hasFlexDisplay = buttonStyle.display === 'flex';
        const hasCenterAlign = buttonStyle.alignItems === 'center';
        const hasPointerCursor = buttonStyle.cursor === 'pointer';
        
        console.log(`Background color: ${hasBackground ? '✅' : '❌'} (${buttonStyle.backgroundColor})`);
        console.log(`Flex display: ${hasFlexDisplay ? '✅' : '❌'} (${buttonStyle.display})`);
        console.log(`Center alignment: ${hasCenterAlign ? '✅' : '❌'} (${buttonStyle.alignItems})`);
        console.log(`Pointer cursor: ${hasPointerCursor ? '✅' : '❌'} (${buttonStyle.cursor})`);
        
        const stylingOK = hasBackground && hasFlexDisplay && hasCenterAlign && hasPointerCursor;
        
        console.log(`Button styling: ${stylingOK ? '✅ PRESERVED' : '⚠️ ISSUES'}`);
        
        // Responsive check
        console.log('\n📱 RESPONSIVE CHECK:');
        const viewportWidth = window.innerWidth;
        console.log(`Viewport width: ${viewportWidth}px`);
        
        if (viewportWidth <= 639) {
            const expectedMobileHeight = 48;
            const isMobileHeight = Math.abs(buttonHeight - expectedMobileHeight) <= 2;
            console.log(`Mobile height (48px): ${isMobileHeight ? '✅' : '⚠️'} (got ${buttonHeight.toFixed(1)}px)`);
        } else {
            const expectedDesktopHeight = 44;
            const isDesktopHeight = Math.abs(buttonHeight - expectedDesktopHeight) <= 2;
            console.log(`Desktop height (44px): ${isDesktopHeight ? '✅' : '⚠️'} (got ${buttonHeight.toFixed(1)}px)`);
        }
        
        // Visual highlight
        console.log('\n🎨 Highlighting elements for 5 seconds...');
        
        // Highlight button in red
        submitButton.style.outline = '3px solid red';
        submitButton.style.outlineOffset = '2px';
        submitButton.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
        
        // Highlight selects in blue
        selects.forEach((select, index) => {
            select.style.outline = '3px solid blue';
            select.style.outlineOffset = '2px';
            select.style.backgroundColor = 'rgba(0, 0, 255, 0.1)';
        });
        
        setTimeout(() => {
            submitButton.style.outline = '';
            submitButton.style.outlineOffset = '';
            submitButton.style.backgroundColor = '';
            
            selects.forEach(select => {
                select.style.outline = '';
                select.style.outlineOffset = '';
                select.style.backgroundColor = '';
            });
            
            console.log('🧹 Highlights removed');
        }, 5000);
        
        // Summary
        console.log('\n📋 SUMMARY:');
        console.log('Red outline = Submit button');
        console.log('Blue outline = Select fields');
        console.log('All elements should have the same height and be visually aligned');
        
        if (perfectAlignment) {
            console.log('🎉 Perfect! Button height matches form fields in Inline layout');
        } else {
            console.log('🔧 Needs adjustment - check CSS rules for .ws-submit .btn-primary');
        }
    }
}
