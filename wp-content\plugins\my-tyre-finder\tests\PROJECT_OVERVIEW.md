# Wheel-Size Plugin – Project Overview

> **Purpose**  
> This document gives a bird’s-eye view of the whole repository so new contributors can quickly understand **what lives where** and **how pieces fit together**.  
> _Auto-generated summary – keep updated after major structural changes._

---

## 1. Top-level Layout

| Path | What it contains |
|------|------------------|
| `assets/` | Source CSS/JS for **frontend & admin** (built via Tailwind/PostCSS/Mix). |
| `bin/` | Helper scripts (e.g. database import, release tooling). |
| `src/` | **PHP source** – all plugin logic. Split into `admin/`, `public/`, `includes/`… |
| `templates/` | Twig templates for widgets (wizard / stepper / inline …). |
| `tests/` | Jest + Playwright smoke tests & visual checks. |
| `languages/` | POT / PO files for i18n. |
| `vendor/` | Composer deps (autoload). |
| Config files | `tailwind*.config.js`, `postcss.config.js`, `webpack.mix.js`, etc. |

<details><summary>Full tree (collapsed)</summary>

```
assets/
  css/
  js/
  ...
node_modules/ (ignored)
src/
  admin/
  public/
  Frontend/
  ...
templates/
  wizard-flow.twig
  finder-form.twig
  ...
PROJECT_OVERVIEW.md  <-- you are here
```
</details>

---

## 2. Build / Tooling

| Tool | File | Role |
|------|------|------|
| **Composer** | `composer.json` | PHP autoload & dependencies. |
| **NPM** | `package.json` | JS build, Tailwind, eslint. |
| **Mix** | `webpack.mix.js` | Bundles + versioning `assets/js` & `assets/css`. |
| **Tailwind** | `tailwind.config.js`, `tailwind.admin.config.js` | Design tokens (wsf-*) & purge paths. |
| **PostCSS** | `postcss.config.js` | Adds autoprefixer, nesting. |

Build output ends in `assets/css/*.css` & `assets/js/*.js` and is enqueued via PHP.

---

## 3. PHP Source (`src/`)

### 3.1 `src/Loader.php`
Central autoloader that wires hooks & actions; instantiates `Admin\Admin` and `Public\Frontend`.

### 3.2 Admin (Settings UI)
* `src/admin/AppearancePage.php` – massive settings panel, Live Preview logic.
* `src/admin/Admin.php` – registers pages, AJAX endpoints for wizard (makes/models/years…).
* `src/admin/BrandFilters.php`, `FeaturesPage.php`, etc. – other WP-Admin sub-pages.

### 3.3 Public / Front-end
* `src/public/Frontend.php` – registers scripts/styles; renders shortcodes `[wheel_fit]`, `[wheel_fit_wizard]` etc.
* `src/Frontend/FormRenderer.php` – chooses Twig template according to layout (wizard/inline/stepper).

### 3.4 Services / Helpers
* `src/services/` – API clients, caching.
* `src/helpers/` – small shared util functions.

---

## 4. Assets

### 4.1 JavaScript
* `assets/js/wizard.js` – SPA-like logic for Wizard form (steps Make → Model → …).
* `assets/js/admin-theme-panel.js` – Tailwind colour picker, live token update.
* `assets/js/admin-appearance.js` – controls Live Preview.

### 4.2 CSS
* `assets/css/wheel-fit-shared.src.css` – Base styles (design tokens, utility classes).
* `assets/css/live-preview-width-fix.css` – Admin-only overrides (recently patched for width & stepper).

Bundled versions live alongside (`.css`), minified on build.

---

## 5. Templates (`templates/`)

| File | Purpose |
|------|---------|
| `wizard-flow.twig` | Wizard Flow (#1) – admin & front. |
| `finder-form.twig` | Classic Stepper (flow 2×2 etc.). |
| `finder-form-inline.twig` | Inline compact form. |
| `finder-popup-horizontal.twig` | Modal/horizontal layout. |

Twig is rendered server-side with variables from `FormRenderer`.

---

## 6. Live Preview Peculiarities

Admin settings inject an iframe-less preview (`#widget-preview`).  
CSS overrides in `assets/css/live-preview-width-fix.css` neutralise WP-Admin rules and keep width = `56rem`.

Recent fixes:
* Transparent brand tiles (wizard step 1)
* Compact circular step indicators (24 px)
* Stepper line now stretches across full container

---

## 7. Testing

`tests/` contains JS-based visual & functional tests:  
* **Playwright** scenarios open admin page & ensure no layout regressions.  
* Jest helpers convert CSS tokens.

Run: `npm test` or `npm run e2e`.

---

## 8. Deployment Notes

1. `npm run prod` – builds minified assets.  
2. `composer install --no-dev` – vendor for production.  
3. Tag release in Git, GitHub Action zips plugin.

---

## 9. Contributing Checklist

- Follow naming convention `wsf-*` for Tailwind utilities.  
- When editing Twig add accompanying i18n keys.  
- Run `npm run lint` + `composer phpcs`.

---

_© Wheel-Size Plugin – internal documentation._ 