<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tire Size Text Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .problem-demo {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .solution-demo {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .demo-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .color-controls {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .color-field {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .color-input {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .color-label {
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        .demo-label {
            color: var(--wsf-text, #1f2937);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        
        .demo-size-card {
            position: relative;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        
        .demo-badge {
            position: absolute;
            top: 6px;
            right: 6px;
            background: #dbeafe;
            color: #1e40af;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .demo-diameter {
            font-size: 2rem;
            font-weight: 900;
            color: var(--wsf-primary, #2563eb);
            line-height: 1;
            margin-bottom: 8px;
        }
        
        .demo-tire-size-old {
            color: var(--wsf-secondary, #6b7280);
            font-size: 14px;
            font-weight: 500;
        }
        
        .demo-tire-size-new {
            color: #374151; /* text-gray-700 - фиксированный цвет */
            font-size: 14px;
            font-weight: 500;
        }
        
        .demo-dual-sizes {
            margin-top: 8px;
            font-size: 14px;
        }
        
        .demo-dual-sizes-old {
            color: var(--wsf-secondary, #6b7280);
        }
        
        .demo-dual-sizes-new {
            color: #374151; /* text-gray-700 - фиксированный цвет */
        }
        
        .demo-dual-label {
            font-weight: 600;
            color: #6b7280; /* text-gray-500 - фиксированный цвет */
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .highlight-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Tire Size Text Fix</h1>
        <p>Исправление проблемы, когда настройка "Text" в Theme Presets влияла на размеры шин (например, "LT185R14").</p>
        
        <!-- Problem Description -->
        <div class="test-section">
            <h3>❌ Проблема</h3>
            <div class="problem-demo">
                <h4>ДО исправления:</h4>
                <p>При изменении цвета <strong>"Text"</strong> в Theme Presets менялся цвет размеров шин:</p>
                <div class="highlight-box">
                    <strong>Примеры:</strong> "LT185R14", "225/45 R17", "P215/60R16"
                </div>
                
                <p><strong>Причина:</strong> Размеры шин в карточках использовали классы <code>wsf-text-secondary</code> и <code>text-wsf-secondary</code>, которые зависят от CSS переменной <code>--wsf-secondary</code>.</p>
                
                <div class="code-block">
// ДО (проблемный код):
const sizesHTML = `&lt;div class="wsf-text-secondary"&gt;${sizeData.tire_full}&lt;/div&gt;`;

/* CSS: */
.wsf-text-secondary { color: var(--wsf-secondary); }
                </div>
            </div>
        </div>
        
        <!-- Solution Description -->
        <div class="test-section">
            <h3>✅ Решение</h3>
            <div class="solution-demo">
                <h4>ПОСЛЕ исправления:</h4>
                <p>Размеры шин теперь используют фиксированные цвета и не зависят от настроек Theme Presets.</p>
                
                <div class="code-block">
// ПОСЛЕ (исправленный код):
const sizesHTML = `&lt;div class="text-gray-700"&gt;${sizeData.tire_full}&lt;/div&gt;`;

/* Фиксированный Tailwind класс, не зависит от CSS переменных */
                </div>
                
                <h4>Исправленные файлы:</h4>
                <ul>
                    <li><code>assets/js/finder.js</code> - функция createSizeCard</li>
                    <li><code>assets/js/wizard.js</code> - функция createSizeCard</li>
                </ul>
                
                <h4>Замены в коде:</h4>
                <ul>
                    <li><code>wsf-text-secondary</code> → <code>text-gray-700</code></li>
                    <li><code>text-wsf-secondary</code> → <code>text-gray-700</code></li>
                    <li><code>wsf-text-muted</code> → <code>text-gray-500</code> (для лейблов Front/Rear)</li>
                </ul>
            </div>
        </div>
        
        <!-- Live Demo -->
        <div class="test-section">
            <h3>🎨 Демонстрация исправления</h3>
            <p>Измените цвета ниже и убедитесь, что размеры шин НЕ меняются:</p>
            
            <div class="color-controls">
                <div class="color-field">
                    <input type="color" class="color-input" value="#1f2937" data-token="--wsf-text">
                    <label class="color-label">Text (для лейблов и заголовков)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#6b7280" data-token="--wsf-secondary">
                    <label class="color-label">Secondary (для вспомогательного текста)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#2563eb" data-token="--wsf-primary">
                    <label class="color-label">Primary (для диаметров)</label>
                </div>
            </div>
            
            <div class="demo-widget" id="demo-widget">
                <div class="demo-label">Factory Sizes</div>
                
                <div class="comparison-grid">
                    <div class="demo-size-card">
                        <div class="demo-badge">FACTORY</div>
                        <div class="demo-diameter">17"</div>
                        <div class="demo-tire-size-new">225/45 R17</div>
                    </div>
                    
                    <div class="demo-size-card">
                        <div class="demo-badge">OPTIONAL</div>
                        <div class="demo-diameter">18"</div>
                        <div class="demo-tire-size-new">LT185R14</div>
                    </div>
                </div>
                
                <div class="demo-size-card">
                    <div class="demo-badge">FACTORY</div>
                    <div class="demo-diameter">17" / 18"</div>
                    <div class="demo-dual-sizes demo-dual-sizes-new">
                        <p><span class="demo-dual-label">Front:</span> P215/60R16</p>
                        <p><span class="demo-dual-label">Rear:</span> P225/55R17</p>
                    </div>
                </div>
                
                <p style="font-size: 12px; color: #6b7280; margin-top: 15px;">
                    ↑ Размеры шин теперь НЕ меняются при изменении "Text" или "Secondary"
                </p>
            </div>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>📊 Сравнение До/После</h3>
            <div class="comparison-grid">
                <div class="problem-demo">
                    <h4>❌ ДО</h4>
                    <p><strong>Проблема:</strong></p>
                    <ul>
                        <li>Text влияет на размеры шин</li>
                        <li>Secondary влияет на размеры шин</li>
                        <li>Неожиданное поведение</li>
                    </ul>
                    
                    <div class="demo-size-card">
                        <div class="demo-diameter">17"</div>
                        <div class="demo-tire-size-old">LT185R14</div>
                        <p style="font-size: 11px; margin-top: 5px;">Меняется при изменении Secondary</p>
                    </div>
                </div>
                
                <div class="solution-demo">
                    <h4>✅ ПОСЛЕ</h4>
                    <p><strong>Решение:</strong></p>
                    <ul>
                        <li>Text влияет только на лейблы</li>
                        <li>Secondary влияет только на UI элементы</li>
                        <li>Размеры шин стабильны</li>
                    </ul>
                    
                    <div class="demo-size-card">
                        <div class="demo-diameter">17"</div>
                        <div class="demo-tire-size-new">LT185R14</div>
                        <p style="font-size: 11px; margin-top: 5px;">Всегда стабильный цвет</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Технические детали</h3>
            
            <h4>Что изменилось в createSizeCard:</h4>
            <div class="code-block">
/* ДО */
wsf-text-secondary    /* Зависел от --wsf-secondary */
text-wsf-secondary    /* Зависел от --wsf-secondary */
wsf-text-muted        /* Зависел от --wsf-muted */

/* ПОСЛЕ */
text-gray-700         /* Фиксированный цвет для размеров */
text-gray-500         /* Фиксированный цвет для лейблов Front/Rear */
            </div>
            
            <h4>Разделение ответственности:</h4>
            <ul>
                <li><strong>--wsf-text</strong> - лейблы полей и заголовки секций</li>
                <li><strong>--wsf-secondary</strong> - вспомогательные UI элементы</li>
                <li><strong>--wsf-primary</strong> - диаметры шин и основные элементы</li>
                <li><strong>text-gray-700</strong> - размеры шин (фиксированный)</li>
                <li><strong>text-gray-500</strong> - лейблы Front/Rear (фиксированный)</li>
            </ul>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить исправление</h4>
            <ol>
                <li><strong>Очистите кэш</strong> WordPress и браузера</li>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>Создайте новую тему или отредактируйте существующую</li>
                <li>Измените цвет <strong>"Text"</strong> на яркий (например, красный)</li>
                <li>Измените цвет <strong>"Secondary"</strong> на яркий (например, зеленый)</li>
                <li>Сохраните и примените тему</li>
                <li>Перейдите на страницу с виджетом и выполните поиск</li>
                <li>Убедитесь, что:
                    <ul>
                        <li>Лейблы полей изменили цвет</li>
                        <li>Заголовки секций изменили цвет</li>
                        <li>Диаметры (17", 18") сохранили primary цвет</li>
                        <li><strong>Размеры шин НЕ изменили цвет</strong></li>
                    </ul>
                </li>
            </ol>
            
            <h4>🎯 Ожидаемый результат</h4>
            <ul>
                <li>✅ Text влияет только на лейблы и заголовки</li>
                <li>✅ Secondary влияет только на UI элементы</li>
                <li>✅ Размеры шин всегда читаемы</li>
                <li>✅ Предсказуемое поведение Theme Presets</li>
            </ul>
        </div>
    </div>

    <script>
        // Color picker functionality
        document.querySelectorAll('.color-input').forEach(input => {
            input.addEventListener('change', function() {
                const token = this.dataset.token;
                const value = this.value;
                const demoWidget = document.getElementById('demo-widget');
                
                demoWidget.style.setProperty(token, value);
                console.log(`Updated ${token} to ${value}`);
            });
        });
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            const demoWidget = document.getElementById('demo-widget');
            
            // Set default values
            demoWidget.style.setProperty('--wsf-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-secondary', '#6b7280');
            demoWidget.style.setProperty('--wsf-primary', '#2563eb');
            demoWidget.style.setProperty('--wsf-border', '#e5e7eb');
            
            console.log('Tire size text fix demo initialized');
        });
    </script>
</body>
</html>
