/**
 * Test script for Grid Garage Spacing
 * 
 * This test verifies that:
 * 1. Vertical spacing between "Find Sizes" button and Garage block is reduced in Grid (2×2) layout
 * 2. Elements appear visually connected, not separated
 * 3. Spacing is balanced and not too tight
 * 4. Changes only apply to Grid layout, not other layouts
 * 5. Responsive behavior works correctly
 * 
 * Run this in browser console on /wp-admin/admin.php?page=wheel-size-appearance
 * Make sure to select "Grid (2×2)" layout first
 */

console.log('📐 Testing Grid Garage Spacing...');

function testGridGarageSpacing() {
    console.log('📋 Starting grid garage spacing tests...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.error('❌ Live Preview container not found');
        return;
    }
    
    console.log('✅ Live Preview container found');
    
    // Test 1: Check if we're in grid layout
    const isGridLayout = testGridLayout(previewContainer);
    
    if (!isGridLayout) {
        console.warn('⚠️ Not in Grid layout - switch to "Grid (2×2)" to test garage spacing');
        return;
    }
    
    // Test 2: Check spacing between elements
    testElementSpacing(previewContainer);
    
    // Test 3: Check visual cohesion
    testVisualCohesion(previewContainer);
    
    // Test 4: Check grid gap
    testGridGap(previewContainer);
    
    // Test 5: Check responsive behavior
    testResponsiveSpacing(previewContainer);
    
    // Test 6: Generate spacing report
    generateSpacingReport(previewContainer);
    
    console.log('🎉 Grid garage spacing tests completed!');
}

function testGridLayout(container) {
    console.log('🔍 Checking if we\'re in Grid layout...');
    
    // Look for grid layout indicators
    const gridForm = container.querySelector('#tab-by-car.grid-2x2');
    const garageButton = container.querySelector('[data-garage-trigger]');
    const submitButton = container.querySelector('.ws-submit .btn-primary');
    
    if (gridForm && garageButton && submitButton) {
        console.log('✅ Grid (2×2) layout detected');
        
        // Check if garage is enabled
        const garageEnabled = container.querySelector('.garage-enabled');
        if (garageEnabled) {
            console.log('✅ Garage is enabled');
        } else {
            console.warn('⚠️ Garage might not be enabled');
        }
        
        return true;
    } else {
        console.log('❌ Grid (2×2) layout not detected');
        console.log('   Make sure to select "Grid (2×2)" in Form Layout dropdown');
        console.log('   And ensure Garage is enabled');
        return false;
    }
}

function testElementSpacing(container) {
    console.log('📏 Testing element spacing...');
    
    const garageButton = container.querySelector('[data-garage-trigger]');
    const submitButton = container.querySelector('.ws-submit .btn-primary');
    const garageContainer = container.querySelector('.flex.flex-col.w-full.sm\\:col-span-2 .items-end, [data-garage-trigger]').closest('.flex.flex-col');
    const submitContainer = container.querySelector('.ws-submit');
    
    if (!garageButton || !submitButton) {
        console.error('❌ Required elements not found');
        console.log('   Garage button:', !!garageButton);
        console.log('   Submit button:', !!submitButton);
        return;
    }
    
    console.log('📊 Element spacing measurements:');
    
    // Get element positions
    const garageRect = garageButton.getBoundingClientRect();
    const submitRect = submitButton.getBoundingClientRect();
    
    console.log(`  Garage button bottom: ${garageRect.bottom.toFixed(1)}px`);
    console.log(`  Submit button top: ${submitRect.top.toFixed(1)}px`);
    
    // Calculate spacing
    const spacing = submitRect.top - garageRect.bottom;
    
    console.log(`  Vertical spacing: ${spacing.toFixed(1)}px`);
    
    // Check if spacing is reasonable (should be reduced but not too tight)
    const minSpacing = 8; // 8px minimum
    const maxSpacing = 24; // 24px maximum (was probably higher before)
    const isReasonableSpacing = spacing >= minSpacing && spacing <= maxSpacing;
    
    if (isReasonableSpacing) {
        console.log(`✅ Spacing is reasonable (${spacing.toFixed(1)}px, range: ${minSpacing}-${maxSpacing}px)`);
    } else if (spacing < minSpacing) {
        console.warn(`⚠️ Spacing too tight (${spacing.toFixed(1)}px, minimum: ${minSpacing}px)`);
    } else {
        console.warn(`⚠️ Spacing too large (${spacing.toFixed(1)}px, maximum: ${maxSpacing}px)`);
    }
    
    // Check container spacing
    if (garageContainer && submitContainer) {
        const garageContainerRect = garageContainer.getBoundingClientRect();
        const submitContainerRect = submitContainer.getBoundingClientRect();
        const containerSpacing = submitContainerRect.top - garageContainerRect.bottom;
        
        console.log(`  Container spacing: ${containerSpacing.toFixed(1)}px`);
    }
    
    return { spacing, isReasonableSpacing, minSpacing, maxSpacing };
}

function testVisualCohesion(container) {
    console.log('👁️ Testing visual cohesion...');
    
    const gridForm = container.querySelector('#tab-by-car.grid-2x2');
    
    if (!gridForm) {
        console.warn('⚠️ Grid form not found for visual test');
        return;
    }
    
    // Get all grid children
    const gridChildren = Array.from(gridForm.children);
    
    console.log(`📊 Grid children analysis (${gridChildren.length} elements):`);
    
    // Find garage and submit elements
    let garageElement = null;
    let submitElement = null;
    
    gridChildren.forEach((child, index) => {
        const hasGarage = child.querySelector('[data-garage-trigger]');
        const hasSubmit = child.classList.contains('ws-submit');
        
        let description = 'Form field';
        if (hasGarage) {
            description = 'Garage container';
            garageElement = child;
        } else if (hasSubmit) {
            description = 'Submit container';
            submitElement = child;
        }
        
        console.log(`  ${index + 1}. ${description} (${child.className})`);
    });
    
    if (garageElement && submitElement) {
        // Check if they appear in correct order
        const garageIndex = gridChildren.indexOf(garageElement);
        const submitIndex = gridChildren.indexOf(submitElement);
        
        console.log(`  Garage position: ${garageIndex + 1}`);
        console.log(`  Submit position: ${submitIndex + 1}`);
        
        const correctOrder = garageIndex < submitIndex;
        
        if (correctOrder) {
            console.log('✅ Elements are in correct order (Garage before Submit)');
        } else {
            console.warn('⚠️ Elements might be in wrong order');
        }
        
        return { correctOrder, garageIndex, submitIndex };
    } else {
        console.warn('⚠️ Could not find garage or submit elements');
        return { correctOrder: false };
    }
}

function testGridGap(container) {
    console.log('📐 Testing grid gap...');
    
    const gridForm = container.querySelector('#tab-by-car.grid-2x2');
    
    if (!gridForm) {
        console.warn('⚠️ Grid form not found for gap test');
        return;
    }
    
    const gridStyle = window.getComputedStyle(gridForm);
    const gap = gridStyle.gap;
    const rowGap = gridStyle.rowGap;
    const columnGap = gridStyle.columnGap;
    
    console.log(`📊 Grid gap measurements:`);
    console.log(`  Gap: ${gap}`);
    console.log(`  Row gap: ${rowGap}`);
    console.log(`  Column gap: ${columnGap}`);
    
    // Check if gap is reduced (should be around 12px instead of 16px)
    const expectedGap = 12; // 0.75rem
    const tolerance = 2;
    
    const gapValue = parseFloat(gap) || parseFloat(rowGap);
    const isCorrectGap = Math.abs(gapValue - expectedGap) <= tolerance;
    
    if (isCorrectGap) {
        console.log(`✅ Grid gap is correct (${gapValue}px ≈ ${expectedGap}px)`);
    } else {
        console.warn(`⚠️ Grid gap might be incorrect (${gapValue}px, expected ~${expectedGap}px)`);
    }
    
    return { gap: gapValue, expectedGap, isCorrectGap };
}

function testResponsiveSpacing(container) {
    console.log('📱 Testing responsive spacing...');
    
    const viewportWidth = window.innerWidth;
    const isMobile = viewportWidth <= 639;
    
    console.log(`📊 Current viewport: ${viewportWidth}px (${isMobile ? 'Mobile' : 'Desktop'})`);
    
    const gridForm = container.querySelector('#tab-by-car.grid-2x2');
    
    if (!gridForm) {
        console.warn('⚠️ Grid form not found for responsive test');
        return;
    }
    
    const gridStyle = window.getComputedStyle(gridForm);
    const gap = parseFloat(gridStyle.gap) || parseFloat(gridStyle.rowGap);
    
    if (isMobile) {
        // On mobile, gap should be even smaller (8px)
        const expectedMobileGap = 8; // 0.5rem
        const isMobileGap = Math.abs(gap - expectedMobileGap) <= 2;
        
        console.log(`  Mobile gap: ${gap}px ${isMobileGap ? '✅' : '⚠️'} (expected ~${expectedMobileGap}px)`);
        
        return { responsive: isMobileGap, isMobile: true, gap };
    } else {
        // On desktop, gap should be 12px
        const expectedDesktopGap = 12; // 0.75rem
        const isDesktopGap = Math.abs(gap - expectedDesktopGap) <= 2;
        
        console.log(`  Desktop gap: ${gap}px ${isDesktopGap ? '✅' : '⚠️'} (expected ~${expectedDesktopGap}px)`);
        
        return { responsive: isDesktopGap, isMobile: false, gap };
    }
}

function generateSpacingReport(container) {
    console.log('📋 Generating spacing report...');
    
    const garageButton = container.querySelector('[data-garage-trigger]');
    const submitButton = container.querySelector('.ws-submit .btn-primary');
    const gridForm = container.querySelector('#tab-by-car.grid-2x2');
    
    if (!garageButton || !submitButton || !gridForm) {
        console.error('❌ Cannot generate report - required elements missing');
        return;
    }
    
    const garageRect = garageButton.getBoundingClientRect();
    const submitRect = submitButton.getBoundingClientRect();
    const spacing = submitRect.top - garageRect.bottom;
    
    const gridStyle = window.getComputedStyle(gridForm);
    const gridGap = parseFloat(gridStyle.gap) || parseFloat(gridStyle.rowGap);
    
    const report = {
        layout: 'GRID_2x2',
        spacing: spacing,
        gridGap: gridGap,
        spacingReduced: spacing <= 24, // Should be reduced from larger value
        spacingReasonable: spacing >= 8 && spacing <= 24,
        gridGapCorrect: Math.abs(gridGap - 12) <= 2,
        status: 'CHECKING',
        issues: []
    };
    
    // Determine overall status
    if (report.spacingReasonable && report.gridGapCorrect) {
        report.status = '✅ PERFECT SPACING';
    } else if (report.spacingReduced) {
        report.status = '⚠️ SPACING REDUCED, MINOR ISSUES';
    } else {
        report.status = '❌ SPACING NEEDS FIXING';
    }
    
    // Generate issues list
    if (!report.spacingReasonable) {
        if (spacing < 8) {
            report.issues.push(`Spacing too tight (${spacing.toFixed(1)}px, should be 8-24px)`);
        } else {
            report.issues.push(`Spacing too large (${spacing.toFixed(1)}px, should be 8-24px)`);
        }
    }
    
    if (!report.gridGapCorrect) {
        report.issues.push(`Grid gap should be ~12px, got ${gridGap}px`);
    }
    
    console.log('\n📊 === SPACING REPORT ===');
    console.log(`Status: ${report.status}`);
    console.log(`Layout: ${report.layout}`);
    console.log(`Garage-Submit spacing: ${report.spacing.toFixed(1)}px`);
    console.log(`Grid gap: ${report.gridGap}px`);
    console.log(`Spacing reduced: ${report.spacingReduced ? '✅' : '❌'}`);
    console.log(`Spacing reasonable: ${report.spacingReasonable ? '✅' : '❌'}`);
    console.log(`Grid gap correct: ${report.gridGapCorrect ? '✅' : '❌'}`);
    
    if (report.issues.length > 0) {
        console.log('\n💡 Issues to fix:');
        report.issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue}`);
        });
    } else {
        console.log('\n🎉 No issues found - spacing is perfectly optimized!');
    }
    
    // Store report for manual access
    window.spacingReport = report;
}

// Manual test helper functions
window.testGridGarageSpacing = function() {
    console.log('🔧 Manual grid garage spacing test...');
    testGridGarageSpacing();
};

window.highlightGridElements = function() {
    console.log('🎨 Highlighting grid elements...');
    
    const garage = document.querySelector('[data-garage-trigger]');
    const submit = document.querySelector('.ws-submit .btn-primary');
    const grid = document.querySelector('#tab-by-car.grid-2x2');
    
    const elements = [
        { element: grid, color: 'rgba(0, 0, 255, 0.1)', label: 'Grid Container' },
        { element: garage, color: 'rgba(0, 255, 0, 0.3)', label: 'Garage Button' },
        { element: submit, color: 'rgba(255, 0, 0, 0.3)', label: 'Submit Button' }
    ];
    
    elements.forEach(({ element, color, label }) => {
        if (element) {
            element.style.backgroundColor = color;
            element.style.border = `2px solid ${color.replace('0.1', '1').replace('0.3', '1')}`;
            console.log(`✅ Highlighted ${label}`);
        } else {
            console.warn(`⚠️ ${label} not found`);
        }
    });
    
    setTimeout(() => {
        elements.forEach(({ element }) => {
            if (element) {
                element.style.backgroundColor = '';
                element.style.border = '';
            }
        });
        console.log('🧹 Highlights removed');
    }, 5000);
};

window.measureGridSpacing = function() {
    console.log('📏 Measuring grid spacing...');
    
    const garage = document.querySelector('[data-garage-trigger]');
    const submit = document.querySelector('.ws-submit .btn-primary');
    const grid = document.querySelector('#tab-by-car.grid-2x2');
    
    if (garage && submit) {
        const garageRect = garage.getBoundingClientRect();
        const submitRect = submit.getBoundingClientRect();
        const spacing = submitRect.top - garageRect.bottom;
        
        console.log('📊 Detailed spacing measurements:');
        console.log(`Garage button bottom: ${garageRect.bottom.toFixed(1)}px`);
        console.log(`Submit button top: ${submitRect.top.toFixed(1)}px`);
        console.log(`Vertical spacing: ${spacing.toFixed(1)}px`);
    }
    
    if (grid) {
        const gridStyle = window.getComputedStyle(grid);
        console.log(`Grid gap: ${gridStyle.gap}`);
        console.log(`Grid row gap: ${gridStyle.rowGap}`);
        console.log(`Grid column gap: ${gridStyle.columnGap}`);
    }
};

// Auto-run the test
testGridGarageSpacing();

console.log('\n🔧 Manual test functions available:');
console.log('  - testGridGarageSpacing() - Run spacing test');
console.log('  - highlightGridElements() - Highlight elements (5s)');
console.log('  - measureGridSpacing() - Show detailed measurements');
console.log('  - spacingReport - Access detailed report');
