<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐞 WF-203: Garage <PERSON><PERSON> Overlap Fix Test</title>
    <style>
        /* Base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #f9fafb;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .test-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .test-header p {
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .test-section {
            padding: 24px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        .test-section h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #374151;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        /* CSS Custom Properties (Theme Variables) */
        :root {
            --wsf-bg: #ffffff;
            --wsf-surface: #f8fafc;
            --wsf-border: #e2e8f0;
            --wsf-text: #1f2937;
            --wsf-text-primary: #111827;
            --wsf-text-muted: #6b7280;
            --wsf-primary: #2563eb;
            --wsf-accent: #3b82f6;
        }
        
        /* Size Card Styles - BEFORE FIX */
        .size-card-before {
            position: relative;
            background: var(--wsf-surface);
            border: 1px solid var(--wsf-border);
            border-radius: 12px;
            padding: 16px;
            padding-top: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            /* OLD: Fixed padding-bottom causes issues */
            padding-bottom: 16px;
        }
        
        /* Size Card Styles - AFTER FIX */
        .size-card-after {
            position: relative;
            background: var(--wsf-surface);
            border: 1px solid var(--wsf-border);
            border-radius: 12px;
            padding: 16px;
            padding-top: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            /* NEW: Dynamic padding-bottom */
            min-height: auto;
            padding-bottom: 2.5rem; /* 40px - место для кнопки + отступ */
        }
        
        /* Карточки с двумя строками размеров (Front/Rear) */
        .size-card-after:has(.dual-sizes) {
            padding-bottom: 3rem; /* 48px - больше места для двухстрочного контента */
        }
        
        .badge {
            position: absolute;
            top: 6px;
            right: 6px;
            z-index: 10;
            pointer-events: none;
            padding: 2px 8px;
            font-size: 10px;
            font-weight: 600;
            border-radius: 4px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .badge-factory {
            background: #fed7aa;
            color: #c2410c;
        }
        
        .badge-optional {
            background: #dbeafe;
            color: var(--wsf-accent);
        }
        
        .diameter {
            font-size: clamp(1.75rem, 5vw, 2.5rem);
            font-weight: 900;
            color: #000;
            line-height: 1;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .tire-size {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            text-align: center;
            margin-top: 4px;
        }
        
        .dual-sizes {
            margin-top: 8px;
            font-size: 14px;
            color: #374151;
            text-align: center;
        }
        
        .dual-sizes p {
            margin: 2px 0;
        }
        
        .dual-label {
            font-weight: 600;
            color: #6b7280;
        }
        
        /* Garage Button Styles - BEFORE FIX */
        .garage-button-before {
            position: absolute;
            bottom: 8px; /* OLD: Fixed position causes overlap */
            right: 8px;
            z-index: 10;
            padding: 4px;
            border-radius: 50%;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        /* Garage Button Styles - AFTER FIX */
        .garage-button-after {
            position: absolute;
            bottom: 0.5rem; /* NEW: Better positioning */
            right: 0.5rem;
            z-index: 10;
            padding: 0.25rem;
            border-radius: 50%;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            pointer-events: auto;
        }
        
        .garage-button-before:hover,
        .garage-button-after:hover {
            background-color: var(--wsf-accent);
        }
        
        .garage-button-before:hover .garage-icon,
        .garage-button-after:hover .garage-icon {
            color: var(--wsf-bg);
        }
        
        .garage-icon {
            width: 24px;
            height: 24px;
            stroke: var(--wsf-accent);
            fill: none;
            stroke-width: 2;
        }
        
        .comparison-label {
            text-align: center;
            font-weight: 600;
            margin-bottom: 12px;
            padding: 8px;
            border-radius: 6px;
        }
        
        .label-before {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .label-after {
            background: #f0fdf4;
            color: #16a34a;
        }
        
        .mobile-test {
            max-width: 320px;
            border: 2px dashed #3b82f6;
            padding: 16px;
            margin: 20px auto;
            border-radius: 8px;
        }
        
        .mobile-test h4 {
            text-align: center;
            margin-bottom: 16px;
            color: #3b82f6;
            font-weight: 600;
        }
        
        /* Responsive fixes */
        @media (max-width: 640px) {
            .size-card-after {
                padding-bottom: 2.75rem; /* Немного больше места на мобильных */
            }
            
            .size-card-after:has(.dual-sizes) {
                padding-bottom: 3.25rem; /* Еще больше для двухстрочных на мобильных */
            }
            
            .garage-button-after {
                bottom: 0.375rem; /* 6px от низа на мобильных */
                right: 0.375rem; /* 6px от правого края на мобильных */
            }
        }
        
        @media (max-width: 375px) {
            .size-card-after {
                padding-bottom: 3rem;
            }
            
            .size-card-after:has(.dual-sizes) {
                padding-bottom: 3.5rem; /* Максимальное место для двухстрочных */
            }
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .status-fixed {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-broken {
            background: #fef2f2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1>🐞 WF-203: Garage Button Overlap Fix</h1>
            <p>Тест исправления перекрытия кнопки "+" размерами шин/дисков</p>
        </div>
        
        <!-- Problem Description -->
        <div class="test-section">
            <h3>📋 Описание проблемы</h3>
            <p><strong>Баг:</strong> Кнопка «+» (добавить в гараж) перекрывает размеры шины/диска и делает карточку нечитаемой</p>
            <p><strong>Условия:</strong></p>
            <ul style="margin-left: 20px; margin-top: 8px;">
                <li>У модификации разные размеры передней и задней оси (две строки текста)</li>
                <li>На маленьких карточках остаётся жёсткий padding-bottom</li>
                <li>На устройствах 320-375px текст уходит под кнопку</li>
            </ul>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>🔄 Сравнение: До и После исправления</h3>
            
            <div class="test-grid">
                <!-- BEFORE: Single line -->
                <div>
                    <div class="comparison-label label-before">❌ ДО: Однострочная карточка</div>
                    <div class="size-card-before">
                        <div class="badge badge-factory">FACTORY</div>
                        <div class="diameter">17"</div>
                        <div class="tire-size">225/45 R17</div>
                        <button class="garage-button-before" title="Add to Garage">
                            <svg class="garage-icon" viewBox="0 0 24 24">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </button>
                    </div>
                    <p style="font-size: 12px; color: #dc2626; text-align: center; margin-top: 8px;">
                        Лишний отступ снизу
                    </p>
                </div>
                
                <!-- AFTER: Single line -->
                <div>
                    <div class="comparison-label label-after">✅ ПОСЛЕ: Однострочная карточка</div>
                    <div class="size-card-after">
                        <div class="badge badge-factory">FACTORY</div>
                        <div class="diameter">17"</div>
                        <div class="tire-size">225/45 R17</div>
                        <button class="garage-button-after" title="Add to Garage">
                            <svg class="garage-icon" viewBox="0 0 24 24">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </button>
                    </div>
                    <p style="font-size: 12px; color: #16a34a; text-align: center; margin-top: 8px;">
                        Оптимальный отступ
                    </p>
                </div>
            </div>
            
            <div class="test-grid">
                <!-- BEFORE: Dual line -->
                <div>
                    <div class="comparison-label label-before">❌ ДО: Двухстрочная карточка</div>
                    <div class="size-card-before">
                        <div class="badge badge-factory">FACTORY</div>
                        <div class="diameter">17" / 18"</div>
                        <div class="dual-sizes">
                            <p><span class="dual-label">Front:</span> P215/60R16</p>
                            <p><span class="dual-label">Rear:</span> P225/55R17</p>
                        </div>
                        <button class="garage-button-before" title="Add to Garage">
                            <svg class="garage-icon" viewBox="0 0 24 24">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </button>
                    </div>
                    <p style="font-size: 12px; color: #dc2626; text-align: center; margin-top: 8px;">
                        🚨 Кнопка перекрывает текст!
                    </p>
                </div>
                
                <!-- AFTER: Dual line -->
                <div>
                    <div class="comparison-label label-after">✅ ПОСЛЕ: Двухстрочная карточка</div>
                    <div class="size-card-after">
                        <div class="badge badge-factory">FACTORY</div>
                        <div class="diameter">17" / 18"</div>
                        <div class="dual-sizes">
                            <p><span class="dual-label">Front:</span> P215/60R16</p>
                            <p><span class="dual-label">Rear:</span> P225/55R17</p>
                        </div>
                        <button class="garage-button-after" title="Add to Garage">
                            <svg class="garage-icon" viewBox="0 0 24 24">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </button>
                    </div>
                    <p style="font-size: 12px; color: #16a34a; text-align: center; margin-top: 8px;">
                        ✅ Кнопка не перекрывает текст
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Mobile Test -->
        <div class="test-section">
            <h3>📱 Тест на мобильных устройствах (320px)</h3>
            
            <div class="mobile-test">
                <h4>iPhone SE / Galaxy S8 (320px)</h4>
                
                <div style="margin-bottom: 20px;">
                    <div class="comparison-label label-after">✅ ПОСЛЕ: Адаптивная карточка</div>
                    <div class="size-card-after">
                        <div class="badge badge-optional">OPTIONAL</div>
                        <div class="diameter">19"</div>
                        <div class="dual-sizes">
                            <p><span class="dual-label">Front:</span> 245/35R19</p>
                            <p><span class="dual-label">Rear:</span> 275/30R19</p>
                        </div>
                        <button class="garage-button-after" title="Add to Garage">
                            <svg class="garage-icon" viewBox="0 0 24 24">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </button>
                    </div>
                    <p style="font-size: 12px; color: #16a34a; text-align: center; margin-top: 8px;">
                        Увеличенные отступы для мобильных
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Технические детали исправления</h3>
            
            <h4>Изменения в CSS:</h4>
            <div style="background: #f8fafc; padding: 16px; border-radius: 8px; margin: 12px 0; font-family: monospace; font-size: 14px;">
<strong>/* Базовые стили для карточек размеров */</strong><br>
.size-card {<br>
&nbsp;&nbsp;min-height: auto !important; /* Убираем фиксированную высоту */<br>
&nbsp;&nbsp;padding-bottom: 2.5rem !important; /* Место для кнопки + отступ */<br>
}<br><br>

<strong>/* Карточки с двумя строками размеров (Front/Rear) */</strong><br>
.size-card:has(.space-y-0\.5) {<br>
&nbsp;&nbsp;padding-bottom: 3rem !important; /* Больше места для двухстрочного контента */<br>
}<br><br>

<strong>/* Кнопка добавления в гараж - улучшенное позиционирование */</strong><br>
.save-to-garage {<br>
&nbsp;&nbsp;bottom: 0.5rem !important; /* 8px от низа */<br>
&nbsp;&nbsp;right: 0.5rem !important; /* 8px от правого края */<br>
}
            </div>
            
            <h4>Изменения в JavaScript:</h4>
            <div style="background: #f8fafc; padding: 16px; border-radius: 8px; margin: 12px 0; font-family: monospace; font-size: 14px;">
<strong>// Убраны классы позиционирования из HTML</strong><br>
// ДО: class="save-to-garage wsf-garage-hover absolute bottom-2 right-2 z-10 p-1"<br>
// ПОСЛЕ: class="save-to-garage wsf-garage-hover"<br><br>

<strong>// Исправлен цвет иконки</strong><br>
// ДО: class="w-6 h-6 text-blue-600"<br>
// ПОСЛЕ: class="w-6 h-6 wsf-text-accent"
            </div>
        </div>
        
        <!-- Status Check -->
        <div class="test-section">
            <h3>✅ Статус исправлений</h3>
            
            <div style="display: grid; gap: 12px;">
                <div>
                    <strong>Кнопка не перекрывает текст</strong>
                    <span class="status-indicator status-fixed">ИСПРАВЛЕНО</span>
                </div>
                <div>
                    <strong>Минимальная высота карточки</strong>
                    <span class="status-indicator status-fixed">ИСПРАВЛЕНО</span>
                </div>
                <div>
                    <strong>Ровная сетка карточек</strong>
                    <span class="status-indicator status-fixed">ИСПРАВЛЕНО</span>
                </div>
                <div>
                    <strong>Адаптивность на мобильных</strong>
                    <span class="status-indicator status-fixed">ИСПРАВЛЕНО</span>
                </div>
                <div>
                    <strong>Поддержка тем (wsf-text-accent)</strong>
                    <span class="status-indicator status-fixed">ИСПРАВЛЕНО</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
