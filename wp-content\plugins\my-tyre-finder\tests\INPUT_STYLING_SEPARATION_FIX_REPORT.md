# Input Styling Separation Fix Report

## Проблема
Элементы select использовали основной цвет фона виджета (--wsf-bg) вместо специальных переменных для полей ввода. Это приводило к тому, что поля ввода наследовали цвет фона виджета, а не имели собственную стилизацию.

## Выполненные исправления

### 1. ✅ Добавлены CSS переменные для полей ввода
**Файл:** `assets/css/wheel-fit-shared.src.css`

Добавлены новые CSS переменные в секции `:root`:
```css
/* Input-specific tokens */
--wsf-input-bg:            var(--wsf-bg);
--wsf-input-text:          var(--wsf-text);
--wsf-input-border:        var(--wsf-border);
--wsf-input-placeholder:   var(--wsf-muted);
--wsf-input-focus:         var(--wsf-primary);
```

Переменные добавлены во все темы:
- Базовая тема (по умолчанию)
- `.wsf-theme-light` 
- `.wsf-theme-dark`

### 2. ✅ Создан унифицированный класс .wsf-input
**Файл:** `assets/css/wheel-fit-shared.src.css`

```css
.wsf-input {
  background: var(--wsf-input-bg);
  color: var(--wsf-input-text);
  border: 1px solid var(--wsf-input-border);
  border-radius: .5rem;
  transition: .15s;
  height: var(--ws-control-height);
  padding: 0 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.3;
}

.wsf-input:focus {
  border-color: var(--wsf-input-focus);
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--wsf-input-focus) 35%, transparent);
  outline: none;
}

.wsf-input::placeholder {
  color: var(--wsf-input-placeholder);
}

.wsf-input:disabled {
  opacity: 0.35;
  pointer-events: none;
  background-color: var(--wsf-surface);
}
```

### 3. ✅ Обновлены шаблоны полей
**Файлы:** `templates/fields/*.twig`

Заменены классы во всех шаблонах полей:

**До:**
```html
<select class="block w-full bg-wsf-bg border-wsf-border rounded-lg px-4 py-3 shadow-sm focus:ring-2 focus:ring-wsf-primary/40 focus:border-wsf-primary transition">
```

**После:**
```html
<select class="wsf-input block w-full">
```

Обновленные файлы:
- `templates/fields/make.twig`
- `templates/fields/model.twig` 
- `templates/fields/year.twig`
- `templates/fields/gen.twig`
- `templates/fields/mod.twig`

### 4. ✅ Исправлены жестко закодированные цвета в JavaScript
**Файлы:** `assets/js/finder.js`, `assets/js/wizard.js`

Добавлены вспомогательные CSS классы:
```css
.wsf-card { /* Карточки */ }
.wsf-text-primary, .wsf-text-secondary, .wsf-text-muted { /* Цвета текста */ }
.wsf-bg-surface, .wsf-bg-surface-hover { /* Фоны поверхностей */ }
.wsf-step-completed, .wsf-step-active, .wsf-step-inactive { /* Индикаторы шагов */ }
```

**Заменены жестко закодированные цвета:**
- `bg-white` → `wsf-card`
- `text-slate-500` → `wsf-text-muted`
- `text-slate-900` → `wsf-text-primary`
- `bg-slate-50` → `wsf-bg-surface`
- `text-blue-600` → `text-wsf-primary`
- `bg-blue-600` → `wsf-step-active`
- И многие другие...

### 5. ✅ Обновлены основные шаблоны
**Файл:** `templates/finder-form.twig`

Обновлены встроенные стили:
```css
/* До */
.wheel-fit-widget select{border:1px solid #d1d5db;}
.wheel-fit-widget select option[value=""]{color:rgb(100 116 139)}

/* После */
.wheel-fit-widget select{border:1px solid var(--wsf-input-border);background:var(--wsf-input-bg);color:var(--wsf-input-text)}
.wheel-fit-widget select option[value=""]{color:var(--wsf-input-placeholder)}
```

### 6. ✅ Создан тестовый файл
**Файл:** `test-input-styling-fix.html`

Тестовый файл включает:
- Сравнение "до" и "после"
- Тест переключения тем
- Проверку CSS переменных
- Демонстрацию нового класса `.wsf-input`

## Результат

### Достигнутые цели:
1. ✅ **Разделение стилей:** Поля ввода теперь имеют собственные CSS переменные, независимые от фона виджета
2. ✅ **Консистентность:** Все поля ввода используют единый класс `.wsf-input`
3. ✅ **Темизация:** Поля ввода корректно работают с переключением тем
4. ✅ **Доступность:** Сохранены состояния focus, disabled, placeholder
5. ✅ **Производительность:** Убраны жестко закодированные цвета из JavaScript

### Преимущества:
- **Гибкость:** Легко изменить стили полей ввода через CSS переменные
- **Консистентность:** Единообразный внешний вид всех полей
- **Поддержка тем:** Автоматическая адаптация к светлой/темной теме
- **Расширяемость:** Простое добавление новых состояний и стилей

### Тестирование:
Откройте `test-input-styling-fix.html` в браузере для проверки:
1. Переключение между светлой и темной темой
2. Состояния focus и disabled
3. Отображение CSS переменных
4. Сравнение старых и новых стилей

## Заключение
Проблема разделения стилей виджета и полей ввода успешно решена. Теперь поля ввода имеют собственную систему стилизации, независимую от фона виджета, что обеспечивает лучшую гибкость и консистентность интерфейса.
