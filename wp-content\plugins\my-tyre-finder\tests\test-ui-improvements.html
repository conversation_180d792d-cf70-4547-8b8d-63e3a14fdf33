<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Improvements Test - Wheel Size Finder</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 2rem;
            background: #f8fafc;
            line-height: 1.6;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .test-checklist {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .checklist-item {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            font-size: 0.875rem;
        }
        
        .checklist-item input[type="checkbox"] {
            margin-top: 0.125rem;
        }
        
        .status-good { color: #059669; }
        .status-warning { color: #d97706; }
        .status-error { color: #dc2626; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>UI Improvements Test</h1>
            <p>Тестирование исправлений интерфейса виджета поиска шин согласно техническому заданию.</p>
        </div>
        
        <div class="test-checklist">
            <h2>Критерии приёмки</h2>
            
            <div class="checklist-item">
                <input type="checkbox" id="stepper">
                <label for="stepper"><strong>A. Степпер и прогресс:</strong> Чипы с номерами, активный шаг заливкой, пройденные зелёным, кликабельность, контраст ≥4.5:1</label>
            </div>
            
            <div class="checklist-item">
                <input type="checkbox" id="compression">
                <label for="compression"><strong>B. Сжатие вертикали:</strong> Паддинг превью ≤24px, заголовка ≤16px, фиксированная высота clamp(480px, 60vh, 560px)</label>
            </div>
            
            <div class="checklist-item">
                <input type="checkbox" id="toggle">
                <label for="toggle"><strong>C. Переключатель Tiles/List:</strong> Компактный сегмент-контрол ≤120px справа, счётчик слева</label>
            </div>
            
            <div class="checklist-item">
                <input type="checkbox" id="search">
                <label for="search"><strong>D. Поиск:</strong> Под заголовком, автофокус, фокус-кольцо 2-3px, Esc очищает, Enter выбирает первый</label>
            </div>
            
            <div class="checklist-item">
                <input type="checkbox" id="tiles">
                <label for="tiles"><strong>E. Карточки марок:</strong> clamp(72px, 8vw, 96px), иконка 32-40px, gap 12px, ≥9 колонок в 1200px</label>
            </div>
            
            <div class="checklist-item">
                <input type="checkbox" id="breadcrumb">
                <label for="breadcrumb"><strong>F. Breadcrumb:</strong> Чипы под степпером, обновление ≤50ms, клавиатурная навигация</label>
            </div>
            
            <div class="checklist-item">
                <input type="checkbox" id="footer">
                <label for="footer"><strong>G. Sticky footer:</strong> 56px высота, Back/Continue, не перекрывает контент</label>
            </div>
            
            <div class="checklist-item">
                <input type="checkbox" id="scroll">
                <label for="scroll"><strong>H. Один скролл:</strong> Только overflow-y:auto в панели контента</label>
            </div>
        </div>

        <!-- Widget Test Area -->
        <div id="wheel-fit-wizard" class="wheel-fit-widget max-w-4xl mx-auto p-4 md:p-6 pb-20 bg-wsf-bg wsf-root-font rounded-lg shadow-lg">
            
            <!-- Slim Header -->
            <div class="wsf-widget__header wsf-slim-header flex items-center justify-between h-14 px-6 mb-4 bg-wsf-bg border-b border-wsf-border-light shadow-sm">
                <h1 class="wsf-widget__title text-left text-lg md:text-xl font-bold tracking-tight wsf-text-primary m-0 flex-shrink-0">Wheel & Tyre Finder</h1>
                
                <div class="wsf-header-controls flex items-center gap-3">
                    <button id="wizard-reset-btn" class="wsf-reset-chip px-3 py-1 text-xs font-medium text-wsf-secondary bg-wsf-surface hover:bg-wsf-surface-hover border border-wsf-border rounded-full transition-all duration-200" title="Reset search">
                        Reset
                    </button>
                    
                    <button id="wizard-share-btn" class="wsf-share-btn p-2 text-wsf-secondary hover:text-wsf-primary hover:bg-wsf-surface rounded-full transition-all duration-200" title="Share search" aria-label="Share search">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Stepper with chips and connectors -->
            <div id="wizard-header" class="mb-4">
                <div class="wsf-stepper flex items-center justify-between mb-3">
                    <button id="wizard-step-chip-1" class="wsf-step-chip wsf-step-chip--active" aria-current="step" tabindex="0" role="button">
                        <span class="wsf-step-number">1</span>
                        <span class="wsf-step-label">Make</span>
                    </button>
                    <div class="wsf-step-connector"></div>
                    <button id="wizard-step-chip-2" class="wsf-step-chip" tabindex="0" role="button">
                        <span class="wsf-step-number">2</span>
                        <span class="wsf-step-label">Model</span>
                    </button>
                    <div class="wsf-step-connector"></div>
                    <button id="wizard-step-chip-3" class="wsf-step-chip" tabindex="0" role="button">
                        <span class="wsf-step-number">3</span>
                        <span class="wsf-step-label">Year</span>
                    </button>
                    <div class="wsf-step-connector"></div>
                    <button id="wizard-step-chip-4" class="wsf-step-chip" tabindex="0" role="button">
                        <span class="wsf-step-number">4</span>
                        <span class="wsf-step-label">Trim</span>
                    </button>
                    <div class="wsf-step-connector"></div>
                    <button id="wizard-step-chip-5" class="wsf-step-chip" tabindex="0" role="button">
                        <span class="wsf-step-number">5</span>
                        <span class="wsf-step-label">Options</span>
                    </button>
                </div>
                
                <!-- Thin progress bar -->
                <div class="wsf-progress-track">
                    <div id="wizard-progress-bar" class="wsf-progress-fill" style="width: 20%"></div>
                </div>
                
                <!-- Breadcrumb path -->
                <div id="wizard-breadcrumb" class="wsf-breadcrumb mt-2">
                    <button class="wsf-breadcrumb-chip">Audi</button>
                    <span class="wsf-breadcrumb-separator">›</span>
                    <button class="wsf-breadcrumb-chip">A4</button>
                </div>
            </div>

            <!-- Step Content - Fixed Height Container -->
            <div class="wsf-content-panel">
                <div class="wsf-content-scroll">
                    <!-- Step 1: Makes -->
                    <div id="wizard-step-1" class="wizard-step">
                        <!-- Compact header with counter and view toggle -->
                        <div class="wsf-step-header">
                            <div class="wsf-step-counter">
                                <span id="wizard-makes-count" class="wsf-counter-text">248 items</span>
                            </div>
                            <div class="wsf-view-segment">
                                <button id="view-toggle-tiles" class="wsf-segment-btn wsf-segment-btn--active" data-view="tiles" title="Tile view">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                                    </svg>
                                    <span class="wsf-segment-label">Tiles</span>
                                </button>
                                <button id="view-toggle-list" class="wsf-segment-btn" data-view="list" title="List view">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                                    </svg>
                                    <span class="wsf-segment-label">List</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Search field -->
                        <div class="wsf-search-wrapper">
                            <div class="wsf-search-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                            <input type="text" class="wsf-search-input" placeholder="Search brands…">
                        </div>
                        
                        <!-- Tiles grid -->
                        <div id="wizard-makes-grid" class="wsf-tiles-grid">
                            <!-- Sample tiles -->
                            <button class="wsf-compact-tile">
                                <div class="wsf-tile-icon">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzMzNzNkYyIvPgo8dGV4dCB4PSIyMCIgeT0iMjQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkE8L3RleHQ+Cjwvc3ZnPgo=" alt="Audi">
                                </div>
                                <span class="wsf-tile-label">Audi</span>
                            </button>
                            
                            <button class="wsf-compact-tile">
                                <div class="wsf-tile-icon">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzAwN2NiYSIvPgo8dGV4dCB4PSIyMCIgeT0iMjQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkI8L3RleHQ+Cjwvc3ZnPgo=" alt="BMW">
                                </div>
                                <span class="wsf-tile-label">BMW</span>
                            </button>
                            
                            <!-- Add more sample tiles as needed -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sticky Footer Navigation -->
            <div id="wizard-nav" class="wsf-sticky-footer fixed bottom-0 left-0 right-0 bg-wsf-bg border-t border-wsf-border-light shadow-lg z-20">
                <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
                    <button id="wizard-back-btn" class="wsf-nav-btn wsf-nav-btn--secondary flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 hidden">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                        </svg>
                        Back
                    </button>
                    
                    <div class="flex-1"></div>
                    
                    <button id="wizard-next-btn" class="wsf-nav-btn wsf-nav-btn--primary flex items-center gap-2 px-6 py-2 rounded-lg transition-all duration-200">
                        Continue
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple test interactions
        document.addEventListener('DOMContentLoaded', function() {
            // View toggle functionality
            const tilesBtn = document.getElementById('view-toggle-tiles');
            const listBtn = document.getElementById('view-toggle-list');
            const grid = document.getElementById('wizard-makes-grid');
            
            tilesBtn.addEventListener('click', function() {
                tilesBtn.classList.add('wsf-segment-btn--active');
                listBtn.classList.remove('wsf-segment-btn--active');
                grid.className = 'wsf-tiles-grid';
            });
            
            listBtn.addEventListener('click', function() {
                listBtn.classList.add('wsf-segment-btn--active');
                tilesBtn.classList.remove('wsf-segment-btn--active');
                grid.className = 'wsf-list-view';
            });
            
            // Search functionality
            const searchInput = document.querySelector('.wsf-search-input');
            searchInput.addEventListener('input', function(e) {
                console.log('Search:', e.target.value);
            });
            
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    e.target.value = '';
                    console.log('Search cleared');
                }
            });
            
            // Step navigation
            document.querySelectorAll('.wsf-step-chip').forEach((chip, index) => {
                chip.addEventListener('click', function() {
                    document.querySelectorAll('.wsf-step-chip').forEach(c => {
                        c.classList.remove('wsf-step-chip--active', 'wsf-step-chip--completed');
                        c.removeAttribute('aria-current');
                    });
                    
                    // Set active step
                    chip.classList.add('wsf-step-chip--active');
                    chip.setAttribute('aria-current', 'step');
                    
                    // Set completed steps
                    for (let i = 0; i < index; i++) {
                        document.querySelectorAll('.wsf-step-chip')[i].classList.add('wsf-step-chip--completed');
                    }
                    
                    // Update progress bar
                    const progress = ((index + 1) / 5) * 100;
                    document.getElementById('wizard-progress-bar').style.width = progress + '%';
                });
            });
        });
    </script>
</body>
</html>
