/**
 * Debug Theme Activation Issues
 * Диагностика проблем с активацией пользовательских тем
 */

(function() {
    'use strict';

    console.log('🔍 === DEBUG THEME ACTIVATION ===');

    // Проверяем доступность API
    if (typeof wpApiSettings === 'undefined') {
        console.error('❌ wpApiSettings not found');
        return;
    }

    const apiBase = wpApiSettings.root + 'wheel-size/v1/themes';
    const nonce = wpApiSettings.nonce;

    console.log('✅ API Base:', apiBase);
    console.log('✅ Nonce available:', !!nonce);

    // Тестовые данные для создания темы
    const testThemeData = {
        name: 'Debug Test Theme',
        properties: {
            '--wsf-primary': '#ff6b35',
            '--wsf-bg': '#ffffff',
            '--wsf-text': '#333333',
            '--wsf-border': '#e1e5e9',
            '--wsf-hover': '#e55a2b',
            '--wsf-secondary': '#6b7280',
            '--wsf-accent': '#3b82f6',
            '--wsf-muted': '#9ca3af',
            '--wsf-success': '#10b981',
            '--wsf-warning': '#f59e0b',
            '--wsf-error': '#ef4444'
        }
    };

    // Функции для тестирования
    const tests = {
        // 1. Получить все темы
        async getAllThemes() {
            console.log('\n--- Test 1: GET /themes ---');
            try {
                const response = await fetch(apiBase, {
                    method: 'GET',
                    headers: {
                        'X-WP-Nonce': nonce
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('✅ GET /themes successful:', data);
                return data;
            } catch (error) {
                console.error('❌ GET /themes failed:', error);
                return null;
            }
        },

        // 2. Создать тестовую тему
        async createTestTheme() {
            console.log('\n--- Test 2: POST /themes (create) ---');
            try {
                const response = await fetch(apiBase, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': nonce
                    },
                    body: JSON.stringify(testThemeData)
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);

                const responseText = await response.text();
                console.log('Raw response:', responseText);

                if (!response.ok) {
                    let errorData;
                    try {
                        errorData = JSON.parse(responseText);
                    } catch (e) {
                        errorData = { message: responseText || 'Unknown error' };
                    }
                    throw new Error(`HTTP ${response.status}: ${errorData.message || response.statusText}`);
                }

                const data = JSON.parse(responseText);
                console.log('✅ POST /themes successful:', data);
                return data;
            } catch (error) {
                console.error('❌ POST /themes failed:', error);
                return null;
            }
        },

        // 3. Активировать тему
        async activateTheme(slug) {
            console.log(`\n--- Test 3: PUT /themes/active (${slug}) ---`);
            try {
                const response = await fetch(apiBase + '/active', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': nonce
                    },
                    body: JSON.stringify({ slug: slug })
                });

                console.log('Response status:', response.status);
                
                const responseText = await response.text();
                console.log('Raw response:', responseText);

                if (!response.ok) {
                    let errorData;
                    try {
                        errorData = JSON.parse(responseText);
                    } catch (e) {
                        errorData = { message: responseText || 'Unknown error' };
                    }
                    throw new Error(`HTTP ${response.status}: ${errorData.message || response.statusText}`);
                }

                const data = JSON.parse(responseText);
                console.log('✅ PUT /themes/active successful:', data);
                return data;
            } catch (error) {
                console.error('❌ PUT /themes/active failed:', error);
                return null;
            }
        },

        // 4. Проверить активную тему
        async getActiveTheme() {
            console.log('\n--- Test 4: GET /themes/active ---');
            try {
                const response = await fetch(apiBase + '/active', {
                    method: 'GET',
                    headers: {
                        'X-WP-Nonce': nonce
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('✅ GET /themes/active successful:', data);
                return data;
            } catch (error) {
                console.error('❌ GET /themes/active failed:', error);
                return null;
            }
        },

        // 5. Проверить валидацию цветов
        async testColorValidation() {
            console.log('\n--- Test 5: Color Validation ---');
            
            const testColors = [
                '#ffffff',      // Валидный hex
                '#FF0000',      // Валидный hex uppercase
                '#f00',         // Короткий hex
                'rgb(255,0,0)', // RGB (может не пройти валидацию)
                'red',          // Именованный цвет (может не пройти валидацию)
                '#gggggg',      // Невалидный hex
                ''              // Пустое значение
            ];

            for (const color of testColors) {
                const testData = {
                    name: `Color Test ${color}`,
                    properties: {
                        '--wsf-primary': color,
                        '--wsf-bg': '#ffffff',
                        '--wsf-text': '#000000'
                    }
                };

                try {
                    const response = await fetch(apiBase, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': nonce
                        },
                        body: JSON.stringify(testData)
                    });

                    if (response.ok) {
                        const data = await response.json();
                        console.log(`✅ Color "${color}" accepted:`, data.slug);
                        
                        // Удаляем тестовую тему
                        await fetch(apiBase + '/' + data.slug, {
                            method: 'DELETE',
                            headers: { 'X-WP-Nonce': nonce }
                        });
                    } else {
                        const errorText = await response.text();
                        console.log(`❌ Color "${color}" rejected:`, errorText);
                    }
                } catch (error) {
                    console.log(`❌ Color "${color}" error:`, error.message);
                }
            }
        },

        // 6. Проверить минимальные требования
        async testMinimalTheme() {
            console.log('\n--- Test 6: Minimal Theme ---');
            
            const minimalData = {
                name: 'Minimal Test',
                properties: {
                    '--wsf-primary': '#2563eb'
                }
            };

            try {
                const response = await fetch(apiBase, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': nonce
                    },
                    body: JSON.stringify(minimalData)
                });

                const responseText = await response.text();
                console.log('Minimal theme response:', responseText);

                if (response.ok) {
                    const data = JSON.parse(responseText);
                    console.log('✅ Minimal theme created:', data.slug);
                    return data.slug;
                } else {
                    console.log('❌ Minimal theme rejected');
                    return null;
                }
            } catch (error) {
                console.error('❌ Minimal theme error:', error);
                return null;
            }
        }
    };

    // Запуск всех тестов
    async function runAllTests() {
        console.log('\n🚀 Starting comprehensive theme tests...\n');

        // 1. Получить существующие темы
        const existingThemes = await tests.getAllThemes();
        
        // 2. Проверить текущую активную тему
        const currentActive = await tests.getActiveTheme();
        
        // 3. Тест валидации цветов
        await tests.testColorValidation();
        
        // 4. Тест минимальной темы
        const minimalSlug = await tests.testMinimalTheme();
        
        // 5. Создать полную тестовую тему
        const createdTheme = await tests.createTestTheme();
        
        if (createdTheme && createdTheme.slug) {
            // 6. Попытаться активировать созданную тему
            const activationResult = await tests.activateTheme(createdTheme.slug);
            
            // 7. Проверить, что тема действительно активна
            const newActive = await tests.getActiveTheme();
            
            if (newActive && newActive.active_theme === createdTheme.slug) {
                console.log('\n🎉 SUCCESS: Theme created and activated successfully!');
            } else {
                console.log('\n❌ FAILURE: Theme created but activation failed');
            }
        }

        console.log('\n📊 Test Summary:');
        console.log('- API endpoints:', existingThemes ? '✅' : '❌');
        console.log('- Theme creation:', createdTheme ? '✅' : '❌');
        console.log('- Theme activation:', '⏳ Check above results');
        
        console.log('\n💡 Next steps:');
        console.log('1. Check browser Network tab for failed requests');
        console.log('2. Check WordPress debug.log for PHP errors');
        console.log('3. Verify theme data in database');
    }

    // Экспорт функций для ручного тестирования
    window.themeDebug = {
        runAllTests,
        ...tests,
        testThemeData
    };

    // Автоматический запуск через 1 секунду
    setTimeout(runAllTests, 1000);

    console.log('\n💡 Manual testing available via window.themeDebug');
    console.log('Example: window.themeDebug.createTestTheme()');

})();
