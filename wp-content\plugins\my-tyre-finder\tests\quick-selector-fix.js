/**
 * Быстрое исправление переводов селекторов
 * Простое и быстрое решение без сложных проверок
 */

(function() {
    'use strict';

    console.log('[Quick Fix] Инициализация быстрого исправления селекторов...');

    // Простая функция быстрого перевода селекторов
    function quickTranslateSelectors() {
        if (!window.WheelFitI18n) return;

        const fixes = [
            { id: 'wf-make', key: 'select_make_placeholder' },
            { id: 'wf-model', key: 'select_model_placeholder' },
            { id: 'wf-year', key: 'select_year_placeholder' },
            { id: 'wf-modification', key: 'select_mods_placeholder' },
            { id: 'wf-generation', key: 'select_gen_placeholder' }
        ];

        fixes.forEach(({ id, key }) => {
            const select = document.getElementById(id);
            if (select && window.WheelFitI18n[key]) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    const currentText = placeholderOption.textContent.trim();
                    const translatedText = window.WheelFitI18n[key];
                    
                    // Быстрая проверка и исправление
                    if (currentText !== translatedText && 
                        (currentText.includes('Choose') || currentText.includes('Select') || currentText.includes('Loading'))) {
                        placeholderOption.textContent = translatedText;
                        placeholderOption.setAttribute('data-i18n', key);
                    }
                }
            }
        });
    }

    // Быстрый мониторинг изменений
    let isMonitoring = false;
    function startQuickMonitoring() {
        if (isMonitoring) return;
        isMonitoring = true;

        // Проверяем каждые 2 секунды
        const interval = setInterval(() => {
            quickTranslateSelectors();
        }, 2000);

        // Останавливаем через 30 секунд
        setTimeout(() => {
            clearInterval(interval);
            isMonitoring = false;
            console.log('[Quick Fix] Мониторинг остановлен');
        }, 30000);

        console.log('[Quick Fix] Быстрый мониторинг запущен');
    }

    // Слушаем события изменения формы
    document.addEventListener('change', function(e) {
        if (e.target && (e.target.id === 'search_flow' || e.target.id === 'form_layout')) {
            console.log('[Quick Fix] Обнаружено изменение настроек, запускаем исправление...');
            
            // Быстрое исправление через 500ms
            setTimeout(() => {
                quickTranslateSelectors();
            }, 500);
            
            // Еще одно исправление через 2 секунды (на всякий случай)
            setTimeout(() => {
                quickTranslateSelectors();
            }, 2000);
        }
    });

    // Слушаем события обновления предварительного просмотра
    document.addEventListener('previewUpdated', function(e) {
        console.log('[Quick Fix] Обнаружено обновление предварительного просмотра');
        
        // Быстрое исправление
        setTimeout(() => {
            quickTranslateSelectors();
        }, 100);
    });

    // ОТКЛЮЧЕНО: Перехват функций populate больше не нужен,
    // так как мы исправили проблему в самих функциях
    function interceptPopulateFunctions() {
        console.log('[Quick Fix] Перехват функций populate ОТКЛЮЧЕН - используем исправленные функции');
        // Больше не перехватываем функции, так как они исправлены
    }

    // Глобальная функция для ручного исправления
    window.quickFixSelectors = quickTranslateSelectors;

    // Запускаем исправления (БЕЗ перехвата функций)
    setTimeout(() => {
        console.log('[Quick Fix] Запуск начальных исправлений...');
        quickTranslateSelectors();
        startQuickMonitoring();
        // interceptPopulateFunctions(); // ОТКЛЮЧЕНО - используем исправленные функции
    }, 1000);

    // Повторяем перехват функций через 5 секунд (на случай, если виджет пересоздается)
    setTimeout(() => {
        interceptPopulateFunctions();
    }, 5000);

    console.log('[Quick Fix] Быстрое исправление селекторов загружено');
    console.log('Доступные функции:');
    console.log('- quickFixSelectors() - ручное исправление');

})();
