/**
 * Test Theme Panel Alignment and Styling
 * Проверяет выравнивание блока Theme Presets и стилизацию карточек
 */

console.log('🎨 Testing Theme Panel Alignment and Styling...');

// Test 1: Check admin grid layout
function testAdminGridLayout() {
    console.log('\n1️⃣ Testing Admin Grid Layout...');
    
    const adminGrid = document.querySelector('.wsf-admin-grid');
    if (!adminGrid) {
        console.error('❌ Admin grid element not found');
        return false;
    }

    const computedStyle = getComputedStyle(adminGrid);
    const checks = [
        {
            property: 'display',
            expected: 'grid',
            actual: computedStyle.display
        },
        {
            property: 'grid-template-columns',
            expected: /1fr.*320px/,
            actual: computedStyle.gridTemplateColumns
        },
        {
            property: 'gap',
            expected: '32px', // 2rem
            actual: computedStyle.gap
        }
    ];

    let allPassed = true;
    checks.forEach(check => {
        const passed = typeof check.expected === 'string' 
            ? check.actual === check.expected
            : check.expected.test(check.actual);
            
        console.log(`  ${passed ? '✅' : '❌'} ${check.property}: ${check.actual} ${passed ? '(correct)' : `(expected: ${check.expected})`}`);
        if (!passed) allPassed = false;
    });

    return allPassed;
}

// Test 2: Check theme panel positioning
function testThemePanelPositioning() {
    console.log('\n2️⃣ Testing Theme Panel Positioning...');
    
    const themePanel = document.querySelector('.wsf-theme-panel');
    if (!themePanel) {
        console.error('❌ Theme panel element not found');
        return false;
    }

    const computedStyle = getComputedStyle(themePanel);
    const checks = [
        {
            property: 'position',
            expected: 'static',
            actual: computedStyle.position
        },
        {
            property: 'background-color',
            expected: 'rgb(255, 255, 255)',
            actual: computedStyle.backgroundColor
        },
        {
            property: 'border-radius',
            expected: '8px',
            actual: computedStyle.borderRadius
        }
    ];

    let allPassed = true;
    checks.forEach(check => {
        const passed = check.actual === check.expected;
        console.log(`  ${passed ? '✅' : '❌'} ${check.property}: ${check.actual} ${passed ? '(correct)' : `(expected: ${check.expected})`}`);
        if (!passed) allPassed = false;
    });

    return allPassed;
}

// Test 3: Check theme panel header alignment
function testThemePanelHeader() {
    console.log('\n3️⃣ Testing Theme Panel Header...');
    
    const header = document.querySelector('.wsf-theme-panel__header');
    const title = document.querySelector('.wsf-theme-panel__title');
    
    if (!header || !title) {
        console.error('❌ Theme panel header or title not found');
        return false;
    }

    const headerStyle = getComputedStyle(header);
    const titleStyle = getComputedStyle(title);
    
    const checks = [
        {
            element: 'header',
            property: 'padding',
            expected: '24px 24px 0px 24px',
            actual: headerStyle.padding
        },
        {
            element: 'title',
            property: 'font-size',
            expected: '18px',
            actual: titleStyle.fontSize
        },
        {
            element: 'title',
            property: 'font-weight',
            expected: '500',
            actual: titleStyle.fontWeight
        }
    ];

    let allPassed = true;
    checks.forEach(check => {
        const passed = check.actual === check.expected;
        console.log(`  ${passed ? '✅' : '❌'} ${check.element} ${check.property}: ${check.actual} ${passed ? '(correct)' : `(expected: ${check.expected})`}`);
        if (!passed) allPassed = false;
    });

    return allPassed;
}

// Test 4: Check theme card styling
function testThemeCardStyling() {
    console.log('\n4️⃣ Testing Theme Card Styling...');
    
    const themeCard = document.querySelector('.wsf-theme-card');
    if (!themeCard) {
        console.error('❌ Theme card element not found');
        return false;
    }

    const computedStyle = getComputedStyle(themeCard);
    const checks = [
        {
            property: 'background-color',
            expected: 'rgb(248, 250, 252)', // slate-50
            actual: computedStyle.backgroundColor
        },
        {
            property: 'border-color',
            expected: 'rgb(226, 232, 240)', // slate-200
            actual: computedStyle.borderColor
        },
        {
            property: 'border-radius',
            expected: '8px',
            actual: computedStyle.borderRadius
        },
        {
            property: 'box-shadow',
            expected: /rgba\(0, 0, 0, 0\.05\)/,
            actual: computedStyle.boxShadow
        }
    ];

    let allPassed = true;
    checks.forEach(check => {
        const passed = typeof check.expected === 'string' 
            ? check.actual === check.expected
            : check.expected.test(check.actual);
            
        console.log(`  ${passed ? '✅' : '❌'} ${check.property}: ${check.actual} ${passed ? '(correct)' : `(expected: ${check.expected})`}`);
        if (!passed) allPassed = false;
    });

    return allPassed;
}

// Test 5: Check active theme card styling
function testActiveThemeCardStyling() {
    console.log('\n5️⃣ Testing Active Theme Card Styling...');
    
    const activeCard = document.querySelector('.wsf-theme-card--active');
    if (!activeCard) {
        console.warn('⚠️ No active theme card found, creating test element...');
        
        // Create a test active card
        const testCard = document.createElement('div');
        testCard.className = 'wsf-theme-card wsf-theme-card--active';
        testCard.style.position = 'absolute';
        testCard.style.top = '-9999px';
        document.body.appendChild(testCard);
        
        const computedStyle = getComputedStyle(testCard);
        const hasBlueRing = computedStyle.boxShadow.includes('rgb(59, 130, 246)');
        
        document.body.removeChild(testCard);
        
        console.log(`  ${hasBlueRing ? '✅' : '❌'} Active card has blue ring: ${hasBlueRing ? 'Yes' : 'No'}`);
        return hasBlueRing;
    }

    const computedStyle = getComputedStyle(activeCard);
    const hasBlueRing = computedStyle.boxShadow.includes('rgb(59, 130, 246)');
    
    console.log(`  ${hasBlueRing ? '✅' : '❌'} Active card has blue ring: ${hasBlueRing ? 'Yes' : 'No'}`);
    return hasBlueRing;
}

// Test 6: Check responsive behavior
function testResponsiveBehavior() {
    console.log('\n6️⃣ Testing Responsive Behavior...');
    
    const adminGrid = document.querySelector('.wsf-admin-grid');
    if (!adminGrid) {
        console.error('❌ Admin grid element not found');
        return false;
    }

    // Test at different viewport widths
    const originalWidth = window.innerWidth;
    
    // Simulate mobile width
    Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 800
    });
    
    // Trigger resize event
    window.dispatchEvent(new Event('resize'));
    
    // Check if grid changes to single column
    const computedStyle = getComputedStyle(adminGrid);
    const isSingleColumn = computedStyle.gridTemplateColumns === '1fr' || 
                          computedStyle.gridTemplateColumns.split(' ').length === 1;
    
    // Restore original width
    Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: originalWidth
    });
    window.dispatchEvent(new Event('resize'));
    
    console.log(`  ${isSingleColumn ? '✅' : '❌'} Grid becomes single column on mobile: ${isSingleColumn ? 'Yes' : 'No'}`);
    return isSingleColumn;
}

// Run all tests
function runAllTests() {
    console.log('🎨 Starting Theme Panel Alignment Tests...\n');
    
    const tests = [
        { name: 'Admin Grid Layout', fn: testAdminGridLayout },
        { name: 'Theme Panel Positioning', fn: testThemePanelPositioning },
        { name: 'Theme Panel Header', fn: testThemePanelHeader },
        { name: 'Theme Card Styling', fn: testThemeCardStyling },
        { name: 'Active Theme Card Styling', fn: testActiveThemeCardStyling },
        { name: 'Responsive Behavior', fn: testResponsiveBehavior }
    ];
    
    let passedTests = 0;
    const totalTests = tests.length;
    
    tests.forEach(test => {
        try {
            const result = test.fn();
            if (result) passedTests++;
        } catch (error) {
            console.error(`❌ Test "${test.name}" failed with error:`, error);
        }
    });
    
    console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Theme panel alignment is working correctly.');
    } else {
        console.log('⚠️ Some tests failed. Please check the implementation.');
    }
    
    return passedTests === totalTests;
}

// Auto-run tests when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}

// Export for manual testing
window.testThemePanelAlignment = runAllTests;
