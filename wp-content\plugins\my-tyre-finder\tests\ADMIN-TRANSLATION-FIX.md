# Исправление переводов в админке

## Проблема
В админ-панели не переводились фразы в селекторах и кнопка поиска при изменении настроек Search Flow или Form Layout, хотя на фронте все работало идеально.

## Исправления

### 1. Добавлен селектор языка в админ-панель ✅
**Файл**: `src/admin/AppearancePage.php`

Добавлен новый селектор "Preview Language" в админ-панель, который позволяет выбирать язык для предварительного просмотра.

**Изменения**:
- До<PERSON>авлен HTML-селектор с доступными языками
- Добавлена переменная `previewLanguageSelect` в JavaScript
- Селектор подключен к обработчикам событий

### 2. Улучшена функция прямого применения переводов ✅
**Файл**: `assets/js/admin-appearance.js`

Добавлена функция `applyDirectTranslations()`, которая гарантированно применяет переводы в админ-панели.

**Особенности**:
- Обрабатывает текстовые элементы с `data-i18n`
- Обрабатывает placeholder'ы с `data-i18n-placeholder`
- Специальная обработка для `<option>` элементов в `<select>`
- Автоматическое определение ключей переводов по ID селектов
- Подробное логирование для отладки

### 3. Обновлена передача локали в AJAX ✅
**Файл**: `src/admin/AppearancePage.php`

Изменена передача локали в AJAX-запросе:
```javascript
// Было:
preview_locale: 'фиксированная_локаль'

// Стало:
preview_locale: previewLanguageSelect ? previewLanguageSelect.value : 'fallback_locale'
```

### 4. Исправлены Twig-шаблоны ✅
**Файлы**: 
- `templates/finder-form-flow.twig`
- `templates/fields/make.twig`
- `templates/fields/model.twig`
- `templates/fields/year.twig`
- `templates/fields/mod.twig`
- `templates/fields/gen.twig`

**Изменения**:
```twig
<!-- Было -->
<option value="" data-i18n="select_make_placeholder">select_make_placeholder</option>

<!-- Стало -->
<option value="" data-i18n="select_make_placeholder">{{ t('select_make_placeholder') }}</option>
```

## Как тестировать

### 1. В WordPress админ-панели:
1. Перейдите в **Tire Finder → Appearance**
2. Выберите язык в селекторе **"Preview Language"**
3. Измените **Search Flow** или **Form Layout**
4. Проверьте, что все элементы остаются переведенными:
   - Заголовок виджета
   - Подписи к полям (Make, Model, Year, etc.)
   - Placeholder'ы в селекторах ("Choose a make", etc.)
   - Текст кнопки поиска

### 2. Тестовый файл:
Откройте `test-admin-translations.html` в браузере для автономного тестирования функций.

### 3. Консоль браузера:
```javascript
// Проверить доступные переводы
console.log(window.WheelFitI18n);

// Применить переводы вручную
applyDirectTranslations(document.getElementById('widget-preview'), window.WheelFitI18n);
```

## Ключевые улучшения

### ✅ Что теперь работает:
1. **Селектор языка** - можно выбрать язык для предварительного просмотра
2. **Переводы селекторов** - все dropdown placeholder'ы переводятся
3. **Кнопка поиска** - текст кнопки переводится
4. **Персистентность** - переводы сохраняются при изменении настроек
5. **Автоматическое определение** - система сама определяет ключи переводов
6. **Отладка** - подробные логи в консоли

### 🔧 Техническая реализация:
- **Множественные методы применения**: Translation Manager → applyStaticTranslations → applyDirectTranslations
- **Умное определение ключей**: автоматическое сопоставление ID селектов с ключами переводов
- **Обработка всех типов элементов**: текст, placeholder'ы, option'ы
- **Надежные fallback'и**: система работает даже если основные функции недоступны

## Поддерживаемые ключи переводов

### Основные элементы:
- `widget_title` - заголовок виджета
- `label_make`, `label_model`, `label_year`, `label_mods` - подписи полей
- `button_search` - текст кнопки поиска

### Placeholder'ы селекторов:
- `select_make_placeholder` - "Choose a make"
- `select_model_placeholder` - "Choose a model"
- `select_year_placeholder` - "Choose a year"
- `select_mods_placeholder` - "Choose a modification"
- `select_gen_placeholder` - "Choose a generation"

### Состояния зависимости:
- `select_make_first_placeholder` - "Select make first"
- `select_model_first_placeholder` - "Select model first"
- `select_year_first_placeholder` - "Select year first"

## Результат
Теперь в админ-панели все элементы виджета корректно переводятся и сохраняют переводы при изменении любых настроек Search Flow или Form Layout. Система работает надежно с множественными fallback механизмами.
