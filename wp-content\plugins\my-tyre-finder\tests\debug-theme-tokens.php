<?php
/**
 * Debug Theme Tokens - Test script to verify token transmission
 * 
 * Usage: Add this to WordPress admin page or run via WP-CLI
 */

// Ensure we're in WordPress context
if (!defined('ABSPATH')) {
    die('Direct access not allowed');
}

// Include the ColorTokens class
require_once plugin_dir_path(__FILE__) . '../src/includes/ColorTokens.php';

use MyTyreFinder\Includes\ColorTokens;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Theme Tokens</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .token { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .property { font-family: monospace; color: #7c3aed; font-weight: bold; }
        .label { color: #059669; font-weight: 600; }
        .help { color: #6b7280; font-size: 14px; }
        .order { color: #dc2626; font-size: 12px; }
        .error { background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 10px; border-radius: 4px; }
        .success { background: #f0fdf4; border: 1px solid #bbf7d0; color: #059669; padding: 10px; border-radius: 4px; }
        pre { background: #1e293b; color: #f1f5f9; padding: 15px; border-radius: 6px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Debug Theme Tokens</h1>
    
    <?php
    try {
        // Test 1: Get raw tokens
        echo '<div class="section">';
        echo '<h2>1. Raw Tokens from ColorTokens::get_tokens()</h2>';
        $rawTokens = ColorTokens::get_tokens();
        
        if (empty($rawTokens)) {
            echo '<div class="error">❌ No tokens found!</div>';
        } else {
            echo '<div class="success">✅ Found ' . count($rawTokens) . ' tokens</div>';
            
            foreach ($rawTokens as $key => $token) {
                echo '<div class="token">';
                echo '<div class="property">' . ($token['property'] ?? 'NO PROPERTY') . '</div>';
                echo '<div class="label">' . ($token['label'] ?? 'NO LABEL') . '</div>';
                echo '<div class="help">' . ($token['help'] ?? 'NO HELP') . '</div>';
                echo '<div class="order">Order: ' . ($token['order'] ?? 'NO ORDER') . '</div>';
                echo '</div>';
            }
        }
        echo '</div>';
        
        // Test 2: Get tokens for JS
        echo '<div class="section">';
        echo '<h2>2. Tokens for JavaScript from ColorTokens::get_tokens_for_js()</h2>';
        $jsTokens = ColorTokens::get_tokens_for_js();
        
        if (empty($jsTokens)) {
            echo '<div class="error">❌ No JS tokens found!</div>';
        } else {
            echo '<div class="success">✅ Found ' . count($jsTokens) . ' JS tokens</div>';
            
            foreach ($jsTokens as $property => $tokenData) {
                echo '<div class="token">';
                echo '<div class="property">' . $property . '</div>';
                echo '<div class="label">' . ($tokenData['label'] ?? 'NO LABEL') . '</div>';
                echo '<div class="help">' . ($tokenData['help'] ?? 'NO HELP') . '</div>';
                echo '<div class="order">Order: ' . ($tokenData['order'] ?? 'NO ORDER') . '</div>';
                echo '</div>';
            }
        }
        echo '</div>';
        
        // Test 3: Check for specific input tokens
        echo '<div class="section">';
        echo '<h2>3. Input Token Verification</h2>';
        $requiredInputTokens = [
            '--wsf-surface' => 'Surface',
            '--wsf-input-bg' => 'Input Background',
            '--wsf-input-text' => 'Input Text',
            '--wsf-input-border' => 'Input Border',
            '--wsf-input-placeholder' => 'Input Placeholder',
            '--wsf-input-focus' => 'Input Focus'
        ];
        
        $missingTokens = [];
        foreach ($requiredInputTokens as $property => $expectedLabel) {
            if (isset($jsTokens[$property])) {
                echo '<div class="success">✅ ' . $property . ' - Found: "' . $jsTokens[$property]['label'] . '"</div>';
            } else {
                echo '<div class="error">❌ ' . $property . ' - Missing!</div>';
                $missingTokens[] = $property;
            }
        }
        
        if (empty($missingTokens)) {
            echo '<div class="success"><strong>🎉 All input tokens are present!</strong></div>';
        } else {
            echo '<div class="error"><strong>⚠️ Missing tokens: ' . implode(', ', $missingTokens) . '</strong></div>';
        }
        echo '</div>';
        
        // Test 4: JSON output for JavaScript
        echo '<div class="section">';
        echo '<h2>4. JSON Output for JavaScript</h2>';
        echo '<p>This is what gets passed to wp_localize_script:</p>';
        echo '<pre>' . json_encode([
            'tokens' => $jsTokens,
            'examples' => ColorTokens::get_examples()
        ], JSON_PRETTY_PRINT) . '</pre>';
        echo '</div>';
        
        // Test 5: Simulate JavaScript fallback
        echo '<div class="section">';
        echo '<h2>5. JavaScript Fallback Labels Test</h2>';
        $fallbackLabels = [
            '--wsf-primary' => 'Primary Color',
            '--wsf-bg' => 'Background',
            '--wsf-text' => 'Text Color',
            '--wsf-border' => 'Border Color',
            '--wsf-hover' => 'Hover State',
            '--wsf-secondary' => 'Secondary',
            '--wsf-accent' => 'Accent',
            '--wsf-muted' => 'Muted',
            '--wsf-surface' => 'Surface',
            '--wsf-input-bg' => 'Input Background',
            '--wsf-input-text' => 'Input Text',
            '--wsf-input-border' => 'Input Border',
            '--wsf-input-placeholder' => 'Input Placeholder',
            '--wsf-input-focus' => 'Input Focus',
            '--wsf-success' => 'Success',
            '--wsf-warning' => 'Warning',
            '--wsf-error' => 'Error'
        ];
        
        echo '<p>Checking if fallback labels include all input tokens:</p>';
        foreach ($requiredInputTokens as $property => $expectedLabel) {
            if (isset($fallbackLabels[$property])) {
                echo '<div class="success">✅ Fallback has ' . $property . ' - "' . $fallbackLabels[$property] . '"</div>';
            } else {
                echo '<div class="error">❌ Fallback missing ' . $property . '</div>';
            }
        }
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">❌ Error: ' . $e->getMessage() . '</div>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
    ?>
    
    <div class="section">
        <h2>6. Next Steps</h2>
        <ol>
            <li>If tokens are missing from ColorTokens.php, add them there</li>
            <li>If tokens are present but not in JS, check wp_localize_script in AppearancePage.php</li>
            <li>If tokens are in JS but not showing in admin, check admin-theme-panel.js fallback labels</li>
            <li>If everything looks correct, check browser console for JavaScript errors</li>
        </ol>
    </div>
    
    <script>
        // Test if tokens are available in JavaScript (if this is loaded in admin)
        if (typeof window.wsfColorTokens !== 'undefined') {
            console.log('✅ wsfColorTokens available in JavaScript:', window.wsfColorTokens);
            
            const tokens = window.wsfColorTokens.tokens || {};
            const inputTokens = [
                '--wsf-surface',
                '--wsf-input-bg', 
                '--wsf-input-text',
                '--wsf-input-border',
                '--wsf-input-placeholder',
                '--wsf-input-focus'
            ];
            
            console.log('Input tokens check:');
            inputTokens.forEach(token => {
                if (tokens[token]) {
                    console.log(`✅ ${token}:`, tokens[token]);
                } else {
                    console.log(`❌ ${token}: Missing`);
                }
            });
        } else {
            console.log('❌ wsfColorTokens not available in JavaScript');
        }
    </script>
</body>
</html>
