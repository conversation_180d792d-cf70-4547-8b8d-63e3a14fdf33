<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Persistence Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-weight: bold;
            font-size: 14px;
        }
        .control-group select, .control-group button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .widget-preview {
            border: 2px solid #007cba;
            border-radius: 8px;
            padding: 20px;
            background: white;
            min-height: 300px;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Translation Persistence Fix Test</h1>
        <p>This test verifies that the new Translation Persistence Manager correctly maintains translations during form re-rendering.</p>
        
        <div class="test-controls">
            <div class="control-group">
                <label for="test-locale">Test Locale:</label>
                <select id="test-locale">
                    <option value="en">English</option>
                    <option value="de">German</option>
                    <option value="fr">French</option>
                    <option value="es">Spanish</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="test-search-flow">Search Flow:</label>
                <select id="test-search-flow">
                    <option value="by_vehicle">By Vehicle</option>
                    <option value="by_year">By Year</option>
                    <option value="by_generation">By Generation</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="test-form-layout">Form Layout:</label>
                <select id="test-form-layout">
                    <option value="popup-horizontal">Popup Horizontal</option>
                    <option value="inline">Inline</option>
                    <option value="stepper">Stepper</option>
                    <option value="wizard">Wizard</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>&nbsp;</label>
                <button id="simulate-update" type="button">Simulate Form Update</button>
                <button id="run-comprehensive-test" type="button">Run Comprehensive Test</button>
                <button id="clear-log" type="button">Clear Log</button>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>Widget Preview</h2>
        <div id="widget-preview" class="widget-preview">
            <div class="wsf-finder-widget" data-wsf-theme="default">
                <h2 data-i18n="widget_title">Wheel & Tyre Finder</h2>
                <form id="wheel-fit-form" class="space-y-6">
                    <div class="form-group">
                        <label data-i18n="label_make">Make</label>
                        <select id="wf-make">
                            <option value="" data-i18n-placeholder="placeholder_make">Choose a make</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label data-i18n="label_model">Model</label>
                        <select id="wf-model">
                            <option value="" data-i18n-placeholder="placeholder_model">Choose a model</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label data-i18n="label_year">Year</label>
                        <select id="wf-year">
                            <option value="" data-i18n-placeholder="placeholder_year">Choose a year</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label data-i18n="label_modification">Modification</label>
                        <select id="wf-modification">
                            <option value="" data-i18n-placeholder="placeholder_mod">Choose a modification</option>
                        </select>
                    </div>
                    <button type="submit" data-i18n="button_search">Find Tire & Wheel Sizes</button>
                </form>
                <div id="search-results" class="hidden">
                    <h3 data-i18n="section_results">Search Results</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>Test Log</h2>
        <div id="test-log" class="test-log">
            <div class="log-entry log-info">Test system initialized. Ready to run tests.</div>
        </div>
    </div>

    <!-- Include the translation persistence manager -->
    <script src="assets/js/translation-persistence-manager.js"></script>

    <script>
        // Mock translation data
        const translations = {
            en: {
                'widget_title': 'Wheel & Tyre Finder',
                'label_make': 'Make',
                'label_model': 'Model',
                'label_year': 'Year',
                'label_modification': 'Modification',
                'placeholder_make': 'Choose a make',
                'placeholder_model': 'Choose a model',
                'placeholder_year': 'Choose a year',
                'placeholder_mod': 'Choose a modification',
                'button_search': 'Find Tire & Wheel Sizes',
                'section_results': 'Search Results'
            },
            de: {
                'widget_title': 'Rad & Reifen Finder',
                'label_make': 'Marke',
                'label_model': 'Modell',
                'label_year': 'Jahr',
                'label_modification': 'Modifikation',
                'placeholder_make': 'Marke wählen',
                'placeholder_model': 'Modell wählen',
                'placeholder_year': 'Jahr wählen',
                'placeholder_mod': 'Modifikation wählen',
                'button_search': 'Reifen- & Radgrößen finden',
                'section_results': 'Suchergebnisse'
            },
            fr: {
                'widget_title': 'Recherche de Roues et Pneus',
                'label_make': 'Marque',
                'label_model': 'Modèle',
                'label_year': 'Année',
                'label_modification': 'Modification',
                'placeholder_make': 'Choisir une marque',
                'placeholder_model': 'Choisir un modèle',
                'placeholder_year': 'Choisir une année',
                'placeholder_mod': 'Choisir une modification',
                'button_search': 'Trouver les tailles de pneus et roues',
                'section_results': 'Résultats de recherche'
            },
            es: {
                'widget_title': 'Buscador de Ruedas y Neumáticos',
                'label_make': 'Marca',
                'label_model': 'Modelo',
                'label_year': 'Año',
                'label_modification': 'Modificación',
                'placeholder_make': 'Elegir una marca',
                'placeholder_model': 'Elegir un modelo',
                'placeholder_year': 'Elegir un año',
                'placeholder_mod': 'Elegir una modificación',
                'button_search': 'Encontrar tamaños de neumáticos y ruedas',
                'section_results': 'Resultados de búsqueda'
            }
        };

        // Initialize with English
        window.WheelFitI18n = translations.en;

        // Test utilities
        const TestUtils = {
            log: function(message, type = 'info') {
                const logContainer = document.getElementById('test-log');
                const entry = document.createElement('div');
                entry.className = `log-entry log-${type}`;
                entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                logContainer.appendChild(entry);
                logContainer.scrollTop = logContainer.scrollHeight;
                console.log(`[Test ${type.toUpperCase()}] ${message}`);
            },

            simulateFormUpdate: function(locale, searchFlow, formLayout) {
                this.log(`Simulating form update: ${locale} + ${searchFlow} + ${formLayout}`, 'info');
                
                // Update translations
                window.WheelFitI18n = translations[locale] || translations.en;
                
                // Simulate form re-rendering by replacing innerHTML
                const previewContainer = document.getElementById('widget-preview');
                const currentHTML = previewContainer.innerHTML;
                
                this.log('Replacing form HTML (simulating AJAX update)', 'info');
                previewContainer.innerHTML = '';
                
                setTimeout(() => {
                    previewContainer.innerHTML = currentHTML;
                    this.log('Form HTML replaced, checking if translations persist', 'info');
                    
                    // Notify translation manager if available
                    if (window.translationManager) {
                        window.translationManager.updateTranslations(window.WheelFitI18n, locale);
                        this.log('Translation manager notified of update', 'success');
                    } else {
                        this.log('Translation manager not available', 'warning');
                    }
                    
                    // Dispatch custom event
                    document.dispatchEvent(new CustomEvent('previewUpdated', {
                        detail: { container: previewContainer, translations: window.WheelFitI18n }
                    }));
                    
                    // Check results after a delay
                    setTimeout(() => this.checkTranslationResults(locale), 100);
                }, 100);
            },

            checkTranslationResults: function(expectedLocale) {
                const previewContainer = document.getElementById('widget-preview');
                const expectedTranslations = translations[expectedLocale] || translations.en;
                
                let passed = 0;
                let failed = 0;
                
                // Check text elements
                const textElements = previewContainer.querySelectorAll('[data-i18n]');
                textElements.forEach(el => {
                    const key = el.dataset.i18n;
                    const expected = expectedTranslations[key];
                    const actual = el.textContent.trim();
                    
                    if (expected && actual === expected) {
                        passed++;
                    } else {
                        failed++;
                        this.log(`FAIL: ${key} expected "${expected}", got "${actual}"`, 'error');
                    }
                });
                
                // Check placeholder elements
                const placeholderElements = previewContainer.querySelectorAll('[data-i18n-placeholder]');
                placeholderElements.forEach(el => {
                    const key = el.dataset.i18nPlaceholder;
                    const expected = expectedTranslations[key];
                    const actual = el.textContent.trim();
                    
                    if (expected && actual === expected) {
                        passed++;
                    } else {
                        failed++;
                        this.log(`FAIL: ${key} placeholder expected "${expected}", got "${actual}"`, 'error');
                    }
                });
                
                const total = passed + failed;
                const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
                
                if (failed === 0) {
                    this.log(`✅ All ${passed} translations applied correctly (${successRate}% success)`, 'success');
                } else {
                    this.log(`❌ ${failed} translations failed, ${passed} passed (${successRate}% success)`, 'error');
                }
                
                return { passed, failed, successRate };
            }
        };

        // Event listeners
        document.getElementById('simulate-update').addEventListener('click', function() {
            const locale = document.getElementById('test-locale').value;
            const searchFlow = document.getElementById('test-search-flow').value;
            const formLayout = document.getElementById('test-form-layout').value;
            
            TestUtils.simulateFormUpdate(locale, searchFlow, formLayout);
        });

        document.getElementById('run-comprehensive-test').addEventListener('click', function() {
            TestUtils.log('Starting comprehensive test...', 'info');
            
            const locales = ['en', 'de', 'fr', 'es'];
            const flows = ['by_vehicle', 'by_year', 'by_generation'];
            const layouts = ['popup-horizontal', 'inline', 'stepper'];
            
            let testIndex = 0;
            const totalTests = locales.length * flows.length * layouts.length;
            
            function runNextTest() {
                if (testIndex >= totalTests) {
                    TestUtils.log('🎉 Comprehensive test completed!', 'success');
                    return;
                }
                
                const localeIndex = Math.floor(testIndex / (flows.length * layouts.length));
                const flowIndex = Math.floor((testIndex % (flows.length * layouts.length)) / layouts.length);
                const layoutIndex = testIndex % layouts.length;
                
                const locale = locales[localeIndex];
                const flow = flows[flowIndex];
                const layout = layouts[layoutIndex];
                
                TestUtils.log(`Test ${testIndex + 1}/${totalTests}: ${locale} + ${flow} + ${layout}`, 'info');
                TestUtils.simulateFormUpdate(locale, flow, layout);
                
                testIndex++;
                setTimeout(runNextTest, 1000);
            }
            
            runNextTest();
        });

        document.getElementById('clear-log').addEventListener('click', function() {
            document.getElementById('test-log').innerHTML = '<div class="log-entry log-info">Log cleared.</div>';
        });

        // Initialize test
        TestUtils.log('Translation Persistence Fix Test initialized', 'success');
        TestUtils.log('Translation Manager available: ' + (window.TranslationPersistenceManager ? 'Yes' : 'No'), 'info');
        
        // Wait for translation manager to initialize
        setTimeout(() => {
            TestUtils.log('Translation Manager instance: ' + (window.translationManager ? 'Available' : 'Not available'), 'info');
        }, 500);
    </script>
</body>
</html>
