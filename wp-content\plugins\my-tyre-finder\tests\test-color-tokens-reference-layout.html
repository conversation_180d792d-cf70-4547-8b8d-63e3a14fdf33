<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Tokens Reference Layout Test</title>
    
    <!-- Подключаем CSS файлы -->
    <link rel="stylesheet" href="../assets/css/admin-theme-panel.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #111827;
            font-size: 24px;
            font-weight: 600;
        }
        
        .demo-section p {
            color: #6b7280;
            margin-bottom: 20px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }
        
        .comparison-item {
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .comparison-item h3 {
            margin-top: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .old-style .wsf-cheat-sheet {
            /* Старые стили для сравнения */
            max-width: none;
            border-radius: 6px;
            box-shadow: none;
            animation: none;
            transform: none;
            opacity: 1;
        }
        
        .old-style .wsf-cheat-sheet__header {
            padding: 12px 16px;
            background: #f5f5f5;
            border-radius: 6px 6px 0 0;
        }
        
        .old-style .wsf-cheat-sheet__title {
            font-size: 14px;
            color: #333;
        }
        
        .old-style .wsf-cheat-sheet__toggle {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }
        
        .old-style .wsf-cheat-sheet__table {
            font-size: 12px;
            border: none;
            box-shadow: none;
        }
        
        .old-style .wsf-cheat-sheet__table th {
            background: #f8f9fa;
            padding: 10px 12px;
            font-size: 12px;
            text-transform: none;
        }
        
        .old-style .wsf-cheat-sheet__table td {
            padding: 10px 12px;
        }
        
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .controls button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .controls button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Color Tokens Reference - Layout Improvements</h1>
        <p>Демонстрация улучшенного дизайна окна "Color Tokens Reference" с современным layout и адаптивностью.</p>
        
        <!-- Контролы -->
        <div class="controls">
            <h3>🎛️ Контролы тестирования</h3>
            <button onclick="toggleCheatSheet('new')" class="active" id="btn-new">Новый дизайн</button>
            <button onclick="toggleCheatSheet('old')" id="btn-old">Старый дизайн</button>
            <button onclick="testMobile()">📱 Мобильный вид</button>
            <button onclick="testDesktop()">🖥️ Десктоп вид</button>
            <button onclick="testAnimation()">✨ Тест анимации</button>
        </div>
        
        <!-- Новый улучшенный дизайн -->
        <div class="demo-section" id="new-design">
            <h2>✅ Новый улучшенный дизайн</h2>
            <p>Современный, адаптивный дизайн с ограниченной шириной, улучшенной типографикой и анимациями.</p>
            
            <div class="wsf-cheat-sheet">
                <div class="wsf-cheat-sheet__header">
                    <h4 class="wsf-cheat-sheet__title">🎨 Color Tokens Reference</h4>
                    <button type="button" class="wsf-cheat-sheet__toggle" onclick="toggleContent('new')">
                        <span class="wsf-cheat-sheet__toggle-text">Show Details</span>
                        <svg class="wsf-cheat-sheet__toggle-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                </div>
                <div class="wsf-cheat-sheet__content" id="new-content" style="display: none;">
                    <div class="wsf-cheat-sheet__table-wrapper">
                        <table class="wsf-cheat-sheet__table">
                            <thead>
                                <tr>
                                    <th>Token</th>
                                    <th>Description</th>
                                    <th>CSS Variables</th>
                                    <th>Examples</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="wsf-cheat-sheet__row">
                                    <td class="wsf-cheat-sheet__token">Background</td>
                                    <td class="wsf-cheat-sheet__description">Main widget background color</td>
                                    <td class="wsf-cheat-sheet__vars">
                                        <div class="wsf-cheat-sheet__var-item">
                                            <code class="wsf-cheat-sheet__code">--wsf-bg</code>
                                            <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-bg')">
                                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                    <td class="wsf-cheat-sheet__example">Widget container, form wrapper</td>
                                </tr>
                                <tr class="wsf-cheat-sheet__row">
                                    <td class="wsf-cheat-sheet__token">Primary</td>
                                    <td class="wsf-cheat-sheet__description">Primary brand color for buttons and accents</td>
                                    <td class="wsf-cheat-sheet__vars">
                                        <div class="wsf-cheat-sheet__var-item">
                                            <code class="wsf-cheat-sheet__code">--wsf-primary</code>
                                            <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-primary')">
                                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                    <td class="wsf-cheat-sheet__example">Search button, links, focus states</td>
                                </tr>
                                <tr class="wsf-cheat-sheet__row">
                                    <td class="wsf-cheat-sheet__token">Text</td>
                                    <td class="wsf-cheat-sheet__description">Primary text color for content</td>
                                    <td class="wsf-cheat-sheet__vars">
                                        <div class="wsf-cheat-sheet__var-item">
                                            <code class="wsf-cheat-sheet__code">--wsf-text</code>
                                            <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-text')">
                                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2 2v1"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                    <td class="wsf-cheat-sheet__example">Labels, headings, body text</td>
                                </tr>
                                <tr class="wsf-cheat-sheet__row">
                                    <td class="wsf-cheat-sheet__token">Border</td>
                                    <td class="wsf-cheat-sheet__description">Border color for form elements</td>
                                    <td class="wsf-cheat-sheet__vars">
                                        <div class="wsf-cheat-sheet__var-item">
                                            <code class="wsf-cheat-sheet__code">--wsf-border</code>
                                            <button class="wsf-cheat-sheet__copy" onclick="copyToClipboard('--wsf-border')">
                                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2 2v1"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                    <td class="wsf-cheat-sheet__example">Input borders, dividers</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Сравнение старого и нового дизайна -->
        <div class="demo-section">
            <h2>📊 Сравнение улучшений</h2>
            <div class="comparison">
                <div class="comparison-item">
                    <h3>❌ Проблемы старого дизайна</h3>
                    <ul>
                        <li>Нет ограничения ширины - выходит за экран</li>
                        <li>Мелкий шрифт (12px) - плохая читаемость</li>
                        <li>Устаревший дизайн без современных теней</li>
                        <li>Нет адаптивности для мобильных</li>
                        <li>Плохие отступы и spacing</li>
                        <li>Нет анимаций появления</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h3>✅ Улучшения нового дизайна</h3>
                    <ul>
                        <li>max-width: 640px - всегда помещается на экране</li>
                        <li>Увеличенный шрифт (14-18px) - лучшая читаемость</li>
                        <li>Современные тени и border-radius</li>
                        <li>Полная адаптивность для мобильных</li>
                        <li>Улучшенные отступы (16-24px)</li>
                        <li>Плавная анимация появления</li>
                        <li>Моноширинный шрифт для кода</li>
                        <li>Улучшенные кнопки копирования</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Технические детали -->
        <div class="demo-section">
            <h2>🔧 Технические улучшения</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>CSS Изменения</h3>
                    <ul>
                        <li><code>max-width: 640px</code> - ограничение ширины</li>
                        <li><code>max-height: 80vh</code> - ограничение высоты</li>
                        <li><code>border-radius: 12px</code> - современные углы</li>
                        <li><code>box-shadow: 0 25px 50px</code> - глубокие тени</li>
                        <li><code>font-size: 14-18px</code> - увеличенные шрифты</li>
                        <li><code>padding: 24px</code> - больше воздуха</li>
                    </ul>
                </div>
                <div>
                    <h3>Адаптивность</h3>
                    <ul>
                        <li>Мобильные breakpoints (@media)</li>
                        <li>Гибкая сетка для таблицы</li>
                        <li>Уменьшенные отступы на мобильных</li>
                        <li>Адаптивная типографика</li>
                        <li>Улучшенные touch targets</li>
                        <li>Вертикальная прокрутка</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentDesign = 'new';
        
        function toggleCheatSheet(design) {
            currentDesign = design;
            
            // Обновляем кнопки
            document.getElementById('btn-new').classList.toggle('active', design === 'new');
            document.getElementById('btn-old').classList.toggle('active', design === 'old');
            
            // Переключаем стили
            const cheatSheet = document.querySelector('.wsf-cheat-sheet');
            if (design === 'old') {
                cheatSheet.parentElement.classList.add('old-style');
            } else {
                cheatSheet.parentElement.classList.remove('old-style');
            }
        }
        
        function toggleContent(type) {
            const content = document.getElementById(type + '-content');
            const toggle = content.previousElementSibling.querySelector('.wsf-cheat-sheet__toggle-text');
            const icon = content.previousElementSibling.querySelector('.wsf-cheat-sheet__toggle-icon');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggle.textContent = 'Hide Details';
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                toggle.textContent = 'Show Details';
                icon.style.transform = 'rotate(0deg)';
            }
        }
        
        function testMobile() {
            document.body.style.maxWidth = '375px';
            document.body.style.margin = '0 auto';
            document.body.style.padding = '10px';
            
            setTimeout(() => {
                alert('Мобильный вид активирован! Проверьте адаптивность.');
            }, 100);
        }
        
        function testDesktop() {
            document.body.style.maxWidth = 'none';
            document.body.style.margin = '0';
            document.body.style.padding = '20px';
            
            setTimeout(() => {
                alert('Десктоп вид восстановлен!');
            }, 100);
        }
        
        function testAnimation() {
            const cheatSheet = document.querySelector('.wsf-cheat-sheet');
            cheatSheet.style.animation = 'none';
            cheatSheet.style.transform = 'scale(0.95)';
            cheatSheet.style.opacity = '0';
            
            setTimeout(() => {
                cheatSheet.style.animation = 'wsf-cheat-sheet-appear 0.3s ease forwards';
            }, 100);
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Показываем feedback
                const button = event.target.closest('.wsf-cheat-sheet__copy');
                button.classList.add('wsf-cheat-sheet__copy--copied');
                
                setTimeout(() => {
                    button.classList.remove('wsf-cheat-sheet__copy--copied');
                }, 1000);
                
                console.log('Скопировано: ' + text);
            });
        }
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Color Tokens Reference Layout Test загружен');
            console.log('📝 Используйте контролы для тестирования различных аспектов дизайна');
        });
    </script>
</body>
</html>
