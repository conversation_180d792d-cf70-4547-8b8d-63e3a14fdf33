<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diameter Black Color Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .problem-demo {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .solution-demo {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .demo-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .color-controls {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .color-field {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .color-input {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .color-label {
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        .demo-size-card {
            position: relative;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 15px;
            text-align: center;
            width: 180px;
            display: inline-block;
        }
        
        .demo-badge {
            position: absolute;
            top: 6px;
            right: 6px;
            background: #dbeafe;
            color: #1e40af;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .demo-badge-factory {
            background: #fed7aa;
            color: #c2410c;
        }
        
        .demo-diameter-old {
            font-size: 2rem;
            font-weight: 900;
            color: var(--wsf-primary, #2563eb);
            line-height: 1;
            margin-bottom: 8px;
        }
        
        .demo-diameter-new {
            font-size: 2rem;
            font-weight: 900;
            color: #000000; /* text-black - фиксированный чёрный */
            line-height: 1;
            margin-bottom: 8px;
        }
        
        .demo-tire-size {
            color: #374151;
            font-size: 14px;
            font-weight: 500;
        }
        
        .demo-dual-diameter {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 4px;
            margin-bottom: 8px;
        }
        
        .demo-dual-separator-old {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--wsf-muted, #9ca3af);
        }
        
        .demo-dual-separator-new {
            font-size: 1.25rem;
            font-weight: 600;
            color: #6b7280; /* text-gray-500 - фиксированный серый */
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .highlight-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Diameter Black Color Fix</h1>
        <p>Исправление проблемы, когда диаметры шин (15", 16", 17") зависели от настройки "Primary" вместо использования чёрного цвета.</p>
        
        <!-- Problem Description -->
        <div class="test-section">
            <h3>❌ Проблема</h3>
            <div class="problem-demo">
                <h4>ДО исправления:</h4>
                <p>Диаметры шин на карточках размеров выглядели серыми вместо чёрных, потому что использовали цвет "Primary" из Theme Presets.</p>
                <div class="highlight-box">
                    <strong>Примеры:</strong> "15"", "16"", "17"", "18"", "17" / 18""
                </div>
                
                <p><strong>Причина:</strong> Диаметры использовали классы <code>wsf-text-primary</code> и <code>text-wsf-primary</code>, которые зависят от CSS переменной <code>--wsf-primary</code>.</p>
                
                <div class="code-block">
// ДО (проблемный код):
const diameterHTML = `&lt;span class="wsf-text-primary"&gt;${diams[0]}"&lt;/span&gt;`;

/* CSS: */
.wsf-text-primary { color: var(--wsf-primary); }
                </div>
            </div>
        </div>
        
        <!-- Solution Description -->
        <div class="test-section">
            <h3>✅ Решение</h3>
            <div class="solution-demo">
                <h4>ПОСЛЕ исправления:</h4>
                <p>Диаметры шин теперь используют фиксированный чёрный цвет и не зависят от настроек Theme Presets.</p>
                
                <div class="code-block">
// ПОСЛЕ (исправленный код):
const diameterHTML = `&lt;span class="text-black"&gt;${diams[0]}"&lt;/span&gt;`;

/* Фиксированный Tailwind класс, не зависит от CSS переменных */
                </div>
                
                <h4>Исправленные файлы:</h4>
                <ul>
                    <li><code>assets/js/finder.js</code> - функция createSizeCard</li>
                    <li><code>assets/js/wizard.js</code> - функция createSizeCard</li>
                </ul>
                
                <h4>Замены в коде:</h4>
                <ul>
                    <li><code>wsf-text-primary</code> → <code>text-black</code> (для диаметров)</li>
                    <li><code>text-wsf-primary</code> → <code>text-black</code> (для диаметров)</li>
                    <li><code>wsf-text-muted</code> → <code>text-gray-500</code> (для разделителя "/")</li>
                </ul>
            </div>
        </div>
        
        <!-- Live Demo -->
        <div class="test-section">
            <h3>🎨 Демонстрация исправления</h3>
            <p>Измените цвет "Primary" ниже и убедитесь, что диаметры НЕ меняются:</p>
            
            <div class="color-controls">
                <div class="color-field">
                    <input type="color" class="color-input" value="#2563eb" data-token="--wsf-primary">
                    <label class="color-label">Primary (НЕ влияет на диаметры)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#9ca3af" data-token="--wsf-muted">
                    <label class="color-label">Muted (НЕ влияет на разделитель "/")</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#1f2937" data-token="--wsf-text">
                    <label class="color-label">Text (для лейблов и заголовков)</label>
                </div>
            </div>
            
            <div class="demo-widget" id="demo-widget">
                <h4 style="color: var(--wsf-text); margin-bottom: 20px;">Factory & Optional Sizes</h4>
                
                <div style="display: flex; gap: 20px; flex-wrap: wrap; justify-content: center;">
                    <div class="demo-size-card">
                        <div class="demo-badge-factory">FACTORY</div>
                        <div class="demo-diameter-new">17"</div>
                        <div class="demo-tire-size">225/45 R17</div>
                    </div>
                    
                    <div class="demo-size-card">
                        <div class="demo-badge">OPTIONAL</div>
                        <div class="demo-diameter-new">18"</div>
                        <div class="demo-tire-size">LT185R14</div>
                    </div>
                    
                    <div class="demo-size-card">
                        <div class="demo-badge-factory">FACTORY</div>
                        <div class="demo-dual-diameter">
                            <span class="demo-diameter-new" style="font-size: 2rem;">17"</span>
                            <span class="demo-dual-separator-new">/</span>
                            <span class="demo-diameter-new" style="font-size: 2rem;">18"</span>
                        </div>
                        <div class="demo-tire-size">Dual sizes</div>
                    </div>
                </div>
                
                <p style="font-size: 12px; color: #6b7280; margin-top: 15px; text-align: center;">
                    ↑ Диаметры теперь всегда чёрные и НЕ меняются при изменении Primary
                </p>
            </div>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>📊 Сравнение До/После</h3>
            <div class="comparison-grid">
                <div class="problem-demo">
                    <h4>❌ ДО</h4>
                    <p><strong>Проблема:</strong></p>
                    <ul>
                        <li>Primary влияет на диаметры</li>
                        <li>Диаметры выглядят серыми</li>
                        <li>Неожиданное поведение</li>
                    </ul>
                    
                    <div class="demo-size-card">
                        <div class="demo-badge">OPTIONAL</div>
                        <div class="demo-diameter-old">17"</div>
                        <div class="demo-tire-size">225/45 R17</div>
                        <p style="font-size: 11px; margin-top: 5px;">Меняется при изменении Primary</p>
                    </div>
                </div>
                
                <div class="solution-demo">
                    <h4>✅ ПОСЛЕ</h4>
                    <p><strong>Решение:</strong></p>
                    <ul>
                        <li>Primary НЕ влияет на диаметры</li>
                        <li>Диаметры всегда чёрные</li>
                        <li>Лучшая читаемость</li>
                    </ul>
                    
                    <div class="demo-size-card">
                        <div class="demo-badge">OPTIONAL</div>
                        <div class="demo-diameter-new">17"</div>
                        <div class="demo-tire-size">225/45 R17</div>
                        <p style="font-size: 11px; margin-top: 5px;">Всегда чёрный цвет</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Технические детали</h3>
            
            <h4>Что изменилось в createSizeCard:</h4>
            <div class="code-block">
/* ДО */
wsf-text-primary      /* Зависел от --wsf-primary */
text-wsf-primary      /* Зависел от --wsf-primary */
wsf-text-muted        /* Зависел от --wsf-muted */

/* ПОСЛЕ */
text-black            /* Фиксированный чёрный цвет */
text-gray-500         /* Фиксированный серый для разделителя */
            </div>
            
            <h4>Новое разделение ответственности:</h4>
            <ul>
                <li><strong>--wsf-primary</strong> - основные кнопки действий (Find Sizes, Search)</li>
                <li><strong>--wsf-accent</strong> - вторичные действия (гараж, ссылки)</li>
                <li><strong>--wsf-text</strong> - лейблы полей и заголовки секций</li>
                <li><strong>text-black</strong> - диаметры шин (фиксированный)</li>
                <li><strong>text-gray-700</strong> - размеры шин (фиксированный)</li>
                <li><strong>text-gray-900</strong> - результаты поиска (фиксированный)</li>
            </ul>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить исправление</h4>
            <ol>
                <li><strong>Очистите кэш</strong> WordPress и браузера</li>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>Создайте новую тему или отредактируйте существующую</li>
                <li>Измените цвет <strong>"Primary"</strong> на яркий (например, красный)</li>
                <li>Сохраните и примените тему</li>
                <li>Перейдите на страницу с виджетом и выполните поиск</li>
                <li>Убедитесь, что:
                    <ul>
                        <li>Основные кнопки изменили цвет ✅</li>
                        <li><strong>Диаметры остались чёрными</strong> ✅</li>
                        <li>Разделитель "/" остался серым ✅</li>
                        <li>Размеры шин остались серыми ✅</li>
                    </ul>
                </li>
            </ol>
            
            <h4>🎯 Ожидаемый результат</h4>
            <ul>
                <li>✅ Диаметры всегда чёрные и читаемые</li>
                <li>✅ Primary влияет только на кнопки действий</li>
                <li>✅ Лучший контраст и читаемость</li>
                <li>✅ Логичное разделение элементов</li>
            </ul>
        </div>
    </div>

    <script>
        // Color picker functionality
        document.querySelectorAll('.color-input').forEach(input => {
            input.addEventListener('change', function() {
                const token = this.dataset.token;
                const value = this.value;
                const demoWidget = document.getElementById('demo-widget');
                
                demoWidget.style.setProperty(token, value);
                console.log(`Updated ${token} to ${value}`);
            });
        });
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            const demoWidget = document.getElementById('demo-widget');
            
            // Set default values
            demoWidget.style.setProperty('--wsf-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-primary', '#2563eb');
            demoWidget.style.setProperty('--wsf-muted', '#9ca3af');
            demoWidget.style.setProperty('--wsf-border', '#e5e7eb');
            
            console.log('Diameter black color fix demo initialized');
        });
    </script>
</body>
</html>
