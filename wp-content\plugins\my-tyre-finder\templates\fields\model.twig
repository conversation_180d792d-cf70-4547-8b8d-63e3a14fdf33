{# Model Field Template - Reusable across layouts #}
{% set is_stepper = (form_layout ?? 'stepper') == 'stepper' %}
{% set is_inline = (form_layout ?? 'inline') == 'inline' %}
{% set is_popup_horizontal = (form_layout ?? 'popup-horizontal') == 'popup-horizontal' %}

{% if is_stepper %}
    {# Step-by-Step layout #}
    <div id="step-2" class="step-container hidden">
        <label for="wf-model" class="block text-sm font-semibold text-wsf-text uppercase tracking-wide mb-2" data-i18n="label_model">Model</label>
        <div class="relative w-full">
            <select id="wf-model" name="model" class="wsf-input block w-full" required disabled>
                <option value="">Select make first</option>
            </select>
            <div id="model-loader" class="hidden absolute right-4 top-1/2 -translate-y-1/2"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-wsf-primary"></div></div>
        </div>
    </div>
{% else %}
    {# Inline and Popup-Horizontal layouts #}
    <div id="step-2" class="flex flex-col w-full min-w-0">
        <label for="wf-model" class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1" data-i18n="label_model">Model</label>
        <div class="relative min-w-0">
            <select id="wf-model" name="model" class="wsf-input block w-full" required disabled>
                <option value="" data-i18n="select_make_first_placeholder">Select make first</option>
            </select>
            <div id="model-loader" class="loader hidden absolute right-3 top-1/2 -translate-y-1/2"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-wsf-primary"></div></div>
        </div>
    </div>
{% endif %} 