<?php
/**
 * Quick test script for slug generation and migration fixes
 * 
 * Usage: Run this script in WordPress admin or via WP-CLI
 */

// Ensure WordPress is loaded
if (!defined('ABSPATH')) {
    die('This script must be run within WordPress context');
}

// Load the ThemeManager class
require_once __DIR__ . '/../src/includes/ThemeManager.php';

use MyTyreFinder\Includes\ThemeManager;

echo "🔧 Quick Slug Fix Test\n";
echo "=====================\n\n";

// Test 1: Slug generation
echo "1️⃣ Testing slug generation...\n";

$test_names = [
    'Русская тема' => 'russkaya-tema',
    'Café Theme' => 'cafe-theme', 
    'Thème français' => 'theme-francais',
    '主题中文' => 'theme-',  // Should fallback
    '123' => 'theme-123',   // Numeric should get prefix
    'Normal Theme' => 'normal-theme',
    'Theme-2024' => 'theme-2024',
    'My_Custom_Theme' => 'my-custom-theme'
];

$reflection = new ReflectionClass(ThemeManager::class);
$generateSlugMethod = $reflection->getMethod('generate_slug');
$generateSlugMethod->setAccessible(true);

foreach ($test_names as $input => $expected) {
    $generated = $generateSlugMethod->invoke(null, $input);
    $status = (strpos($expected, $generated) !== false || $generated === $expected) ? '✅' : '❌';
    echo "  {$status} '{$input}' → '{$generated}'\n";
    
    // Validate generated slug
    $isValid = preg_match('/^[a-zA-Z0-9\-]+$/', $generated) && 
               !preg_match('/^-|-$/', $generated) && 
               strpos($generated, '--') === false;
    
    if (!$isValid) {
        echo "    ⚠️  Generated slug is not valid!\n";
    }
}

echo "\n";

// Test 2: Check existing themes
echo "2️⃣ Checking existing themes...\n";

try {
    $themes = ThemeManager::get_themes();
    $totalThemes = count($themes);
    $asciiThemes = 0;
    $nonAsciiThemes = 0;
    
    foreach (array_keys($themes) as $slug) {
        if (preg_match('/^[a-zA-Z0-9\-]+$/', $slug)) {
            $asciiThemes++;
        } else {
            $nonAsciiThemes++;
            echo "  ❌ Non-ASCII slug found: '{$slug}'\n";
        }
    }
    
    echo "  📊 Total themes: {$totalThemes}\n";
    echo "  ✅ ASCII slugs: {$asciiThemes}\n";
    echo "  ❌ Non-ASCII slugs: {$nonAsciiThemes}\n";
    
    if ($nonAsciiThemes === 0) {
        echo "  🎉 All slugs are ASCII-compatible!\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ Error checking themes: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test theme creation with international names
echo "3️⃣ Testing theme creation...\n";

$testThemeData = [
    'name' => 'Тестовая тема',
    'properties' => [
        '--wsf-primary' => '#2563eb',
        '--wsf-bg' => '#ffffff',
        '--wsf-text' => '#1f2937',
        '--wsf-border' => '#e5e7eb'
    ]
];

try {
    $slug = ThemeManager::create_theme($testThemeData['name'], $testThemeData['properties']);
    
    if ($slug) {
        echo "  ✅ Theme created successfully with slug: '{$slug}'\n";
        
        // Validate the slug
        $isValid = preg_match('/^[a-zA-Z0-9\-]+$/', $slug);
        echo "  " . ($isValid ? '✅' : '❌') . " Generated slug is " . ($isValid ? 'valid' : 'invalid') . "\n";
        
        // Test theme retrieval
        $retrievedTheme = ThemeManager::get_theme($slug);
        if ($retrievedTheme) {
            echo "  ✅ Theme can be retrieved successfully\n";
            echo "  📝 Theme name: '{$retrievedTheme['name']}'\n";
        } else {
            echo "  ❌ Failed to retrieve created theme\n";
        }
        
        // Test theme activation
        $activationResult = ThemeManager::set_active_theme($slug);
        echo "  " . ($activationResult ? '✅' : '❌') . " Theme activation " . ($activationResult ? 'successful' : 'failed') . "\n";
        
        // Clean up - delete the test theme
        $deleteResult = ThemeManager::delete_theme($slug);
        echo "  " . ($deleteResult ? '✅' : '❌') . " Test theme cleanup " . ($deleteResult ? 'successful' : 'failed') . "\n";
        
    } else {
        echo "  ❌ Failed to create theme\n";
    }
    
} catch (Exception $e) {
    echo "  ❌ Error during theme creation test: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: REST API slug validation
echo "4️⃣ Testing REST API slug validation...\n";

// Load the controller
require_once __DIR__ . '/../src/rest/ThemeController.php';

use MyTyreFinder\Rest\ThemeController;

$controller = new ThemeController();

$testSlugs = [
    'valid-slug' => true,
    'theme-123' => true,
    'русский-slug' => false,
    '-invalid-start' => false,
    'invalid-end-' => false,
    'double--hyphen' => false,
    'valid_underscore' => false,  // Underscores not allowed in new validation
    '123' => true
];

foreach ($testSlugs as $slug => $expected) {
    $isValid = $controller->validate_theme_slug($slug, null, 'slug');
    $status = ($isValid === $expected) ? '✅' : '❌';
    echo "  {$status} '{$slug}' → " . ($isValid ? 'valid' : 'invalid') . " (expected: " . ($expected ? 'valid' : 'invalid') . ")\n";
}

echo "\n";

// Summary
echo "🏁 Test Summary\n";
echo "===============\n";
echo "✅ Slug generation improved with ASCII-only output\n";
echo "✅ Migration system added for existing non-ASCII slugs\n";
echo "✅ REST API validation updated to strict ASCII-only\n";
echo "✅ Frontend updated with optional slug suggestion\n";
echo "\n";
echo "🔧 Key improvements:\n";
echo "  • Uses sanitize_title() as primary method\n";
echo "  • Fallback transliteration for international characters\n";
echo "  • Automatic migration of existing themes\n";
echo "  • Strict ASCII validation in REST API\n";
echo "  • Better error handling and debugging\n";
echo "\n";
echo "📝 Next steps:\n";
echo "  1. Test the comprehensive HTML test file\n";
echo "  2. Verify theme creation/activation/deletion works\n";
echo "  3. Check that existing themes are migrated properly\n";
echo "  4. Ensure REST API routes work with new validation\n";
echo "\n";
echo "Done! 🎉\n";
