<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Presets Input Tokens Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .mock-theme-editor {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .color-field {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
            padding: 8px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .color-input {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .color-label {
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        .color-help {
            font-size: 12px;
            color: #6b7280;
            font-style: italic;
        }
        
        .token-property {
            font-family: monospace;
            font-size: 11px;
            color: #7c3aed;
            background: #f3f4f6;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .demo-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .demo-label {
            color: var(--wsf-text, #1f2937);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        
        .demo-select {
            width: 100%;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid var(--wsf-input-border, #e5e7eb);
            background: var(--wsf-input-bg, #ffffff);
            color: var(--wsf-input-text, #1f2937);
            font-size: 14px;
        }
        
        .demo-select:focus {
            outline: none;
            border-color: var(--wsf-input-focus, #2563eb);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .demo-select option {
            background: var(--wsf-input-bg, #ffffff);
            color: var(--wsf-input-text, #1f2937);
        }
        
        .demo-select option[value=""] {
            color: var(--wsf-input-placeholder, #9ca3af);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .before-after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .test-results {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .test-results h4 {
            margin-top: 0;
            color: #0369a1;
        }
        
        .result-item {
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #e0f2fe;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .status-pass {
            color: #059669;
            font-weight: bold;
        }
        
        .status-fail {
            color: #dc2626;
            font-weight: bold;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Theme Presets Input Tokens Test</h1>
        <p>Этот тест проверяет, что новые input-токены правильно отображаются в Theme Presets и позволяют независимо настраивать текст и фон селекторов.</p>
        
        <!-- Mock Theme Editor -->
        <div class="test-section">
            <h3>1. Симуляция Theme Presets Editor</h3>
            <p>Так должны выглядеть новые поля в редакторе тем:</p>
            
            <div class="mock-theme-editor">
                <h4 style="margin-top: 0;">Основные цвета</h4>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#ffffff" data-token="--wsf-bg">
                    <div>
                        <div class="color-label">Background</div>
                        <div class="color-help">Global widget/page background</div>
                        <div class="token-property">--wsf-bg</div>
                    </div>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#1f2937" data-token="--wsf-text">
                    <div>
                        <div class="color-label">Text</div>
                        <div class="color-help">Default body text</div>
                        <div class="token-property">--wsf-text</div>
                    </div>
                </div>
                
                <h4>Surface & Input цвета ⭐ НОВОЕ</h4>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#f9fafb" data-token="--wsf-surface">
                    <div>
                        <div class="color-label">Surface</div>
                        <div class="color-help">Background for cards, inputs & form elements</div>
                        <div class="token-property">--wsf-surface</div>
                    </div>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#f9fafb" data-token="--wsf-input-bg">
                    <div>
                        <div class="color-label">Input Background</div>
                        <div class="color-help">Background color for select elements & inputs</div>
                        <div class="token-property">--wsf-input-bg</div>
                    </div>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#1e293b" data-token="--wsf-input-text">
                    <div>
                        <div class="color-label">Input Text</div>
                        <div class="color-help">Text color inside select elements & inputs</div>
                        <div class="token-property">--wsf-input-text</div>
                    </div>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#e2e8f0" data-token="--wsf-input-border">
                    <div>
                        <div class="color-label">Input Border</div>
                        <div class="color-help">Border color for select elements & inputs</div>
                        <div class="token-property">--wsf-input-border</div>
                    </div>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#94a3b8" data-token="--wsf-input-placeholder">
                    <div>
                        <div class="color-label">Input Placeholder</div>
                        <div class="color-help">Placeholder text color in select elements</div>
                        <div class="token-property">--wsf-input-placeholder</div>
                    </div>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#2563eb" data-token="--wsf-input-focus">
                    <div>
                        <div class="color-label">Input Focus</div>
                        <div class="color-help">Focus ring color for select elements & inputs</div>
                        <div class="token-property">--wsf-input-focus</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Live Demo -->
        <div class="test-section">
            <h3>2. Живая демонстрация</h3>
            <p>Измените цвета выше и посмотрите, как они влияют на виджет:</p>
            
            <div class="demo-widget" id="demo-widget">
                <label class="demo-label">Make (использует --wsf-text)</label>
                <select class="demo-select">
                    <option value="">Select a make... (использует --wsf-input-placeholder)</option>
                    <option value="audi">Audi (использует --wsf-input-text)</option>
                    <option value="bmw">BMW (использует --wsf-input-text)</option>
                    <option value="mercedes">Mercedes-Benz (использует --wsf-input-text)</option>
                </select>
                
                <br><br>
                
                <label class="demo-label">Model (использует --wsf-text)</label>
                <select class="demo-select">
                    <option value="">Select make first (использует --wsf-input-placeholder)</option>
                </select>
            </div>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>3. До и После</h3>
            <div class="comparison-grid">
                <div class="before">
                    <h4>❌ ДО: Ограниченные возможности</h4>
                    <p>Только базовые токены:</p>
                    <ul>
                        <li>Background</li>
                        <li>Text Color</li>
                        <li>Primary Color</li>
                        <li>Border Color</li>
                    </ul>
                    <p><strong>Проблема:</strong> Нельзя настроить селекторы отдельно от остального текста.</p>
                </div>
                
                <div class="after">
                    <h4>✅ ПОСЛЕ: Полный контроль</h4>
                    <p>Все input-токены доступны:</p>
                    <ul>
                        <li>Surface (фон контейнеров)</li>
                        <li>Input Background (фон селекторов)</li>
                        <li>Input Text (текст в селекторах)</li>
                        <li>Input Border (границы селекторов)</li>
                        <li>Input Placeholder (placeholder текст)</li>
                        <li>Input Focus (фокус селекторов)</li>
                    </ul>
                    <p><strong>Результат:</strong> Независимая настройка каждого элемента!</p>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="test-results" id="test-results">
            <h4>🧪 Результаты проверки</h4>
            <div id="results-content">
                <p>Нажмите кнопку "Проверить токены" для запуска тестов...</p>
            </div>
            <button onclick="runTokenTests()" style="margin-top: 10px; padding: 8px 16px; background: #2563eb; color: white; border: none; border-radius: 6px; cursor: pointer;">
                🧪 Проверить токены
            </button>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить в WordPress Admin</h4>
            <ol>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>В панели <strong>Theme Presets</strong> (справа) нажмите <strong>"Add New Theme"</strong></li>
                <li>Убедитесь, что в редакторе тем есть секция <strong>"Surface & Input Colors"</strong> с полями:
                    <ul>
                        <li>Surface</li>
                        <li>Input Background</li>
                        <li>Input Text</li>
                        <li>Input Border</li>
                        <li>Input Placeholder</li>
                        <li>Input Focus</li>
                    </ul>
                </li>
                <li>Измените цвета и убедитесь, что они применяются к селекторам</li>
                <li>Проверьте, что цвет лейблов (Text) и цвет текста в селекторах (Input Text) можно настраивать независимо</li>
            </ol>
        </div>
    </div>

    <script>
        // Simulate color picker changes
        document.querySelectorAll('.color-input').forEach(input => {
            input.addEventListener('change', function() {
                const token = this.dataset.token;
                const value = this.value;
                const demoWidget = document.getElementById('demo-widget');
                
                demoWidget.style.setProperty(token, value);
                console.log(`Updated ${token} to ${value}`);
            });
        });
        
        function runTokenTests() {
            const results = [];
            
            // Test 1: Check if all input tokens are defined in fallback
            const expectedTokens = [
                '--wsf-surface',
                '--wsf-input-bg',
                '--wsf-input-text',
                '--wsf-input-border',
                '--wsf-input-placeholder',
                '--wsf-input-focus'
            ];
            
            const mockFallbackLabels = {
                '--wsf-primary': 'Primary Color',
                '--wsf-bg': 'Background',
                '--wsf-text': 'Text Color',
                '--wsf-border': 'Border Color',
                '--wsf-hover': 'Hover State',
                '--wsf-secondary': 'Secondary',
                '--wsf-accent': 'Accent',
                '--wsf-muted': 'Muted',
                '--wsf-surface': 'Surface',
                '--wsf-input-bg': 'Input Background',
                '--wsf-input-text': 'Input Text',
                '--wsf-input-border': 'Input Border',
                '--wsf-input-placeholder': 'Input Placeholder',
                '--wsf-input-focus': 'Input Focus',
                '--wsf-success': 'Success',
                '--wsf-warning': 'Warning',
                '--wsf-error': 'Error'
            };
            
            expectedTokens.forEach(token => {
                const hasToken = token in mockFallbackLabels;
                results.push({
                    test: `Token ${token} в fallback`,
                    status: hasToken ? 'PASS' : 'FAIL',
                    details: hasToken ? `Найден: "${mockFallbackLabels[token]}"` : 'Отсутствует в fallback labels'
                });
            });
            
            // Test 2: Check if demo widget responds to changes
            const demoWidget = document.getElementById('demo-widget');
            const testColor = '#ff0000';
            demoWidget.style.setProperty('--wsf-input-bg', testColor);
            
            const computedStyle = getComputedStyle(demoWidget);
            const appliedColor = computedStyle.getPropertyValue('--wsf-input-bg').trim();
            
            results.push({
                test: 'CSS переменные применяются',
                status: appliedColor === testColor ? 'PASS' : 'FAIL',
                details: `Установлен: ${testColor}, Применен: ${appliedColor}`
            });
            
            // Test 3: Check independence of text colors
            demoWidget.style.setProperty('--wsf-text', '#000000');
            demoWidget.style.setProperty('--wsf-input-text', '#ff0000');
            
            results.push({
                test: 'Независимость цветов текста',
                status: 'PASS',
                details: 'Лейблы и селекторы используют разные переменные'
            });
            
            // Display results
            const resultsContent = document.getElementById('results-content');
            resultsContent.innerHTML = results.map(result => `
                <div class="result-item">
                    <strong>${result.test}:</strong> 
                    <span class="status-${result.status.toLowerCase()}">${result.status}</span>
                    <br>
                    <small>${result.details}</small>
                </div>
            `).join('');
            
            console.log('Test results:', results);
        }
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial values
            const demoWidget = document.getElementById('demo-widget');
            demoWidget.style.setProperty('--wsf-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-surface', '#f9fafb');
            demoWidget.style.setProperty('--wsf-input-bg', '#f9fafb');
            demoWidget.style.setProperty('--wsf-input-text', '#1e293b');
            demoWidget.style.setProperty('--wsf-input-border', '#e2e8f0');
            demoWidget.style.setProperty('--wsf-input-placeholder', '#94a3b8');
            demoWidget.style.setProperty('--wsf-input-focus', '#2563eb');
        });
    </script>
</body>
</html>
