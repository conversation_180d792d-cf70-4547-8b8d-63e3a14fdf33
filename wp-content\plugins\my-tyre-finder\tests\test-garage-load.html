<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Garage Load Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Garage Load Functionality Test</h1>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-lg font-semibold mb-4">Test Garage Data</h2>
            <div id="test-results" class="space-y-2"></div>
            <button id="run-tests" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Run Tests
            </button>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">Console Output</h2>
            <div id="console-output" class="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto"></div>
        </div>
    </div>

    <script>
        // Mock console to capture output
        const originalConsole = { ...console };
        const consoleOutput = document.getElementById('console-output');
        
        function logToDiv(level, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const div = document.createElement('div');
            div.className = level === 'error' ? 'text-red-400' : level === 'warn' ? 'text-yellow-400' : 'text-green-400';
            div.textContent = `[${timestamp}] ${level.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            
            // Also call original console
            originalConsole[level](...args);
        }
        
        console.log = (...args) => logToDiv('log', ...args);
        console.error = (...args) => logToDiv('error', ...args);
        console.warn = (...args) => logToDiv('warn', ...args);

        // Test data samples
        const testData = {
            yearFlowItem: {
                id: 'test-year-1',
                make: 'bmw',
                model: 'x5',
                year: '2020',
                modification: 'xdrive30d',
                tire_full: '275/45R20',
                garage_version: '2.0',
                flow_order: ['make', 'model', 'year', 'modification']
            },
            generationFlowItem: {
                id: 'test-gen-1',
                make: 'audi',
                model: 'a4',
                generation: 'b9',
                modification: '2.0-tfsi',
                tire_full: '225/50R17',
                garage_version: '2.0',
                flow_order: ['make', 'model', 'gen', 'modification']
            },
            legacyItem: {
                id: 'test-legacy-1',
                tire_full: '205/55R16'
                // Missing vehicle data
            }
        };

        function runTests() {
            console.log('Starting garage load functionality tests...');
            
            // Test 1: Data validation
            console.log('\n=== Test 1: Data Validation ===');
            testDataValidation();
            
            // Test 2: Flow detection
            console.log('\n=== Test 2: Flow Detection ===');
            testFlowDetection();
            
            // Test 3: Cross-flow compatibility
            console.log('\n=== Test 3: Cross-flow Compatibility ===');
            testCrossFlowCompatibility();
            
            console.log('\n=== Tests Completed ===');
        }

        function testDataValidation() {
            const { yearFlowItem, generationFlowItem, legacyItem } = testData;
            
            // Test year flow item validation
            const yearValid = yearFlowItem.make && yearFlowItem.model && yearFlowItem.year && yearFlowItem.modification;
            console.log('Year flow item validation:', yearValid ? 'PASS' : 'FAIL', yearFlowItem);
            
            // Test generation flow item validation
            const genValid = generationFlowItem.make && generationFlowItem.model && generationFlowItem.generation && generationFlowItem.modification;
            console.log('Generation flow item validation:', genValid ? 'PASS' : 'FAIL', generationFlowItem);
            
            // Test legacy item validation
            const legacyValid = legacyItem.make && legacyItem.model && (legacyItem.year || legacyItem.generation) && legacyItem.modification;
            console.log('Legacy item validation:', legacyValid ? 'FAIL (should be invalid)' : 'PASS', legacyItem);
        }

        function testFlowDetection() {
            const { yearFlowItem, generationFlowItem } = testData;
            
            // Test year flow detection
            const isYearFlow = yearFlowItem.year && !yearFlowItem.generation;
            console.log('Year flow detection:', isYearFlow ? 'PASS' : 'FAIL', { hasYear: !!yearFlowItem.year, hasGeneration: !!yearFlowItem.generation });
            
            // Test generation flow detection
            const isGenFlow = generationFlowItem.generation && !generationFlowItem.year;
            console.log('Generation flow detection:', isGenFlow ? 'PASS' : 'FAIL', { hasYear: !!generationFlowItem.year, hasGeneration: !!generationFlowItem.generation });
        }

        function testCrossFlowCompatibility() {
            // Simulate loading year data in generation flow
            console.log('Cross-flow test: Loading year data in generation flow');
            const yearInGenFlow = testData.yearFlowItem;
            const currentIsGenFlow = true;
            const dataHasGeneration = !!yearInGenFlow.generation;
            
            if (currentIsGenFlow && !dataHasGeneration) {
                console.log('PASS: Correctly detected incompatibility');
            } else {
                console.log('FAIL: Should detect incompatibility');
            }
            
            // Simulate loading generation data in year flow
            console.log('Cross-flow test: Loading generation data in year flow');
            const genInYearFlow = testData.generationFlowItem;
            const currentIsYearFlow = true;
            const dataHasYear = !!genInYearFlow.year;
            
            if (currentIsYearFlow && !dataHasYear) {
                console.log('PASS: Correctly detected incompatibility');
            } else {
                console.log('FAIL: Should detect incompatibility');
            }
        }

        document.getElementById('run-tests').addEventListener('click', runTests);
        
        // Auto-run tests on load
        setTimeout(runTests, 1000);
    </script>
</body>
</html>
