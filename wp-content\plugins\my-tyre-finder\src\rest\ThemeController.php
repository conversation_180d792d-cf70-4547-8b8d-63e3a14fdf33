<?php
declare(strict_types=1);

namespace MyTyreFinder\Rest;

use WP_REST_Controller;
use WP_REST_Request;
use WP_REST_Response;
use WP_REST_Server;
use WP_Error;
use MyTyreFinder\Includes\ThemeManager;

/**
 * REST API controller for theme management
 */
final class ThemeController extends WP_REST_Controller
{
    protected $namespace = 'wheel-size/v1';
    protected $rest_base = 'themes';

    public function register(): void
    {
        add_action('rest_api_init', [$this, 'register_routes']);
    }

    /**
     * Register REST API routes
     */
    public function register_routes(): void
    {
        // GET /wheel-size/v1/themes - Get all themes
        register_rest_route($this->namespace, '/' . $this->rest_base, [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [$this, 'get_themes'],
                'permission_callback' => [$this, 'get_themes_permissions_check'],
            ],
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [$this, 'create_theme'],
                'permission_callback' => [$this, 'create_theme_permissions_check'],
                'args' => $this->get_theme_schema(),
            ],
        ]);

        // IMPORTANT: Register /active route BEFORE /{slug} route to avoid conflicts
        // GET /wheel-size/v1/themes/active - Get active theme
        // PUT /wheel-size/v1/themes/active - Set active theme
        register_rest_route($this->namespace, '/' . $this->rest_base . '/active', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [$this, 'get_active_theme'],
                'permission_callback' => [$this, 'get_active_theme_permissions_check'],
            ],
            [
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => [$this, 'set_active_theme'],
                'permission_callback' => [$this, 'set_active_theme_permissions_check'],
                'args' => [
                    'slug' => [
                        'description' => __('Theme slug to set as active', 'wheel-size'),
                        'type' => 'string',
                        'required' => true,
                        'validate_callback' => [$this, 'validate_theme_slug'],
                        'sanitize_callback' => 'sanitize_text_field',
                    ],
                ],
            ],
        ]);

        // GET /wheel-size/v1/themes/{slug} - Get specific theme
        // Use strict ASCII-only regex for better security and compatibility
        register_rest_route($this->namespace, '/' . $this->rest_base . '/(?P<slug>[A-Za-z0-9\-]+)', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [$this, 'get_theme'],
                'permission_callback' => [$this, 'get_theme_permissions_check'],
                'args' => [
                    'slug' => [
                        'description' => __('Theme slug', 'wheel-size'),
                        'type' => 'string',
                        'required' => true,
                        'validate_callback' => [$this, 'validate_theme_slug'],
                        'sanitize_callback' => 'sanitize_text_field',
                    ],
                ],
            ],
            [
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => [$this, 'update_theme'],
                'permission_callback' => [$this, 'update_theme_permissions_check'],
                'args' => $this->get_theme_schema(),
            ],
            [
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => [$this, 'delete_theme'],
                'permission_callback' => [$this, 'delete_theme_permissions_check'],
                'args' => [
                    'slug' => [
                        'description' => __('Theme slug', 'wheel-size'),
                        'type' => 'string',
                        'required' => true,
                        'validate_callback' => [$this, 'validate_theme_slug'],
                        'sanitize_callback' => 'sanitize_text_field',
                    ],
                ],
            ],
        ]);
    }

    /**
     * Get all themes
     */
    public function get_themes(WP_REST_Request $request): WP_REST_Response
    {
        $themes = ThemeManager::get_themes();
        $active_theme = ThemeManager::get_active_theme();

        return new WP_REST_Response([
            'themes' => $themes,
            'active_theme' => $active_theme,
        ], 200);
    }

    /**
     * Get specific theme
     */
    public function get_theme(WP_REST_Request $request): WP_REST_Response|WP_Error
    {
        $slug = $request->get_param('slug');
        $theme = ThemeManager::get_theme($slug);

        if (!$theme) {
            return new WP_Error(
                'theme_not_found',
                __('Theme not found', 'wheel-size'),
                ['status' => 404]
            );
        }

        return new WP_REST_Response($theme, 200);
    }

    /**
     * Create new theme
     */
    public function create_theme(WP_REST_Request $request): WP_REST_Response|WP_Error
    {
        $name = $request->get_param('name');
        $properties = $request->get_param('properties');
        $suggested_slug = $request->get_param('suggested_slug');

        // Enhanced validation with better error messages
        if (empty($name)) {
            return new WP_Error(
                'missing_name',
                __('Theme name is required', 'wheel-size'),
                ['status' => 400]
            );
        }

        if (!is_array($properties)) {
            return new WP_Error(
                'invalid_properties',
                __('Theme properties must be an array', 'wheel-size'),
                ['status' => 400]
            );
        }

        if (empty($properties)) {
            return new WP_Error(
                'empty_properties',
                __('Theme must have at least one color property', 'wheel-size'),
                ['status' => 400]
            );
        }

        // Validate suggested slug if provided
        if ($suggested_slug && !$this->validate_theme_slug($suggested_slug, $request, 'suggested_slug')) {
            // Log invalid suggested slug but don't fail - server will generate its own
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeController::create_theme - Invalid suggested slug ignored: '{$suggested_slug}'");
            }
            $suggested_slug = null;
        }

        // Debug logging for theme creation
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ThemeController::create_theme - Name: {$name}");
            error_log("ThemeController::create_theme - Properties: " . json_encode($properties));
        }

        $slug = ThemeManager::create_theme($name, $properties);

        if (!$slug) {
            // More detailed error information
            $error_message = __('Failed to create theme. This could be due to invalid color values or other validation issues.', 'wheel-size');

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeController::create_theme - Creation failed for theme: {$name}");
            }

            return new WP_Error(
                'creation_failed',
                $error_message,
                [
                    'status' => 500,
                    'debug_info' => [
                        'name' => $name,
                        'properties_count' => count($properties),
                        'properties_keys' => array_keys($properties)
                    ]
                ]
            );
        }

        $theme = ThemeManager::get_theme($slug);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ThemeController::create_theme - Successfully created theme: {$slug}");
        }

        return new WP_REST_Response([
            'slug' => $slug,
            'theme' => $theme,
            'message' => __('Theme created successfully', 'wheel-size'),
        ], 201);
    }

    /**
     * Update existing theme
     */
    public function update_theme(WP_REST_Request $request): WP_REST_Response|WP_Error
    {
        $slug = $request->get_param('slug');
        $name = $request->get_param('name');
        $properties = $request->get_param('properties');

        if (!ThemeManager::get_theme($slug)) {
            return new WP_Error(
                'theme_not_found',
                __('Theme not found', 'wheel-size'),
                ['status' => 404]
            );
        }

        if (empty($name)) {
            return new WP_Error(
                'missing_name',
                __('Theme name is required', 'wheel-size'),
                ['status' => 400]
            );
        }

        if (!is_array($properties)) {
            return new WP_Error(
                'invalid_properties',
                __('Theme properties must be an array', 'wheel-size'),
                ['status' => 400]
            );
        }

        $success = ThemeManager::update_theme($slug, $name, $properties);

        if (!$success) {
            return new WP_Error(
                'update_failed',
                __('Failed to update theme', 'wheel-size'),
                ['status' => 500]
            );
        }

        $theme = ThemeManager::get_theme($slug);

        return new WP_REST_Response([
            'theme' => $theme,
            'message' => __('Theme updated successfully', 'wheel-size'),
        ], 200);
    }

    /**
     * Delete theme
     */
    public function delete_theme(WP_REST_Request $request): WP_REST_Response|WP_Error
    {
        $slug = $request->get_param('slug');

        if (!ThemeManager::get_theme($slug)) {
            return new WP_Error(
                'theme_not_found',
                __('Theme not found', 'wheel-size'),
                ['status' => 404]
            );
        }

        $success = ThemeManager::delete_theme($slug);

        if (!$success) {
            return new WP_Error(
                'deletion_failed',
                __('Failed to delete theme', 'wheel-size'),
                ['status' => 500]
            );
        }

        return new WP_REST_Response([
            'message' => __('Theme deleted successfully', 'wheel-size'),
        ], 200);
    }

    /**
     * Get active theme
     */
    public function get_active_theme(WP_REST_Request $request): WP_REST_Response
    {
        $active_slug = ThemeManager::get_active_theme();
        $active_theme = ThemeManager::get_active_theme_properties();

        return new WP_REST_Response([
            'slug' => $active_slug,
            'theme' => $active_theme,
        ], 200);
    }

    /**
     * Set active theme
     */
    public function set_active_theme(WP_REST_Request $request): WP_REST_Response|WP_Error
    {
        $slug = $request->get_param('slug');

        if (empty($slug)) {
            return new WP_Error(
                'missing_slug',
                __('Theme slug is required', 'wheel-size'),
                ['status' => 400]
            );
        }

        // Check if theme exists
        $theme = ThemeManager::get_theme($slug);
        if (!$theme) {
            // Get available themes for debugging
            $available_themes = array_keys(ThemeManager::get_themes());

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeController::set_active_theme - Theme '{$slug}' not found");
                error_log("ThemeController::set_active_theme - Available themes: " . implode(', ', $available_themes));
            }

            return new WP_Error(
                'theme_not_found',
                sprintf(__('Theme "%s" not found', 'wheel-size'), $slug),
                [
                    'status' => 404,
                    'debug_info' => [
                        'requested_slug' => $slug,
                        'available_themes' => $available_themes
                    ]
                ]
            );
        }

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ThemeController::set_active_theme - Attempting to activate theme: {$slug}");
        }

        $success = ThemeManager::set_active_theme($slug);

        if (!$success) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeController::set_active_theme - Failed to activate theme: {$slug}");
            }

            return new WP_Error(
                'activation_failed',
                __('Failed to set active theme. This could be a database issue.', 'wheel-size'),
                [
                    'status' => 500,
                    'debug_info' => [
                        'slug' => $slug,
                        'theme_exists' => true
                    ]
                ]
            );
        }

        // Verify activation was successful
        $current_active = ThemeManager::get_active_theme();
        if ($current_active !== $slug) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeController::set_active_theme - Activation verification failed. Expected: {$slug}, Got: {$current_active}");
            }

            return new WP_Error(
                'activation_verification_failed',
                __('Theme activation could not be verified', 'wheel-size'),
                ['status' => 500]
            );
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ThemeController::set_active_theme - Successfully activated theme: {$slug}");
        }

        return new WP_REST_Response([
            'active_theme' => $slug,
            'theme_properties' => $theme,
            'message' => __('Active theme updated successfully', 'wheel-size'),
        ], 200);
    }

    /**
     * Get theme schema for validation
     */
    protected function get_theme_schema(): array
    {
        return [
            'name' => [
                'description' => __('Theme name', 'wheel-size'),
                'type' => 'string',
                'required' => true,
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'properties' => [
                'description' => __('Theme CSS custom properties', 'wheel-size'),
                'type' => 'object',
                'required' => true,
                'properties' => [
                    '--wsf-primary' => ['type' => 'string'],
                    '--wsf-bg' => ['type' => 'string'],
                    '--wsf-text' => ['type' => 'string'],
                    '--wsf-border' => ['type' => 'string'],
                    '--wsf-hover' => ['type' => 'string'],
                    '--wsf-secondary' => ['type' => 'string'],
                    '--wsf-accent' => ['type' => 'string'],
                    '--wsf-muted' => ['type' => 'string'],
                    '--wsf-success' => ['type' => 'string'],
                    '--wsf-warning' => ['type' => 'string'],
                    '--wsf-error' => ['type' => 'string'],
                ],
            ],
            'suggested_slug' => [
                'description' => __('Optional suggested slug for the theme', 'wheel-size'),
                'type' => 'string',
                'required' => false,
                'validate_callback' => [$this, 'validate_theme_slug'],
                'sanitize_callback' => 'sanitize_text_field',
            ],
        ];
    }

    /**
     * Validate theme slug parameter - strict ASCII-only validation
     */
    public function validate_theme_slug($value, $request, $param): bool
    {
        // Must be a string
        if (!is_string($value)) {
            return false;
        }

        // Must not be empty
        $value = trim($value);
        if (empty($value)) {
            return false;
        }

        // Must not be too long (reasonable limit)
        if (strlen($value) > 100) {
            return false;
        }

        // Only allow ASCII alphanumeric characters and hyphens
        // This ensures compatibility with REST routes and prevents encoding issues
        if (!preg_match('/^[a-zA-Z0-9\-]+$/', $value)) {
            // Log validation failures for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeController::validate_theme_slug - Rejected non-ASCII slug: '{$value}'");
            }
            return false;
        }

        // Additional validation: slug cannot start or end with hyphen
        if (preg_match('/^-|-$/', $value)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeController::validate_theme_slug - Rejected slug with leading/trailing hyphen: '{$value}'");
            }
            return false;
        }

        // Additional validation: slug cannot have consecutive hyphens
        if (strpos($value, '--') !== false) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeController::validate_theme_slug - Rejected slug with consecutive hyphens: '{$value}'");
            }
            return false;
        }

        return true;
    }

    // Permission callbacks
    public function get_themes_permissions_check(): bool
    {
        return current_user_can('manage_options');
    }

    public function get_theme_permissions_check(): bool
    {
        return current_user_can('manage_options');
    }

    public function create_theme_permissions_check(): bool
    {
        return current_user_can('manage_options');
    }

    public function update_theme_permissions_check(): bool
    {
        return current_user_can('manage_options');
    }

    public function delete_theme_permissions_check(): bool
    {
        return current_user_can('manage_options');
    }

    public function get_active_theme_permissions_check(): bool
    {
        return current_user_can('manage_options');
    }

    public function set_active_theme_permissions_check(): bool
    {
        return current_user_can('manage_options');
    }
} 