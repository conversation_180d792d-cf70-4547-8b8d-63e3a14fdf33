document.addEventListener('DOMContentLoaded', () => {
    const page = document.getElementById('wheel-size-translations-page');
    if (!page) return;

    const form = document.getElementById('translations-form');
    const tableBody = document.getElementById('translations-body');
    const searchInput = document.getElementById('translation-search-input');
    const addNewRowBtn = document.getElementById('add-new-row');
    const saveBtn = document.getElementById('save-translations');
    let originalValues = new Map();
    let rowIndex = tableBody.rows.length;

    const storeOriginalValues = () => {
        originalValues.clear();
        tableBody.querySelectorAll('tr').forEach(row => {
            const keyInput = row.querySelector('.key-input');
            const valueInput = row.querySelector('.value-input');
            if (keyInput && valueInput) {
                originalValues.set(keyInput.value, valueInput.value);
            }
        });
    };

    const handleFieldChange = (e) => {
        const input = e.target;
        if (input.classList.contains('value-input')) {
            const key = input.closest('tr').querySelector('.key-input').value;
            const originalValue = originalValues.get(key);
            input.classList.toggle('is-changed', input.value !== originalValue);
        }
        validateAllKeys();
    };

    const validateKey = (keyInput) => {
        const key = keyInput.value.trim();
        keyInput.classList.remove('is-empty', 'is-duplicate');
        if (key === '') {
            keyInput.classList.add('is-empty');
            return false;
        }
        const allKeys = [...tableBody.querySelectorAll('.key-input')].map(i => i.value.trim());
        const isDuplicate = allKeys.filter(k => k === key).length > 1;
        if (isDuplicate) {
            keyInput.classList.add('is-duplicate');
            return false;
        }
        return true;
    };

    const validateAllKeys = () => {
        let allValid = true;
        tableBody.querySelectorAll('.key-input').forEach(input => {
            if (!validateKey(input)) {
                allValid = false;
            }
        });
        saveBtn.disabled = !allValid;
        return allValid;
    };
    
    // Add new row
    addNewRowBtn.addEventListener('click', () => {
        const noItemsRow = tableBody.querySelector('.no-items');
        if (noItemsRow) noItemsRow.remove();
        
        const newRow = tableBody.insertRow();
        newRow.innerHTML = `
            <td>
                <div class="key-cell">
                    <input type="text" class="key-input" name="translations[${rowIndex}][key]" placeholder="new_translation_key">
                    <span class="dashicons dashicons-info-outline" title="No description available."></span>
                </div>
            </td>
            <td><input type="text" class="value-input widefat" name="translations[${rowIndex}][value]" placeholder="Translated text"></td>
            <td class="actions-cell"><button type="button" class="button-link-delete delete-row" aria-label="Delete key"><span class="dashicons dashicons-trash"></span></button></td>
        `;
        newRow.querySelector('.key-input').focus();
        rowIndex++;
        validateAllKeys();
    });

    // Delete row
    tableBody.addEventListener('click', (e) => {
        const deleteBtn = e.target.closest('.delete-row');
        if (deleteBtn) {
            if (window.confirm('Are you sure you want to delete this key?')) {
                deleteBtn.closest('tr').remove();
                if (tableBody.rows.length === 0) {
                     tableBody.innerHTML = '<tr class="no-items"><td colspan="3">No translations found. Start by adding a new key.</td></tr>';
                }
                validateAllKeys();
            }
        }
    });

    // Search/Filter
    searchInput.addEventListener('input', () => {
        const query = searchInput.value.toLowerCase();
        tableBody.querySelectorAll('tr').forEach(row => {
            const key = row.querySelector('.key-input')?.value.toLowerCase() || '';
            row.classList.toggle('hidden-row', !key.includes(query));
        });
    });

    // Save form (with validation)
    form.addEventListener('submit', (e) => {
        if (!validateAllKeys()) {
            e.preventDefault();
            alert('Please fix the errors before saving (empty or duplicate keys).');
        }
    });

    // Hotkey for save
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveBtn.click();
        }
    });

    // Track changes
    tableBody.addEventListener('input', handleFieldChange);
    
    // Initial setup
    storeOriginalValues();
    validateAllKeys();
}); 