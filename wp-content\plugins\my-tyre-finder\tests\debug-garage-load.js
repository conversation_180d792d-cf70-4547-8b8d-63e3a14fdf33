// Enhanced Debug script for garage load functionality
// Add this to browser console to debug garage load issues

console.log('=== Enhanced Garage Load Debug Script ===');

// Check if all required objects are available
console.log('1. Checking required objects:');
console.log('   window.wheelFitWidget:', !!window.wheelFitWidget);
console.log('   window.wheelFitWidgetReady:', !!window.wheelFitWidgetReady);
console.log('   window.garageWidgetReadyFlag:', !!window.garageWidgetReadyFlag);
console.log('   LocalStorageHandler:', typeof LocalStorageHandler);
console.log('   isWidgetReady function:', typeof isWidgetReady);
console.log('   performGarageLoad function:', typeof performGarageLoad);
console.log('   waitForWidget function:', typeof waitForWidget);

// Check garage data
if (typeof LocalStorageHandler !== 'undefined') {
    const storage = new LocalStorageHandler();
    const garage = storage.getGarage();
    console.log('2. Garage data:');
    console.log('   Items count:', garage.length);
    console.log('   Items:', garage);
    
    // Analyze each item
    garage.forEach((item, index) => {
        console.log(`   Item ${index + 1}:`, {
            id: item.id,
            make: item.make,
            model: item.model,
            year: item.year,
            generation: item.generation,
            modification: item.modification,
            tire_full: item.tire_full,
            garage_version: item.garage_version,
            flow_order: item.flow_order,
            isGenerationFlow: !!(item.generation && !item.year),
            isYearFlow: !!item.year,
            hasRequiredFields: !!(item.make && item.model && item.modification && (item.year || item.generation))
        });
    });
} else {
    console.log('2. LocalStorageHandler not available');
}

// Check DOM elements
console.log('3. Checking DOM elements:');
console.log('   garage-items-list:', !!document.getElementById('garage-items-list'));
console.log('   garage-drawer:', !!document.getElementById('garage-drawer'));
console.log('   Load buttons:', document.querySelectorAll('.garage-load').length);

// Check widget state
if (window.wheelFitWidget) {
    console.log('4. Widget state:');
    console.log('   mode:', window.wheelFitWidget.mode);
    console.log('   flowOrder:', window.wheelFitWidget.flowOrder);
    console.log('   selectedData:', window.wheelFitWidget.selectedData);
    console.log('   loadFromHistory method:', typeof window.wheelFitWidget.loadFromHistory);
} else {
    console.log('4. Widget not available');
}

// Test widget readiness
function testWidgetReadiness() {
    console.log('=== Testing Widget Readiness ===');

    if (typeof isWidgetReady === 'function') {
        const ready = isWidgetReady();
        console.log('Widget ready status:', ready);

        if (!ready) {
            console.log('Testing waitForWidget function...');
            if (typeof waitForWidget === 'function') {
                waitForWidget(3000)
                    .then(() => {
                        console.log('Widget became ready!');
                    })
                    .catch(error => {
                        console.error('Widget failed to become ready:', error);
                    });
            } else {
                console.error('waitForWidget function not available');
            }
        }
    } else {
        console.error('isWidgetReady function not available');
    }
}

// Test load functionality
function testGarageLoad() {
    console.log('=== Testing Garage Load ===');

    // First check widget readiness
    if (typeof isWidgetReady === 'function' && !isWidgetReady()) {
        console.log('Widget not ready, testing wait mechanism...');
        testWidgetReadiness();
        return;
    }

    if (!window.wheelFitWidget) {
        console.error('wheelFitWidget not available');
        return;
    }

    if (typeof LocalStorageHandler === 'undefined') {
        console.error('LocalStorageHandler not available');
        return;
    }

    const storage = new LocalStorageHandler();
    const garage = storage.getGarage();

    if (garage.length === 0) {
        console.log('No items in garage to test');
        return;
    }

    const testItem = garage[0];
    console.log('Testing with item:', testItem);

    // Test the new performGarageLoad function if available
    if (typeof performGarageLoad === 'function') {
        console.log('Testing performGarageLoad function...');
        performGarageLoad(testItem)
            .then(() => {
                console.log('performGarageLoad test completed successfully');
            })
            .catch(error => {
                console.error('performGarageLoad test failed:', error);
            });
    } else {
        // Fallback to direct widget call
        try {
            const encodedData = encodeURIComponent(JSON.stringify(testItem));
            console.log('Encoded data:', encodedData);

            window.wheelFitWidget.loadFromHistory(encodedData)
                .then(() => {
                    console.log('Direct load test completed successfully');
                })
                .catch(error => {
                    console.error('Direct load test failed:', error);
                });
        } catch (error) {
            console.error('Load test error:', error);
        }
    }
}

// Expose test functions globally
window.testGarageLoad = testGarageLoad;
window.testWidgetReadiness = testWidgetReadiness;

// Auto-run widget readiness test
setTimeout(() => {
    console.log('=== Auto-running widget readiness test ===');
    testWidgetReadiness();
}, 1000);

console.log('=== Enhanced Debug script loaded ===');
console.log('Available functions:');
console.log('- testGarageLoad() - Test garage loading functionality');
console.log('- testWidgetReadiness() - Test widget readiness detection');
console.log('- window.debugGarageWidget() - Comprehensive debug info (if available)');
