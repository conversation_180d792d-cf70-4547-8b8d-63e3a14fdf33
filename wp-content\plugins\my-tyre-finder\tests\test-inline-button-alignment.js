/**
 * Test script to verify "Find Sizes" button alignment with dropdowns
 * in Inline (1x4) layout after top-edge alignment changes
 */

console.log('🧪 Testing Inline (1x4) button alignment...');

function testButtonTopAlignment() {
    console.log('\n📍 Testing "Find Sizes" button top alignment...');
    
    // Find the inline form
    const inlineForm = document.getElementById('tab-by-car');
    if (!inlineForm || !inlineForm.classList.contains('grid')) {
        console.log('❌ Inline form not found or not in grid layout');
        return false;
    }
    
    console.log('✅ Found inline form with grid layout');
    
    // Find the search button container
    const submitContainer = inlineForm.querySelector('.ws-submit');
    if (!submitContainer) {
        console.log('❌ Search button container not found');
        return false;
    }
    
    // Check if container uses justify-start (top alignment)
    const hasTopAlignment = submitContainer.classList.contains('justify-start');
    console.log(`${hasTopAlignment ? '✅' : '❌'} Search button container uses top alignment (justify-start)`);
    
    // Check if invisible label exists
    const invisibleLabel = submitContainer.querySelector('label[class*="text-transparent"]');
    const hasInvisibleLabel = !!invisibleLabel;
    console.log(`${hasInvisibleLabel ? '✅' : '❌'} Invisible label exists for alignment`);
    
    // Find dropdown fields for comparison
    const dropdownFields = inlineForm.querySelectorAll('select');
    const searchButton = submitContainer.querySelector('button[type="submit"]');
    
    if (dropdownFields.length > 0 && searchButton) {
        console.log(`✅ Found ${dropdownFields.length} dropdown field(s) and search button`);
        
        // Compare vertical positions
        const firstDropdown = dropdownFields[0];
        const dropdownRect = firstDropdown.getBoundingClientRect();
        const buttonRect = searchButton.getBoundingClientRect();
        
        // Check if they're roughly aligned (within 5px tolerance)
        const topDifference = Math.abs(dropdownRect.top - buttonRect.top);
        const isAligned = topDifference <= 5;
        
        console.log(`${isAligned ? '✅' : '❌'} Button and dropdown top alignment`);
        console.log(`   Dropdown top: ${dropdownRect.top}px`);
        console.log(`   Button top: ${buttonRect.top}px`);
        console.log(`   Difference: ${topDifference}px`);
        
        return hasTopAlignment && hasInvisibleLabel && isAligned;
    } else {
        console.log('❌ Could not find dropdown fields or search button for comparison');
        return hasTopAlignment && hasInvisibleLabel;
    }
}

function testLabelConsistency() {
    console.log('\n📐 Testing label consistency...');
    
    const inlineForm = document.getElementById('tab-by-car');
    if (!inlineForm) {
        console.log('❌ Inline form not found');
        return false;
    }
    
    // Find all visible labels (from dropdown fields)
    const visibleLabels = inlineForm.querySelectorAll('label:not([class*="text-transparent"])');
    
    // Find the invisible label (from search button)
    const invisibleLabel = inlineForm.querySelector('label[class*="text-transparent"]');
    
    if (visibleLabels.length > 0 && invisibleLabel) {
        const firstVisibleLabel = visibleLabels[0];
        
        // Compare classes (should be similar except for text color)
        const visibleClasses = firstVisibleLabel.className;
        const invisibleClasses = invisibleLabel.className;
        
        const hasBlockClass = invisibleClasses.includes('block');
        const hasTextXsClass = invisibleClasses.includes('text-xs');
        const hasFontSemiboldClass = invisibleClasses.includes('font-semibold');
        const hasMb1Class = invisibleClasses.includes('mb-1');
        
        console.log(`${hasBlockClass ? '✅' : '❌'} Invisible label has 'block' class`);
        console.log(`${hasTextXsClass ? '✅' : '❌'} Invisible label has 'text-xs' class`);
        console.log(`${hasFontSemiboldClass ? '✅' : '❌'} Invisible label has 'font-semibold' class`);
        console.log(`${hasMb1Class ? '✅' : '❌'} Invisible label has 'mb-1' class`);
        
        console.log(`   Visible label classes: ${visibleClasses}`);
        console.log(`   Invisible label classes: ${invisibleClasses}`);
        
        return hasBlockClass && hasTextXsClass && hasFontSemiboldClass && hasMb1Class;
    } else {
        console.log('❌ Could not find labels for comparison');
        return false;
    }
}

function testGarageButtonPosition() {
    console.log('\n🚗 Testing Garage button position after alignment change...');
    
    const submitContainer = document.querySelector('.ws-submit');
    if (!submitContainer) {
        console.log('❌ Search button container not found');
        return false;
    }
    
    const garageButton = submitContainer.querySelector('[data-garage-trigger]');
    if (!garageButton) {
        console.log('ℹ️ Garage button not found (may be disabled)');
        return true; // Not an error if garage is disabled
    }
    
    console.log('✅ Garage button found in search container');
    
    // Check if garage button is still below search button
    const searchButton = submitContainer.querySelector('button[type="submit"]');
    if (searchButton) {
        const searchRect = searchButton.getBoundingClientRect();
        const garageRect = garageButton.getBoundingClientRect();
        
        const isBelow = garageRect.top > searchRect.bottom;
        console.log(`${isBelow ? '✅' : '❌'} Garage button still positioned below search button`);
        
        return isBelow;
    }
    
    return true;
}

function runButtonAlignmentTests() {
    console.log('🚀 Starting Inline (1x4) button alignment tests...\n');
    
    const results = {
        topAlignment: testButtonTopAlignment(),
        labelConsistency: testLabelConsistency(),
        garagePosition: testGarageButtonPosition()
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Button alignment is correct.');
    } else {
        console.log('⚠️ Some tests failed. Check the details above.');
    }
    
    return results;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runButtonAlignmentTests);
} else {
    runButtonAlignmentTests();
}

// Export for manual testing
window.testInlineButtonAlignment = runButtonAlignmentTests;
