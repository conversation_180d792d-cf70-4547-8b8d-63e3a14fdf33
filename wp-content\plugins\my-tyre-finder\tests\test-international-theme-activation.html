<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>International Theme Activation Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
        }
        
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #1d4ed8;
        }
        
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .success {
            color: #16a34a;
            font-weight: bold;
        }
        
        .error {
            color: #dc2626;
            font-weight: bold;
        }
        
        .log {
            background: #1f2937;
            color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .theme-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .theme-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        
        .theme-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .theme-slug {
            font-family: monospace;
            color: #6b7280;
            font-size: 12px;
            margin-bottom: 10px;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .status.success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status.error {
            background: #fef2f2;
            color: #991b1b;
        }
        
        .status.pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status.info {
            background: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">International Theme Activation Test</h1>
            <p style="color: #6b7280;">Testing theme creation and activation with international characters and numeric names</p>
        </header>

        <div class="test-section">
            <h3>🌍 Test Themes</h3>
            <p>These themes will be created and tested for activation:</p>
            <div class="theme-grid" id="theme-grid">
                <!-- Themes will be populated by JavaScript -->
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Test Controls</h3>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
            <button class="test-button" onclick="createAllThemes()">Create All Themes</button>
            <button class="test-button" onclick="testAllActivations()">Test All Activations</button>
            <button class="test-button" onclick="cleanupTestThemes()">Cleanup Test Themes</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>📊 Results Summary</h3>
            <div id="results-summary">
                <div class="status info">Ready to run tests</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Test Log</h3>
            <div id="log" class="log">Test log will appear here...\n</div>
        </div>
    </div>

    <script>
        // Test themes with various international characters and numeric names
        const testThemes = [
            { name: '123', description: 'Pure numeric name' },
            { name: '456789', description: 'Longer numeric name' },
            { name: 'Тема', description: 'Cyrillic (Russian)' },
            { name: '主题', description: 'Chinese characters' },
            { name: 'テーマ', description: 'Japanese characters' },
            { name: 'موضوع', description: 'Arabic characters' },
            { name: 'Thème', description: 'French with accents' },
            { name: 'Téma', description: 'Spanish with accents' },
            { name: 'Thema123', description: 'Mixed English + numbers' },
            { name: 'Тема2024', description: 'Mixed Cyrillic + numbers' },
            { name: 'Theme-Test', description: 'English with hyphen' },
            { name: 'Theme_Test', description: 'English with underscore' },
            { name: 'Ελληνικό', description: 'Greek characters' },
            { name: 'हिंदी', description: 'Hindi characters' },
            { name: '한국어', description: 'Korean characters' }
        ];

        let createdThemes = [];
        let testResults = {};

        // Logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logDiv.innerHTML += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Initialize theme grid
        function initializeThemeGrid() {
            const grid = document.getElementById('theme-grid');
            grid.innerHTML = '';
            
            testThemes.forEach((theme, index) => {
                const card = document.createElement('div');
                card.className = 'theme-card';
                card.id = `theme-${index}`;
                card.innerHTML = `
                    <div class="theme-name">${theme.name}</div>
                    <div class="theme-slug" id="slug-${index}">Slug: (not created)</div>
                    <div class="status info" id="status-${index}">Ready</div>
                    <div style="font-size: 12px; color: #6b7280; margin-top: 5px;">${theme.description}</div>
                `;
                grid.appendChild(card);
            });
        }

        // Update theme status
        function updateThemeStatus(index, status, type = 'info') {
            const statusElement = document.getElementById(`status-${index}`);
            if (statusElement) {
                statusElement.className = `status ${type}`;
                statusElement.textContent = status;
            }
        }

        // Update theme slug display
        function updateThemeSlug(index, slug) {
            const slugElement = document.getElementById(`slug-${index}`);
            if (slugElement) {
                slugElement.textContent = `Slug: ${slug}`;
            }
        }

        // Check API availability
        async function checkAPI() {
            if (typeof wpApiSettings === 'undefined') {
                log('wpApiSettings not found - test must be run in WordPress admin', 'error');
                return false;
            }
            
            log(`API Base: ${wpApiSettings.root}wheel-size/v1/themes`);
            log(`Nonce available: ${!!wpApiSettings.nonce}`);
            return true;
        }

        // Create a single theme
        async function createTheme(themeName, index) {
            if (!await checkAPI()) return null;

            const themeData = {
                name: themeName,
                properties: {
                    '--wsf-primary': `hsl(${index * 25}, 70%, 50%)`,
                    '--wsf-bg': '#ffffff',
                    '--wsf-text': '#333333',
                    '--wsf-border': '#e1e5e9'
                }
            };

            try {
                updateThemeStatus(index, 'Creating...', 'pending');
                log(`Creating theme: "${themeName}"`);

                const response = await fetch(wpApiSettings.root + 'wheel-size/v1/themes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': wpApiSettings.nonce
                    },
                    body: JSON.stringify(themeData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                log(`✅ Created theme "${themeName}" with slug: ${data.slug}`, 'success');
                updateThemeStatus(index, 'Created', 'success');
                updateThemeSlug(index, data.slug);
                
                return data.slug;
            } catch (error) {
                log(`❌ Failed to create theme "${themeName}": ${error.message}`, 'error');
                updateThemeStatus(index, `Failed: ${error.message}`, 'error');
                return null;
            }
        }

        // Activate a theme
        async function activateTheme(slug, themeName, index) {
            if (!await checkAPI()) return false;

            try {
                updateThemeStatus(index, 'Activating...', 'pending');
                log(`Activating theme: "${themeName}" (${slug})`);

                const response = await fetch(wpApiSettings.root + 'wheel-size/v1/themes/active', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': wpApiSettings.nonce
                    },
                    body: JSON.stringify({ slug: slug })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                log(`✅ Activated theme "${themeName}" (${slug})`, 'success');
                updateThemeStatus(index, 'Activated', 'success');
                return true;
            } catch (error) {
                log(`❌ Failed to activate theme "${themeName}": ${error.message}`, 'error');
                updateThemeStatus(index, `Activation failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Create all test themes
        async function createAllThemes() {
            log('=== CREATING ALL TEST THEMES ===');
            createdThemes = [];
            
            for (let i = 0; i < testThemes.length; i++) {
                const slug = await createTheme(testThemes[i].name, i);
                if (slug) {
                    createdThemes.push({ slug, name: testThemes[i].name, index: i });
                }
                
                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            log(`Created ${createdThemes.length}/${testThemes.length} themes`);
        }

        // Test all activations
        async function testAllActivations() {
            log('=== TESTING ALL ACTIVATIONS ===');
            testResults = {};
            
            for (const theme of createdThemes) {
                const success = await activateTheme(theme.slug, theme.name, theme.index);
                testResults[theme.name] = success;
                
                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            updateResultsSummary();
        }

        // Update results summary
        function updateResultsSummary() {
            const summaryDiv = document.getElementById('results-summary');
            const total = Object.keys(testResults).length;
            const successful = Object.values(testResults).filter(Boolean).length;
            const failed = total - successful;
            
            let summaryHTML = `
                <div class="status info">Total themes tested: ${total}</div>
                <div class="status success">Successful activations: ${successful}</div>
            `;
            
            if (failed > 0) {
                summaryHTML += `<div class="status error">Failed activations: ${failed}</div>`;
            }
            
            if (total > 0) {
                const successRate = ((successful / total) * 100).toFixed(1);
                summaryHTML += `<div class="status ${successRate === '100.0' ? 'success' : 'error'}">Success rate: ${successRate}%</div>`;
            }
            
            summaryDiv.innerHTML = summaryHTML;
        }

        // Cleanup test themes
        async function cleanupTestThemes() {
            if (!await checkAPI()) return;
            
            log('=== CLEANING UP TEST THEMES ===');
            
            for (const theme of createdThemes) {
                try {
                    const response = await fetch(wpApiSettings.root + `wheel-size/v1/themes/${encodeURIComponent(theme.slug)}`, {
                        method: 'DELETE',
                        headers: {
                            'X-WP-Nonce': wpApiSettings.nonce
                        }
                    });
                    
                    if (response.ok) {
                        log(`✅ Deleted theme: ${theme.name} (${theme.slug})`, 'success');
                        updateThemeStatus(theme.index, 'Deleted', 'info');
                    } else {
                        log(`❌ Failed to delete theme: ${theme.name}`, 'error');
                    }
                } catch (error) {
                    log(`❌ Error deleting theme ${theme.name}: ${error.message}`, 'error');
                }
                
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            createdThemes = [];
            testResults = {};
            updateResultsSummary();
        }

        // Run all tests
        async function runAllTests() {
            log('🚀 === STARTING COMPREHENSIVE INTERNATIONAL THEME TEST ===');
            
            await createAllThemes();
            await testAllActivations();
            
            log('=== TEST COMPLETED ===');
            
            const total = Object.keys(testResults).length;
            const successful = Object.values(testResults).filter(Boolean).length;
            
            if (successful === total && total > 0) {
                log('🎉 ALL TESTS PASSED! International theme activation is working correctly.', 'success');
            } else {
                log(`⚠️ ${total - successful} tests failed. Check the log for details.`, 'warning');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeThemeGrid();
            log('International Theme Activation Test loaded');
            checkAPI();
        });
    </script>
</body>
</html>
