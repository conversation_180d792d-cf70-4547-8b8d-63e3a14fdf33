<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест исправления slug'ов тем</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2563eb; margin-bottom: 30px; }
        h2 { color: #1e40af; margin-top: 30px; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: #f9fafb;
        }
        .test-item {
            margin: 10px 0;
            padding: 15px;
            border-left: 4px solid #6b7280;
            background: white;
        }
        .test-item.success { border-left-color: #10b981; }
        .test-item.error { border-left-color: #ef4444; }
        .test-item.warning { border-left-color: #f59e0b; }
        .status {
            font-weight: bold;
            margin-right: 10px;
        }
        .success .status { color: #10b981; }
        .error .status { color: #ef4444; }
        .warning .status { color: #f59e0b; }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #1d4ed8; }
        button:disabled { background: #9ca3af; cursor: not-allowed; }
        .log {
            background: #1f2937;
            color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .theme-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .theme-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
        }
        .theme-name { font-weight: bold; color: #1f2937; }
        .theme-slug { font-family: monospace; color: #6b7280; font-size: 12px; }
        .theme-actions {
            margin-top: 10px;
        }
        .theme-actions button {
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2563eb;
        }
        .stat-label {
            color: #6b7280;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Тест исправления slug'ов тем</h1>
        
        <div class="test-section">
            <h2>📊 Статистика</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="total-themes">0</div>
                    <div class="stat-label">Всего тем</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="ascii-themes">0</div>
                    <div class="stat-label">ASCII slug'ы</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="non-ascii-themes">0</div>
                    <div class="stat-label">Не-ASCII slug'ы</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="migrated-themes">0</div>
                    <div class="stat-label">Мигрировано</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎮 Управление</h2>
            <button onclick="loadThemes()">🔄 Загрузить темы</button>
            <button onclick="createTestThemes()">➕ Создать тестовые темы</button>
            <button onclick="testMigration()">🔄 Тест миграции</button>
            <button onclick="testSlugValidation()">✅ Тест валидации</button>
            <button onclick="cleanupTestThemes()">🗑️ Очистить тестовые темы</button>
        </div>

        <div class="test-section">
            <h2>📋 Существующие темы</h2>
            <div id="theme-list" class="theme-list"></div>
        </div>

        <div class="test-section">
            <h2>🧪 Результаты тестов</h2>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h2>📝 Лог</h2>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // Глобальные переменные
        let themes = {};
        let testThemes = [];
        let migratedCount = 0;

        // API настройки
        const apiBase = '/wp-json/wheel-size/v1/themes';
        const nonce = document.querySelector('meta[name="wp-nonce"]')?.content || 'test-nonce';

        // Утилиты
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const color = {
                'info': '#60a5fa',
                'success': '#34d399', 
                'error': '#f87171',
                'warning': '#fbbf24'
            }[type] || '#60a5fa';
            
            logElement.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStats() {
            const totalThemes = Object.keys(themes).length;
            const asciiThemes = Object.keys(themes).filter(slug => /^[a-zA-Z0-9\-]+$/.test(slug)).length;
            const nonAsciiThemes = totalThemes - asciiThemes;

            document.getElementById('total-themes').textContent = totalThemes;
            document.getElementById('ascii-themes').textContent = asciiThemes;
            document.getElementById('non-ascii-themes').textContent = nonAsciiThemes;
            document.getElementById('migrated-themes').textContent = migratedCount;
        }

        function isValidSlug(slug) {
            return /^[a-zA-Z0-9\-]+$/.test(slug) && 
                   !slug.startsWith('-') && 
                   !slug.endsWith('-') && 
                   !slug.includes('--');
        }

        function addTestResult(name, status, details) {
            const resultsDiv = document.getElementById('test-results');
            const testItem = document.createElement('div');
            testItem.className = `test-item ${status}`;
            testItem.innerHTML = `
                <span class="status">${status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️'}</span>
                <strong>${name}</strong><br>
                <small>${details}</small>
            `;
            resultsDiv.appendChild(testItem);
        }

        // Основные функции
        async function loadThemes() {
            log('Загрузка тем...');
            try {
                const response = await fetch(apiBase);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                themes = data.themes || {};
                
                log(`Загружено ${Object.keys(themes).length} тем`, 'success');
                updateStats();
                renderThemes();
                
            } catch (error) {
                log(`Ошибка загрузки тем: ${error.message}`, 'error');
            }
        }

        function renderThemes() {
            const container = document.getElementById('theme-list');
            container.innerHTML = '';

            Object.entries(themes).forEach(([slug, theme]) => {
                const isValidAscii = isValidSlug(slug);
                const card = document.createElement('div');
                card.className = 'theme-card';
                card.innerHTML = `
                    <div class="theme-name">${theme.name || slug}</div>
                    <div class="theme-slug">slug: ${slug} ${isValidAscii ? '✅' : '❌'}</div>
                    <div class="theme-actions">
                        <button onclick="activateTheme('${slug}')">Активировать</button>
                        <button onclick="deleteTheme('${slug}')">Удалить</button>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        async function createTestThemes() {
            log('Создание тестовых тем...');
            
            const testNames = [
                'Русская тема',
                'Тёмная тема',
                'Café Theme',
                'Thème français',
                'Diseño español',
                '主题中文',
                'テーマ日本語',
                '123',
                'Theme-2024',
                'My_Custom_Theme'
            ];

            const baseProperties = {
                '--wsf-primary': '#2563eb',
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#1f2937',
                '--wsf-border': '#e5e7eb'
            };

            for (const name of testNames) {
                try {
                    const response = await fetch(apiBase, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': nonce
                        },
                        body: JSON.stringify({
                            name: name,
                            properties: baseProperties
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        testThemes.push(data.slug);
                        log(`Создана тема "${name}" → "${data.slug}"`, 'success');
                    } else {
                        log(`Ошибка создания темы "${name}": ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`Исключение при создании темы "${name}": ${error.message}`, 'error');
                }
            }

            await loadThemes();
        }

        async function testMigration() {
            log('Тестирование миграции...');
            
            // Просто перезагружаем темы - миграция должна произойти автоматически
            const beforeCount = Object.keys(themes).length;
            await loadThemes();
            const afterCount = Object.keys(themes).length;
            
            const nonAsciiCount = Object.keys(themes).filter(slug => !/^[a-zA-Z0-9\-]+$/.test(slug)).length;
            
            if (nonAsciiCount === 0) {
                addTestResult('Миграция slug\'ов', 'success', 'Все slug\'ы теперь ASCII-совместимы');
                log('Миграция завершена успешно', 'success');
            } else {
                addTestResult('Миграция slug\'ов', 'warning', `Остались ${nonAsciiCount} не-ASCII slug'ов`);
                log(`Внимание: ${nonAsciiCount} slug'ов всё ещё не ASCII`, 'warning');
            }
        }

        async function testSlugValidation() {
            log('Тестирование валидации slug\'ов...');
            
            const testCases = [
                { slug: 'valid-slug', expected: true },
                { slug: 'русский-slug', expected: false },
                { slug: '-invalid-start', expected: false },
                { slug: 'invalid-end-', expected: false },
                { slug: 'double--hyphen', expected: false },
                { slug: '123', expected: true },
                { slug: 'theme-123', expected: true }
            ];

            for (const testCase of testCases) {
                const isValid = isValidSlug(testCase.slug);
                const passed = isValid === testCase.expected;
                
                addTestResult(
                    `Валидация "${testCase.slug}"`,
                    passed ? 'success' : 'error',
                    `Ожидалось: ${testCase.expected}, получено: ${isValid}`
                );
                
                log(`Тест валидации "${testCase.slug}": ${passed ? 'ПРОШЁЛ' : 'ПРОВАЛИЛСЯ'}`, passed ? 'success' : 'error');
            }
        }

        async function activateTheme(slug) {
            log(`Активация темы "${slug}"...`);
            try {
                const response = await fetch(`${apiBase}/active`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': nonce
                    },
                    body: JSON.stringify({ slug })
                });

                if (response.ok) {
                    log(`Тема "${slug}" активирована`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`Ошибка активации темы "${slug}": ${errorText}`, 'error');
                }
            } catch (error) {
                log(`Исключение при активации темы "${slug}": ${error.message}`, 'error');
            }
        }

        async function deleteTheme(slug) {
            if (!confirm(`Удалить тему "${slug}"?`)) return;
            
            log(`Удаление темы "${slug}"...`);
            try {
                const response = await fetch(`${apiBase}/${encodeURIComponent(slug)}`, {
                    method: 'DELETE',
                    headers: {
                        'X-WP-Nonce': nonce
                    }
                });

                if (response.ok) {
                    log(`Тема "${slug}" удалена`, 'success');
                    await loadThemes();
                } else {
                    const errorText = await response.text();
                    log(`Ошибка удаления темы "${slug}": ${errorText}`, 'error');
                }
            } catch (error) {
                log(`Исключение при удалении темы "${slug}": ${error.message}`, 'error');
            }
        }

        async function cleanupTestThemes() {
            log('Очистка тестовых тем...');
            
            for (const slug of testThemes) {
                try {
                    await deleteTheme(slug);
                } catch (error) {
                    log(`Ошибка удаления тестовой темы "${slug}": ${error.message}`, 'error');
                }
            }
            
            testThemes = [];
            log('Очистка завершена', 'success');
        }

        // Инициализация
        document.addEventListener('DOMContentLoaded', () => {
            log('Тест исправления slug\'ов тем загружен', 'success');
            loadThemes();
        });
    </script>
</body>
</html>
