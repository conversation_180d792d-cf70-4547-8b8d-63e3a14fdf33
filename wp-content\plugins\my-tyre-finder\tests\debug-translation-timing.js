/**
 * Debug script for translation timing issues
 * Add this to the admin appearance page to debug translation loading
 */

(function() {
    'use strict';

    console.log('[Translation Debug] Starting translation timing debug');

    // Monitor global translation object changes
    let lastTranslationCount = 0;
    let translationCheckInterval;

    function monitorTranslations() {
        const currentCount = window.WheelFitI18n ? Object.keys(window.WheelFitI18n).length : 0;
        
        if (currentCount !== lastTranslationCount) {
            console.log(`[Translation Debug] Translation count changed: ${lastTranslationCount} -> ${currentCount}`);
            console.log('[Translation Debug] Current translations:', window.WheelFitI18n);
            lastTranslationCount = currentCount;
            
            // Check if translation manager is available and working
            if (window.translationManager) {
                console.log('[Translation Debug] Translation manager status:', window.translationManager.healthCheck());
            }
        }
    }

    // Start monitoring
    translationCheckInterval = setInterval(monitorTranslations, 1000);

    // Monitor DOM changes in preview container
    const previewContainer = document.getElementById('widget-preview');
    if (previewContainer) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.target === previewContainer) {
                    console.log('[Translation Debug] Preview container content changed');
                    console.log('[Translation Debug] Current translations available:', !!window.WheelFitI18n);
                    console.log('[Translation Debug] Translation count:', window.WheelFitI18n ? Object.keys(window.WheelFitI18n).length : 0);
                    
                    // Check for translation elements
                    const i18nElements = previewContainer.querySelectorAll('[data-i18n]');
                    const placeholderElements = previewContainer.querySelectorAll('[data-i18n-placeholder]');
                    console.log(`[Translation Debug] Found ${i18nElements.length} text elements and ${placeholderElements.length} placeholder elements`);
                    
                    // Sample a few elements to see their current state
                    if (i18nElements.length > 0) {
                        const sample = Array.from(i18nElements).slice(0, 3).map(el => ({
                            key: el.dataset.i18n,
                            text: el.textContent.trim(),
                            expected: window.WheelFitI18n ? window.WheelFitI18n[el.dataset.i18n] : 'N/A'
                        }));
                        console.log('[Translation Debug] Sample elements:', sample);
                    }
                }
            });
        });

        observer.observe(previewContainer, {
            childList: true,
            subtree: false
        });

        console.log('[Translation Debug] Preview container observer set up');
    }

    // Monitor AJAX requests
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && url.includes('wheel_size_render_preview')) {
            console.log('[Translation Debug] Preview AJAX request detected');
        }
        
        return originalFetch.apply(this, args).then(response => {
            if (typeof url === 'string' && url.includes('wheel_size_render_preview')) {
                console.log('[Translation Debug] Preview AJAX response received');
                
                // Clone response to read it without consuming it
                const clonedResponse = response.clone();
                clonedResponse.json().then(data => {
                    if (data.success && data.data) {
                        console.log('[Translation Debug] AJAX response data:', {
                            hasHtml: !!data.data.html,
                            hasI18n: !!data.data.i18n,
                            i18nCount: data.data.i18n ? Object.keys(data.data.i18n).length : 0,
                            locale: data.data.locale,
                            timestamp: data.data.timestamp
                        });
                        
                        if (data.data.i18n) {
                            console.log('[Translation Debug] Received translations:', data.data.i18n);
                        }
                    }
                }).catch(e => {
                    console.log('[Translation Debug] Could not parse AJAX response as JSON');
                });
            }
            
            return response;
        });
    };

    // Listen for custom events
    document.addEventListener('previewUpdated', (event) => {
        console.log('[Translation Debug] previewUpdated event received:', event.detail);
    });

    // Global debug functions
    window.debugTranslationTiming = {
        checkTranslations: function() {
            console.group('[Translation Debug] Manual Check');
            console.log('Global translations:', window.WheelFitI18n);
            console.log('Translation manager:', window.translationManager);
            
            if (window.translationManager) {
                console.log('Manager health:', window.translationManager.healthCheck());
                if (typeof window.translationManager.debug === 'function') {
                    window.translationManager.debug();
                }
            }
            
            const previewContainer = document.getElementById('widget-preview');
            if (previewContainer) {
                const i18nElements = previewContainer.querySelectorAll('[data-i18n]');
                console.log(`Found ${i18nElements.length} translatable elements`);
                
                if (i18nElements.length > 0 && window.WheelFitI18n) {
                    const results = Array.from(i18nElements).map(el => {
                        const key = el.dataset.i18n;
                        const current = el.textContent.trim();
                        const expected = window.WheelFitI18n[key];
                        return {
                            key,
                            current,
                            expected,
                            correct: current === expected
                        };
                    });
                    
                    console.table(results);
                    
                    const correct = results.filter(r => r.correct).length;
                    const total = results.length;
                    console.log(`Translation accuracy: ${correct}/${total} (${((correct/total)*100).toFixed(1)}%)`);
                }
            }
            
            console.groupEnd();
        },

        forceApplyTranslations: function() {
            console.log('[Translation Debug] Forcing translation application');
            if (window.translationManager && typeof window.translationManager.applyTranslations === 'function') {
                const result = window.translationManager.applyTranslations();
                console.log('[Translation Debug] Force apply result:', result);
            } else if (typeof window.applyStaticTranslations === 'function') {
                const result = window.applyStaticTranslations();
                console.log('[Translation Debug] Fallback apply result:', result);
            } else {
                console.log('[Translation Debug] No translation application method available');
            }
        },

        simulateUpdate: function() {
            console.log('[Translation Debug] Simulating preview update');
            const previewContainer = document.getElementById('widget-preview');
            if (previewContainer) {
                // Trigger the same sequence as the real update
                document.dispatchEvent(new CustomEvent('previewUpdated', {
                    detail: { 
                        container: previewContainer, 
                        translations: window.WheelFitI18n,
                        timestamp: Date.now()
                    }
                }));
            }
        },

        stopMonitoring: function() {
            if (translationCheckInterval) {
                clearInterval(translationCheckInterval);
                console.log('[Translation Debug] Stopped monitoring');
            }
        }
    };

    console.log('[Translation Debug] Debug tools available as window.debugTranslationTiming');
    console.log('[Translation Debug] Available methods: checkTranslations(), forceApplyTranslations(), simulateUpdate(), stopMonitoring()');

    // Initial check
    setTimeout(() => {
        window.debugTranslationTiming.checkTranslations();
    }, 2000);

})();
