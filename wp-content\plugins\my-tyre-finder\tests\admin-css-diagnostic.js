/**
 * Admin CSS Diagnostic Script
 * Run this in browser console on the Appearance admin page to diagnose CSS loading issues
 */

(function() {
    'use strict';

    console.log('🔍 === ADMIN CSS DIAGNOSTIC ===');

    // Check if CSS file is loaded
    function checkCssLoading() {
        console.log('\n1️⃣ Checking CSS File Loading...');
        
        const stylesheets = Array.from(document.styleSheets);
        const themePanel = stylesheets.find(sheet => {
            try {
                return sheet.href && sheet.href.includes('admin-theme-panel.css');
            } catch (e) {
                return false;
            }
        });

        if (themePanel) {
            console.log('✅ admin-theme-panel.css is loaded');
            console.log('📄 CSS URL:', themePanel.href);
            
            // Try to access CSS rules
            try {
                const rules = themePanel.cssRules || themePanel.rules;
                console.log('✅ CSS rules accessible, count:', rules.length);
                
                // Check for specific theme panel rules
                const themePanelRule = Array.from(rules).find(rule => 
                    rule.selectorText && rule.selectorText.includes('.wsf-theme-panel')
                );
                
                if (themePanelRule) {
                    console.log('✅ Theme panel CSS rules found');
                    console.log('🎨 Sample rule:', themePanelRule.cssText.substring(0, 100) + '...');
                } else {
                    console.warn('⚠️ No theme panel CSS rules found in stylesheet');
                }
            } catch (e) {
                console.warn('⚠️ Cannot access CSS rules (CORS issue):', e.message);
            }
        } else {
            console.error('❌ admin-theme-panel.css is NOT loaded');
            console.log('📋 Available stylesheets:');
            stylesheets.forEach((sheet, idx) => {
                try {
                    console.log(`   ${idx + 1}. ${sheet.href || 'inline styles'}`);
                } catch (e) {
                    console.log(`   ${idx + 1}. [Cannot access href]`);
                }
            });
        }
    }

    // Check DOM elements
    function checkDomElements() {
        console.log('\n2️⃣ Checking DOM Elements...');
        
        const themePanel = document.querySelector('.wsf-theme-panel');
        if (themePanel) {
            console.log('✅ Theme panel element found');
            
            // Check computed styles
            const computedStyle = getComputedStyle(themePanel);
            console.log('🎨 Computed styles:');
            console.log('   Background:', computedStyle.backgroundColor);
            console.log('   Border:', computedStyle.border);
            console.log('   Display:', computedStyle.display);
            console.log('   Position:', computedStyle.position);
            
            // Check if styles are being applied
            if (computedStyle.backgroundColor === 'rgba(0, 0, 0, 0)' || 
                computedStyle.backgroundColor === 'transparent') {
                console.warn('⚠️ Background color not applied - CSS may not be loading');
            }
            
            // Check for CSS classes
            console.log('📝 CSS classes:', Array.from(themePanel.classList));
            
        } else {
            console.error('❌ Theme panel element (.wsf-theme-panel) not found');
            
            // Check for admin grid
            const adminGrid = document.querySelector('.wsf-admin-grid');
            if (adminGrid) {
                console.log('✅ Admin grid found');
            } else {
                console.error('❌ Admin grid (.wsf-admin-grid) not found');
            }
        }
    }

    // Check for CSS conflicts
    function checkCssConflicts() {
        console.log('\n3️⃣ Checking for CSS Conflicts...');
        
        const themePanel = document.querySelector('.wsf-theme-panel');
        if (!themePanel) return;

        // Check for conflicting styles
        const computedStyle = getComputedStyle(themePanel);
        const importantStyles = [];
        
        // Get all stylesheets and check for conflicting rules
        Array.from(document.styleSheets).forEach(sheet => {
            try {
                const rules = sheet.cssRules || sheet.rules;
                Array.from(rules).forEach(rule => {
                    if (rule.selectorText && rule.selectorText.includes('.wsf-theme-panel')) {
                        if (rule.style && rule.style.cssText.includes('!important')) {
                            importantStyles.push({
                                selector: rule.selectorText,
                                style: rule.style.cssText,
                                sheet: sheet.href || 'inline'
                            });
                        }
                    }
                });
            } catch (e) {
                // CORS or other access issues
            }
        });

        if (importantStyles.length > 0) {
            console.warn('⚠️ Found !important styles that might conflict:');
            importantStyles.forEach(style => {
                console.log(`   ${style.selector} in ${style.sheet}`);
            });
        } else {
            console.log('✅ No obvious CSS conflicts found');
        }
    }

    // Check WordPress admin styles
    function checkWordPressStyles() {
        console.log('\n4️⃣ Checking WordPress Admin Styles...');
        
        // Check for common WordPress admin CSS
        const wpAdminStyles = [
            'wp-admin',
            'common',
            'forms',
            'admin-menu',
            'dashboard'
        ];

        wpAdminStyles.forEach(styleName => {
            const found = Array.from(document.styleSheets).some(sheet => {
                try {
                    return sheet.href && sheet.href.includes(styleName);
                } catch (e) {
                    return false;
                }
            });
            
            if (found) {
                console.log(`✅ ${styleName} styles loaded`);
            } else {
                console.log(`⚠️ ${styleName} styles not found`);
            }
        });
    }

    // Check file timestamps and caching
    function checkCaching() {
        console.log('\n5️⃣ Checking Caching Issues...');
        
        const stylesheets = Array.from(document.styleSheets);
        const themePanel = stylesheets.find(sheet => {
            try {
                return sheet.href && sheet.href.includes('admin-theme-panel.css');
            } catch (e) {
                return false;
            }
        });

        if (themePanel && themePanel.href) {
            const url = new URL(themePanel.href);
            const version = url.searchParams.get('ver');
            
            if (version) {
                console.log('✅ CSS file has version parameter:', version);
                
                // Check if version looks like a timestamp
                if (/^\d{10}$/.test(version)) {
                    const date = new Date(parseInt(version) * 1000);
                    console.log('📅 File timestamp:', date.toLocaleString());
                } else {
                    console.log('📝 Version string:', version);
                }
            } else {
                console.warn('⚠️ No version parameter - caching might be an issue');
            }
        }
    }

    // Test CSS rule application
    function testCssApplication() {
        console.log('\n6️⃣ Testing CSS Rule Application...');
        
        const themePanel = document.querySelector('.wsf-theme-panel');
        if (!themePanel) return;

        // Test applying a style directly
        const originalBorder = themePanel.style.border;
        themePanel.style.border = '3px solid red';
        
        setTimeout(() => {
            const computedStyle = getComputedStyle(themePanel);
            if (computedStyle.borderColor.includes('red')) {
                console.log('✅ Direct style application works');
            } else {
                console.warn('⚠️ Direct style application failed');
            }
            
            // Restore original
            themePanel.style.border = originalBorder;
        }, 100);
    }

    // Generate report
    function generateReport() {
        console.log('\n📋 === DIAGNOSTIC SUMMARY ===');
        console.log('1. Check browser Network tab for 404 errors on CSS files');
        console.log('2. Verify file permissions on admin-theme-panel.css');
        console.log('3. Clear browser cache and WordPress cache');
        console.log('4. Check if other plugins are interfering');
        console.log('5. Verify WordPress admin is loading properly');
        
        console.log('\n💡 Quick Fixes to Try:');
        console.log('• Hard refresh: Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)');
        console.log('• Clear browser cache completely');
        console.log('• Disable other plugins temporarily');
        console.log('• Check file permissions (should be 644)');
        console.log('• Verify plugin path is correct');
    }

    // Run all diagnostics
    function runDiagnostic() {
        checkCssLoading();
        checkDomElements();
        checkCssConflicts();
        checkWordPressStyles();
        checkCaching();
        testCssApplication();
        generateReport();
    }

    // Export to window for manual access
    window.adminCssDiagnostic = {
        run: runDiagnostic,
        individual: {
            checkCssLoading,
            checkDomElements,
            checkCssConflicts,
            checkWordPressStyles,
            checkCaching,
            testCssApplication
        }
    };

    // Auto-run diagnostic
    runDiagnostic();

})();
