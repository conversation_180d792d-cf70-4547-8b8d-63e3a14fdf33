<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generation Translations Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-header {
            background: #059669;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
        }
        .status-good { color: #059669; }
        .status-bad { color: #dc2626; }
        .status-warning { color: #d97706; }
        .button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #1d4ed8;
        }
        .button.success {
            background: #059669;
        }
        .button.success:hover {
            background: #047857;
        }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .translation-demo {
            background: #f3f4f6;
            padding: 20px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .translation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .translation-item:last-child {
            border-bottom: none;
        }
        .translation-key {
            font-family: monospace;
            background: #e5e7eb;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        .translation-value {
            font-weight: bold;
        }
        .language-selector {
            margin: 10px 0;
        }
        .language-selector select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #d1d5db;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🌍 Generation Field Translations Test</h1>
        <p>This page tests the Russian and other language translations for Generation field strings in the my-tyre-finder plugin.</p>
    </div>

    <div class="instructions">
        <h3>📋 Test Instructions</h3>
        <ol>
            <li><strong>Load this page</strong> on a WordPress page with the my-tyre-finder plugin active</li>
            <li><strong>Select a language</strong> using the dropdown below</li>
            <li><strong>Run the translation test</strong> to verify all generation strings are translated</li>
            <li><strong>Check the demo section</strong> to see how translations should appear</li>
        </ol>
    </div>

    <div class="test-container">
        <div class="test-section">
            <h3>🌍 Language Selection</h3>
            <div class="language-selector">
                <label for="language-select">Select Language:</label>
                <select id="language-select" onchange="changeLanguage()">
                    <option value="en">English</option>
                    <option value="ru">Русский (Russian)</option>
                    <option value="de">Deutsch (German)</option>
                    <option value="fr">Français (French)</option>
                    <option value="es">Español (Spanish)</option>
                </select>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Translation Tests</h3>
            <button class="button success" onclick="runTranslationTest()">Run Translation Test</button>
            <button class="button" onclick="testSpecificLanguage('ru')">Test Russian Specifically</button>
            <button class="button" onclick="testAllLanguages()">Test All Languages</button>
            <button class="button" onclick="clearConsole()">Clear Console</button>
        </div>

        <div class="test-section">
            <h3>📊 Translation Demo</h3>
            <div id="translation-demo" class="translation-demo">
                <h4>Generation Field Translations:</h4>
                <div id="demo-translations">
                    <div class="translation-item">
                        <span class="translation-key">label_generation</span>
                        <span class="translation-value" id="demo-label">Loading...</span>
                    </div>
                    <div class="translation-item">
                        <span class="translation-key">select_gen_placeholder</span>
                        <span class="translation-value" id="demo-placeholder">Loading...</span>
                    </div>
                    <div class="translation-item">
                        <span class="translation-key">select_gen_first_placeholder</span>
                        <span class="translation-value" id="demo-first">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Test Status</h3>
            <div id="test-status">
                <p>Click "Run Translation Test" to see current status...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🖥️ Console Output</h3>
            <div id="console-output" class="console-output">
                Console output will appear here...
            </div>
        </div>
    </div>

    <script>
        // Mock translation data for testing
        const MOCK_TRANSLATIONS = {
            en: {
                'label_generation': 'Generation',
                'select_gen_placeholder': 'Choose a generation',
                'select_gen_first_placeholder': 'Select generation first'
            },
            ru: {
                'label_generation': 'Поколение',
                'select_gen_placeholder': 'Выберите поколение',
                'select_gen_first_placeholder': 'Сначала выберите поколение'
            },
            de: {
                'label_generation': 'Generation',
                'select_gen_placeholder': 'Wähle eine Generation',
                'select_gen_first_placeholder': 'Bitte zuerst Generation wählen'
            },
            fr: {
                'label_generation': 'Génération',
                'select_gen_placeholder': 'Choisissez une génération',
                'select_gen_first_placeholder': 'Sélectionnez d\'abord la génération'
            },
            es: {
                'label_generation': 'Generación',
                'select_gen_placeholder': 'Elige una generación',
                'select_gen_first_placeholder': 'Seleccione la generación primero'
            }
        };

        // Capture console output
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function logToPage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            logToPage(args.join(' '), 'log');
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            logToPage(args.join(' '), 'error');
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            logToPage(args.join(' '), 'warn');
            originalWarn.apply(console, args);
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function changeLanguage() {
            const selectedLang = document.getElementById('language-select').value;
            console.log(`Language changed to: ${selectedLang}`);
            updateDemo(selectedLang);
        }

        function updateDemo(language) {
            const translations = MOCK_TRANSLATIONS[language] || MOCK_TRANSLATIONS.en;
            
            document.getElementById('demo-label').textContent = translations['label_generation'];
            document.getElementById('demo-placeholder').textContent = translations['select_gen_placeholder'];
            document.getElementById('demo-first').textContent = translations['select_gen_first_placeholder'];
        }

        function runTranslationTest() {
            console.log('Loading Russian generation translations test...');
            loadAndRunScript('test-russian-generation-translations.js');
        }

        function testSpecificLanguage(lang) {
            console.log(`=== Testing ${lang.toUpperCase()} Translations ===`);
            
            const translations = MOCK_TRANSLATIONS[lang];
            if (!translations) {
                console.error(`No translations found for language: ${lang}`);
                return;
            }
            
            Object.keys(translations).forEach(key => {
                const expected = translations[key];
                
                // Test with real translation function if available
                if (typeof window.t === 'function') {
                    const actual = window.t(key, 'FALLBACK');
                    const isCorrect = actual === expected;
                    console.log(`${isCorrect ? '✅' : '❌'} ${key}: Expected "${expected}", Got "${actual}"`);
                } else {
                    console.log(`📝 ${key}: Expected "${expected}" (translation function not available)`);
                }
            });
        }

        function testAllLanguages() {
            console.log('=== Testing All Language Translations ===');
            Object.keys(MOCK_TRANSLATIONS).forEach(lang => {
                console.log(`\n--- ${lang.toUpperCase()} ---`);
                testSpecificLanguage(lang);
            });
        }

        function loadAndRunScript(scriptName) {
            const script = document.createElement('script');
            script.src = scriptName;
            script.onload = function() {
                console.log(`✅ ${scriptName} loaded and executed`);
            };
            script.onerror = function() {
                console.error(`❌ Failed to load ${scriptName}`);
            };
            document.head.appendChild(script);
        }

        // Initialize page
        window.addEventListener('load', function() {
            console.log('🚀 Generation translations test page loaded');
            console.log('Ready to test generation field translations...');
            
            // Update demo with default language
            updateDemo('en');
            
            // Check if translation system is available
            if (typeof window.t === 'function') {
                console.log('✅ Translation function available');
            } else {
                console.log('⚠️ Translation function not available - using mock data');
            }
            
            if (window.WheelFitI18n) {
                console.log('✅ WheelFitI18n available');
                console.log('Available translation keys:', Object.keys(window.WheelFitI18n));
            } else {
                console.log('⚠️ WheelFitI18n not available');
            }
        });
    </script>
</body>
</html>
