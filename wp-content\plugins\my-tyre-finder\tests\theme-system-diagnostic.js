/**
 * Comprehensive Theme System Diagnostic Script
 * Run this in browser console on the Appearance admin page to diagnose theme issues
 */

(function() {
    'use strict';

    console.log('🔍 === THEME SYSTEM DIAGNOSTIC ===');
    console.log('Running comprehensive diagnostic of theme presets functionality...\n');

    const results = {
        featureFlag: false,
        assets: { css: false, js: false },
        apiEndpoints: false,
        themeData: false,
        domElements: false,
        integration: false,
        errors: []
    };

    // 1. Check Feature Flag
    function checkFeatureFlag() {
        console.log('1️⃣ Checking Feature Flag...');
        
        if (typeof window.WSF_THEME_PRESETS !== 'undefined' && window.WSF_THEME_PRESETS === true) {
            console.log('✅ WSF_THEME_PRESETS feature flag is enabled');
            results.featureFlag = true;
        } else {
            console.error('❌ WSF_THEME_PRESETS feature flag not found or disabled');
            results.errors.push('Feature flag WSF_THEME_PRESETS not enabled');
        }
    }

    // 2. Check Asset Loading
    function checkAssets() {
        console.log('\n2️⃣ Checking Asset Loading...');
        
        // Check CSS
        const cssLoaded = Array.from(document.styleSheets).some(sheet => {
            try {
                return sheet.href && sheet.href.includes('admin-theme-panel.css');
            } catch (e) {
                return false;
            }
        });
        
        if (cssLoaded) {
            console.log('✅ Theme panel CSS loaded');
            results.assets.css = true;
        } else {
            console.warn('⚠️ Theme panel CSS not found');
            results.errors.push('admin-theme-panel.css not loaded');
        }

        // Check JavaScript
        const scripts = Array.from(document.scripts);
        const jsLoaded = scripts.some(script => 
            script.src && script.src.includes('admin-theme-panel.js')
        );
        
        if (jsLoaded) {
            console.log('✅ Theme panel JavaScript loaded');
            results.assets.js = true;
        } else {
            console.warn('⚠️ Theme panel JavaScript not found');
            results.errors.push('admin-theme-panel.js not loaded');
        }

        // Check dependencies
        if (typeof jQuery !== 'undefined') {
            console.log('✅ jQuery dependency available');
        } else {
            console.error('❌ jQuery dependency missing');
            results.errors.push('jQuery dependency missing');
        }

        if (typeof wpApiSettings !== 'undefined') {
            console.log('✅ wpApiSettings available');
        } else {
            console.error('❌ wpApiSettings missing');
            results.errors.push('wpApiSettings not available');
        }
    }

    // 3. Check API Endpoints
    async function checkApiEndpoints() {
        console.log('\n3️⃣ Checking API Endpoints...');
        
        if (typeof wpApiSettings === 'undefined') {
            console.error('❌ Cannot test API - wpApiSettings not available');
            results.errors.push('wpApiSettings required for API testing');
            return;
        }

        try {
            const apiBase = wpApiSettings.root + 'wheel-size/v1/themes';
            const response = await fetch(apiBase, {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Theme API endpoints accessible');
                console.log('📊 API Response:', data);
                results.apiEndpoints = true;
                
                if (data.themes && Object.keys(data.themes).length > 0) {
                    console.log('✅ Default themes available:', Object.keys(data.themes));
                    results.themeData = true;
                } else {
                    console.warn('⚠️ No themes found in API response');
                    results.errors.push('No themes available from API');
                }
            } else {
                console.error('❌ API endpoint returned error:', response.status, response.statusText);
                results.errors.push(`API endpoint error: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ API endpoint test failed:', error);
            results.errors.push(`API test failed: ${error.message}`);
        }
    }

    // 4. Check DOM Elements
    function checkDomElements() {
        console.log('\n4️⃣ Checking DOM Elements...');
        
        // Check theme panel
        const themePanel = document.querySelector('.wsf-theme-panel');
        if (themePanel) {
            console.log('✅ Theme panel container found');
            
            const panelContent = themePanel.querySelector('.wsf-theme-panel__content');
            if (panelContent) {
                console.log('✅ Theme panel content area found');
            } else {
                console.warn('⚠️ Theme panel content area missing');
                results.errors.push('Theme panel content area not found');
            }
        } else {
            console.error('❌ Theme panel container not found');
            results.errors.push('Theme panel DOM element missing');
        }

        // Check preview container
        const previewContainer = document.getElementById('widget-preview');
        if (previewContainer) {
            console.log('✅ Preview container found');

            const widget = previewContainer.querySelector('.wheel-fit-widget, .wsf-finder-widget, [data-wsf-theme]');
            if (widget) {
                console.log('✅ Widget element found in preview');
                console.log('🎨 Current theme attribute:', widget.getAttribute('data-wsf-theme') || 'none');

                // Check theme classes
                const themeClasses = Array.from(widget.classList).filter(cls => cls.startsWith('wsf-theme-'));
                if (themeClasses.length > 0) {
                    console.log('✅ Theme classes found:', themeClasses);
                } else {
                    console.warn('⚠️ No theme classes found on widget');
                    results.errors.push('No theme classes found on widget element');
                }

                // Check if wsf-finder-widget class is present
                if (widget.classList.contains('wsf-finder-widget')) {
                    console.log('✅ wsf-finder-widget class found');
                } else {
                    console.warn('⚠️ wsf-finder-widget class missing');
                    results.errors.push('wsf-finder-widget class missing from widget element');
                }

                // Check CSS custom properties
                const computedStyle = getComputedStyle(widget);
                const primaryColor = computedStyle.getPropertyValue('--wsf-primary').trim();
                if (primaryColor) {
                    console.log('✅ CSS custom properties working, --wsf-primary:', primaryColor);
                    results.domElements = true;
                } else {
                    console.warn('⚠️ CSS custom properties not found or not working');
                    results.errors.push('CSS custom properties not working on widget element');
                }
            } else {
                console.warn('⚠️ Widget element not found in preview');
                results.errors.push('Widget element not found in preview container');
            }
        } else {
            console.error('❌ Preview container not found');
            results.errors.push('Preview container #widget-preview not found');
        }

        // Check active theme data
        if (typeof window.wsfActiveTheme !== 'undefined') {
            console.log('✅ Active theme data available:', window.wsfActiveTheme);
        } else {
            console.warn('⚠️ Active theme data not available');
            results.errors.push('window.wsfActiveTheme not set');
        }
    }

    // 5. Check Integration
    function checkIntegration() {
        console.log('\n5️⃣ Checking Integration...');
        
        // Check if ThemePresetsPanel class is available
        if (typeof window.ThemePresetsPanel !== 'undefined' || 
            (typeof jQuery !== 'undefined' && jQuery.fn.themePresetsPanel)) {
            console.log('✅ Theme panel JavaScript class available');
            results.integration = true;
        } else {
            console.warn('⚠️ Theme panel JavaScript class not found');
            results.errors.push('ThemePresetsPanel class not initialized');
        }

        // Test theme application function
        const previewContainer = document.getElementById('widget-preview');
        if (previewContainer) {
            const widget = previewContainer.querySelector('.wheel-fit-widget, .wsf-finder-widget, [data-wsf-theme]');
            if (widget) {
                // Test setting a CSS custom property
                widget.style.setProperty('--test-property', '#ff0000');
                const testValue = getComputedStyle(widget).getPropertyValue('--test-property');
                if (testValue.trim() === '#ff0000') {
                    console.log('✅ CSS custom property application works');
                    widget.style.removeProperty('--test-property'); // Clean up
                } else {
                    console.warn('⚠️ CSS custom property application may not work');
                    results.errors.push('CSS custom property application failed');
                }
            }
        }
    }

    // 6. Generate Report
    function generateReport() {
        console.log('\n📋 === DIAGNOSTIC REPORT ===');
        
        const totalChecks = Object.keys(results).length - 1; // Exclude errors array
        const passedChecks = Object.values(results).filter((val, idx) => 
            idx < totalChecks && val === true
        ).length;
        
        console.log(`📊 Overall Status: ${passedChecks}/${totalChecks} checks passed`);
        
        if (results.errors.length === 0) {
            console.log('🎉 No critical errors found! Theme system should be working.');
        } else {
            console.log('⚠️ Issues found:');
            results.errors.forEach((error, idx) => {
                console.log(`   ${idx + 1}. ${error}`);
            });
        }

        console.log('\n🔧 Detailed Results:');
        console.log('   Feature Flag:', results.featureFlag ? '✅' : '❌');
        console.log('   CSS Assets:', results.assets.css ? '✅' : '❌');
        console.log('   JS Assets:', results.assets.js ? '✅' : '❌');
        console.log('   API Endpoints:', results.apiEndpoints ? '✅' : '❌');
        console.log('   Theme Data:', results.themeData ? '✅' : '❌');
        console.log('   DOM Elements:', results.domElements ? '✅' : '❌');
        console.log('   Integration:', results.integration ? '✅' : '❌');

        return results;
    }

    // Run diagnostic
    async function runDiagnostic() {
        checkFeatureFlag();
        checkAssets();
        await checkApiEndpoints();
        checkDomElements();
        checkIntegration();
        
        const report = generateReport();
        
        console.log('\n💡 Next Steps:');
        if (results.errors.length > 0) {
            console.log('1. Address the issues listed above');
            console.log('2. Refresh the page and run diagnostic again');
            console.log('3. Check browser console for additional errors');
        } else {
            console.log('1. Try creating a new theme via the theme panel');
            console.log('2. Test switching between themes');
            console.log('3. Verify live preview updates work');
        }
        
        return report;
    }

    // Export to window for manual access
    window.themeSystemDiagnostic = {
        run: runDiagnostic,
        results: results
    };

    // Auto-run diagnostic
    runDiagnostic();

})();
