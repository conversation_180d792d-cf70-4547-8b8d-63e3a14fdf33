<?php
/**
 * Test Color Tokens Output
 * 
 * This file tests the ColorTokens class output to verify that input tokens are properly defined.
 * Run this by accessing it directly or including it in a WordPress context.
 */

// Include WordPress if not already loaded
if (!defined('ABSPATH')) {
    // Try to find WordPress
    $wp_load_paths = [
        __DIR__ . '/../../../../wp-load.php',
        __DIR__ . '/../../../wp-load.php', 
        __DIR__ . '/../../wp-load.php',
        __DIR__ . '/../wp-load.php'
    ];
    
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            break;
        }
    }
    
    if (!defined('ABSPATH')) {
        die('WordPress not found. Please run this file from within WordPress context.');
    }
}

// Include the ColorTokens class
require_once plugin_dir_path(__FILE__) . '../src/includes/ColorTokens.php';

use MyTyreFinder\Includes\ColorTokens;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Color Tokens Test Output</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .token { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px; border-left: 4px solid #007cba; }
        .property { font-family: monospace; color: #7c3aed; font-weight: bold; }
        .label { color: #059669; font-weight: 600; margin: 5px 0; }
        .help { color: #6b7280; font-size: 14px; margin: 5px 0; }
        .order { color: #dc2626; font-size: 12px; }
        .error { background: #fef2f2; border-color: #fecaca; color: #dc2626; }
        .success { background: #f0fdf4; border-color: #bbf7d0; color: #059669; }
        .warning { background: #fffbeb; border-color: #fbbf24; color: #d97706; }
        pre { background: #1e293b; color: #f1f5f9; padding: 15px; border-radius: 6px; overflow-x: auto; }
        .input-token { border-left-color: #3b82f6; }
        .new-token { border-left-color: #10b981; background: #ecfdf5; }
    </style>
</head>
<body>
    <h1>🎨 Color Tokens Test Output</h1>
    <p>Тестирование вывода ColorTokens класса для проверки input-токенов.</p>
    
    <?php
    try {
        // Test 1: Get all tokens
        echo '<div class="section">';
        echo '<h2>1. Все токены из ColorTokens::get_tokens()</h2>';
        $allTokens = ColorTokens::get_tokens();
        
        if (empty($allTokens)) {
            echo '<div class="error">❌ Токены не найдены!</div>';
        } else {
            echo '<div class="success">✅ Найдено ' . count($allTokens) . ' токенов</div>';
            
            foreach ($allTokens as $key => $token) {
                $isInputToken = strpos($token['property'] ?? '', 'input') !== false || $token['property'] === '--wsf-surface';
                $tokenClass = $isInputToken ? 'token input-token' : 'token';
                if ($isInputToken) $tokenClass .= ' new-token';
                
                echo '<div class="' . $tokenClass . '">';
                echo '<div class="property">' . ($token['property'] ?? 'NO PROPERTY') . '</div>';
                echo '<div class="label">' . ($token['label'] ?? 'NO LABEL') . '</div>';
                echo '<div class="help">' . ($token['help'] ?? 'NO HELP') . '</div>';
                echo '<div class="order">Order: ' . ($token['order'] ?? 'NO ORDER') . '</div>';
                echo '</div>';
            }
        }
        echo '</div>';
        
        // Test 2: Get tokens for JS
        echo '<div class="section">';
        echo '<h2>2. Токены для JavaScript из ColorTokens::get_tokens_for_js()</h2>';
        $jsTokens = ColorTokens::get_tokens_for_js();
        
        if (empty($jsTokens)) {
            echo '<div class="error">❌ JS токены не найдены!</div>';
        } else {
            echo '<div class="success">✅ Найдено ' . count($jsTokens) . ' JS токенов</div>';
            
            // Check for specific input tokens
            $inputTokens = [
                '--wsf-surface' => 'Surface',
                '--wsf-input-bg' => 'Input Background',
                '--wsf-input-text' => 'Input Text',
                '--wsf-input-border' => 'Input Border',
                '--wsf-input-placeholder' => 'Input Placeholder',
                '--wsf-input-focus' => 'Input Focus'
            ];
            
            echo '<h3>Проверка input-токенов:</h3>';
            foreach ($inputTokens as $property => $expectedLabel) {
                if (isset($jsTokens[$property])) {
                    echo '<div class="success">✅ ' . $property . ' - "' . $jsTokens[$property]['label'] . '"</div>';
                } else {
                    echo '<div class="error">❌ ' . $property . ' - Отсутствует!</div>';
                }
            }
            
            echo '<h3>Все JS токены:</h3>';
            foreach ($jsTokens as $property => $tokenData) {
                $isInputToken = strpos($property, 'input') !== false || $property === '--wsf-surface';
                $tokenClass = $isInputToken ? 'token input-token new-token' : 'token';
                
                echo '<div class="' . $tokenClass . '">';
                echo '<div class="property">' . $property . '</div>';
                echo '<div class="label">' . ($tokenData['label'] ?? 'NO LABEL') . '</div>';
                echo '<div class="help">' . ($tokenData['help'] ?? 'NO HELP') . '</div>';
                echo '<div class="order">Order: ' . ($tokenData['order'] ?? 'NO ORDER') . '</div>';
                echo '</div>';
            }
        }
        echo '</div>';
        
        // Test 3: Get examples
        echo '<div class="section">';
        echo '<h2>3. Примеры из ColorTokens::get_examples()</h2>';
        $examples = ColorTokens::get_examples();
        
        if (empty($examples)) {
            echo '<div class="error">❌ Примеры не найдены!</div>';
        } else {
            echo '<div class="success">✅ Найдено ' . count($examples) . ' примеров</div>';
            
            foreach ($examples as $key => $example) {
                $isInputExample = strpos($key, 'input') !== false || $key === 'surface';
                $exampleClass = $isInputExample ? 'token input-token new-token' : 'token';
                
                echo '<div class="' . $exampleClass . '">';
                echo '<div class="property">' . $key . '</div>';
                echo '<div class="label">' . $example . '</div>';
                echo '</div>';
            }
        }
        echo '</div>';
        
        // Test 4: JSON output for wp_localize_script
        echo '<div class="section">';
        echo '<h2>4. JSON для wp_localize_script</h2>';
        $localizationData = [
            'tokens' => $jsTokens,
            'examples' => $examples
        ];
        
        echo '<p>Это данные, которые передаются в JavaScript через wp_localize_script:</p>';
        echo '<pre>' . json_encode($localizationData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        echo '</div>';
        
        // Test 5: Summary
        echo '<div class="section">';
        echo '<h2>5. Итоговая проверка</h2>';
        
        $inputTokensFound = 0;
        foreach ($inputTokens as $property => $expectedLabel) {
            if (isset($jsTokens[$property])) {
                $inputTokensFound++;
            }
        }
        
        if ($inputTokensFound === count($inputTokens)) {
            echo '<div class="success"><strong>🎉 Все input-токены найдены и готовы к использованию!</strong></div>';
            echo '<div class="success">Теперь они должны появиться в WordPress admin Theme Presets.</div>';
        } else {
            echo '<div class="error"><strong>⚠️ Найдено только ' . $inputTokensFound . ' из ' . count($inputTokens) . ' input-токенов.</strong></div>';
            echo '<div class="warning">Проверьте ColorTokens.php и убедитесь, что все токены правильно определены.</div>';
        }
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">❌ Ошибка: ' . $e->getMessage() . '</div>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
    ?>
    
    <div class="section">
        <h2>6. Следующие шаги</h2>
        <ol>
            <li><strong>Очистите кэш WordPress</strong> (если используется кэширование)</li>
            <li><strong>Перезагрузите админку</strong> WordPress</li>
            <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
            <li>Нажмите <strong>"Add New Theme"</strong> в Theme Presets панели</li>
            <li>Проверьте, что появились новые поля для input-токенов</li>
            <li>Если поля не появились, проверьте консоль браузера на ошибки JavaScript</li>
        </ol>
    </div>
</body>
</html>
