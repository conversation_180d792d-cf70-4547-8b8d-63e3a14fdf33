<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simplified Input Tokens Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .token-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .token-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
        }
        
        .token-item.new {
            border-left: 4px solid #10b981;
            background: #ecfdf5;
        }
        
        .token-property {
            font-family: monospace;
            font-size: 12px;
            color: #7c3aed;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .token-label {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .token-help {
            font-size: 12px;
            color: #64748b;
            font-style: italic;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .before-after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .demo-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .demo-label {
            color: var(--wsf-text, #1f2937);
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        
        .demo-select {
            width: 100%;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid var(--wsf-input-border, #e5e7eb);
            background: var(--wsf-input-bg, #ffffff);
            color: var(--wsf-input-text, #1f2937);
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .demo-select:focus {
            outline: none;
            border-color: var(--wsf-primary, #2563eb);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .status-pass {
            color: #059669;
            font-weight: bold;
        }
        
        .status-fail {
            color: #dc2626;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Упрощенные Input Tokens</h1>
        <p>Тест проверяет упрощенную систему токенов для настройки селекторов (убраны Input Placeholder и Input Focus).</p>
        
        <!-- Simplified Token List -->
        <div class="test-section">
            <h3>1. Упрощенный список токенов</h3>
            <p>Теперь в Theme Presets должны быть только эти 4 новых поля:</p>
            
            <div class="token-list">
                <div class="token-item new">
                    <div class="token-property">--wsf-surface</div>
                    <div class="token-label">Surface</div>
                    <div class="token-help">Background for cards, inputs & form elements</div>
                </div>
                
                <div class="token-item new">
                    <div class="token-property">--wsf-input-bg</div>
                    <div class="token-label">Input Background</div>
                    <div class="token-help">Background color for select elements & inputs</div>
                </div>
                
                <div class="token-item new">
                    <div class="token-property">--wsf-input-text</div>
                    <div class="token-label">Input Text</div>
                    <div class="token-help">Text color inside select elements & inputs</div>
                </div>
                
                <div class="token-item new">
                    <div class="token-property">--wsf-input-border</div>
                    <div class="token-label">Input Border</div>
                    <div class="token-help">Border color for select elements & inputs</div>
                </div>
            </div>
        </div>
        
        <!-- Before/After Comparison -->
        <div class="test-section">
            <h3>2. До и После упрощения</h3>
            <div class="comparison-grid">
                <div class="before">
                    <h4>❌ ДО: Слишком много полей</h4>
                    <ul>
                        <li>Surface</li>
                        <li>Input Background</li>
                        <li>Input Text</li>
                        <li>Input Border</li>
                        <li><del>Input Placeholder</del> (убрано)</li>
                        <li><del>Input Focus</del> (убрано)</li>
                    </ul>
                    <p><strong>Проблема:</strong> Слишком сложно для пользователей</p>
                </div>
                
                <div class="after">
                    <h4>✅ ПОСЛЕ: Только основное</h4>
                    <ul>
                        <li>Surface</li>
                        <li>Input Background</li>
                        <li>Input Text</li>
                        <li>Input Border</li>
                    </ul>
                    <p><strong>Результат:</strong> Простая и понятная настройка</p>
                </div>
            </div>
        </div>
        
        <!-- Demo Widget -->
        <div class="test-section">
            <h3>3. Демонстрация виджета</h3>
            <p>Основные элементы, которые можно настраивать:</p>
            
            <div class="demo-widget" id="demo-widget">
                <label class="demo-label">Make (использует --wsf-text)</label>
                <select class="demo-select">
                    <option value="">Select a make...</option>
                    <option value="audi">Audi (использует --wsf-input-text)</option>
                    <option value="bmw">BMW</option>
                    <option value="mercedes">Mercedes-Benz</option>
                </select>
                
                <label class="demo-label">Model (использует --wsf-text)</label>
                <select class="demo-select">
                    <option value="">Select make first</option>
                </select>
            </div>
        </div>
        
        <!-- Fixed Issues -->
        <div class="test-section">
            <h3>4. Исправленные проблемы</h3>
            
            <h4>✅ Исправлена ошибка "Failed to save theme"</h4>
            <p>Добавлены недостающие токены в <code>allowed_properties</code> в ThemeManager.php:</p>
            <ul>
                <li><code>--wsf-input-text</code></li>
                <li><code>--wsf-input-border</code></li>
            </ul>
            
            <h4>✅ Упрощена система токенов</h4>
            <p>Убраны сложные для понимания поля:</p>
            <ul>
                <li><del>Input Placeholder</del> - теперь использует --wsf-muted</li>
                <li><del>Input Focus</del> - теперь использует --wsf-primary</li>
            </ul>
            
            <h4>✅ Обновлены все файлы</h4>
            <ul>
                <li>ColorTokens.php - убраны лишние токены</li>
                <li>ThemeManager.php - добавлены недостающие токены в allowed_properties</li>
                <li>admin-theme-panel.js - обновлены fallback labels и createNewTheme</li>
            </ul>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить исправления</h4>
            <ol>
                <li><strong>Очистите кэш</strong> WordPress и браузера</li>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>Нажмите <strong>"Add New Theme"</strong> в Theme Presets панели</li>
                <li>Убедитесь, что появились <strong>4 новых поля</strong>:
                    <ul>
                        <li>Surface</li>
                        <li>Input Background</li>
                        <li>Input Text</li>
                        <li>Input Border</li>
                    </ul>
                </li>
                <li>Попробуйте <strong>изменить цвета</strong> и сохранить тему</li>
                <li>Проверьте, что <strong>ошибка "Failed to save theme" исчезла</strong></li>
                <li>Убедитесь, что цвета применяются к селекторам на сайте</li>
            </ol>
            
            <h4>🎯 Ожидаемый результат</h4>
            <ul>
                <li>✅ Новые поля появляются в Theme Presets</li>
                <li>✅ Темы сохраняются без ошибок</li>
                <li>✅ Цвета лейблов и селекторов настраиваются независимо</li>
                <li>✅ Простой и понятный интерфейс</li>
            </ul>
        </div>
    </div>

    <script>
        // Initialize demo with default colors
        document.addEventListener('DOMContentLoaded', function() {
            const demoWidget = document.getElementById('demo-widget');
            
            // Set default theme colors
            demoWidget.style.setProperty('--wsf-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-border', '#e5e7eb');
            demoWidget.style.setProperty('--wsf-primary', '#2563eb');
            
            // Set input-specific colors
            demoWidget.style.setProperty('--wsf-surface', '#f9fafb');
            demoWidget.style.setProperty('--wsf-input-bg', '#f9fafb');
            demoWidget.style.setProperty('--wsf-input-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-input-border', '#e5e7eb');
            
            console.log('Demo widget initialized with simplified input tokens');
        });
    </script>
</body>
</html>
