// Verification test for translation key cleanup
console.log('=== Translation Key Cleanup Verification Test ===');

// Test configuration - only the SHORT format keys should exist now
const EXPECTED_GENERATION_KEYS = {
    'label_generation': {
        ru: 'Поколение',
        en: 'Generation',
        de: 'Generation',
        fr: 'Génération',
        es: 'Generación'
    },
    'select_gen_placeholder': {
        ru: 'Выберите поколение',
        en: 'Choose a generation',
        de: 'Wähle eine Generation',
        fr: 'Choisissez une génération',
        es: 'Elige una generación'
    },
    'select_gen_first_placeholder': {
        ru: 'Сначала выберите поколение',
        en: 'Select generation first',
        de: 'Bitte zuerst Generation wählen',
        fr: 'Sélectionnez d\'abord la génération',
        es: 'Seleccione la generación primero'
    }
};

// Keys that should NO LONGER exist (removed duplicates)
const REMOVED_KEYS = [
    'select_generation_placeholder',
    'select_generation_first_placeholder'
];

// Test results tracking
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function logResult(test, status, message) {
    const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${test}: ${message}`);
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Test 1: Verify required keys still exist
console.log('\n1. Required Keys Existence Test:');
if (window.WheelFitI18n) {
    Object.keys(EXPECTED_GENERATION_KEYS).forEach(key => {
        const exists = key in window.WheelFitI18n;
        logResult(`Key "${key}" exists`, exists ? 'pass' : 'fail',
            exists ? 'Found in WheelFitI18n' : 'Missing from WheelFitI18n');
    });
} else {
    logResult('WheelFitI18n availability', 'fail', 'WheelFitI18n not available for testing');
}

// Test 2: Verify removed keys are gone
console.log('\n2. Removed Keys Cleanup Test:');
if (window.WheelFitI18n) {
    REMOVED_KEYS.forEach(key => {
        const exists = key in window.WheelFitI18n;
        logResult(`Removed key "${key}"`, !exists ? 'pass' : 'fail',
            !exists ? 'Successfully removed' : 'Still exists (should be removed)');
    });
} else {
    logResult('Removed keys test', 'warning', 'Cannot test - WheelFitI18n not available');
}

// Test 3: Test translation function with remaining keys
console.log('\n3. Translation Function Test:');
if (typeof window.t === 'function') {
    Object.keys(EXPECTED_GENERATION_KEYS).forEach(key => {
        const translation = window.t(key, 'FALLBACK');
        const isWorking = translation !== 'FALLBACK';
        
        logResult(`Translation for "${key}"`, isWorking ? 'pass' : 'fail',
            isWorking ? `Returns: "${translation}"` : 'Returns fallback (translation failed)');
    });
} else {
    logResult('Translation function test', 'fail', 'window.t function not available');
}

// Test 4: Test that removed keys return fallbacks
console.log('\n4. Removed Keys Fallback Test:');
if (typeof window.t === 'function') {
    REMOVED_KEYS.forEach(key => {
        const translation = window.t(key, 'EXPECTED_FALLBACK');
        const usesFallback = translation === 'EXPECTED_FALLBACK';
        
        logResult(`Removed key "${key}" fallback`, usesFallback ? 'pass' : 'warning',
            usesFallback ? 'Correctly returns fallback' : `Returns: "${translation}"`);
    });
} else {
    logResult('Removed keys fallback test', 'warning', 'Cannot test - translation function not available');
}

// Test 5: Verify DOM elements still work
console.log('\n5. DOM Elements Translation Test:');
const testElements = [
    { selector: 'label[for="wf-generation"]', expectedKey: 'label_generation' },
    { selector: '#wf-generation option[value=""]', expectedKey: 'select_gen_placeholder' }
];

testElements.forEach(({ selector, expectedKey }) => {
    const element = document.querySelector(selector);
    if (element) {
        const dataI18nKey = element.getAttribute('data-i18n');
        const keyMatches = dataI18nKey === expectedKey;
        
        logResult(`Element "${selector}" key`, keyMatches ? 'pass' : 'warning',
            keyMatches ? `Correct key: "${expectedKey}"` : `Key: "${dataI18nKey}", Expected: "${expectedKey}"`);
            
        // Check if the element has been translated
        const currentText = element.textContent.trim();
        const isTranslated = currentText && currentText !== expectedKey;
        
        logResult(`Element "${selector}" translation`, isTranslated ? 'pass' : 'warning',
            isTranslated ? `Text: "${currentText}"` : 'Not translated or empty');
    } else {
        logResult(`Element "${selector}"`, 'warning', 'Not found (normal if not in generation flow)');
    }
});

// Test 6: Test JavaScript code usage
console.log('\n6. JavaScript Code Usage Test:');
if (window.wheelFitWidget) {
    // Test populateGenerations method if available
    if (typeof window.wheelFitWidget.populateGenerations === 'function') {
        logResult('populateGenerations method', 'pass', 'Available for testing');
        
        // We can't easily test the actual method without data, but we can verify it exists
        console.log('   Note: populateGenerations uses select_gen_placeholder key');
    } else {
        logResult('populateGenerations method', 'warning', 'Not available');
    }
    
    // Test other generation-related methods
    const generationMethods = ['onGenerationSelect', 'getGenerationName'];
    generationMethods.forEach(methodName => {
        const exists = typeof window.wheelFitWidget[methodName] === 'function';
        logResult(`Method "${methodName}"`, exists ? 'pass' : 'warning',
            exists ? 'Available' : 'Not available');
    });
} else {
    logResult('Widget availability', 'warning', 'wheelFitWidget not available');
}

// Test 7: File size reduction verification
console.log('\n7. Translation File Optimization:');
if (window.WheelFitI18n) {
    const totalKeys = Object.keys(window.WheelFitI18n).length;
    const generationKeys = Object.keys(window.WheelFitI18n).filter(key => 
        key.includes('generation') || key.includes('gen')
    );
    
    logResult('Total translation keys', 'pass', `${totalKeys} keys loaded`);
    logResult('Generation-related keys', 'pass', `${generationKeys.length} keys found`);
    
    // Check that we only have the expected generation keys
    const expectedGenKeys = Object.keys(EXPECTED_GENERATION_KEYS);
    const actualGenKeys = generationKeys.filter(key => expectedGenKeys.includes(key));
    
    logResult('Expected generation keys', actualGenKeys.length === expectedGenKeys.length ? 'pass' : 'warning',
        `${actualGenKeys.length}/${expectedGenKeys.length} expected keys found`);
        
    // List all generation-related keys for verification
    console.log('   All generation-related keys:', generationKeys);
} else {
    logResult('Translation file optimization', 'warning', 'Cannot verify - WheelFitI18n not available');
}

// Test 8: Backward compatibility check
console.log('\n8. Backward Compatibility Test:');
console.log('   Checking that existing functionality still works...');

// Simulate the key usage patterns from the actual code
const codeUsageTests = [
    { key: 'select_gen_placeholder', context: 'populateGenerations method' },
    { key: 'select_gen_first_placeholder', context: 'placeholder update logic' },
    { key: 'label_generation', context: 'field labels' }
];

codeUsageTests.forEach(({ key, context }) => {
    if (typeof window.t === 'function') {
        const translation = window.t(key, 'DEFAULT');
        const works = translation !== 'DEFAULT';
        
        logResult(`Code usage: ${context}`, works ? 'pass' : 'fail',
            works ? `Key "${key}" works correctly` : `Key "${key}" failed`);
    } else {
        logResult(`Code usage: ${context}`, 'warning', 'Cannot test - translation function not available');
    }
});

// Final summary
setTimeout(() => {
    console.log('\n=== Cleanup Verification Summary ===');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⚠️ Warnings: ${testResults.warnings}`);
    
    const totalTests = testResults.passed + testResults.failed + testResults.warnings;
    const successRate = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
    
    console.log(`\n📊 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 Translation cleanup successful! All functionality preserved.');
        console.log('\n📋 Cleanup Results:');
        console.log('✅ Removed duplicate long-format keys:');
        REMOVED_KEYS.forEach(key => console.log(`   - ${key}`));
        console.log('✅ Kept short-format keys that match code usage:');
        Object.keys(EXPECTED_GENERATION_KEYS).forEach(key => console.log(`   - ${key}`));
        console.log('✅ All translations still work correctly');
        console.log('✅ File sizes reduced by removing redundancy');
    } else {
        console.log('\n⚠️ Some issues detected. Please review the failures above.');
    }
    
    console.log('\n🔧 What was cleaned up:');
    console.log('- Removed redundant long-format translation keys');
    console.log('- Kept only the short-format keys used by actual code');
    console.log('- Updated test files to reflect the cleanup');
    console.log('- Maintained all translation functionality');
    
}, 100);

console.log('\n=== Translation cleanup verification test initiated ===');
