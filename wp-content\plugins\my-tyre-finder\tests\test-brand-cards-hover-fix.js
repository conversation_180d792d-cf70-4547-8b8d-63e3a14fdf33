/**
 * Test script for brand cards hover state fix
 * 
 * This test verifies that:
 * 1. Brand cards have transparent background by default
 * 2. Hover state uses --wsf-surface color (dark gray in dark theme)
 * 3. Active state uses --wsf-primary color
 * 4. CSS classes are consistent (text-wsf-* format)
 * 5. Focus-visible state works for accessibility
 */

console.log('🧪 Testing Brand Cards Hover State Fix...');

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    testBrandCardsHoverFix();
});

function testBrandCardsHoverFix() {
    console.log('📋 Starting brand cards hover state tests...');
    
    // Test 1: Check if wizard container exists
    const wizard = document.getElementById('wheel-fit-wizard');
    if (!wizard) {
        console.error('❌ Wizard container not found');
        return;
    }
    console.log('✅ Wizard container found');
    
    // Test 2: Check if makes grid exists
    const makesGrid = document.getElementById('wizard-makes-grid');
    if (!makesGrid) {
        console.error('❌ Makes grid not found');
        return;
    }
    console.log('✅ Makes grid found');
    
    // Test 3: Wait for brand cards to load and test them
    setTimeout(() => {
        testBrandCardStyles();
    }, 2000);
}

function testBrandCardStyles() {
    const brandCards = document.querySelectorAll('#wizard-makes-grid > button');
    
    if (brandCards.length === 0) {
        console.warn('⚠️ No brand cards found yet. They might still be loading.');
        return;
    }
    
    console.log(`📊 Found ${brandCards.length} brand cards`);
    
    brandCards.forEach((card, index) => {
        if (index < 3) { // Test first 3 cards only
            testSingleBrandCard(card, index + 1);
        }
    });
    
    // Test CSS custom properties
    testCSSCustomProperties();
    
    // Test CSS overrides
    testCSSOverrides();
    
    console.log('🎉 Brand cards hover state tests completed!');
}

function testSingleBrandCard(card, cardNumber) {
    console.log(`🔍 Testing brand card #${cardNumber}:`);
    
    // Test 1: Check if card has correct classes
    const expectedClasses = ['bg-transparent', 'border', 'border-wsf-border'];
    const hasCorrectClasses = expectedClasses.every(cls => card.classList.contains(cls));
    
    if (hasCorrectClasses) {
        console.log(`  ✅ Card #${cardNumber} has correct base classes`);
    } else {
        console.log(`  ❌ Card #${cardNumber} missing expected classes:`, expectedClasses);
        console.log(`  📝 Actual classes:`, Array.from(card.classList));
    }
    
    // Test 2: Check computed styles
    const computedStyle = window.getComputedStyle(card);
    const backgroundColor = computedStyle.backgroundColor;
    
    console.log(`  📊 Card #${cardNumber} background-color:`, backgroundColor);
    
    // Test 3: Check text classes
    const textSpan = card.querySelector('span');
    if (textSpan) {
        const hasCorrectTextClass = textSpan.classList.contains('text-wsf-primary');
        if (hasCorrectTextClass) {
            console.log(`  ✅ Card #${cardNumber} text has correct class`);
        } else {
            console.log(`  ❌ Card #${cardNumber} text missing text-wsf-primary class`);
            console.log(`  📝 Text classes:`, Array.from(textSpan.classList));
        }
    }
}

function testCSSCustomProperties() {
    console.log('🎨 Testing CSS custom properties...');
    
    const widget = document.querySelector('.wsf-finder-widget, .wheel-fit-widget');
    if (!widget) {
        console.error('❌ Widget container not found');
        return;
    }
    
    const computedStyle = window.getComputedStyle(widget);
    
    // Test key CSS variables
    const variables = [
        '--wsf-bg',
        '--wsf-surface',
        '--wsf-surface-hover',
        '--wsf-primary',
        '--wsf-border',
        '--wsf-text-primary'
    ];
    
    variables.forEach(varName => {
        const value = computedStyle.getPropertyValue(varName).trim();
        if (value) {
            console.log(`  ✅ ${varName}: ${value}`);
        } else {
            console.log(`  ❌ ${varName}: not defined`);
        }
    });
}

function testCSSOverrides() {
    console.log('🔧 Testing CSS overrides...');
    
    // Create a temporary test button to check CSS rules
    const testButton = document.createElement('button');
    testButton.id = 'test-brand-card';
    testButton.className = 'group flex flex-col items-center justify-center bg-transparent border border-wsf-border rounded-xl p-3 h-24';
    testButton.innerHTML = '<span class="text-xs font-medium text-wsf-primary">Test</span>';
    
    // Add to makes grid temporarily
    const makesGrid = document.getElementById('wizard-makes-grid');
    if (makesGrid) {
        makesGrid.appendChild(testButton);
        
        // Test computed styles
        const computedStyle = window.getComputedStyle(testButton);
        console.log('  📊 Test button background:', computedStyle.backgroundColor);
        
        // Test hover styles by checking CSS rules
        const styleSheets = Array.from(document.styleSheets);
        let hoverRuleFound = false;
        
        try {
            styleSheets.forEach(sheet => {
                if (sheet.cssRules) {
                    Array.from(sheet.cssRules).forEach(rule => {
                        if (rule.selectorText && rule.selectorText.includes('#wizard-makes-grid > button:hover')) {
                            console.log('  ✅ Hover CSS rule found:', rule.cssText);
                            hoverRuleFound = true;
                        }
                        if (rule.selectorText && rule.selectorText.includes('#wizard-makes-grid > button:focus-visible')) {
                            console.log('  ✅ Focus-visible CSS rule found:', rule.cssText);
                        }
                    });
                }
            });
        } catch (e) {
            console.log('  ⚠️ Could not access some stylesheets (CORS)');
        }
        
        if (!hoverRuleFound) {
            console.log('  ❌ Hover CSS rule not found');
        }
        
        // Clean up
        makesGrid.removeChild(testButton);
    }
}

// Manual test helper functions
window.testBrandHover = function() {
    console.log('🖱️ Manual hover test - hover over brand cards and check console');
    
    const brandCards = document.querySelectorAll('#wizard-makes-grid > button');
    brandCards.forEach((card, index) => {
        card.addEventListener('mouseenter', () => {
            const computedStyle = window.getComputedStyle(card);
            console.log(`🖱️ Hovering card #${index + 1}:`, {
                backgroundColor: computedStyle.backgroundColor,
                borderColor: computedStyle.borderColor
            });
        });
    });
};

window.testThemeSwitch = function() {
    console.log('🌓 Testing theme switch...');
    
    const widget = document.querySelector('.wsf-finder-widget, .wheel-fit-widget');
    if (widget) {
        // Toggle between light and dark theme classes
        const isDark = widget.classList.contains('dark');
        if (isDark) {
            widget.classList.remove('dark');
            console.log('🌞 Switched to light theme');
        } else {
            widget.classList.add('dark');
            console.log('🌙 Switched to dark theme');
        }
        
        // Re-test CSS properties
        setTimeout(() => {
            testCSSCustomProperties();
        }, 100);
    }
};

console.log('🔧 Manual test functions available:');
console.log('  - testBrandHover() - Test hover states manually');
console.log('  - testThemeSwitch() - Toggle between light/dark theme');
