<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Notifications System Test</title>
    
    <!-- Подключаем CSS файлы -->
    <link rel="stylesheet" href="../assets/css/admin-theme-panel.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #111827;
            font-size: 24px;
            font-weight: 600;
        }
        
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .controls button {
            margin: 5px;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            font-weight: 500;
        }
        
        .controls button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
            transform: translateY(-1px);
        }
        
        .controls button.success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }
        
        .controls button.warning {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }
        
        .controls button.error {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .comparison-item {
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .comparison-item h3 {
            margin-top: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .old-alert-demo {
            padding: 15px;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            color: #dc2626;
            font-family: monospace;
            margin: 10px 0;
        }
        
        .theme-card-demo {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .theme-card-demo:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .theme-card-demo.active {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .theme-card-demo .badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #10b981;
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            text-transform: uppercase;
        }
        
        .theme-info h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .theme-info p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        
        .config-panel {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .config-panel h3 {
            margin-top: 0;
            color: #1e293b;
        }
        
        .config-option {
            display: flex;
            align-items: center;
            margin: 12px 0;
        }
        
        .config-option input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .config-option input[type="range"] {
            margin: 0 12px;
            flex: 1;
        }
        
        .duration-display {
            min-width: 60px;
            font-weight: 500;
            color: #374151;
        }
        
        @media (max-width: 768px) {
            .test-container {
                padding: 20px;
            }
            
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔔 Modern Notifications System Test</h1>
        <p>Тест новой системы уведомлений для активации тем - замена устаревшего alert() на современные стилизованные уведомления.</p>
        
        <!-- Контролы тестирования -->
        <div class="controls">
            <h3>🎛️ Тест уведомлений</h3>
            <button onclick="showModernNotification('Theme activated successfully', 'success')" class="success">
                ✅ Success Notification
            </button>
            <button onclick="showModernNotification('Theme updated successfully', 'success')" class="success">
                🔄 Update Notification
            </button>
            <button onclick="showModernNotification('Warning: Theme may need adjustments', 'warning')" class="warning">
                ⚠️ Warning Notification
            </button>
            <button onclick="showModernNotification('Error: Failed to activate theme', 'error')" class="error">
                ❌ Error Notification
            </button>
            <button onclick="showOldAlert()">
                🚫 Old Alert (Deprecated)
            </button>
        </div>
        
        <!-- Конфигурация -->
        <div class="config-panel">
            <h3>⚙️ Настройки уведомлений</h3>
            <div class="config-option">
                <input type="checkbox" id="enableNotifications" checked onchange="toggleNotifications()">
                <label for="enableNotifications">Показывать уведомления при активации темы</label>
            </div>
            <div class="config-option">
                <label>Длительность показа:</label>
                <input type="range" id="durationSlider" min="1000" max="8000" value="3000" step="500" onchange="updateDuration()">
                <span class="duration-display" id="durationDisplay">3.0s</span>
            </div>
        </div>
        
        <!-- Сравнение старого и нового подхода -->
        <div class="demo-section">
            <h2>📊 Сравнение: До и После</h2>
            <div class="comparison">
                <div class="comparison-item">
                    <h3>❌ Старый подход (alert)</h3>
                    <div class="old-alert-demo">
                        alert("Theme activated successfully");
                    </div>
                    <ul>
                        <li>Блокирует весь интерфейс</li>
                        <li>Устаревший дизайн браузера</li>
                        <li>Прерывает поток работы</li>
                        <li>Нет кастомизации</li>
                        <li>Требует действия пользователя</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h3>✅ Новый подход (.wsf-notification)</h3>
                    <div style="position: relative; height: 60px; background: #f9fafb; border-radius: 6px; overflow: hidden;">
                        <div style="position: absolute; top: 10px; right: 10px; background: #10b981; color: white; padding: 8px 12px; border-radius: 6px; font-size: 14px; font-weight: 500; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                            ✅ Theme activated successfully
                        </div>
                    </div>
                    <ul>
                        <li>Не блокирует интерфейс</li>
                        <li>Современный стилизованный дизайн</li>
                        <li>Автоматическое скрытие</li>
                        <li>Полная кастомизация</li>
                        <li>Позиционирование справа сверху</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Демо активации темы -->
        <div class="demo-section">
            <h2>🎨 Демо активации темы</h2>
            <p>Кликните на карточку темы, чтобы "активировать" её и увидеть уведомление:</p>
            
            <div class="theme-card-demo" onclick="activateTheme('modern-blue', this)">
                <div class="theme-info">
                    <h4>Modern Blue</h4>
                    <p>Современная синяя тема для виджета</p>
                </div>
                <div style="display: flex; gap: 8px;">
                    <div style="width: 20px; height: 20px; background: #2563eb; border-radius: 4px;"></div>
                    <div style="width: 20px; height: 20px; background: #ffffff; border: 1px solid #e5e7eb; border-radius: 4px;"></div>
                    <div style="width: 20px; height: 20px; background: #1f2937; border-radius: 4px;"></div>
                </div>
            </div>
            
            <div class="theme-card-demo active" onclick="activateTheme('green-nature', this)">
                <div class="badge">Active</div>
                <div class="theme-info">
                    <h4>Green Nature</h4>
                    <p>Природная зеленая тема</p>
                </div>
                <div style="display: flex; gap: 8px;">
                    <div style="width: 20px; height: 20px; background: #10b981; border-radius: 4px;"></div>
                    <div style="width: 20px; height: 20px; background: #f0fdf4; border-radius: 4px;"></div>
                    <div style="width: 20px; height: 20px; background: #065f46; border-radius: 4px;"></div>
                </div>
            </div>
            
            <div class="theme-card-demo" onclick="activateTheme('elegant-purple', this)">
                <div class="theme-info">
                    <h4>Elegant Purple</h4>
                    <p>Элегантная фиолетовая тема</p>
                </div>
                <div style="display: flex; gap: 8px;">
                    <div style="width: 20px; height: 20px; background: #7c3aed; border-radius: 4px;"></div>
                    <div style="width: 20px; height: 20px; background: #faf5ff; border-radius: 4px;"></div>
                    <div style="width: 20px; height: 20px; background: #581c87; border-radius: 4px;"></div>
                </div>
            </div>
        </div>
        
        <!-- Технические детали -->
        <div class="demo-section">
            <h2>🔧 Технические улучшения</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>JavaScript изменения</h3>
                    <ul>
                        <li><code>showStyledNotification()</code> - новая функция</li>
                        <li>Удален fallback на <code>alert()</code></li>
                        <li>Конфигурируемая длительность</li>
                        <li>Опциональные уведомления</li>
                        <li>Автоматическая очистка DOM</li>
                    </ul>
                </div>
                <div>
                    <h3>CSS стили</h3>
                    <ul>
                        <li><code>.wsf-notification</code> - базовый класс</li>
                        <li><code>--success</code>, <code>--warning</code>, <code>--error</code></li>
                        <li>Позиционирование <code>top: 32px; right: 20px</code></li>
                        <li>Анимации <code>transform: translateX()</code></li>
                        <li>Z-index: 100000 для приоритета</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Конфигурация (имитация ThemePresetsPanel.config)
        let notificationConfig = {
            showActivationNotifications: true,
            notificationDuration: 3000
        };
        
        // Современная система уведомлений
        function showModernNotification(message, type = 'info') {
            if (!notificationConfig.showActivationNotifications && type === 'success') {
                console.log('Notification suppressed:', message);
                return;
            }
            
            // Создаем элемент уведомления
            const notification = document.createElement('div');
            notification.className = `wsf-notification wsf-notification--${type}`;
            notification.textContent = message;

            // Добавляем в DOM
            document.body.appendChild(notification);

            // Показываем с анимацией
            requestAnimationFrame(() => {
                notification.classList.add('wsf-notification--show');
            });

            // Автоматически скрываем
            setTimeout(() => {
                notification.classList.remove('wsf-notification--show');
                
                // Удаляем из DOM после анимации
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, notificationConfig.notificationDuration);
        }
        
        // Старый подход (для сравнения)
        function showOldAlert() {
            alert("Theme activated successfully");
        }
        
        // Демо активации темы
        function activateTheme(themeName, element) {
            // Убираем active класс со всех карточек
            document.querySelectorAll('.theme-card-demo').forEach(card => {
                card.classList.remove('active');
                const badge = card.querySelector('.badge');
                if (badge) badge.remove();
            });
            
            // Добавляем active класс к выбранной карточке
            element.classList.add('active');
            const badge = document.createElement('div');
            badge.className = 'badge';
            badge.textContent = 'Active';
            element.appendChild(badge);
            
            // Показываем уведомление
            showModernNotification(`Theme "${themeName}" activated successfully`, 'success');
        }
        
        // Переключение уведомлений
        function toggleNotifications() {
            const checkbox = document.getElementById('enableNotifications');
            notificationConfig.showActivationNotifications = checkbox.checked;
            
            if (checkbox.checked) {
                showModernNotification('Notifications enabled', 'success');
            } else {
                console.log('Notifications disabled');
            }
        }
        
        // Обновление длительности
        function updateDuration() {
            const slider = document.getElementById('durationSlider');
            const display = document.getElementById('durationDisplay');
            
            notificationConfig.notificationDuration = parseInt(slider.value);
            display.textContent = (notificationConfig.notificationDuration / 1000).toFixed(1) + 's';
        }
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔔 Modern Notifications System Test загружен');
            console.log('📝 Тестируйте различные типы уведомлений и настройки');
            
            // Показываем приветственное уведомление
            setTimeout(() => {
                showModernNotification('Welcome! Test the new notification system', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
