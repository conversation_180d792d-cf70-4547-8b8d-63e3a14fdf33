<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wizard Scroll Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            height: 500px;
            overflow-y: auto;
        }
        
        .before-section {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after-section {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .widget-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* Wizard styles simulation */
        .wheel-fit-widget {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }
        
        .wsf-widget__header {
            margin-bottom: 24px;
        }
        
        .wsf-widget__title {
            text-align: center;
            font-size: 24px;
            font-weight: 800;
            color: #2563eb;
            margin: 0;
        }
        
        #wizard-header {
            margin-bottom: 24px;
        }
        
        .wizard-steps {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            font-weight: 600;
            color: #9ca3af;
            margin-bottom: 8px;
        }
        
        .wizard-step-name {
            color: #2563eb;
        }
        
        .wizard-step-name.active {
            color: #2563eb;
        }
        
        .progress-bar {
            background: #f3f4f6;
            border-radius: 9999px;
            height: 6px;
            margin-top: 8px;
        }
        
        .progress-fill {
            background: #2563eb;
            height: 6px;
            border-radius: 9999px;
            width: 40%;
            transition: width 0.5s ease;
        }
        
        .wsf-form-wrapper {
            background: #ffffff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .wizard-content {
            text-align: center;
            color: #6b7280;
        }
        
        .wizard-content.broken {
            min-height: 1200px; /* Симуляция проблемы с высотой */
            background: linear-gradient(to bottom, #ffffff 0%, #ffffff 10%, #fef2f2 50%, #ffffff 90%, #ffffff 100%);
            padding-top: 800px; /* Контент далеко внизу */
        }
        
        .wizard-content.fixed {
            min-height: auto;
            height: auto;
            padding-top: 20px;
        }
        
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
            margin-top: 20px;
        }
        
        .model-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 8px;
            text-align: center;
            font-size: 12px;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .model-item:hover {
            background: #e5e7eb;
            border-color: #2563eb;
        }
        
        .scroll-indicator {
            background: #fbbf24;
            color: #92400e;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .scroll-indicator.fixed {
            background: #10b981;
            color: white;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">🔧 Wizard Scroll Fix Test</h1>
            <p style="color: #6b7280; font-size: 18px;">Исправление проблемы с избыточной прокруткой в wizard форме</p>
        </header>

        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h3 style="margin-top: 0; color: #92400e;">🐛 Критическая проблема UX</h3>
            <p><strong>Симптомы:</strong></p>
            <ul>
                <li>❌ Пользователь должен прокручивать до самого низа, чтобы увидеть контент второго шага</li>
                <li>❌ Контент "Choose a model" находится очень далеко внизу</li>
                <li>❌ Избыточная высота контейнеров создает пустое пространство</li>
                <li>❌ Плохой UX - пользователь думает, что форма сломана</li>
            </ul>
            
            <p><strong>Причина:</strong> CSS правила создают избыточную высоту контейнеров, а скрытые шаги все еще занимают место</p>
        </div>

        <div class="comparison-grid">
            <!-- Before -->
            <div class="comparison-section before-section">
                <h2 style="color: #dc2626; margin-top: 0;">❌ До исправления</h2>
                <div class="scroll-indicator">⚠️ Прокрутите вниз до конца!</div>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <!-- Заголовок вверху -->
                        <div class="wsf-widget__header">
                            <h1 class="wsf-widget__title">Wheel & Tyre Finder</h1>
                        </div>
                        
                        <!-- Стадии под заголовком -->
                        <div id="wizard-header">
                            <div class="wizard-steps">
                                <div class="wizard-step-name">Make</div>
                                <div class="wizard-step-name active">Model</div>
                                <div class="wizard-step-name">Year</div>
                                <div class="wizard-step-name">Modification</div>
                                <div class="wizard-step-name">Wheel Options</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                        
                        <div class="wsf-form-wrapper">
                            <div class="wizard-content broken">
                                <h2 style="color: #2563eb; margin-bottom: 20px;">Choose a model</h2>
                                <p style="color: #ef4444; font-weight: 600;">Контент находится в самом низу!</p>
                                <div class="models-grid">
                                    <div class="model-item">CL</div>
                                    <div class="model-item">CLK</div>
                                    <div class="model-item">CLS</div>
                                    <div class="model-item">SL</div>
                                    <div class="model-item">SLK</div>
                                    <div class="model-item">SLR</div>
                                    <div class="model-item">SLS</div>
                                    <div class="model-item">AMG GT</div>
                                    <div class="model-item">A-Class</div>
                                    <div class="model-item">B-Class</div>
                                    <div class="model-item">C-Class</div>
                                    <div class="model-item">E-Class</div>
                                    <div class="model-item">S-Class</div>
                                    <div class="model-item">G-Class</div>
                                    <div class="model-item">GLA</div>
                                    <div class="model-item">GLB</div>
                                    <div class="model-item">GLC</div>
                                    <div class="model-item">GLE</div>
                                    <div class="model-item">GLS</div>
                                    <div class="model-item">ML</div>
                                    <div class="model-item">GL</div>
                                    <div class="model-item">GLK</div>
                                    <div class="model-item">R-Class</div>
                                    <div class="model-item">V-Class</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #7f1d1d; margin-top: 15px; font-size: 14px;">
                    <strong>Проблема:</strong> Контент находится в самом низу из-за избыточной высоты контейнеров
                </p>
            </div>

            <!-- After -->
            <div class="comparison-section after-section">
                <h2 style="color: #059669; margin-top: 0;">✅ После исправления</h2>
                <div class="scroll-indicator fixed">✅ Контент сразу доступен!</div>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <!-- Заголовок вверху -->
                        <div class="wsf-widget__header">
                            <h1 class="wsf-widget__title">Wheel & Tyre Finder</h1>
                        </div>
                        
                        <!-- Стадии под заголовком -->
                        <div id="wizard-header">
                            <div class="wizard-steps">
                                <div class="wizard-step-name">Make</div>
                                <div class="wizard-step-name active">Model</div>
                                <div class="wizard-step-name">Year</div>
                                <div class="wizard-step-name">Modification</div>
                                <div class="wizard-step-name">Wheel Options</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                        
                        <div class="wsf-form-wrapper">
                            <div class="wizard-content fixed">
                                <h2 style="color: #2563eb; margin-bottom: 20px;">Choose a model</h2>
                                <p style="color: #10b981; font-weight: 600;">Контент сразу доступен!</p>
                                <div class="models-grid">
                                    <div class="model-item">CL</div>
                                    <div class="model-item">CLK</div>
                                    <div class="model-item">CLS</div>
                                    <div class="model-item">SL</div>
                                    <div class="model-item">SLK</div>
                                    <div class="model-item">SLR</div>
                                    <div class="model-item">SLS</div>
                                    <div class="model-item">AMG GT</div>
                                    <div class="model-item">A-Class</div>
                                    <div class="model-item">B-Class</div>
                                    <div class="model-item">C-Class</div>
                                    <div class="model-item">E-Class</div>
                                    <div class="model-item">S-Class</div>
                                    <div class="model-item">G-Class</div>
                                    <div class="model-item">GLA</div>
                                    <div class="model-item">GLB</div>
                                    <div class="model-item">GLC</div>
                                    <div class="model-item">GLE</div>
                                    <div class="model-item">GLS</div>
                                    <div class="model-item">ML</div>
                                    <div class="model-item">GL</div>
                                    <div class="model-item">GLK</div>
                                    <div class="model-item">R-Class</div>
                                    <div class="model-item">V-Class</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #065f46; margin-top: 15px; font-size: 14px;">
                    <strong>Решение:</strong> Агрессивный сброс высоты + автопрокрутка к началу шага
                </p>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f0fdf4; border: 1px solid #16a34a; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #15803d;">✅ Выполненные исправления</h3>
            
            <p><strong>1. Добавлена автопрокрутка при переключении шагов:</strong></p>
            <ul>
                <li>✅ <code>wizard.js</code> - добавлен <code>scrollIntoView()</code> в метод <code>goToStep()</code></li>
                <li>✅ При переходе на новый шаг форма автоматически прокручивается к началу</li>
            </ul>
            
            <p><strong>2. Агрессивный сброс высоты всех элементов:</strong></p>
            <ul>
                <li>✅ <code>wizard-flow.twig</code> - добавлены правила <code>min-height: auto !important</code> для всех элементов</li>
                <li>✅ Скрытые шаги теперь имеют <code>display: none</code> и <code>height: 0</code></li>
                <li>✅ Убраны избыточные padding и margin</li>
            </ul>
            
            <p><strong>3. Исправления для админки:</strong></p>
            <ul>
                <li>✅ <code>live-preview-width-fix.css</code> - аналогичные агрессивные правила для Live Preview</li>
                <li>✅ Принудительный сброс высоты для всех контейнеров widget</li>
                <li>✅ Исправлены конфликты с существующими CSS правилами</li>
            </ul>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #eff6ff; border: 1px solid #3b82f6; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1d4ed8;">🔧 Для тестирования</h3>
            <p>Чтобы проверить исправления:</p>
            <ol>
                <li>Перейдите в админку WordPress → Wheel-Size → Appearance</li>
                <li>Установите <strong>Form Layout: Wizard</strong></li>
                <li>В Live Preview выберите любого производителя (например, BMW)</li>
                <li>При переходе на второй шаг "Choose a model":</li>
                <ul>
                    <li>✅ Контент должен быть сразу виден</li>
                    <li>✅ Не должно требоваться прокрутки</li>
                    <li>✅ Список моделей отображается в верхней части</li>
                </ul>
                <li>Проверьте переходы между всеми шагами</li>
                <li>Проверьте также на фронтенде сайта</li>
            </ol>
            
            <p><strong>Ожидаемый результат:</strong></p>
            <ul>
                <li>✅ Контент каждого шага сразу виден после перехода</li>
                <li>✅ Автоматическая прокрутка к началу шага</li>
                <li>✅ Нет избыточного пустого пространства</li>
                <li>✅ Улучшенный UX - пользователь сразу видит доступные опции</li>
            </ul>
        </div>
    </div>
</body>
</html>
