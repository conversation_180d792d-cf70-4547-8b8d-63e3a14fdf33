<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Тест цвета текста кнопки Garage</title>
    <link rel="stylesheet" href="../assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1e293b;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .demo-widget {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .color-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .color-demo {
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .before {
            background: #fef2f2;
            border-color: #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }
        
        .garage-button-demo {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 6px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 600;
        }
        
        .garage-button-old {
            color: var(--wsf-muted, #9ca3af);
        }
        
        .garage-button-new {
            color: var(--wsf-text, #1f2937);
        }
        
        .garage-button-demo:hover {
            background: var(--wsf-surface, #f9fafb);
        }
        
        .garage-icon {
            width: 20px;
            height: 20px;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }
        
        .badge {
            background: var(--wsf-accent, #3b82f6);
            color: var(--wsf-bg, #ffffff);
            font-size: 11px;
            font-weight: 600;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 6px;
        }
        
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .status-item {
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .status-pass {
            background: #f0fdf4;
            border-color: #22c55e;
        }
        
        .status-info {
            background: #eff6ff;
            border-color: #3b82f6;
        }
        
        h1 {
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        h2 {
            color: #374151;
            margin-bottom: 15px;
        }
        
        h3 {
            color: #4b5563;
            margin-bottom: 10px;
        }
        
        .highlight {
            background: #fef3c7;
            color: #92400e;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Тест цвета текста кнопки Garage</h1>
        <p>Проверка изменения цвета иконки машинки и текста "Гараж" с <span class="highlight">--wsf-muted</span> на <span class="highlight">--wsf-text</span></p>
        
        <!-- Color Comparison -->
        <div class="test-section">
            <h2>🔄 Сравнение: До и После</h2>
            
            <div class="color-comparison">
                <div class="color-demo before">
                    <h3>❌ ДО (проблемный)</h3>
                    <p><strong>Цвет:</strong> <code>text-wsf-muted</code> = <code>--wsf-muted</code> = <code>#9ca3af</code></p>
                    
                    <button class="garage-button-demo garage-button-old">
                        <svg class="garage-icon" viewBox="0 0 24 24">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                        </svg>
                        <span>Garage</span>
                        <span class="badge">3</span>
                    </button>
                    
                    <p style="font-size: 12px; color: #6b7280; margin-top: 10px;">
                        Слишком блеклый, плохо читается
                    </p>
                </div>
                
                <div class="color-demo after">
                    <h3>✅ ПОСЛЕ (исправленный)</h3>
                    <p><strong>Цвет:</strong> <code>text-wsf-text</code> = <code>--wsf-text</code> = <code>#1f2937</code></p>
                    
                    <button class="garage-button-demo garage-button-new">
                        <svg class="garage-icon" viewBox="0 0 24 24">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                        </svg>
                        <span>Garage</span>
                        <span class="badge">3</span>
                    </button>
                    
                    <p style="font-size: 12px; color: #6b7280; margin-top: 10px;">
                        Хорошо читается, основной цвет текста
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Widget Demo -->
        <div class="test-section">
            <h2>🎯 Демо в виджете</h2>
            
            <div class="demo-widget wsf-finder-widget">
                <h3>Wheel Size Finder</h3>
                
                <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                    <div>
                        <label>Make:</label>
                        <select style="margin-left: 10px; padding: 5px;">
                            <option>BMW</option>
                            <option>Mercedes</option>
                            <option>Audi</option>
                        </select>
                    </div>
                    
                    <button type="button" data-garage-trigger class="inline-flex items-center gap-2 text-sm text-wsf-text px-3 py-1.5 rounded-md hover:bg-wsf-surface hover:text-wsf-text transition">
                        <svg class="garage-icon" viewBox="0 0 24 24">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                        </svg>
                        <span class="font-semibold">Garage</span>
                        <span class="wsf-garage-count-badge">3</span>
                    </button>
                </div>
                
                <button style="background: var(--wsf-primary); color: white; padding: 10px 20px; border: none; border-radius: 6px; font-weight: 600;">
                    Find Sizes
                </button>
            </div>
        </div>
        
        <!-- Status Check -->
        <div class="test-section">
            <h2>✅ Статус исправлений</h2>
            
            <div class="status-grid">
                <div class="status-item status-pass">
                    <h3>✅ finder-form-inline.twig</h3>
                    <p><strong>Изменено:</strong> <code>text-wsf-muted</code> → <code>text-wsf-text</code></p>
                    <p><strong>Строка:</strong> 53</p>
                </div>
                
                <div class="status-item status-pass">
                    <h3>✅ finder-form-flow.twig</h3>
                    <p><strong>Изменено:</strong> <code>text-wsf-muted</code> → <code>text-wsf-text</code></p>
                    <p><strong>Строки:</strong> 74, 87</p>
                </div>
                
                <div class="status-item status-pass">
                    <h3>✅ finder-form.twig</h3>
                    <p><strong>Изменено:</strong> <code>text-wsf-muted</code> → <code>text-wsf-text</code></p>
                    <p><strong>Строка:</strong> 156</p>
                </div>
                
                <div class="status-item status-pass">
                    <h3>✅ fields/mod.twig</h3>
                    <p><strong>Изменено:</strong> <code>text-wsf-muted</code> → <code>text-wsf-text</code></p>
                    <p><strong>Строка:</strong> 26</p>
                </div>
                
                <div class="status-item status-info">
                    <h3>ℹ️ Кружок с цифрой</h3>
                    <p><strong>Остается:</strong> <code>--wsf-accent</code> (синий)</p>
                    <p><strong>Причина:</strong> Хорошо контрастирует с белым фоном</p>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h2>🔧 Технические детали</h2>
            
            <h3>Измененные файлы:</h3>
            <div class="code-block">
wp-content/plugins/my-tyre-finder/templates/finder-form-inline.twig
wp-content/plugins/my-tyre-finder/templates/finder-form-flow.twig  
wp-content/plugins/my-tyre-finder/templates/finder-form.twig
wp-content/plugins/my-tyre-finder/templates/fields/mod.twig
            </div>
            
            <h3>Изменение в коде:</h3>
            <div class="code-block">
// ДО:
class="... text-wsf-muted ..."

// ПОСЛЕ:
class="... text-wsf-text ..."
            </div>
            
            <h3>Цветовые токены:</h3>
            <div class="code-block">
--wsf-text: #1f2937;     /* Основной цвет текста (темно-серый) */
--wsf-muted: #9ca3af;    /* Приглушенный цвет (светло-серый) */
--wsf-accent: #3b82f6;   /* Акцентный цвет (синий) - для кружка */
            </div>
        </div>
        
        <!-- Result -->
        <div class="test-section">
            <h2>🎉 Результат</h2>
            <p><strong>✅ Проблема решена:</strong></p>
            <ul>
                <li>🚗 Иконка машинки теперь использует <code>--wsf-text</code> (основной цвет текста)</li>
                <li>📝 Текст "Гараж" теперь использует <code>--wsf-text</code> (основной цвет текста)</li>
                <li>⭕ Кружок с цифрой остается <code>--wsf-accent</code> (синий) для хорошего контраста</li>
                <li>🎨 Все элементы теперь хорошо читаются на белом фоне</li>
                <li>📱 Изменения применены во всех шаблонах виджета</li>
            </ul>
        </div>
    </div>
</body>
</html>
