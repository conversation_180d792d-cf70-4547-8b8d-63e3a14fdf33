/** @type {import('tailwindcss').Config} */
module.exports = {
  prefix: 'wsf-',
  content: [
    './src/admin/AppearancePage.php',
    './assets/css/admin-theme-panel.src.css',
    './assets/js/admin-theme-panel.js',
    './templates/**/*.twig',
    './src/**/*.php',
    './assets/**/*.js',
  ],
  safelist: [
    // Prevent purging of all wsf- prefixed classes
    { pattern: /^wsf-/ },
    // Font utility classes
    'wsf-root-font',
    // Specific classes used in theme panel
    'wsf-admin-grid',
    'wsf-admin-grid__main',
    'wsf-admin-grid__sidebar',
    'wsf-theme-panel',
    'wsf-theme-panel__header',
    'wsf-theme-panel__title',
    'wsf-theme-panel__content',
    'wsf-theme-panel__loader',
    'wsf-theme-panel--loading',
    'wsf-theme-card',
    'wsf-theme-card--active',
    'wsf-theme-card__badge',
    'wsf-theme-card__swatch',
    'wsf-theme-card__swatches',
    'wsf-theme-card__header',
    'wsf-theme-card__name',
    'wsf-theme-card__actions',
    'wsf-theme-card__action',
    'wsf-theme-card__content',
    'wsf-theme-cards-grid',
    'wsf-theme-panel__add',
    'wsf-drag-handle',
    'wsf-icon-edit',
    'wsf-icon-duplicate',
    'wsf-icon-trash',
    // WordPress admin style classes
    'bg-white',
    'bg-gray-50',
    'bg-gray-100',
    'bg-gray-700',
    'bg-blue-50',
    'bg-slate-100',
    'bg-slate-200',
    'bg-slate-300',
    'bg-slate-800',
    'text-gray-300',
    'text-gray-400',
    'text-gray-500',
    'text-gray-600',
    'text-gray-700',
    'text-gray-900',
    'text-red-500',
    'text-red-600',
    'text-slate-300',
    'text-slate-400',
    'text-slate-500',
    'text-slate-600',
    'text-slate-700',
    'border-gray-200',
    'border-gray-300',
    'border-gray-600',
    'border-slate-200',
    'border-slate-300',
    'border-slate-400',
    'border-slate-600',
    'border-slate-700',
    'ring-slate-400',
    'ring-slate-500',
    'ring-primary',
    'border-primary',
    'bg-primary',
    'text-primary',
    'rounded',
    'rounded-lg',
    'shadow-sm',
    'shadow-md',
    'shadow-inner',
    'p-3',
    'p-4',
    'p-6',
    'pl-6',
    'pr-2',
    'py-4',
    'py-0.5',
    'px-1.5',
    'mb-6',
    'pb-4',
    'mt-6',
    'gap-1',
    'gap-2',
    'gap-3',
    'space-y-3',
    'w-4',
    'w-5',
    'w-6',
    'h-4',
    'h-5',
    'h-6',
    'w-8',
    'h-8',
    'w-full',
    'sm:w-auto',
    'absolute',
    'relative',
    'left-2',
    'top-1/2',
    'transform',
    '-translate-y-1/2',
    'flex',
    'inline-flex',
    'items-center',
    'items-start',
    'justify-center',
    'justify-between',
    'cursor-move',
    'cursor-pointer',
    'opacity-60',
    'hover:opacity-100',
    'transition-all',
    'transition-colors',
    'transition-opacity',
    'text-xs',
    'text-sm',
    'text-[10px]',
    'font-medium',
    'border-2',
    'border-dashed',
    'hover:bg-slate-200',
    'hover:border-slate-400',
    'hover:text-slate-600',
    'hover:text-red-500',
    'dark:bg-slate-800',
    'dark:border-slate-700',
    'dark:text-slate-300',
    // Common utility patterns
    { pattern: /^wsf-(bg|text|border|p|m|flex|grid|rounded|shadow)/ },
    { pattern: /^wsf-(hover|focus|active):/ },
    { pattern: /^(bg|text|border|ring)-(gray|blue|red|slate)/ },
    { pattern: /^(p|m|gap|w|h)-\d+/ },
  ],
  darkMode: ['class', '[data-theme="dark"]'],
  theme: {
    extend: {
      colors: {
        // Legacy support - updated to neutral gray
        'primary': {
          DEFAULT: '#cbd5e1', // slate-300
          'dark': '#94a3b8',   // slate-400
        },
        'primary-dark': '#94a3b8',

        // Standard color palette for @apply directives
        'slate': {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        'gray': {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        'blue': {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        'red': {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        'white': '#ffffff',
        'black': '#000000',

        // New theme token system - flat structure for easier usage
        'wsf-bg': 'var(--wsf-bg)',
        'wsf-text': 'var(--wsf-text)',
        'wsf-primary': 'var(--wsf-primary)',
        'wsf-border': 'var(--wsf-border)',
        'wsf-hover': 'var(--wsf-hover)',
        'wsf-secondary': 'var(--wsf-secondary)',
        'wsf-accent': 'var(--wsf-accent)',
        'wsf-muted': 'var(--wsf-muted)',
        'wsf-success': 'var(--wsf-success)',
        'wsf-warning': 'var(--wsf-warning)',
        'wsf-error': 'var(--wsf-error)',
        'wsf-surface': 'var(--wsf-surface)',
        'wsf-surface-hover': 'var(--wsf-surface-hover)',
        'wsf-border-light': 'var(--wsf-border-light)',
        'wsf-border-focus': 'var(--wsf-border-focus)',
        'wsf-text-primary': 'var(--wsf-text-primary)',
        'wsf-text-secondary': 'var(--wsf-text-secondary)',
        'wsf-text-muted': 'var(--wsf-text-muted)',
        'wsf-text-inverse': 'var(--wsf-text-inverse)',

        // Nested structure for compatibility
        'wsf': {
          'bg': 'var(--wsf-bg)',
          'text': 'var(--wsf-text)',
          'primary': 'var(--wsf-primary)',
          'border': 'var(--wsf-border)',
          'hover': 'var(--wsf-hover)',
          'secondary': 'var(--wsf-secondary)',
          'accent': 'var(--wsf-accent)',
          'muted': 'var(--wsf-muted)',
          'success': 'var(--wsf-success)',
          'warning': 'var(--wsf-warning)',
          'error': 'var(--wsf-error)',
          'surface': 'var(--wsf-surface)',
          'surface-hover': 'var(--wsf-surface-hover)',
          'border-light': 'var(--wsf-border-light)',
          'border-focus': 'var(--wsf-border-focus)',
          'text-primary': 'var(--wsf-text-primary)',
          'text-secondary': 'var(--wsf-text-secondary)',
          'text-muted': 'var(--wsf-text-muted)',
          'text-inverse': 'var(--wsf-text-inverse)',
        }
      },
      spacing: {
        'wsf-xs': 'var(--wsf-spacing-xs)',
        'wsf-sm': 'var(--wsf-spacing-sm)',
        'wsf-md': 'var(--wsf-spacing-md)',
        'wsf-lg': 'var(--wsf-spacing-lg)',
        'wsf-xl': 'var(--wsf-spacing-xl)',
      },
      borderRadius: {
        'wsf': 'var(--wsf-radius)',
        'wsf-lg': 'var(--wsf-radius-lg)',
      },
      boxShadow: {
        'wsf': '0 1px 3px var(--wsf-shadow)',
        'wsf-hover': '0 4px 6px var(--wsf-shadow-hover)',
      },
      fontFamily: {
        'wsf': ['var(--wsf-font-base)', 'Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
        'wsf-variable': ['var(--wsf-font-variable)', 'InterVariable', 'Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
  variants: {
    extend: {
      backgroundColor: ['disabled'],
      textColor: ['disabled'],
      opacity: ['disabled'],
      cursor: ['disabled'],
      borderColor: ['disabled'],
    },
  },
}