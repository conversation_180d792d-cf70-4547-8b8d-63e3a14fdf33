// Test script for shortcode block visibility based on API configuration
console.log('=== Shortcode Block Visibility Test ===');

// Test results tracking
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function logResult(test, status, message) {
    const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${test}: ${message}`);
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Test 1: Check initial shortcode block visibility
console.log('\n1. Initial Shortcode Block Visibility Test:');

function checkInitialShortcodeVisibility() {
    const pageHeader = document.querySelector('.api-page-header');
    const shortcodeBox = document.querySelector('.shortcode-box');
    const apiStatusText = document.getElementById('api-status-text');
    
    if (!pageHeader) {
        logResult('Page elements present', 'warning', 'Not on API Settings page');
        return;
    }
    
    logResult('Page elements present', 'pass', 'API Settings page detected');
    
    // Check current API configuration status
    const isConfigured = apiStatusText && apiStatusText.textContent.includes('✅');
    const isNotConfigured = apiStatusText && apiStatusText.textContent.includes('❌');
    
    logResult('API configuration status detected', (isConfigured || isNotConfigured) ? 'pass' : 'warning',
        isConfigured ? 'API appears configured' : isNotConfigured ? 'API appears not configured' : 'Status unclear');
    
    // Check shortcode box visibility based on configuration
    if (isConfigured) {
        logResult('Shortcode visible when configured', shortcodeBox ? 'pass' : 'fail',
            shortcodeBox ? 'Shortcode box is visible' : 'Shortcode box should be visible but is not');
    } else if (isNotConfigured) {
        logResult('Shortcode hidden when not configured', !shortcodeBox ? 'pass' : 'fail',
            !shortcodeBox ? 'Shortcode box is correctly hidden' : 'Shortcode box should be hidden but is visible');
    } else {
        logResult('Shortcode visibility logic', 'warning', 'Cannot determine expected behavior due to unclear API status');
    }
    
    // Check shortcode content if visible
    if (shortcodeBox) {
        const hasCorrectContent = shortcodeBox.textContent.includes('[wheel_fit]') && 
                                 shortcodeBox.textContent.includes('Shortcode');
        
        logResult('Shortcode content correct', hasCorrectContent ? 'pass' : 'fail',
            hasCorrectContent ? 'Shortcode contains correct content' : 'Shortcode content is incorrect');
    }
}

checkInitialShortcodeVisibility();

// Test 2: Test shortcode visibility during API validation
console.log('\n2. API Validation Shortcode Behavior Test:');

function testValidationBehavior() {
    const apiKeyInput = document.getElementById('api_key');
    const testButton = document.getElementById('test-api-key');
    
    if (!apiKeyInput || !testButton) {
        logResult('Validation elements present', 'warning', 'API validation elements not found');
        return;
    }
    
    logResult('Validation elements present', 'pass', 'API validation elements found');
    
    // Monitor shortcode box changes during validation
    const pageHeader = document.querySelector('.api-page-header');
    let shortcodeBoxBefore = pageHeader?.querySelector('.shortcode-box');
    
    console.log('\n   🔧 Manual Validation Test Instructions:');
    console.log('   1. Enter an invalid API key (e.g., "123456789")');
    console.log('   2. Click "Test Connection"');
    console.log('   3. Watch for shortcode box to be hidden/removed');
    console.log('   4. Enter a valid API key');
    console.log('   5. Click "Test Connection"');
    console.log('   6. Watch for shortcode box to appear');
    
    // Add event listener to monitor validation
    if (!testButton.hasAttribute('data-shortcode-test-listener')) {
        testButton.setAttribute('data-shortcode-test-listener', 'true');
        
        testButton.addEventListener('click', function() {
            console.log('\n🔄 API Validation Started - Monitoring Shortcode Box...');
            
            const initialShortcodeBox = pageHeader?.querySelector('.shortcode-box');
            console.log(`📊 Initial state: Shortcode box ${initialShortcodeBox ? 'present' : 'absent'}`);
            
            // Monitor for changes
            let checkCount = 0;
            const monitor = setInterval(() => {
                checkCount++;
                const currentShortcodeBox = pageHeader?.querySelector('.shortcode-box');
                const statusSpan = document.getElementById('api-test-status');
                const currentStatus = statusSpan ? statusSpan.textContent : '';
                
                console.log(`📊 Check ${checkCount}: Status="${currentStatus}", Shortcode=${currentShortcodeBox ? 'present' : 'absent'}`);
                
                // Check for completion
                if (currentStatus.includes('✅') || currentStatus.includes('❌')) {
                    clearInterval(monitor);
                    
                    const isSuccess = currentStatus.includes('✅');
                    const hasShortcode = !!currentShortcodeBox;
                    
                    if (isSuccess && hasShortcode) {
                        console.log('✅ SUCCESS: Shortcode box appeared after successful validation');
                    } else if (isSuccess && !hasShortcode) {
                        console.log('⚠️ WARNING: Shortcode box not shown after successful validation (will appear after page reload)');
                    } else if (!isSuccess && !hasShortcode) {
                        console.log('✅ SUCCESS: Shortcode box hidden after failed validation');
                    } else if (!isSuccess && hasShortcode) {
                        console.log('❌ FAIL: Shortcode box still visible after failed validation');
                    }
                }
                
                // Stop monitoring after 15 seconds
                if (checkCount >= 30) {
                    clearInterval(monitor);
                    console.log('⏹️ Monitoring stopped - timeout reached');
                }
            }, 500);
        });
    }
}

testValidationBehavior();

// Test 3: Test CSS and styling
console.log('\n3. Shortcode Box Styling Test:');

function testShortcodeBoxStyling() {
    const shortcodeBox = document.querySelector('.shortcode-box');
    
    if (!shortcodeBox) {
        logResult('Shortcode styling test', 'warning', 'Shortcode box not present for styling test');
        return;
    }
    
    const style = window.getComputedStyle(shortcodeBox);
    
    // Check key styling properties
    const hasBackground = style.backgroundColor !== 'rgba(0, 0, 0, 0)';
    const hasBorder = style.borderWidth !== '0px';
    const hasPadding = style.paddingTop !== '0px' || style.paddingLeft !== '0px';
    const hasMinWidth = style.minWidth !== 'auto';
    
    logResult('Shortcode box styling', (hasBackground && hasBorder && hasPadding) ? 'pass' : 'warning',
        `Background: ${hasBackground}, Border: ${hasBorder}, Padding: ${hasPadding}, MinWidth: ${hasMinWidth}`);
    
    // Check flexbox parent
    const pageHeader = document.querySelector('.api-page-header');
    if (pageHeader) {
        const headerStyle = window.getComputedStyle(pageHeader);
        const isFlexbox = headerStyle.display === 'flex';
        
        logResult('Header flexbox layout', isFlexbox ? 'pass' : 'warning',
            `Header display: ${headerStyle.display}`);
    }
}

testShortcodeBoxStyling();

// Test 4: Test responsive behavior
console.log('\n4. Responsive Behavior Test:');

function testResponsiveBehavior() {
    const shortcodeBox = document.querySelector('.shortcode-box');
    const pageHeader = document.querySelector('.api-page-header');
    
    if (!shortcodeBox || !pageHeader) {
        logResult('Responsive test elements', 'warning', 'Elements not present for responsive test');
        return;
    }
    
    // Check for media query styles
    const styles = Array.from(document.styleSheets).flatMap(sheet => {
        try {
            return Array.from(sheet.cssRules || []);
        } catch (e) {
            return [];
        }
    });
    
    const hasMediaQueries = styles.some(rule => 
        rule.type === CSSRule.MEDIA_RULE && 
        rule.conditionText && 
        rule.conditionText.includes('768px')
    );
    
    logResult('Responsive CSS present', hasMediaQueries ? 'pass' : 'warning',
        hasMediaQueries ? 'Mobile media queries found' : 'No mobile media queries detected');
    
    // Test current layout
    const headerStyle = window.getComputedStyle(pageHeader);
    const shortcodeStyle = window.getComputedStyle(shortcodeBox);
    
    logResult('Current layout properties', 'pass',
        `Header: ${headerStyle.flexDirection}, Shortcode: ${shortcodeStyle.width}`);
}

testResponsiveBehavior();

// Test 5: Test page reload behavior
console.log('\n5. Page Reload Behavior Test:');

function testPageReloadBehavior() {
    console.log('\n   🔧 Page Reload Test Instructions:');
    console.log('   1. Note current shortcode box visibility');
    console.log('   2. Refresh the page (F5 or Ctrl+R)');
    console.log('   3. Check if shortcode visibility matches API configuration status');
    console.log('   4. If API is configured → shortcode should be visible');
    console.log('   5. If API is not configured → shortcode should be hidden');
    
    const currentShortcode = document.querySelector('.shortcode-box');
    const apiStatusText = document.getElementById('api-status-text');
    const isConfigured = apiStatusText && apiStatusText.textContent.includes('✅');
    
    logResult('Page reload consistency check', 'pass',
        `Current state: API ${isConfigured ? 'configured' : 'not configured'}, Shortcode ${currentShortcode ? 'visible' : 'hidden'}`);
}

testPageReloadBehavior();

// Test 6: Test deactivation/reactivation behavior
console.log('\n6. Deactivation/Reactivation Test:');

function testDeactivationBehavior() {
    console.log('\n   🔧 Deactivation/Reactivation Test Instructions:');
    console.log('   1. Configure a valid API key (shortcode should appear)');
    console.log('   2. Go to WordPress Admin → Plugins');
    console.log('   3. Deactivate the "My Tyre Finder" plugin');
    console.log('   4. Reactivate the plugin');
    console.log('   5. Return to Wheel-Size → API Settings');
    console.log('   6. Verify shortcode box is hidden (API should be reset to unconfigured)');
    console.log('   7. Re-validate API key');
    console.log('   8. Verify shortcode box appears after successful validation');
}

testDeactivationBehavior();

// Final summary
setTimeout(() => {
    console.log('\n=== Shortcode Block Visibility Test Summary ===');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⚠️ Warnings: ${testResults.warnings}`);
    
    const totalTests = testResults.passed + testResults.failed + testResults.warnings;
    const successRate = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
    
    console.log(`\n📊 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 All critical tests passed! Shortcode visibility logic is working correctly.');
    } else {
        console.log('\n⚠️ Some tests failed. Please review the failures above.');
    }
    
    console.log('\n📋 Expected Behavior Summary:');
    console.log('✅ When API not configured: Shortcode box hidden');
    console.log('✅ When API configured: Shortcode box visible');
    console.log('✅ During validation failure: Shortcode box gets hidden');
    console.log('✅ During validation success: Shortcode box appears');
    console.log('✅ After page reload: Visibility matches API configuration status');
    console.log('✅ After plugin reactivation: Shortcode hidden until API re-validated');
    
    console.log('\n🔧 Implementation Features:');
    console.log('- Conditional PHP rendering based on $is_configured');
    console.log('- JavaScript dynamic show/hide during validation');
    console.log('- Proper CSS styling and responsive design');
    console.log('- Consistent behavior across page reloads');
    console.log('- Integration with API validation gate');
    
}, 1000);

console.log('\n=== Shortcode visibility test initiated ===');
