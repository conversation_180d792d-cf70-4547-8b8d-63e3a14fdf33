/**
 * Test script for Live Preview selector width fix
 * 
 * This test verifies that:
 * 1. All selectors (Make, Model, Year, Modification) have 100% width in Live Preview
 * 2. No WordPress admin styles interfere with widget layout
 * 3. Flex containers maintain proper width
 * 4. Form containers don't constrain selector width
 * 
 * Run this in browser console on /wp-admin/admin.php?page=wheel-size-appearance
 */

console.log('🔍 Testing Live Preview Selector Width Fix...');

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    testLivePreviewWidths();
});

function testLivePreviewWidths() {
    console.log('📋 Starting Live Preview width tests...');
    
    // Test 1: Check if preview container exists
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.error('❌ Live Preview container not found');
        return;
    }
    console.log('✅ Live Preview container found');
    
    // Test 2: Check widget containers
    testWidgetContainers(previewContainer);
    
    // Test 3: Check selectors
    testSelectors(previewContainer);
    
    // Test 4: Check form containers
    testFormContainers(previewContainer);
    
    // Test 5: Check flex containers
    testFlexContainers(previewContainer);

    // Test 6: Check navigation steps
    testNavigationSteps(previewContainer);

    // Test 7: Check CSS rules
    testCSSRules();
    
    console.log('🎉 Live Preview width tests completed!');
}

function testWidgetContainers(container) {
    console.log('🏠 Testing widget containers...');

    const widgets = container.querySelectorAll('.wheel-fit-widget, .wsf-finder-widget');

    if (widgets.length === 0) {
        console.warn('⚠️ No widgets found in preview');
        return;
    }

    widgets.forEach((widget, index) => {
        const computedStyle = window.getComputedStyle(widget);
        const width = computedStyle.width;
        const maxWidth = computedStyle.maxWidth;

        console.log(`  📊 Widget #${index + 1}:`);
        console.log(`    Width: ${width}`);
        console.log(`    Max-width: ${maxWidth}`);
        console.log(`    Classes: ${Array.from(widget.classList).join(', ')}`);

        // Check if width is properly constrained
        const widthValue = parseFloat(width);
        const maxWidthValue = parseFloat(maxWidth);

        // Check if max-width is reasonable (between 400px and 900px)
        if (maxWidthValue >= 400 && maxWidthValue <= 900) {
            console.log(`    ✅ Widget max-width is reasonable: ${maxWidth}`);
        } else if (maxWidth === 'none') {
            console.warn(`    ⚠️ Widget has no max-width constraint`);
        } else {
            console.warn(`    ⚠️ Widget max-width might be too ${maxWidthValue > 900 ? 'large' : 'small'}: ${maxWidth}`);
        }

        // Check if widget is centered
        const marginLeft = computedStyle.marginLeft;
        const marginRight = computedStyle.marginRight;
        if (marginLeft === 'auto' && marginRight === 'auto') {
            console.log(`    ✅ Widget is centered`);
        } else {
            console.warn(`    ⚠️ Widget might not be centered: margin-left: ${marginLeft}, margin-right: ${marginRight}`);
        }
    });
}

function testSelectors(container) {
    console.log('🎛️ Testing selectors...');
    
    const selectors = container.querySelectorAll('select, .wsf-input, input[type="text"], input[type="search"]');
    
    if (selectors.length === 0) {
        console.warn('⚠️ No selectors found in preview');
        return;
    }
    
    selectors.forEach((selector, index) => {
        const computedStyle = window.getComputedStyle(selector);
        const width = computedStyle.width;
        const maxWidth = computedStyle.maxWidth;
        const minWidth = computedStyle.minWidth;
        const boxSizing = computedStyle.boxSizing;
        
        // Get parent container width for comparison
        const parent = selector.parentElement;
        const parentWidth = parent ? window.getComputedStyle(parent).width : 'unknown';
        
        console.log(`  📊 Selector #${index + 1} (${selector.tagName.toLowerCase()}${selector.id ? '#' + selector.id : ''}):`);
        console.log(`    Width: ${width}`);
        console.log(`    Max-width: ${maxWidth}`);
        console.log(`    Min-width: ${minWidth}`);
        console.log(`    Box-sizing: ${boxSizing}`);
        console.log(`    Parent width: ${parentWidth}`);
        console.log(`    Classes: ${Array.from(selector.classList).join(', ')}`);
        
        // Check if selector width matches parent
        if (parent) {
            const selectorWidth = parseFloat(width);
            const parentWidthValue = parseFloat(parentWidth);
            const widthRatio = selectorWidth / parentWidthValue;
            
            if (widthRatio < 0.95) {
                console.warn(`    ⚠️ Selector width constrained: ${(widthRatio * 100).toFixed(1)}% of parent`);
                
                // Check for interfering styles
                const interferingStyles = checkInterferingStyles(selector);
                if (interferingStyles.length > 0) {
                    console.warn(`    🚫 Interfering styles found:`, interferingStyles);
                }
            } else {
                console.log(`    ✅ Selector width looks good: ${(widthRatio * 100).toFixed(1)}% of parent`);
            }
        }
    });
}

function testFormContainers(container) {
    console.log('📝 Testing form containers...');
    
    const forms = container.querySelectorAll('form, .wsf-form-wrapper, .form-container');
    
    forms.forEach((form, index) => {
        const computedStyle = window.getComputedStyle(form);
        const width = computedStyle.width;
        const maxWidth = computedStyle.maxWidth;
        
        console.log(`  📊 Form container #${index + 1}:`);
        console.log(`    Width: ${width}`);
        console.log(`    Max-width: ${maxWidth}`);
        console.log(`    Classes: ${Array.from(form.classList).join(', ')}`);
    });
}

function testFlexContainers(container) {
    console.log('📐 Testing flex containers...');

    const flexContainers = container.querySelectorAll('.flex, .flex-col, .w-full, [class*="flex"]');

    flexContainers.forEach((flexContainer, index) => {
        const computedStyle = window.getComputedStyle(flexContainer);
        const display = computedStyle.display;
        const flexGrow = computedStyle.flexGrow;
        const flexShrink = computedStyle.flexShrink;
        const flexBasis = computedStyle.flexBasis;
        const width = computedStyle.width;

        if (display.includes('flex')) {
            console.log(`  📊 Flex container #${index + 1}:`);
            console.log(`    Display: ${display}`);
            console.log(`    Flex: ${flexGrow} ${flexShrink} ${flexBasis}`);
            console.log(`    Width: ${width}`);
            console.log(`    Classes: ${Array.from(flexContainer.classList).join(', ')}`);
        }
    });
}

function testNavigationSteps(container) {
    console.log('🧭 Testing navigation steps...');

    // Test wizard header
    const wizardHeader = container.querySelector('#wizard-header');
    if (wizardHeader) {
        const computedStyle = window.getComputedStyle(wizardHeader);
        const width = computedStyle.width;
        const maxWidth = computedStyle.maxWidth;

        console.log(`  📊 Wizard header:`);
        console.log(`    Width: ${width}`);
        console.log(`    Max-width: ${maxWidth}`);
    }

    // Test step names
    const stepNames = container.querySelectorAll('.wizard-step-name');
    if (stepNames.length > 0) {
        console.log(`  📊 Found ${stepNames.length} step names:`);
        stepNames.forEach((stepName, index) => {
            const computedStyle = window.getComputedStyle(stepName);
            const width = computedStyle.width;
            const maxWidth = computedStyle.maxWidth;

            console.log(`    Step ${index + 1}: width: ${width}, max-width: ${maxWidth}`);
        });
    }

    // Test makes grid
    const makesGrid = container.querySelector('#wizard-makes-grid');
    if (makesGrid) {
        const computedStyle = window.getComputedStyle(makesGrid);
        const width = computedStyle.width;
        const maxWidth = computedStyle.maxWidth;
        const justifyContent = computedStyle.justifyContent;

        console.log(`  📊 Makes grid:`);
        console.log(`    Width: ${width}`);
        console.log(`    Max-width: ${maxWidth}`);
        console.log(`    Justify-content: ${justifyContent}`);

        if (justifyContent === 'center') {
            console.log(`    ✅ Makes grid is centered`);
        } else {
            console.warn(`    ⚠️ Makes grid might not be centered`);
        }
    }
}

function testCSSRules() {
    console.log('🎨 Testing CSS rules...');
    
    const expectedRules = [
        '#widget-preview select',
        '#widget-preview .wsf-input',
        '#widget-preview .wheel-fit-widget',
        '#widget-preview .wsf-finder-widget'
    ];
    
    expectedRules.forEach(selector => {
        const hasRule = checkCSSRule(selector);
        if (hasRule) {
            console.log(`  ✅ CSS rule found: ${selector}`);
        } else {
            console.warn(`  ❌ CSS rule missing: ${selector}`);
        }
    });
}

function checkCSSRule(selector) {
    try {
        const styleSheets = Array.from(document.styleSheets);
        
        for (const sheet of styleSheets) {
            try {
                const rules = Array.from(sheet.cssRules || sheet.rules || []);
                for (const rule of rules) {
                    if (rule.selectorText && rule.selectorText.includes(selector)) {
                        console.log(`    📝 Found rule: ${rule.cssText}`);
                        return true;
                    }
                }
            } catch (e) {
                // CORS or other access issues
                continue;
            }
        }
    } catch (e) {
        console.warn(`  ⚠️ Could not check CSS rules: ${e.message}`);
    }
    
    return false;
}

function checkInterferingStyles(element) {
    const interferingStyles = [];
    const computedStyle = window.getComputedStyle(element);
    
    // Check for common interfering properties
    const checks = [
        { prop: 'width', expected: /100%|auto/, current: computedStyle.width },
        { prop: 'max-width', expected: /none|100%/, current: computedStyle.maxWidth },
        { prop: 'min-width', expected: /0px|auto/, current: computedStyle.minWidth },
        { prop: 'box-sizing', expected: /border-box/, current: computedStyle.boxSizing }
    ];
    
    checks.forEach(check => {
        if (!check.expected.test(check.current)) {
            interferingStyles.push(`${check.prop}: ${check.current}`);
        }
    });
    
    return interferingStyles;
}

// Manual test helper functions
window.testPreviewWidths = function() {
    console.log('🔧 Manual width test - checking all elements...');
    testLivePreviewWidths();
};

window.logSelectorStyles = function() {
    console.log('📊 Logging all selector styles...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.error('❌ Preview container not found');
        return;
    }
    
    const selectors = previewContainer.querySelectorAll('select, input, .wsf-input');
    selectors.forEach((selector, index) => {
        console.log(`Selector #${index + 1}:`, {
            element: selector,
            id: selector.id,
            classes: Array.from(selector.classList),
            computedStyle: {
                width: getComputedStyle(selector).width,
                maxWidth: getComputedStyle(selector).maxWidth,
                minWidth: getComputedStyle(selector).minWidth,
                boxSizing: getComputedStyle(selector).boxSizing,
                display: getComputedStyle(selector).display
            }
        });
    });
};

window.fixWidthIssues = function() {
    console.log('🔧 Applying emergency width fixes...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.error('❌ Preview container not found');
        return;
    }
    
    // Apply emergency styles
    const style = document.createElement('style');
    style.textContent = `
        #widget-preview select,
        #widget-preview .wsf-input,
        #widget-preview input {
            width: 100% !important;
            max-width: 100% !important;
            min-width: 0 !important;
            box-sizing: border-box !important;
        }
    `;
    document.head.appendChild(style);
    
    console.log('✅ Emergency width fixes applied');
};

console.log('🔧 Manual test functions available:');
console.log('  - testPreviewWidths() - Run all width tests');
console.log('  - logSelectorStyles() - Log detailed selector styles');
console.log('  - fixWidthIssues() - Apply emergency width fixes');
