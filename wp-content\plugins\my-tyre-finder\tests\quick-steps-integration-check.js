/**
 * Quick Steps Integration Check
 * 
 * Copy and paste this entire script into browser console on:
 * /wp-admin/admin.php?page=wheel-size-appearance
 */

console.log('📋 Quick Steps Integration Check...');

// Find elements
const form = document.querySelector('.wsf-form-wrapper');
const steps = document.querySelector('.flex.items-center.gap-3');
const title = document.querySelector('.wsf-widget__title, h1');
const widget = document.querySelector('.wheel-fit-widget, .wsf-finder-widget');

if (!form || !steps || !title || !widget) {
    console.error('❌ Required elements not found');
    console.log('Form:', !!form, 'Steps:', !!steps, 'Title:', !!title, 'Widget:', !!widget);
} else {
    console.log('✅ All elements found');
    
    console.log('\n📊 STEPS INTEGRATION CHECK:');
    
    // Check 1: Are steps inside form?
    const stepsInForm = form.contains(steps);
    const titleInForm = form.contains(title);
    
    console.log(`1. Steps inside form: ${stepsInForm ? '✅' : '❌'}`);
    console.log(`2. Title inside form: ${titleInForm ? '✅' : '❌'}`);
    
    if (stepsInForm && titleInForm) {
        // Check 3: DOM order
        const formChildren = Array.from(form.children);
        const titleIndex = formChildren.findIndex(child => child.contains(title));
        const stepsIndex = formChildren.findIndex(child => child === steps || child.contains(steps));
        
        const correctOrder = titleIndex < stepsIndex;
        
        console.log(`3. DOM order: ${correctOrder ? '✅' : '❌'}`);
        console.log(`   Title position: ${titleIndex + 1} of ${formChildren.length}`);
        console.log(`   Steps position: ${stepsIndex + 1} of ${formChildren.length}`);
        
        // Check 4: Visual positioning
        const titleRect = title.getBoundingClientRect();
        const stepsRect = steps.getBoundingClientRect();
        
        const stepsBelowTitle = stepsRect.top > titleRect.bottom;
        const gap = stepsRect.top - titleRect.bottom;
        
        console.log(`4. Visual position: ${stepsBelowTitle ? '✅' : '❌'}`);
        console.log(`   Gap between title and steps: ${gap.toFixed(1)}px`);
        
        // Check 5: Background inheritance
        const stepsStyle = window.getComputedStyle(steps);
        const formStyle = window.getComputedStyle(form);
        const widgetStyle = window.getComputedStyle(widget);
        
        const stepsBg = stepsStyle.backgroundColor;
        const formBg = formStyle.backgroundColor;
        const widgetBg = widgetStyle.backgroundColor;
        
        const isTransparent = stepsBg === 'rgba(0, 0, 0, 0)' || stepsBg === 'transparent';
        const bgInherited = isTransparent || stepsBg === formBg || stepsBg === widgetBg;
        
        console.log(`5. Background inheritance: ${bgInherited ? '✅' : '❌'}`);
        console.log(`   Steps bg: ${stepsBg}`);
        console.log(`   Form bg: ${formBg}`);
        console.log(`   Widget bg: ${widgetBg}`);
        
        // Check 6: Step indicators
        const stepIndicators = steps.querySelectorAll('[id^="step-indicator-"]');
        const stepTexts = steps.querySelectorAll('[id^="step-text-"]');
        
        console.log(`6. Step indicators: ${stepIndicators.length > 0 ? '✅' : '❌'}`);
        console.log(`   Found ${stepIndicators.length} indicators and ${stepTexts.length} texts`);
        
        // Check 7: Theme colors
        let themeColorsUsed = false;
        
        if (stepIndicators.length > 0) {
            const firstIndicator = stepIndicators[0];
            const indicatorStyle = window.getComputedStyle(firstIndicator);
            const bgColor = indicatorStyle.backgroundColor;
            
            // Check if it's using theme colors (not default browser colors)
            themeColorsUsed = bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent';
            
            console.log(`7. Theme colors: ${themeColorsUsed ? '✅' : '❌'}`);
            console.log(`   Indicator bg: ${bgColor}`);
        } else {
            console.log(`7. Theme colors: ❓ (no indicators to check)`);
        }
        
        // Check 8: Visual bounds
        const formRect = form.getBoundingClientRect();
        const stepsInsideBounds = stepsRect.left >= formRect.left && 
                                 stepsRect.right <= formRect.right &&
                                 stepsRect.top >= formRect.top &&
                                 stepsRect.bottom <= formRect.bottom;
        
        console.log(`8. Visual containment: ${stepsInsideBounds ? '✅' : '❌'}`);
        
        // Overall status
        const allGood = stepsInForm && titleInForm && correctOrder && 
                       stepsBelowTitle && bgInherited && stepIndicators.length > 0 && 
                       stepsInsideBounds;
        
        console.log(`\n🎯 OVERALL STATUS: ${allGood ? '✅ PERFECT INTEGRATION' : '❌ NEEDS ATTENTION'}`);
        
        if (!allGood) {
            console.log('\n💡 ISSUES TO FIX:');
            if (!stepsInForm) console.log('   - Move steps inside .wsf-form-wrapper');
            if (!titleInForm) console.log('   - Move title inside .wsf-form-wrapper');
            if (!correctOrder) console.log('   - Ensure title comes before steps in DOM');
            if (!stepsBelowTitle) console.log('   - Fix visual positioning');
            if (!bgInherited) console.log('   - Fix background inheritance');
            if (stepIndicators.length === 0) console.log('   - Check step indicators rendering');
            if (!stepsInsideBounds) console.log('   - Fix visual containment within form');
        }
        
        // Show expected vs actual structure
        console.log('\n🏗️ EXPECTED STRUCTURE:');
        console.log('Widget Container > Form Wrapper > [Title, Steps, Form Content]');
        
        console.log('\n🔍 ACTUAL STRUCTURE:');
        if (stepsInForm && titleInForm) {
            console.log('Widget Container > Form Wrapper > [Title ✅, Steps ✅, Form Content] ✅');
        } else {
            console.log('Widget Container > Form Wrapper > [Title ?, Steps ?, Form Content] ❌');
        }
        
        // Show form hierarchy
        console.log('\n📋 FORM CHILDREN ORDER:');
        formChildren.forEach((child, index) => {
            let description = 'Unknown element';
            
            if (child.contains(title)) {
                description = 'Widget Title ✅';
            } else if (child === steps || child.contains(steps)) {
                description = 'Steps Panel ✅';
            } else if (child.tagName === 'FORM') {
                description = 'Form Content';
            } else {
                const classes = Array.from(child.classList).slice(0, 2).join('.');
                description = `${child.tagName.toLowerCase()}.${classes}`;
            }
            
            console.log(`${index + 1}. ${description}`);
        });
        
    } else {
        console.log('\n❌ Elements are not properly placed inside form wrapper');
        
        if (!stepsInForm) {
            console.log('💡 Steps panel needs to be moved inside .wsf-form-wrapper');
        }
        
        if (!titleInForm) {
            console.log('💡 Title needs to be moved inside .wsf-form-wrapper');
        }
    }
    
    // Visual highlight
    console.log('\n🎨 Highlighting elements for 5 seconds...');
    
    // Widget in blue (outermost container)
    widget.style.outline = '3px solid blue';
    
    // Form in green (should contain title and steps)
    form.style.outline = '3px solid green';
    form.style.backgroundColor = 'rgba(0, 255, 0, 0.1)';
    
    // Title in yellow (should be first)
    title.style.outline = '3px solid orange';
    title.style.backgroundColor = 'rgba(255, 165, 0, 0.1)';
    
    // Steps in red (should be second)
    steps.style.outline = '3px solid red';
    steps.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
    
    setTimeout(() => {
        widget.style.outline = '';
        form.style.outline = '';
        form.style.backgroundColor = '';
        title.style.outline = '';
        title.style.backgroundColor = '';
        steps.style.outline = '';
        steps.style.backgroundColor = '';
        console.log('🧹 Highlights removed');
    }, 5000);
}
