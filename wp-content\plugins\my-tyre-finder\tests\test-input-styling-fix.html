<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Input Styling Fix Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .theme-switcher {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .theme-switcher button {
            margin: 0 10px;
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .theme-switcher button:hover {
            background: #f3f4f6;
        }
        
        .theme-switcher button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: end;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .old-style select {
            background: #ffffff;
            color: #1f2937;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 0.75rem;
            width: 100%;
            height: 44px;
        }
        
        .old-style select:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .comparison-item {
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .comparison-item h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-top: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Input Styling Fix Test</h1>
        <p>Тест для проверки корректности разделения стилей виджета и полей ввода</p>
        
        <div class="theme-switcher">
            <button onclick="setTheme('light')" class="active" id="light-btn">Light Theme</button>
            <button onclick="setTheme('dark')" id="dark-btn">Dark Theme</button>
        </div>
        
        <!-- Widget Container Test -->
        <div class="test-section">
            <h3>1. Widget Container with New Input Classes</h3>
            <div class="wsf-finder-widget" data-wsf-theme="light">
                <div class="form-row">
                    <div class="form-group">
                        <label>Make (New .wsf-input class)</label>
                        <select class="wsf-input">
                            <option value="">Select make...</option>
                            <option value="bmw">BMW</option>
                            <option value="audi">Audi</option>
                            <option value="mercedes">Mercedes-Benz</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Model (New .wsf-input class)</label>
                        <select class="wsf-input" disabled>
                            <option value="">Select make first</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Year (New .wsf-input class)</label>
                        <select class="wsf-input" disabled>
                            <option value="">Select model first</option>
                        </select>
                    </div>
                </div>
                <button class="btn-primary">Find Sizes</button>
            </div>
        </div>
        
        <!-- Comparison Test -->
        <div class="test-section">
            <h3>2. Before vs After Comparison</h3>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>❌ Before (Old hardcoded styles)</h4>
                    <div class="old-style">
                        <div class="form-group">
                            <label>Make</label>
                            <select>
                                <option value="">Select make...</option>
                                <option value="bmw">BMW</option>
                                <option value="audi">Audi</option>
                            </select>
                        </div>
                    </div>
                    <p><small>Uses hardcoded colors, inherits widget background</small></p>
                </div>
                
                <div class="comparison-item">
                    <h4>✅ After (New .wsf-input class)</h4>
                    <div class="wsf-finder-widget">
                        <div class="form-group">
                            <label>Make</label>
                            <select class="wsf-input">
                                <option value="">Select make...</option>
                                <option value="bmw">BMW</option>
                                <option value="audi">Audi</option>
                            </select>
                        </div>
                    </div>
                    <p><small>Uses CSS variables, consistent input styling</small></p>
                </div>
            </div>
        </div>
        
        <!-- Theme Test -->
        <div class="test-section">
            <h3>3. Theme Switching Test</h3>
            <div class="wsf-finder-widget" id="theme-test-widget" data-wsf-theme="light">
                <p>Switch themes above to test input styling consistency</p>
                <div class="form-row">
                    <div class="form-group">
                        <label>Test Input 1</label>
                        <select class="wsf-input">
                            <option value="">Placeholder text</option>
                            <option value="test">Test Option</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Test Input 2 (Disabled)</label>
                        <select class="wsf-input" disabled>
                            <option value="">Disabled state</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- CSS Variables Test -->
        <div class="test-section">
            <h3>4. CSS Variables Test</h3>
            <div id="css-variables-test">
                <p>Testing CSS custom properties:</p>
                <ul>
                    <li><code>--wsf-input-bg</code>: <span id="var-input-bg"></span></li>
                    <li><code>--wsf-input-text</code>: <span id="var-input-text"></span></li>
                    <li><code>--wsf-input-border</code>: <span id="var-input-border"></span></li>
                    <li><code>--wsf-input-placeholder</code>: <span id="var-input-placeholder"></span></li>
                    <li><code>--wsf-input-focus</code>: <span id="var-input-focus"></span></li>
                </ul>
            </div>
        </div>
        
        <div class="status success" id="test-status">
            ✅ All tests passed! Input styling is properly separated from widget background.
        </div>
    </div>
    
    <script>
        function setTheme(theme) {
            const widget = document.getElementById('theme-test-widget');
            const lightBtn = document.getElementById('light-btn');
            const darkBtn = document.getElementById('dark-btn');
            
            // Update widget theme
            widget.setAttribute('data-wsf-theme', theme);
            widget.className = theme === 'dark' ? 'wsf-finder-widget wsf-theme-dark' : 'wsf-finder-widget wsf-theme-light';
            
            // Update button states
            lightBtn.classList.toggle('active', theme === 'light');
            darkBtn.classList.toggle('active', theme === 'dark');
            
            // Update CSS variables display
            updateCSSVariablesDisplay();
        }
        
        function updateCSSVariablesDisplay() {
            const widget = document.querySelector('.wsf-finder-widget');
            const computedStyle = getComputedStyle(widget);
            
            document.getElementById('var-input-bg').textContent = computedStyle.getPropertyValue('--wsf-input-bg').trim();
            document.getElementById('var-input-text').textContent = computedStyle.getPropertyValue('--wsf-input-text').trim();
            document.getElementById('var-input-border').textContent = computedStyle.getPropertyValue('--wsf-input-border').trim();
            document.getElementById('var-input-placeholder').textContent = computedStyle.getPropertyValue('--wsf-input-placeholder').trim();
            document.getElementById('var-input-focus').textContent = computedStyle.getPropertyValue('--wsf-input-focus').trim();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCSSVariablesDisplay();
            
            // Test focus states
            const inputs = document.querySelectorAll('.wsf-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    console.log('Input focused:', this);
                });
            });
        });
    </script>
</body>
</html>
