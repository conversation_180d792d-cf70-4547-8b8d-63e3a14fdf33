/**
 * Dark Theme Activation Debug Script
 * Run this in browser console on the WordPress admin Appearance page
 */

(function() {
    'use strict';

    console.log('🌙 === DARK THEME ACTIVATION DEBUG ===');

    // Check prerequisites
    if (typeof wpApiSettings === 'undefined') {
        console.error('❌ wpApiSettings not found - run this on the admin Appearance page');
        return;
    }

    console.log('✅ wpApiSettings found:', wpApiSettings);

    const apiBase = wpApiSettings.root + 'wheel-size/v1/themes';

    // Step 1: Check if themes are loaded correctly
    async function checkThemeData() {
        console.log('\n1️⃣ Checking theme data...');
        
        try {
            const response = await fetch(apiBase, {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                console.error('❌ Failed to fetch themes:', response.status);
                return null;
            }

            const data = await response.json();
            console.log('✅ Themes loaded:', data);

            // Check if dark theme exists
            if (data.themes && data.themes.dark) {
                console.log('✅ Dark theme found:', data.themes.dark);
                return data;
            } else {
                console.error('❌ Dark theme not found in themes data');
                console.log('Available themes:', Object.keys(data.themes || {}));
                return data;
            }
        } catch (error) {
            console.error('❌ Error fetching themes:', error);
            return null;
        }
    }

    // Step 2: Test light theme activation (for comparison)
    async function testLightThemeActivation() {
        console.log('\n2️⃣ Testing light theme activation (for comparison)...');
        
        try {
            const response = await fetch(apiBase + '/active', {
                method: 'PUT',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ slug: 'light' })
            });

            console.log('Light theme response status:', response.status);
            console.log('Light theme response headers:', Object.fromEntries(response.headers.entries()));

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Light theme activation successful:', data);
                return true;
            } else {
                const errorText = await response.text();
                console.error('❌ Light theme activation failed:', response.status, errorText);
                return false;
            }
        } catch (error) {
            console.error('❌ Light theme activation error:', error);
            return false;
        }
    }

    // Step 3: Test dark theme activation with detailed logging
    async function testDarkThemeActivation() {
        console.log('\n3️⃣ Testing dark theme activation...');
        
        try {
            console.log('Sending request to:', apiBase + '/active');
            console.log('Request body:', JSON.stringify({ slug: 'dark' }));
            console.log('Request headers:', {
                'X-WP-Nonce': wpApiSettings.nonce,
                'Content-Type': 'application/json'
            });

            const response = await fetch(apiBase + '/active', {
                method: 'PUT',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ slug: 'dark' })
            });

            console.log('Dark theme response status:', response.status);
            console.log('Dark theme response headers:', Object.fromEntries(response.headers.entries()));

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Dark theme activation successful:', data);
                return true;
            } else {
                const errorText = await response.text();
                console.error('❌ Dark theme activation failed:', response.status, errorText);
                
                // Try to parse error as JSON for more details
                try {
                    const errorData = JSON.parse(errorText);
                    console.error('Error details:', errorData);
                } catch (e) {
                    console.error('Raw error response:', errorText);
                }
                return false;
            }
        } catch (error) {
            console.error('❌ Dark theme activation error:', error);
            return false;
        }
    }

    // Step 4: Check current active theme
    async function checkActiveTheme() {
        console.log('\n4️⃣ Checking current active theme...');
        
        try {
            const response = await fetch(apiBase + '/active', {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ Current active theme:', data);
                return data;
            } else {
                const errorText = await response.text();
                console.error('❌ Failed to get active theme:', response.status, errorText);
                return null;
            }
        } catch (error) {
            console.error('❌ Error getting active theme:', error);
            return null;
        }
    }

    // Step 5: Check WordPress options directly (if possible)
    function checkWordPressOptions() {
        console.log('\n5️⃣ Checking WordPress options...');
        
        // This will only work if we have access to WordPress admin AJAX
        if (typeof ajaxurl !== 'undefined') {
            console.log('✅ WordPress AJAX URL available:', ajaxurl);
            // We could make a custom AJAX call here to check options
        } else {
            console.log('⚠️ WordPress AJAX URL not available');
        }
    }

    // Step 6: Check browser network tab for additional info
    function checkNetworkRequests() {
        console.log('\n6️⃣ Network debugging tips...');
        console.log('💡 Open browser DevTools > Network tab');
        console.log('💡 Filter by "themes" to see API requests');
        console.log('💡 Look for failed requests or error responses');
        console.log('💡 Check request/response headers and body');
    }

    // Step 7: Simulate the exact click that's failing
    function simulateThemeCardClick() {
        console.log('\n7️⃣ Simulating dark theme card click...');
        
        const darkThemeCard = document.querySelector('[data-theme-slug="dark"]');
        if (darkThemeCard) {
            console.log('✅ Found dark theme card:', darkThemeCard);
            console.log('Card data attributes:', darkThemeCard.dataset);
            
            // Check if the card has the right event listeners
            console.log('💡 Try clicking the dark theme card now and watch the console');
            
            // Highlight the card
            darkThemeCard.style.border = '3px solid red';
            setTimeout(() => {
                darkThemeCard.style.border = '';
            }, 3000);
            
        } else {
            console.error('❌ Dark theme card not found');
            console.log('Available theme cards:', 
                Array.from(document.querySelectorAll('[data-theme-slug]'))
                    .map(card => card.dataset.themeSlug)
            );
        }
    }

    // Run all diagnostic steps
    async function runDiagnostic() {
        console.log('🚀 Starting dark theme diagnostic...\n');
        
        const themeData = await checkThemeData();
        const activeTheme = await checkActiveTheme();
        const lightTest = await testLightThemeActivation();
        const darkTest = await testDarkThemeActivation();
        
        checkWordPressOptions();
        checkNetworkRequests();
        simulateThemeCardClick();
        
        console.log('\n📊 === DIAGNOSTIC RESULTS ===');
        console.log(`Theme Data Loaded: ${themeData ? '✅ YES' : '❌ NO'}`);
        console.log(`Dark Theme Exists: ${themeData?.themes?.dark ? '✅ YES' : '❌ NO'}`);
        console.log(`Light Theme Works: ${lightTest ? '✅ YES' : '❌ NO'}`);
        console.log(`Dark Theme Works: ${darkTest ? '✅ YES' : '❌ NO'}`);
        console.log(`Current Active: ${activeTheme?.slug || 'UNKNOWN'}`);
        
        if (!darkTest && lightTest) {
            console.log('\n🔍 ANALYSIS: Light theme works but dark theme fails');
            console.log('This suggests a dark theme-specific issue');
        } else if (!darkTest && !lightTest) {
            console.log('\n🔍 ANALYSIS: Both themes fail');
            console.log('This suggests a general API or authentication issue');
        }
        
        console.log('\n💡 Next steps:');
        console.log('1. Check browser Network tab for failed requests');
        console.log('2. Look for PHP errors in WordPress debug log');
        console.log('3. Verify theme data integrity in database');
    }

    // Start the diagnostic
    runDiagnostic();

})();
