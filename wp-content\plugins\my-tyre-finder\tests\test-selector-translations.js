/**
 * Тест переводов селекторов
 * Добавьте этот скрипт в админ-панель для тестирования
 */

(function() {
    'use strict';

    console.log('[Selector Translation Test] Инициализация теста переводов селекторов');

    // Тестовые переводы
    const testTranslations = {
        en: {
            'select_make_placeholder': 'Choose a make',
            'select_model_placeholder': 'Choose a model',
            'select_year_placeholder': 'Choose a year',
            'select_mods_placeholder': 'Choose a modification',
            'select_gen_placeholder': 'Choose a generation',
            'button_search': 'Find Tire & Wheel Sizes'
        },
        ru: {
            'select_make_placeholder': 'Выберите марку',
            'select_model_placeholder': 'Выберите модель',
            'select_year_placeholder': 'Выберите год',
            'select_mods_placeholder': 'Выберите модификацию',
            'select_gen_placeholder': 'Выберите поколение',
            'button_search': 'Подобрать размеры'
        },
        de: {
            'select_make_placeholder': 'Marke wählen',
            'select_model_placeholder': 'Modell wählen',
            'select_year_placeholder': 'Jahr wählen',
            'select_mods_placeholder': 'Modifikation wählen',
            'select_gen_placeholder': 'Generation wählen',
            'button_search': 'Reifen- & Radgrößen finden'
        }
    };

    // Функция тестирования
    function testSelectorTranslations(locale = 'ru') {
        console.log(`[Selector Test] Тестирование переводов для языка: ${locale}`);
        
        // Устанавливаем переводы
        window.WheelFitI18n = testTranslations[locale] || testTranslations.en;
        console.log('[Selector Test] Установлены переводы:', window.WheelFitI18n);

        // Находим виджет
        const widget = document.querySelector('.wsf-finder-widget, #widget-preview');
        if (!widget) {
            console.error('[Selector Test] Виджет не найден');
            return;
        }

        // Тестируем селекторы
        const selectors = [
            { id: 'wf-make', key: 'select_make_placeholder' },
            { id: 'wf-model', key: 'select_model_placeholder' },
            { id: 'wf-year', key: 'select_year_placeholder' },
            { id: 'wf-modification', key: 'select_mods_placeholder' },
            { id: 'wf-generation', key: 'select_gen_placeholder' }
        ];

        console.log('[Selector Test] Проверяем селекторы...');
        
        selectors.forEach(({ id, key }) => {
            const select = document.getElementById(id);
            if (select) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    const currentText = placeholderOption.textContent.trim();
                    const expectedText = window.WheelFitI18n[key];
                    const hasDataI18n = placeholderOption.hasAttribute('data-i18n');
                    const dataI18nValue = placeholderOption.getAttribute('data-i18n');
                    
                    console.log(`[Selector Test] ${id}:`, {
                        currentText,
                        expectedText,
                        hasDataI18n,
                        dataI18nValue,
                        correct: currentText === expectedText
                    });
                    
                    if (currentText !== expectedText) {
                        console.warn(`[Selector Test] ❌ ${id}: ожидался "${expectedText}", получен "${currentText}"`);
                    } else {
                        console.log(`[Selector Test] ✅ ${id}: перевод корректен`);
                    }
                } else {
                    console.warn(`[Selector Test] ❌ ${id}: placeholder option не найден`);
                }
            } else {
                console.warn(`[Selector Test] ❌ ${id}: селектор не найден`);
            }
        });

        // Тестируем кнопку поиска
        const searchButton = widget.querySelector('[data-i18n="button_search"]');
        if (searchButton) {
            const currentText = searchButton.textContent.trim();
            const expectedText = window.WheelFitI18n['button_search'];
            console.log(`[Selector Test] Кнопка поиска:`, {
                currentText,
                expectedText,
                correct: currentText === expectedText
            });
            
            if (currentText !== expectedText) {
                console.warn(`[Selector Test] ❌ Кнопка: ожидался "${expectedText}", получен "${currentText}"`);
            } else {
                console.log(`[Selector Test] ✅ Кнопка: перевод корректен`);
            }
        } else {
            console.warn('[Selector Test] ❌ Кнопка поиска не найдена');
        }
    }

    // Функция симуляции populateSelect
    function testPopulateSelect() {
        console.log('[Selector Test] Тестирование функции populateSelect...');
        
        // Проверяем, доступна ли функция
        if (typeof window.wheelFitWidget === 'undefined' || !window.wheelFitWidget.populateSelect) {
            console.warn('[Selector Test] populateSelect недоступна, создаем тестовую');
            
            // Создаем тестовую функцию
            window.testPopulateSelect = function(elementId, options, placeholder) {
                const select = document.getElementById(elementId);
                if (!select) {
                    console.warn(`[Test populateSelect] Селектор ${elementId} не найден`);
                    return;
                }

                console.log(`[Test populateSelect] Заполняем ${elementId} с placeholder: "${placeholder}"`);

                // Определяем ключ перевода
                const translationKeyMap = {
                    'wf-make': 'select_make_placeholder',
                    'wf-model': 'select_model_placeholder',
                    'wf-year': 'select_year_placeholder',
                    'wf-modification': 'select_mods_placeholder',
                    'wf-generation': 'select_gen_placeholder'
                };

                const translationKey = translationKeyMap[elementId];
                
                // Создаем placeholder option
                const placeholderOption = document.createElement('option');
                placeholderOption.value = '';
                
                if (translationKey) {
                    placeholderOption.setAttribute('data-i18n', translationKey);
                    const translatedText = window.WheelFitI18n && window.WheelFitI18n[translationKey] 
                        ? window.WheelFitI18n[translationKey] 
                        : placeholder;
                    placeholderOption.textContent = translatedText;
                    console.log(`[Test populateSelect] Установлен ключ "${translationKey}": "${translatedText}"`);
                } else {
                    placeholderOption.textContent = placeholder;
                    console.log(`[Test populateSelect] Ключ не найден, используем прямой текст: "${placeholder}"`);
                }
                
                // Очищаем и добавляем
                select.innerHTML = '';
                select.appendChild(placeholderOption);
                
                // Добавляем тестовые опции
                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value || option.name;
                    optionElement.textContent = option.name;
                    select.appendChild(optionElement);
                });
                
                console.log(`[Test populateSelect] Заполнен ${elementId} успешно`);
            };
            
            // Тестируем с русскими переводами
            window.WheelFitI18n = testTranslations.ru;
            
            // Заполняем селекторы тестовыми данными
            window.testPopulateSelect('wf-make', [
                { name: 'BMW', value: 'bmw' },
                { name: 'Mercedes', value: 'mercedes' },
                { name: 'Audi', value: 'audi' }
            ], 'Choose a make');
            
            window.testPopulateSelect('wf-model', [], 'Select make first');
            
        } else {
            console.log('[Selector Test] populateSelect доступна в wheelFitWidget');
        }
    }

    // Функция применения переводов
    function forceApplyTranslations() {
        console.log('[Selector Test] Принудительное применение переводов...');
        
        if (typeof applyStaticTranslations === 'function') {
            applyStaticTranslations();
            console.log('[Selector Test] applyStaticTranslations выполнена');
        }
        
        if (typeof reapplyTranslations === 'function') {
            reapplyTranslations();
            console.log('[Selector Test] reapplyTranslations выполнена');
        }
        
        if (window.translationManager && typeof window.translationManager.applyTranslations === 'function') {
            window.translationManager.applyTranslations();
            console.log('[Selector Test] translationManager.applyTranslations выполнена');
        }
    }

    // Глобальные функции для тестирования
    window.testSelectorTranslations = testSelectorTranslations;
    window.testPopulateSelect = testPopulateSelect;
    window.forceApplyTranslations = forceApplyTranslations;

    // Автоматический тест через 2 секунды
    setTimeout(() => {
        console.log('[Selector Test] Запуск автоматического теста...');
        testSelectorTranslations('ru');
        
        setTimeout(() => {
            testPopulateSelect();
            setTimeout(() => {
                testSelectorTranslations('ru');
            }, 500);
        }, 1000);
    }, 2000);

    console.log('[Selector Translation Test] Тест загружен. Доступные функции:');
    console.log('- testSelectorTranslations("ru") - тест переводов');
    console.log('- testPopulateSelect() - тест заполнения селекторов');
    console.log('- forceApplyTranslations() - принудительное применение переводов');

})();
