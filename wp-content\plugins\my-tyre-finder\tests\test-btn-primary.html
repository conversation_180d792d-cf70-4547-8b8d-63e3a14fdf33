<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Primary Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .color-controls {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .color-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .color-input-group label {
            min-width: 120px;
            font-weight: 500;
        }
        .color-input-group input[type="color"] {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Button Primary Component Test</h1>
        <p>Тест нового универсального компонента .btn-primary для проверки правильной работы с --wsf-primary переменной.</p>

        <!-- Color Controls -->
        <div class="color-controls">
            <h3>🎨 Color Controls</h3>
            <div class="color-input-group">
                <label for="primary-color">Primary Color:</label>
                <input type="color" id="primary-color" value="#2563eb">
                <span id="primary-value">#2563eb</span>
            </div>
            <div class="color-input-group">
                <label for="hover-color">Hover Color:</label>
                <input type="color" id="hover-color" value="#1d4ed8">
                <span id="hover-value">#1d4ed8</span>
            </div>
        </div>

        <!-- Test Sections -->
        <div class="test-section">
            <h3>1. Основные состояния btn-primary</h3>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb); --wsf-hover: var(--test-hover, #1d4ed8);">
                <div class="button-grid">
                    <button type="submit" class="btn-primary">
                        Normal State
                    </button>
                    <button type="submit" class="btn-primary" disabled>
                        Disabled State
                    </button>
                    <button type="submit" class="btn-primary w-full">
                        Full Width
                    </button>
                    <button type="submit" class="btn-primary py-3 px-10">
                        Large Padding
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. Сравнение со старыми классами</h3>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb); --wsf-hover: var(--test-hover, #1d4ed8);">
                <div class="button-grid">
                    <button type="submit" class="btn-primary">
                        NEW: btn-primary
                    </button>
                    <button type="submit" class="bg-[color:var(--wsf-primary)] hover:bg-[color:var(--wsf-hover)] text-white font-semibold py-2.5 px-6 rounded-lg shadow-lg transition">
                        OLD: Long classes
                    </button>
                </div>
                <p style="margin-top: 15px; font-size: 14px; color: #666;">
                    Оба должны выглядеть одинаково, но btn-primary проще в использовании.
                </p>
            </div>
        </div>

        <div class="test-section">
            <h3>3. Интерактивные состояния</h3>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb); --wsf-hover: var(--test-hover, #1d4ed8);">
                <div class="button-grid">
                    <button type="submit" class="btn-primary" onmouseenter="showHoverInfo(this)" onmouseleave="hideHoverInfo(this)">
                        Hover Me
                    </button>
                    <button type="submit" class="btn-primary" onclick="this.focus()">
                        Click to Focus
                    </button>
                    <button type="submit" class="btn-primary" onclick="toggleDisabled(this)">
                        Toggle Disabled
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. Разные размеры</h3>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb); --wsf-hover: var(--test-hover, #1d4ed8);">
                <div class="button-grid">
                    <button type="submit" class="btn-primary text-sm py-1 px-3">
                        Small
                    </button>
                    <button type="submit" class="btn-primary">
                        Default
                    </button>
                    <button type="submit" class="btn-primary text-lg py-3 px-8">
                        Large
                    </button>
                    <button type="submit" class="btn-primary text-xl py-4 px-12">
                        Extra Large
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-results">
            <h3>🧪 Test Results</h3>
            <button onclick="runBtnPrimaryTest()" style="background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: 500;">
                Run btn-primary Test
            </button>
            <div id="test-results" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script>
        function updateColors() {
            const primaryColor = document.getElementById('primary-color').value;
            const hoverColor = document.getElementById('hover-color').value;
            
            document.getElementById('primary-value').textContent = primaryColor;
            document.getElementById('hover-value').textContent = hoverColor;
            
            // Apply to all test widgets
            document.documentElement.style.setProperty('--test-primary', primaryColor);
            document.documentElement.style.setProperty('--test-hover', hoverColor);
            
            // Also apply to widgets directly
            document.querySelectorAll('.wsf-finder-widget').forEach(widget => {
                widget.style.setProperty('--wsf-primary', primaryColor);
                widget.style.setProperty('--wsf-hover', hoverColor);
            });
        }

        function showHoverInfo(button) {
            const style = window.getComputedStyle(button);
            console.log('Hover state:', {
                backgroundColor: style.backgroundColor,
                transform: style.transform
            });
        }

        function hideHoverInfo(button) {
            // Reset any hover info
        }

        function toggleDisabled(button) {
            button.disabled = !button.disabled;
            button.textContent = button.disabled ? 'Disabled' : 'Toggle Disabled';
        }

        function runBtnPrimaryTest() {
            const results = [];
            
            // Test 1: Check if all btn-primary buttons have the same computed background color
            const btnPrimaryButtons = document.querySelectorAll('.btn-primary:not([disabled])');
            const buttonColors = Array.from(btnPrimaryButtons).map(btn => {
                const style = window.getComputedStyle(btn);
                return style.backgroundColor;
            });
            
            const uniqueButtonColors = [...new Set(buttonColors)];
            const buttonsConsistent = uniqueButtonColors.length === 1;
            
            // Test 2: Check disabled state
            const disabledButtons = document.querySelectorAll('.btn-primary[disabled]');
            const disabledCorrect = Array.from(disabledButtons).every(btn => {
                const style = window.getComputedStyle(btn);
                return parseFloat(style.opacity) < 1 && style.cursor === 'not-allowed';
            });
            
            // Test 3: Check CSS variable usage
            const testPrimary = document.documentElement.style.getPropertyValue('--test-primary');
            const expectedPrimary = document.getElementById('primary-color').value;
            
            // Display results
            const resultsDiv = document.getElementById('test-results');
            let html = '<h4>Test Results:</h4>';
            
            html += `<p><strong>Button Consistency:</strong> ${buttonsConsistent ? '✅ All btn-primary buttons same color' : '❌ Buttons have different colors'}</p>`;
            if (!buttonsConsistent) {
                html += `<p>Found colors: ${uniqueButtonColors.join(', ')}</p>`;
            }
            
            html += `<p><strong>Disabled States:</strong> ${disabledCorrect ? '✅ All disabled buttons correct' : '❌ Disabled buttons incorrect'}</p>`;
            
            html += `<p><strong>CSS Variables:</strong> ${testPrimary ? '✅ CSS variables working' : '❌ CSS variables not found'}</p>`;
            html += `<p>Expected: ${expectedPrimary}, Found: ${testPrimary}</p>`;
            
            const allPassed = buttonsConsistent && disabledCorrect && testPrimary;
            html += `<p><strong>Overall:</strong> ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}</p>`;
            
            if (allPassed) {
                html += '<p style="color: green; font-weight: bold;">🎉 btn-primary component is working correctly!</p>';
            }
            
            resultsDiv.innerHTML = html;
        }

        // Initialize
        document.getElementById('primary-color').addEventListener('change', updateColors);
        document.getElementById('hover-color').addEventListener('change', updateColors);
        updateColors();
    </script>
</body>
</html>
