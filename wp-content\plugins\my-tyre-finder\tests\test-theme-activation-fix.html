<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Activation Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
        }
        
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #1d4ed8;
        }
        
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .success {
            color: #16a34a;
            font-weight: bold;
        }
        
        .error {
            color: #dc2626;
            font-weight: bold;
        }
        
        .log {
            background: #1f2937;
            color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .theme-preview {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        
        .color-swatch {
            width: 30px;
            height: 30px;
            border-radius: 4px;
            border: 1px solid #ccc;
            display: inline-block;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .status.success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status.error {
            background: #fef2f2;
            color: #991b1b;
        }
        
        .status.info {
            background: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">Theme Activation Fix Test</h1>
            <p style="color: #6b7280;">Тестирование исправлений для активации пользовательских тем</p>
        </header>

        <div class="test-section">
            <h3>🔧 Диагностика API</h3>
            <button class="test-button" onclick="checkAPI()">Проверить API</button>
            <button class="test-button" onclick="getAllThemes()">Получить все темы</button>
            <button class="test-button" onclick="getActiveTheme()">Получить активную тему</button>
            <div id="api-status"></div>
        </div>

        <div class="test-section">
            <h3>🎨 Создание тестовой темы</h3>
            <div style="margin: 15px 0;">
                <label>Название темы: <input type="text" id="theme-name" value="Test Theme 123" style="margin-left: 10px; padding: 5px;"></label>
            </div>
            <div class="theme-preview">
                <div class="color-swatch" style="background: #ff6b35" title="Primary"></div>
                <div class="color-swatch" style="background: #ffffff" title="Background"></div>
                <div class="color-swatch" style="background: #333333" title="Text"></div>
            </div>
            <button class="test-button" onclick="createTestTheme()">Создать тему</button>
            <div id="create-status"></div>
        </div>

        <div class="test-section">
            <h3>⚡ Активация темы</h3>
            <div style="margin: 15px 0;">
                <label>Slug темы: <input type="text" id="activation-slug" placeholder="Введите slug темы" style="margin-left: 10px; padding: 5px;"></label>
            </div>
            <button class="test-button" onclick="activateTheme()">Активировать тему</button>
            <button class="test-button" onclick="activateTheme('light')">Активировать Light</button>
            <button class="test-button" onclick="activateTheme('dark')">Активировать Dark</button>
            <div id="activation-status"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Полный тест</h3>
            <button class="test-button" onclick="runFullTest()">Запустить полный тест</button>
            <div id="full-test-status"></div>
        </div>

        <div class="test-section">
            <h3>📋 Лог операций</h3>
            <button class="test-button" onclick="clearLog()">Очистить лог</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // Глобальные переменные
        let createdThemeSlug = null;
        
        // Функция логирования
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logDiv.innerHTML += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // Проверка API
        async function checkAPI() {
            log('Проверка доступности API...');
            
            if (typeof wpApiSettings === 'undefined') {
                log('wpApiSettings не найден - тест запущен вне WordPress админки', 'error');
                setStatus('api-status', 'API недоступен - запустите тест в админке WordPress', 'error');
                return false;
            }
            
            log(`API Base: ${wpApiSettings.root}wheel-size/v1/themes`);
            log(`Nonce доступен: ${!!wpApiSettings.nonce}`);
            setStatus('api-status', 'API доступен', 'success');
            return true;
        }
        
        // Получить все темы
        async function getAllThemes() {
            if (!await checkAPI()) return;
            
            try {
                log('Получение всех тем...');
                const response = await fetch(wpApiSettings.root + 'wheel-size/v1/themes', {
                    headers: { 'X-WP-Nonce': wpApiSettings.nonce }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                log(`Получено тем: ${Object.keys(data.themes).length}`, 'success');
                log(`Темы: ${Object.keys(data.themes).join(', ')}`);
                setStatus('api-status', `Найдено тем: ${Object.keys(data.themes).length}`, 'success');
                return data;
            } catch (error) {
                log(`Ошибка получения тем: ${error.message}`, 'error');
                setStatus('api-status', `Ошибка: ${error.message}`, 'error');
                return null;
            }
        }
        
        // Получить активную тему
        async function getActiveTheme() {
            if (!await checkAPI()) return;
            
            try {
                log('Получение активной темы...');
                const response = await fetch(wpApiSettings.root + 'wheel-size/v1/themes/active', {
                    headers: { 'X-WP-Nonce': wpApiSettings.nonce }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                log(`Активная тема: ${data.active_theme}`, 'success');
                setStatus('api-status', `Активная тема: ${data.active_theme}`, 'info');
                return data;
            } catch (error) {
                log(`Ошибка получения активной темы: ${error.message}`, 'error');
                return null;
            }
        }
        
        // Создать тестовую тему
        async function createTestTheme() {
            if (!await checkAPI()) return;
            
            const name = document.getElementById('theme-name').value.trim();
            if (!name) {
                log('Введите название темы', 'error');
                setStatus('create-status', 'Введите название темы', 'error');
                return;
            }
            
            const themeData = {
                name: name,
                properties: {
                    '--wsf-primary': '#ff6b35',
                    '--wsf-bg': '#ffffff',
                    '--wsf-text': '#333333',
                    '--wsf-border': '#e1e5e9',
                    '--wsf-hover': '#e55a2b',
                    '--wsf-secondary': '#6b7280',
                    '--wsf-accent': '#3b82f6',
                    '--wsf-muted': '#9ca3af',
                    '--wsf-success': '#10b981',
                    '--wsf-warning': '#f59e0b',
                    '--wsf-error': '#ef4444'
                }
            };
            
            try {
                log(`Создание темы "${name}"...`);
                const response = await fetch(wpApiSettings.root + 'wheel-size/v1/themes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': wpApiSettings.nonce
                    },
                    body: JSON.stringify(themeData)
                });
                
                const responseText = await response.text();
                log(`Response: ${responseText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }
                
                const data = JSON.parse(responseText);
                createdThemeSlug = data.slug;
                document.getElementById('activation-slug').value = createdThemeSlug;
                
                log(`Тема создана успешно: ${createdThemeSlug}`, 'success');
                setStatus('create-status', `Тема создана: ${createdThemeSlug}`, 'success');
                return data;
            } catch (error) {
                log(`Ошибка создания темы: ${error.message}`, 'error');
                setStatus('create-status', `Ошибка: ${error.message}`, 'error');
                return null;
            }
        }
        
        // Активировать тему
        async function activateTheme(slug = null) {
            if (!await checkAPI()) return;
            
            if (!slug) {
                slug = document.getElementById('activation-slug').value.trim();
            }
            
            if (!slug) {
                log('Введите slug темы для активации', 'error');
                setStatus('activation-status', 'Введите slug темы', 'error');
                return;
            }
            
            try {
                log(`Активация темы "${slug}"...`);
                const response = await fetch(wpApiSettings.root + 'wheel-size/v1/themes/active', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': wpApiSettings.nonce
                    },
                    body: JSON.stringify({ slug: slug })
                });
                
                const responseText = await response.text();
                log(`Response: ${responseText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }
                
                const data = JSON.parse(responseText);
                log(`Тема активирована успешно: ${data.active_theme}`, 'success');
                setStatus('activation-status', `Тема активирована: ${data.active_theme}`, 'success');
                return data;
            } catch (error) {
                log(`Ошибка активации темы: ${error.message}`, 'error');
                setStatus('activation-status', `Ошибка: ${error.message}`, 'error');
                return null;
            }
        }
        
        // Полный тест
        async function runFullTest() {
            log('=== ЗАПУСК ПОЛНОГО ТЕСТА ===');
            setStatus('full-test-status', 'Выполняется полный тест...', 'info');
            
            // 1. Проверка API
            if (!await checkAPI()) {
                setStatus('full-test-status', 'Тест прерван - API недоступен', 'error');
                return;
            }
            
            // 2. Получение существующих тем
            const themes = await getAllThemes();
            if (!themes) {
                setStatus('full-test-status', 'Тест прерван - не удалось получить темы', 'error');
                return;
            }
            
            // 3. Создание тестовой темы
            const created = await createTestTheme();
            if (!created) {
                setStatus('full-test-status', 'Тест прерван - не удалось создать тему', 'error');
                return;
            }
            
            // 4. Активация созданной темы
            const activated = await activateTheme(created.slug);
            if (!activated) {
                setStatus('full-test-status', 'Тест прерван - не удалось активировать тему', 'error');
                return;
            }
            
            // 5. Проверка активации
            const activeCheck = await getActiveTheme();
            if (activeCheck && activeCheck.active_theme === created.slug) {
                log('=== ПОЛНЫЙ ТЕСТ ЗАВЕРШЕН УСПЕШНО ===', 'success');
                setStatus('full-test-status', 'Все тесты пройдены успешно!', 'success');
            } else {
                log('=== ТЕСТ ЗАВЕРШЕН С ОШИБКОЙ ===', 'error');
                setStatus('full-test-status', 'Тема создана, но активация не подтверждена', 'error');
            }
        }
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            log('Тест активации тем загружен');
            checkAPI();
        });
    </script>
</body>
</html>
