/**
 * Финальный тест исправления переводов селекторов
 * Проверяет, что все функции используют переводы напрямую
 */

(function() {
    'use strict';

    console.log('[Final Fix Test] 🎯 Финальный тест исправления...');

    // Функция проверки состояния селекторов
    function checkAllSelectors() {
        console.log('[Final Fix Test] === Проверка всех селекторов ===');
        
        const selectors = [
            { id: 'wf-make', key: 'select_make_placeholder' },
            { id: 'wf-model', key: 'select_model_placeholder' },
            { id: 'wf-year', key: 'select_year_placeholder' },
            { id: 'wf-modification', key: 'select_mods_placeholder' },
            { id: 'wf-generation', key: 'select_gen_placeholder' }
        ];
        
        let russianCount = 0;
        let englishCount = 0;
        let totalCount = 0;
        
        selectors.forEach(({ id, key }) => {
            const select = document.getElementById(id);
            if (select) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    totalCount++;
                    const text = placeholderOption.textContent.trim();
                    const dataI18n = placeholderOption.getAttribute('data-i18n');
                    
                    const isRussian = text.includes('Выберите') || text.includes('Сначала') || text.includes('Загрузка');
                    const isEnglish = text.includes('Choose') || text.includes('Select') || text.includes('Loading');
                    
                    if (isRussian) russianCount++;
                    if (isEnglish) englishCount++;
                    
                    const status = isRussian ? '✅ RU' : isEnglish ? '❌ EN' : '⚠️ OTHER';
                    console.log(`[Final Fix Test] ${id}: ${status} "${text}" (data-i18n: ${dataI18n})`);
                }
            }
        });
        
        console.log(`[Final Fix Test] Итого: ${russianCount}/${totalCount} русских, ${englishCount}/${totalCount} английских`);
        
        if (englishCount === 0 && russianCount > 0) {
            console.log('[Final Fix Test] 🎉 ВСЕ СЕЛЕКТОРЫ НА РУССКОМ - ИСПРАВЛЕНИЕ РАБОТАЕТ!');
            return true;
        } else if (englishCount > 0) {
            console.error('[Final Fix Test] ❌ ЕСТЬ АНГЛИЙСКИЕ СЕЛЕКТОРЫ - ИСПРАВЛЕНИЕ НЕ РАБОТАЕТ!');
            return false;
        } else {
            console.warn('[Final Fix Test] ⚠️ НЕОПРЕДЕЛЕННОЕ СОСТОЯНИЕ');
            return false;
        }
    }

    // Функция проверки переводов
    function checkTranslations() {
        console.log('[Final Fix Test] === Проверка переводов ===');
        
        if (!window.WheelFitI18n) {
            console.error('[Final Fix Test] ❌ window.WheelFitI18n недоступен!');
            return false;
        }
        
        const requiredKeys = [
            'select_make_placeholder',
            'select_model_placeholder', 
            'select_year_placeholder',
            'select_mods_placeholder',
            'select_gen_placeholder',
            'select_make_first_placeholder',
            'select_model_first_placeholder',
            'select_year_first_placeholder',
            'select_gen_first_placeholder'
        ];
        
        let availableCount = 0;
        requiredKeys.forEach(key => {
            const value = window.WheelFitI18n[key];
            const available = !!value;
            if (available) availableCount++;
            
            console.log(`[Final Fix Test] ${available ? '✅' : '❌'} ${key}: "${value}"`);
        });
        
        console.log(`[Final Fix Test] Доступно переводов: ${availableCount}/${requiredKeys.length}`);
        return availableCount > 0;
    }

    // Функция тестирования функций populate
    function testPopulateFunctions() {
        console.log('[Final Fix Test] === Тест функций populate ===');
        
        if (!window.wheelFitWidget) {
            console.error('[Final Fix Test] ❌ window.wheelFitWidget недоступен!');
            return false;
        }
        
        const widget = window.wheelFitWidget;
        
        // Тестируем populateMakes
        if (typeof widget.populateMakes === 'function') {
            console.log('[Final Fix Test] Тестируем populateMakes...');
            widget.populateMakes([
                { name: 'BMW', slug: 'bmw' },
                { name: 'Mercedes', slug: 'mercedes' }
            ]);
            
            setTimeout(() => {
                const makeSelect = document.getElementById('wf-make');
                if (makeSelect) {
                    const option = makeSelect.querySelector('option[value=""]');
                    if (option) {
                        const text = option.textContent.trim();
                        const isRussian = text.includes('Выберите');
                        console.log(`[Final Fix Test] populateMakes результат: ${isRussian ? '✅' : '❌'} "${text}"`);
                    }
                }
            }, 100);
        }
        
        // Тестируем populateModels
        if (typeof widget.populateModels === 'function') {
            console.log('[Final Fix Test] Тестируем populateModels...');
            widget.populateModels([
                { name: 'X5', slug: 'x5' },
                { name: 'X3', slug: 'x3' }
            ]);
            
            setTimeout(() => {
                const modelSelect = document.getElementById('wf-model');
                if (modelSelect) {
                    const option = modelSelect.querySelector('option[value=""]');
                    if (option) {
                        const text = option.textContent.trim();
                        const isRussian = text.includes('Выберите');
                        console.log(`[Final Fix Test] populateModels результат: ${isRussian ? '✅' : '❌'} "${text}"`);
                    }
                }
            }, 100);
        }
        
        return true;
    }

    // Функция мониторинга изменений настроек
    function monitorSettingsChanges() {
        console.log('[Final Fix Test] === Мониторинг изменений настроек ===');
        
        document.addEventListener('change', function(e) {
            if (e.target && (e.target.id === 'search_flow' || e.target.id === 'form_layout')) {
                console.log(`[Final Fix Test] 🔄 Изменение ${e.target.id}: ${e.target.value}`);
                
                // Проверяем через разные интервалы
                setTimeout(() => {
                    console.log('[Final Fix Test] Проверка через 1 секунду:');
                    checkAllSelectors();
                }, 1000);
                
                setTimeout(() => {
                    console.log('[Final Fix Test] Проверка через 3 секунды:');
                    const result = checkAllSelectors();
                    
                    if (result) {
                        console.log('[Final Fix Test] 🎉 УСПЕХ! Исправление работает!');
                    } else {
                        console.error('[Final Fix Test] 💥 ПРОВАЛ! Исправление не работает!');
                    }
                }, 3000);
            }
        });
        
        console.log('[Final Fix Test] Мониторинг настроен. Измените настройки для проверки.');
    }

    // Основная функция тестирования
    function runFinalTest() {
        console.log('[Final Fix Test] 🚀 ЗАПУСК ФИНАЛЬНОГО ТЕСТА');
        
        // 1. Проверяем переводы
        console.log('[Final Fix Test] 1. Проверка переводов...');
        const translationsOk = checkTranslations();
        
        // 2. Проверяем текущее состояние
        console.log('[Final Fix Test] 2. Текущее состояние селекторов...');
        checkAllSelectors();
        
        // 3. Тестируем функции populate
        if (translationsOk) {
            setTimeout(() => {
                console.log('[Final Fix Test] 3. Тест функций populate...');
                testPopulateFunctions();
            }, 1000);
        }
        
        // 4. Запускаем мониторинг
        setTimeout(() => {
            console.log('[Final Fix Test] 4. Запуск мониторинга...');
            monitorSettingsChanges();
        }, 2000);
        
        console.log('[Final Fix Test] ✅ Финальный тест готов');
    }

    // Глобальные функции
    window.finalFixTest = {
        run: runFinalTest,
        checkSelectors: checkAllSelectors,
        checkTranslations: checkTranslations,
        testPopulate: testPopulateFunctions,
        monitor: monitorSettingsChanges
    };

    // Автоматический запуск
    setTimeout(() => {
        console.log('[Final Fix Test] Автоматический запуск финального теста...');
        runFinalTest();
    }, 1000);

    console.log('[Final Fix Test] Финальный тест загружен. Доступные функции:');
    console.log('- finalFixTest.run() - полный тест');
    console.log('- finalFixTest.checkSelectors() - проверка селекторов');

})();
