/**
 * Ана<PERSON>из вертикального выравнивания админ-панели WordPress
 * Запустите в консоли браузера на странице настроек плагина
 */

(function() {
    'use strict';

    console.log('📐 === АНАЛИЗ ВЕРТИКАЛЬНОГО ВЫРАВНИВАНИЯ ===');

    // Анализ левой колонки (Form Configuration)
    function analyzeLeftColumn() {
        console.log('\n1️⃣ Анализ левой колонки (Form Configuration)...');
        
        // Ищем основные элементы левой колонки
        const selectors = [
            '.wrap',
            '.wrap h1',
            '.wrap h2', 
            '.form-table',
            '.wsf-admin-grid',
            '.wsf-admin-grid > div:first-child'
        ];
        
        selectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                const styles = window.getComputedStyle(element);
                console.log(`${selector}:`, {
                    marginTop: styles.marginTop,
                    marginBottom: styles.marginBottom,
                    paddingTop: styles.paddingTop,
                    paddingBottom: styles.paddingBottom,
                    position: styles.position,
                    display: styles.display
                });
            } else {
                console.log(`${selector}: не найден`);
            }
        });
    }

    // Анализ правой колонки (Theme Presets)
    function analyzeRightColumn() {
        console.log('\n2️⃣ Анализ правой колонки (Theme Presets)...');
        
        const selectors = [
            '.wsf-theme-panel',
            '.wsf-theme-panel__title',
            '.wsf-admin-grid__sidebar'
        ];
        
        selectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                const styles = window.getComputedStyle(element);
                console.log(`${selector}:`, {
                    marginTop: styles.marginTop,
                    marginBottom: styles.marginBottom,
                    paddingTop: styles.paddingTop,
                    paddingBottom: styles.paddingBottom,
                    position: styles.position,
                    display: styles.display
                });
            } else {
                console.log(`${selector}: не найден`);
            }
        });
    }

    // Измерение фактических позиций элементов
    function measureActualPositions() {
        console.log('\n3️⃣ Измерение фактических позиций...');
        
        // Ищем заголовки обеих колонок
        const leftTitle = document.querySelector('.wrap h1, .wrap h2');
        const rightTitle = document.querySelector('.wsf-theme-panel__title');
        
        if (leftTitle && rightTitle) {
            const leftRect = leftTitle.getBoundingClientRect();
            const rightRect = rightTitle.getBoundingClientRect();
            
            console.log('Позиции заголовков:');
            console.log('Левый заголовок:', {
                top: leftRect.top,
                left: leftRect.left,
                text: leftTitle.textContent.trim()
            });
            console.log('Правый заголовок:', {
                top: rightRect.top,
                left: rightRect.left,
                text: rightTitle.textContent.trim()
            });
            
            const verticalDiff = Math.abs(leftRect.top - rightRect.top);
            console.log(`Вертикальная разница: ${verticalDiff.toFixed(1)}px`);
            
            if (verticalDiff > 5) {
                console.log('❌ Заголовки НЕ выровнены по вертикали');
                if (leftRect.top < rightRect.top) {
                    console.log(`💡 Правый заголовок ниже на ${verticalDiff.toFixed(1)}px`);
                } else {
                    console.log(`💡 Левый заголовок ниже на ${verticalDiff.toFixed(1)}px`);
                }
            } else {
                console.log('✅ Заголовки выровнены по вертикали');
            }
            
            return verticalDiff;
        } else {
            console.log('⚠️ Не удалось найти заголовки для сравнения');
            return null;
        }
    }

    // Анализ сетки
    function analyzeGrid() {
        console.log('\n4️⃣ Анализ сетки...');
        
        const grid = document.querySelector('.wsf-admin-grid');
        if (grid) {
            const styles = window.getComputedStyle(grid);
            console.log('Сетка:', {
                display: styles.display,
                gridTemplateColumns: styles.gridTemplateColumns,
                gap: styles.gap,
                alignItems: styles.alignItems
            });
            
            const children = grid.children;
            console.log(`Количество колонок: ${children.length}`);
            
            Array.from(children).forEach((child, index) => {
                const childStyles = window.getComputedStyle(child);
                console.log(`Колонка ${index + 1}:`, {
                    marginTop: childStyles.marginTop,
                    paddingTop: childStyles.paddingTop,
                    className: child.className
                });
            });
        } else {
            console.log('⚠️ Сетка .wsf-admin-grid не найдена');
        }
    }

    // Проверка медиа-запросов
    function checkMediaQueries() {
        console.log('\n5️⃣ Проверка медиа-запросов...');
        
        const width = window.innerWidth;
        console.log(`Ширина экрана: ${width}px`);
        
        if (width >= 1200) {
            console.log('📱 Режим: Desktop (двухколоночная сетка)');
        } else {
            console.log('📱 Режим: Mobile (одноколоночный layout)');
        }
        
        // Проверяем CSS медиа-запросы
        const mediaQuery = window.matchMedia('(max-width: 1200px)');
        console.log('Media query (max-width: 1200px):', mediaQuery.matches ? 'активен' : 'неактивен');
    }

    // Создание визуальных направляющих
    function createVisualGuides() {
        console.log('\n6️⃣ Создание визуальных направляющих...');
        
        // Удаляем существующие направляющие
        const existingGuides = document.querySelectorAll('.alignment-guide');
        existingGuides.forEach(guide => guide.remove());
        
        const leftTitle = document.querySelector('.wrap h1, .wrap h2');
        const rightTitle = document.querySelector('.wsf-theme-panel__title');
        
        if (leftTitle && rightTitle) {
            const leftRect = leftTitle.getBoundingClientRect();
            const rightRect = rightTitle.getBoundingClientRect();
            
            // Создаем горизонтальные линии
            [leftRect, rightRect].forEach((rect, index) => {
                const guide = document.createElement('div');
                guide.className = 'alignment-guide';
                guide.style.cssText = `
                    position: fixed;
                    top: ${rect.top}px;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background: ${index === 0 ? '#007cba' : '#d63638'};
                    z-index: 9999;
                    pointer-events: none;
                    opacity: 0.7;
                `;
                document.body.appendChild(guide);
            });
            
            // Создаем подписи
            const label = document.createElement('div');
            label.className = 'alignment-guide';
            label.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 10000;
                pointer-events: none;
            `;
            label.innerHTML = `
                <div>🔵 Левый заголовок</div>
                <div>🔴 Правый заголовок</div>
                <div>Разница: ${Math.abs(leftRect.top - rightRect.top).toFixed(1)}px</div>
            `;
            document.body.appendChild(label);
            
            console.log('✅ Визуальные направляющие созданы');
            console.log('💡 Синяя линия - левый заголовок, красная - правый');
            
            // Автоматически удаляем через 10 секунд
            setTimeout(() => {
                const guides = document.querySelectorAll('.alignment-guide');
                guides.forEach(guide => guide.remove());
                console.log('🧹 Визуальные направляющие удалены');
            }, 10000);
        }
    }

    // Автоматическая настройка выравнивания
    function autoAdjustAlignment(verticalDiff) {
        console.log('\n🔧 === АВТОМАТИЧЕСКАЯ НАСТРОЙКА ===');

        if (verticalDiff === null || verticalDiff <= 5) {
            console.log('✅ Выравнивание в норме, настройка не требуется');
            return;
        }

        const leftTitle = document.querySelector('.wrap h1, .wrap h2');
        const rightTitle = document.querySelector('.wsf-theme-panel__title');
        const leftRect = leftTitle.getBoundingClientRect();
        const rightRect = rightTitle.getBoundingClientRect();

        let adjustment = 0;

        if (rightRect.top > leftRect.top) {
            adjustment = -Math.ceil(verticalDiff);
            console.log(`🔧 Правая колонка ниже на ${verticalDiff.toFixed(1)}px`);
        } else {
            adjustment = Math.ceil(verticalDiff);
            console.log(`🔧 Левая колонка ниже на ${verticalDiff.toFixed(1)}px`);
        }

        // Применяем CSS переменную
        document.documentElement.style.setProperty('--wsf-admin-column-top', `${adjustment}px`);

        console.log(`✅ Применена CSS переменная: --wsf-admin-column-top: ${adjustment}px`);
        console.log('💡 Для постоянного исправления добавьте в CSS:');
        console.log(`   :root { --wsf-admin-column-top: ${adjustment}px; }`);

        // Проверяем результат через небольшую задержку
        setTimeout(() => {
            const newVerticalDiff = measureActualPositions();
            if (newVerticalDiff !== null && newVerticalDiff <= 5) {
                console.log('🎉 Выравнивание исправлено автоматически!');
            } else {
                console.log('⚠️ Требуется дополнительная настройка');
            }
        }, 100);
    }

    // Рекомендации по исправлению
    function generateRecommendations(verticalDiff) {
        console.log('\n💡 === РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ ===');

        if (verticalDiff === null) {
            console.log('⚠️ Не удалось измерить разницу в выравнивании');
            return;
        }

        if (verticalDiff <= 5) {
            console.log('✅ Выравнивание в норме, дополнительные действия не требуются');
            return;
        }

        const leftTitle = document.querySelector('.wrap h1, .wrap h2');
        const rightTitle = document.querySelector('.wsf-theme-panel__title');
        const leftRect = leftTitle.getBoundingClientRect();
        const rightRect = rightTitle.getBoundingClientRect();

        if (rightRect.top > leftRect.top) {
            const adjustment = Math.ceil(verticalDiff);
            console.log(`🔧 Правая колонка ниже на ${verticalDiff.toFixed(1)}px`);
            console.log(`📝 Рекомендуемое исправление:`);
            console.log(`   :root { --wsf-admin-column-top: -${adjustment}px; }`);
        } else {
            const adjustment = Math.ceil(verticalDiff);
            console.log(`🔧 Левая колонка ниже на ${verticalDiff.toFixed(1)}px`);
            console.log(`📝 Рекомендуемое исправление:`);
            console.log(`   :root { --wsf-admin-column-top: ${adjustment}px; }`);
        }

        console.log('\n🎯 Нажмите Enter в консоли для автоматической настройки');

        // Добавляем глобальную функцию для быстрого исправления
        window.fixAlignment = () => autoAdjustAlignment(verticalDiff);
    }

    // Основная функция
    function runAnalysis() {
        console.log('🚀 Запуск анализа вертикального выравнивания...\n');
        
        analyzeLeftColumn();
        analyzeRightColumn();
        const verticalDiff = measureActualPositions();
        analyzeGrid();
        checkMediaQueries();
        createVisualGuides();
        generateRecommendations(verticalDiff);

        console.log('\n🏁 Анализ завершен. Проверьте визуальные направляющие на странице.');
        console.log('💡 Для автоматического исправления выполните: fixAlignment()');
    }

    // Запускаем анализ
    runAnalysis();

})();
