<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Button Test</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Simulate the critical styles that should be loaded by WordPress -->
    <style id="wheel-fit-critical-button-styles">
        /* 🎨 Critical btn-secondary styles - maximum specificity */
        .btn-secondary,
        button.btn-secondary,
        #garage-clear-all {
            background-color: transparent !important;
            background-image: none !important;
            color: #ef4444 !important;
            font-weight: 500 !important;
            font-size: 14px !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
            padding: 8px 12px !important;
            border-radius: 6px !important;
            border: 1px solid #f3f4f6 !important;
            transition: all 0.15s ease !important;
            cursor: pointer !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 6px !important;
            text-decoration: none !important;
            box-shadow: none !important;
            outline: none !important;
            vertical-align: middle !important;
            line-height: 1.4 !important;
            white-space: nowrap !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            word-spacing: normal !important;
            text-indent: 0 !important;
            text-shadow: none !important;
            text-align: center !important;
        }

        .btn-secondary:hover:not(:disabled),
        button.btn-secondary:hover:not(:disabled),
        #garage-clear-all:hover:not(:disabled) {
            background-color: #f3f4f6 !important;
            border-color: #ef4444 !important;
            color: #dc2626 !important;
            transform: none !important;
        }

        .btn-secondary:focus,
        button.btn-secondary:focus,
        #garage-clear-all:focus {
            outline: none !important;
            box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
        }

        .btn-secondary:disabled,
        button.btn-secondary:disabled,
        #garage-clear-all:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .btn-secondary i,
        button.btn-secondary i,
        #garage-clear-all i {
            width: 16px !important;
            height: 16px !important;
            flex-shrink: 0 !important;
            margin: 0 !important;
        }

        /* Responsive styles for mobile devices */
        @media (max-width: 640px) {
            .btn-secondary,
            button.btn-secondary,
            #garage-clear-all {
                padding: 8px !important;
                min-width: 40px !important;
            }
            
            .btn-secondary span,
            button.btn-secondary span,
            #garage-clear-all span {
                display: none !important;
            }
            
            .btn-secondary i,
            button.btn-secondary i,
            #garage-clear-all i {
                margin: 0 !important;
            }
        }
    </style>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 2rem;
            background: #f0f0f1;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
        }
        
        .success {
            background: #d1fae5;
            border-color: #10b981;
        }
        
        .warning {
            background: #fef3c7;
            border-color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend Button Test</h1>
        
        <div class="test-section success">
            <h2>✅ Styled Buttons (Should look good)</h2>
            <p>These buttons should have red text, transparent background, and light border:</p>
            
            <button class="btn-secondary" style="margin: 0.5rem;">
                <i data-lucide="trash-2"></i>
                <span>Clear all (class)</span>
            </button>
            
            <button id="garage-clear-all" style="margin: 0.5rem;">
                <i data-lucide="trash-2"></i>
                <span>Clear all (ID)</span>
            </button>
        </div>
        
        <div class="test-section warning">
            <h2>⚠️ Unstyled Button (For comparison)</h2>
            <p>This button should look like a default browser button:</p>
            
            <button style="margin: 0.5rem;">
                <i data-lucide="trash-2"></i>
                <span>Unstyled button</span>
            </button>
        </div>
        
        <div class="test-section">
            <h2>🔍 Instructions</h2>
            <ol>
                <li><strong>Compare this page</strong> with what you see in WordPress</li>
                <li><strong>If WordPress buttons look like the unstyled button</strong> - styles aren't loading</li>
                <li><strong>If WordPress buttons look like the styled buttons</strong> - styles are working!</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🔧 Next Steps</h2>
            <p>If styles aren't working in WordPress:</p>
            <ul>
                <li>Check browser DevTools → Elements → Look for <code>&lt;style id="wheel-fit-critical-button-styles"&gt;</code></li>
                <li>Check browser DevTools → Console for any JavaScript errors</li>
                <li>Verify the hooks are registered correctly in <code>Frontend.php</code></li>
            </ul>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Log button styles for debugging
        setTimeout(() => {
            const styledButton = document.querySelector('.btn-secondary');
            const unstyledButton = document.querySelector('button:not([class]):not([id])');
            
            if (styledButton) {
                const computed = window.getComputedStyle(styledButton);
                console.log('Styled button:', {
                    backgroundColor: computed.backgroundColor,
                    color: computed.color,
                    border: computed.border,
                    fontSize: computed.fontSize
                });
            }
            
            if (unstyledButton) {
                const computed = window.getComputedStyle(unstyledButton);
                console.log('Unstyled button:', {
                    backgroundColor: computed.backgroundColor,
                    color: computed.color,
                    border: computed.border,
                    fontSize: computed.fontSize
                });
            }
        }, 100);
    </script>
</body>
</html>
