/**
 * Скрипт для массового обновления CSS классов в шаблонах
 * Заменяет жестко заданные цвета на CSS переменные
 */

const fs = require('fs');
const path = require('path');

// Карта замен классов
const classReplacements = {
    // Фон
    'bg-white': 'bg-wsf-bg',
    'bg-slate-50': 'bg-wsf-bg',
    'bg-slate-100': 'bg-wsf-surface',

    // Текст
    'text-slate-900': 'text-wsf-text',
    'text-slate-800': 'text-wsf-text',
    'text-slate-700': 'text-wsf-text',
    'text-slate-600': 'text-wsf-muted',
    'text-slate-500': 'text-wsf-muted',
    'text-slate-400': 'text-wsf-muted',

    // Основные кнопки
    'bg-blue-600': 'btn-primary',
    'hover:bg-blue-700': '', // удаляем, так как btn-primary уже включает hover
    'bg-blue-700': 'btn-primary',

    // Границы
    'border-slate-300': 'border-wsf-border',
    'border-slate-200': 'border-wsf-border',
    'border-b': 'border-b border-wsf-border',
    'border-blue-600': 'border-wsf-primary',
    'border-blue-700': 'border-wsf-primary',
    'border-b-2 border-blue-600': 'border-b-2 border-wsf-primary',

    // Фокус
    'focus:ring-blue-500': 'focus:ring-wsf-primary',
    'focus:border-blue-500': 'focus:border-wsf-primary',
    'ring-blue-500': 'ring-wsf-primary',
    'focus:ring-blue-500/40': 'focus:ring-wsf-primary/40',

    // Hover состояния
    'hover:bg-slate-100': 'hover:bg-wsf-surface',
    'hover:text-slate-700': 'hover:text-wsf-text',
    'hover:text-slate-600': 'hover:text-wsf-muted',
    'hover:text-blue-600': 'hover:text-wsf-primary',

    // Disabled состояния - НЕ меняем цвет, только прозрачность
    'disabled:bg-blue-600': '', // удаляем - за disabled отвечает CSS
    'disabled:bg-blue-300': '', // удаляем - за disabled отвечает CSS
    'disabled:text-white': '', // удаляем - за disabled отвечает CSS
    'disabled:opacity-50': 'disabled:opacity-50',
    'disabled:cursor-not-allowed': 'disabled:cursor-not-allowed',

    // Дополнительные текстовые цвета
    'text-blue-700': 'text-wsf-primary',
    'text-blue-600': 'text-wsf-primary',
    'text-gray-50': 'text-wsf-surface',
    'text-gray-600': 'text-wsf-muted',
    'text-gray-800': 'text-wsf-text',

    // Дополнительные фоновые цвета
    'bg-gray-50': 'bg-wsf-surface',
    'bg-gray-200': 'bg-wsf-surface',
};

// Файлы для обновления
const templateFiles = [
    'templates/finder-form.twig',
    'templates/finder-form-inline.twig',
    'templates/finder-form-flow.twig',
    'templates/finder-popup-horizontal.twig',
    'templates/finder-wizard.twig',
    'templates/admin-theme-preview.html'
];

function updateFile(filePath) {
    try {
        console.log(`Обновление файла: ${filePath}`);

        if (!fs.existsSync(filePath)) {
            console.log(`⚠️ Файл не найден: ${filePath}`);
            return false;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        let changed = false;

        // Специальная обработка кнопок: bg-white на кнопках заменяем на btn-primary
        const buttonBgWhiteRegex = /<button[^>]*class="[^"]*bg-white[^"]*"[^>]*>/g;
        const buttonMatches = content.match(buttonBgWhiteRegex);
        if (buttonMatches) {
            buttonMatches.forEach(match => {
                // Заменяем bg-white на btn-primary в кнопках
                const newMatch = match.replace(/\bbg-white\b/g, 'btn-primary');
                // Удаляем дублирующие hover/focus классы, которые уже включены в btn-primary
                const cleanMatch = newMatch
                    .replace(/\bhover:bg-blue-\d+\b/g, '')
                    .replace(/\bfocus:ring-blue-\d+\b/g, '')
                    .replace(/\s+/g, ' ') // убираем лишние пробелы
                    .trim();

                if (match !== cleanMatch) {
                    content = content.replace(match, cleanMatch);
                    changed = true;
                    console.log(`  ✅ Кнопка: bg-white → btn-primary`);
                }
            });
        }

        // Применяем остальные замены
        Object.entries(classReplacements).forEach(([oldClass, newClass]) => {
            if (oldClass === 'bg-white') return; // уже обработали выше для кнопок

            const regex = new RegExp(`\\b${oldClass.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
            if (content.match(regex)) {
                if (newClass === '') {
                    // Удаляем класс
                    content = content.replace(regex, '');
                    // Убираем лишние пробелы
                    content = content.replace(/\s+/g, ' ');
                } else {
                    content = content.replace(regex, newClass);
                }
                changed = true;
                console.log(`  ✅ ${oldClass} → ${newClass || '(удален)'}`);
            }
        });

        if (changed) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ Файл обновлен: ${filePath}`);
            return true;
        } else {
            console.log(`ℹ️ Изменения не требуются: ${filePath}`);
            return false;
        }

    } catch (error) {
        console.error(`❌ Ошибка при обновлении ${filePath}:`, error.message);
        return false;
    }
}

function main() {
    console.log('🎨 === ОБНОВЛЕНИЕ CSS ПЕРЕМЕННЫХ ===\n');
    
    let totalFiles = 0;
    let updatedFiles = 0;
    
    templateFiles.forEach(file => {
        totalFiles++;
        if (updateFile(file)) {
            updatedFiles++;
        }
        console.log(''); // Пустая строка для разделения
    });
    
    console.log('📊 === РЕЗУЛЬТАТЫ ===');
    console.log(`Всего файлов: ${totalFiles}`);
    console.log(`Обновлено: ${updatedFiles}`);
    console.log(`Без изменений: ${totalFiles - updatedFiles}`);
    
    if (updatedFiles > 0) {
        console.log('\n💡 Следующие шаги:');
        console.log('1. Проверьте изменения в файлах');
        console.log('2. Запустите: npm run build:widget');
        console.log('3. Протестируйте виджеты');
    }
}

// Запуск скрипта
if (require.main === module) {
    main();
}

module.exports = { updateFile, classReplacements };
