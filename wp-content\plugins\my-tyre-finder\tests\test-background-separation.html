<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Separation Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            font-size: 20px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }
        
        .demo-item {
            padding: 20px;
            border: 2px solid #d1d5db;
            border-radius: 10px;
            background: white;
        }
        
        .demo-item h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: #374151;
        }
        
        .token-demo {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #e5e7eb;
        }
        
        .widget-demo {
            background: var(--wsf-bg);
            color: var(--wsf-text-primary);
        }
        
        .input-demo {
            background: var(--wsf-input-bg);
            color: var(--wsf-input-text);
            border-color: var(--wsf-input-border);
        }
        
        .surface-demo {
            background: var(--wsf-surface);
            color: var(--wsf-text-primary);
            border-color: var(--wsf-border-light);
        }
        
        .demo-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .demo-field {
            display: flex;
            flex-direction: column;
        }
        
        .demo-label {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 5px;
            color: var(--wsf-text);
        }
        
        .demo-select {
            height: 44px;
            padding: 0 12px;
            border: 1px solid var(--wsf-input-border);
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            background: var(--wsf-input-bg);
            color: var(--wsf-input-text);
        }
        
        .demo-select:focus {
            border-color: var(--wsf-input-focus);
            box-shadow: 0 0 0 1px var(--wsf-input-focus);
            outline: none;
        }
        
        .demo-button {
            height: 44px;
            padding: 0 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--wsf-primary) !important;
            color: var(--wsf-text-inverse) !important;
        }
        
        .demo-button:hover:not(:disabled) {
            background: var(--wsf-hover) !important;
        }
        
        .demo-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .garage-demo {
            background: var(--wsf-surface);
            border: 1px solid var(--wsf-border-light);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .garage-demo h5 {
            margin-top: 0;
            color: var(--wsf-text-primary);
        }
        
        .garage-item {
            background: var(--wsf-bg);
            border: 1px solid var(--wsf-border);
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
        }
        
        .color-swatch {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            border: 2px solid #e5e7eb;
            display: inline-block;
            margin-right: 10px;
            vertical-align: middle;
        }
        
        .swatch-bg { background: var(--wsf-bg); }
        .swatch-input-bg { background: var(--wsf-input-bg); }
        .swatch-surface { background: var(--wsf-surface); }
        
        .theme-switcher {
            margin: 20px 0;
            text-align: center;
        }
        
        .theme-switcher button {
            margin: 0 10px;
            padding: 10px 20px;
            border: 2px solid #d1d5db;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 600;
        }
        
        .theme-switcher button:hover {
            background: #f3f4f6;
        }
        
        .theme-switcher button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .explanation {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .explanation h4 {
            margin-top: 0;
            color: #0c4a6e;
        }
        
        .token-list {
            list-style: none;
            padding: 0;
        }
        
        .token-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .token-list li:last-child {
            border-bottom: none;
        }
        
        .token-code {
            font-family: 'Courier New', monospace;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-right: 10px;
            min-width: 120px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .real-widget-container {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Background Separation Test</h1>
        <p>Проверка корректного разделения фонов: виджет (--wsf-bg), инпуты (--wsf-input-bg), панели/гараж (--wsf-surface)</p>
        
        <div class="theme-switcher">
            <button onclick="setTheme('light')" class="active" id="light-btn">Light Theme</button>
            <button onclick="setTheme('dark')" id="dark-btn">Dark Theme</button>
        </div>
        
        <!-- Explanation -->
        <div class="explanation">
            <h4>🎯 Цель разделения фонов</h4>
            <p><strong>Проблема:</strong> Раньше все элементы использовали <code>--wsf-bg</code>, что не позволяло создать визуальную иерархию.</p>
            <p><strong>Решение:</strong> Разделили на три уровня:</p>
            <ul class="token-list">
                <li>
                    <span class="color-swatch swatch-bg"></span>
                    <code class="token-code">--wsf-bg</code>
                    <span>Основной фон виджета и результатов</span>
                </li>
                <li>
                    <span class="color-swatch swatch-input-bg"></span>
                    <code class="token-code">--wsf-input-bg</code>
                    <span>Фон полей ввода (селекты, инпуты)</span>
                </li>
                <li>
                    <span class="color-swatch swatch-surface"></span>
                    <code class="token-code">--wsf-surface</code>
                    <span>Фон панелей, модалок, гаража</span>
                </li>
            </ul>
        </div>
        
        <!-- Token Demonstration -->
        <div class="test-section">
            <h3>Демонстрация токенов</h3>
            <div class="demo-grid">
                <div class="demo-item">
                    <h4>🏠 Widget Background (--wsf-bg)</h4>
                    <div class="token-demo widget-demo">
                        <p>Основной фон виджета</p>
                        <p>Используется для корневого контейнера и секций результатов</p>
                        <code>background: var(--wsf-bg)</code>
                    </div>
                </div>
                
                <div class="demo-item">
                    <h4>📝 Input Background (--wsf-input-bg)</h4>
                    <div class="token-demo input-demo">
                        <p>Фон полей ввода</p>
                        <p>Используется для селектов, инпутов, текстовых полей</p>
                        <code>background: var(--wsf-input-bg)</code>
                    </div>
                </div>
                
                <div class="demo-item">
                    <h4>🗂️ Surface Background (--wsf-surface)</h4>
                    <div class="token-demo surface-demo">
                        <p>Фон панелей и модалок</p>
                        <p>Используется для гаража, дропдаунов, всплывающих панелей</p>
                        <code>background: var(--wsf-surface)</code>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Real Widget Test -->
        <div class="test-section">
            <h3>Тест с реальным виджетом</h3>
            <div class="real-widget-container">
                <div class="wsf-finder-widget" id="test-widget" data-wsf-theme="light">
                    <div style="padding: 20px;">
                        <h4 style="margin-top: 0; color: var(--wsf-text-primary);">Wheel Size Finder</h4>
                        <div class="demo-form">
                            <div class="demo-field">
                                <label class="demo-label">Make</label>
                                <select class="demo-select">
                                    <option value="">Select make...</option>
                                    <option value="bmw">BMW</option>
                                    <option value="audi">Audi</option>
                                </select>
                            </div>
                            <div class="demo-field">
                                <label class="demo-label">Model</label>
                                <select class="demo-select" disabled>
                                    <option value="">Select make first</option>
                                </select>
                            </div>
                            <button class="demo-button" disabled>Find Sizes</button>
                        </div>
                        
                        <!-- Garage Demo -->
                        <div class="garage-demo">
                            <h5>🚗 My Garage (Surface Background)</h5>
                            <div class="garage-item">
                                <strong>BMW X5 2020</strong><br>
                                <small>255/50R19</small>
                            </div>
                            <div class="garage-item">
                                <strong>Audi A4 2019</strong><br>
                                <small>225/45R17</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- CSS Variables Display -->
        <div class="test-section">
            <h3>Текущие значения CSS переменных</h3>
            <div id="css-variables-display">
                <div class="demo-grid">
                    <div class="demo-item">
                        <h4>Background Tokens:</h4>
                        <ul class="token-list">
                            <li>
                                <code class="token-code">--wsf-bg:</code>
                                <span id="var-bg"></span>
                            </li>
                            <li>
                                <code class="token-code">--wsf-input-bg:</code>
                                <span id="var-input-bg"></span>
                            </li>
                            <li>
                                <code class="token-code">--wsf-surface:</code>
                                <span id="var-surface"></span>
                            </li>
                        </ul>
                    </div>
                    <div class="demo-item">
                        <h4>Text & Border Tokens:</h4>
                        <ul class="token-list">
                            <li>
                                <code class="token-code">--wsf-text:</code>
                                <span id="var-text"></span>
                            </li>
                            <li>
                                <code class="token-code">--wsf-input-text:</code>
                                <span id="var-input-text"></span>
                            </li>
                            <li>
                                <code class="token-code">--wsf-border-light:</code>
                                <span id="var-border-light"></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="status success">
            ✅ Background токен теперь правильно разделен на три уровня!<br>
            🏠 Widget: основной фон | 📝 Input: поля ввода | 🗂️ Surface: панели/гараж
        </div>
    </div>
    
    <script>
        function setTheme(theme) {
            const widget = document.getElementById('test-widget');
            const lightBtn = document.getElementById('light-btn');
            const darkBtn = document.getElementById('dark-btn');
            
            // Update widget theme
            widget.setAttribute('data-wsf-theme', theme);
            
            if (theme === 'dark') {
                widget.classList.add('wsf-theme-dark');
            } else {
                widget.classList.remove('wsf-theme-dark');
            }
            
            // Update button states
            lightBtn.classList.toggle('active', theme === 'light');
            darkBtn.classList.toggle('active', theme === 'dark');
            
            // Update CSS variables display
            updateCSSVariablesDisplay();
        }
        
        function updateCSSVariablesDisplay() {
            const widget = document.getElementById('test-widget');
            const computedStyle = getComputedStyle(widget);
            
            document.getElementById('var-bg').textContent = computedStyle.getPropertyValue('--wsf-bg').trim() || 'Not defined';
            document.getElementById('var-input-bg').textContent = computedStyle.getPropertyValue('--wsf-input-bg').trim() || 'Not defined';
            document.getElementById('var-surface').textContent = computedStyle.getPropertyValue('--wsf-surface').trim() || 'Not defined';
            document.getElementById('var-text').textContent = computedStyle.getPropertyValue('--wsf-text').trim() || 'Not defined';
            document.getElementById('var-input-text').textContent = computedStyle.getPropertyValue('--wsf-input-text').trim() || 'Not defined';
            document.getElementById('var-border-light').textContent = computedStyle.getPropertyValue('--wsf-border-light').trim() || 'Not defined';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCSSVariablesDisplay();
            
            // Test button enabling
            const makeSelect = document.querySelector('#test-widget select');
            const button = document.querySelector('#test-widget button');
            
            if (makeSelect && button) {
                makeSelect.addEventListener('change', function() {
                    if (this.value) {
                        button.disabled = false;
                        button.textContent = 'Find Sizes (Enabled)';
                    } else {
                        button.disabled = true;
                        button.textContent = 'Find Sizes';
                    }
                });
            }
        });
    </script>
</body>
</html>
