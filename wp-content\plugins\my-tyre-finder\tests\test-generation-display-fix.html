<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generation Display Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
        select { padding: 5px; margin: 5px; min-width: 200px; }
    </style>
</head>
<body>
    <h1>Generation Display Fix Test</h1>
    <p>This test verifies that generation names are displayed correctly instead of "Generation" placeholder.</p>

    <div class="test-section">
        <h2>Test 1: Single Generation with Internal ID but Proper Name</h2>
        <p>Generation data: <code>{ id: "561918f13a", slug: "561918f13a", name: "GB" }</code></p>
        <select id="test1-select">
            <option value="">Choose a generation</option>
        </select>
        <div id="test1-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Single Generation with Only Internal ID</h2>
        <p>Generation data: <code>{ id: "7f8e9d2c1b", slug: "7f8e9d2c1b", name: null }</code></p>
        <select id="test2-select">
            <option value="">Choose a generation</option>
        </select>
        <div id="test2-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Single Generation with Readable Data</h2>
        <p>Generation data: <code>{ id: "gen_8x", slug: "8x", name: "8X", title: "Generation 8X" }</code></p>
        <select id="test3-select">
            <option value="">Choose a generation</option>
        </select>
        <div id="test3-result"></div>
    </div>

    <div class="test-section">
        <h2>Test Results Summary</h2>
        <div id="summary-result"></div>
    </div>

    <script>
        // Test data
        const testGenerations = {
            test1: {
                id: "561918f13a",
                slug: "561918f13a", 
                name: "GB",
                title: null,
                range: null
            },
            test2: {
                id: "7f8e9d2c1b",
                slug: "7f8e9d2c1b",
                name: null,
                title: null,
                range: null
            },
            test3: {
                id: "gen_8x",
                slug: "8x",
                name: "8X",
                title: "Generation 8X",
                range: "2019-2023"
            }
        };

        // Helper function to detect internal IDs (copied from actual code)
        function isInternalId(str) {
            if (!str || typeof str !== 'string') return false;
            return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
        }

        // Simulate the improved populateGenerations logic
        function getDisplayLabel(g) {
            let displayLabel = null;
            
            // Check priority fields, excluding internal IDs
            const candidateFields = ['name', 'title', 'range', 'year_range', 'gen'];
            for (const field of candidateFields) {
                if (g[field] && !isInternalId(g[field])) {
                    displayLabel = g[field];
                    break;
                }
            }
            
            // Check slug if no good name found
            if (!displayLabel && g.slug && !isInternalId(g.slug)) {
                displayLabel = g.slug;
            }
            
            // Check id if still no name found
            if (!displayLabel && g.id && !isInternalId(g.id)) {
                displayLabel = g.id;
            }
            
            // Final fallback - use any available data instead of generic "Generation"
            if (!displayLabel) {
                const anyField = g.name || g.title || g.range || g.year_range || g.gen || g.slug || g.id;
                if (anyField) {
                    displayLabel = anyField; // Use actual data even if it's an internal ID
                } else {
                    displayLabel = 'Unknown Generation';
                }
            }
            
            return displayLabel;
        }

        // Run tests
        function runTest(testId, generation) {
            const select = document.getElementById(`${testId}-select`);
            const resultDiv = document.getElementById(`${testId}-result`);
            
            // Get display label using improved logic
            const displayLabel = getDisplayLabel(generation);
            const value = generation.slug || generation.id || generation.name || generation.title || generation.range || displayLabel;
            
            // Add option to select
            select.add(new Option(displayLabel, value));
            
            // Auto-select for single generation (simulate the real behavior)
            select.value = value;
            
            // Check results
            const isGenericPlaceholder = displayLabel === 'Generation';
            const selectedText = select.options[select.selectedIndex]?.text;
            
            let resultHTML = `
                <p><strong>Display Label:</strong> "${displayLabel}"</p>
                <p><strong>Selected Text:</strong> "${selectedText}"</p>
                <p><strong>Value:</strong> "${value}"</p>
                <p><strong>Is Internal ID:</strong> ${isInternalId(displayLabel)}</p>
            `;
            
            if (isGenericPlaceholder) {
                resultHTML += `<p class="error">❌ FAILED: Shows generic "Generation" placeholder</p>`;
                return false;
            } else {
                resultHTML += `<p class="success">✅ PASSED: Shows actual generation data</p>`;
                return true;
            }
            
            resultDiv.innerHTML = resultHTML;
        }

        // Run all tests
        let allPassed = true;
        
        Object.keys(testGenerations).forEach(testId => {
            const passed = runTest(testId, testGenerations[testId]);
            allPassed = allPassed && passed;
        });

        // Show summary
        const summaryDiv = document.getElementById('summary-result');
        if (allPassed) {
            summaryDiv.innerHTML = `
                <p class="success">✅ ALL TESTS PASSED!</p>
                <p>The generation display fix is working correctly.</p>
                <p>Users will now see actual generation data instead of generic "Generation" placeholder.</p>
            `;
        } else {
            summaryDiv.innerHTML = `
                <p class="error">❌ SOME TESTS FAILED</p>
                <p>Please review the logic and ensure all cases are handled correctly.</p>
            `;
        }
    </script>
</body>
</html>
