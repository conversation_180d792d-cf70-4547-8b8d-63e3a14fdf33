/**
 * Тест перевода "Searching..." на разные языки
 */

(function() {
    'use strict';

    console.log('[Searching Translation Test] Инициализация теста...');

    // Тестовые переводы для всех языков
    const testTranslations = {
        en: {
            'searching': 'Searching...',
            'button_search': 'Find Tire & Wheel Sizes'
        },
        ru: {
            'searching': 'Поиск...',
            'button_search': 'Подобрать размеры'
        },
        de: {
            'searching': 'Suche läuft...',
            'button_search': 'Größen finden'
        },
        es: {
            'searching': 'Buscando...',
            'button_search': 'Buscar tamaños'
        },
        fr: {
            'searching': 'Recherche en cours...',
            'button_search': 'Trouver les tailles'
        }
    };

    // Функция тестирования перевода "Searching..."
    function testSearchingTranslation() {
        console.log('[Searching Translation Test] === Тестирование перевода "Searching..." ===');

        Object.keys(testTranslations).forEach(lang => {
            console.log(`\n[Searching Translation Test] Тестируем язык: ${lang.toUpperCase()}`);
            
            // Устанавливаем переводы для языка
            window.WheelFitI18n = testTranslations[lang];
            
            // Тестируем функцию t()
            if (typeof t === 'function') {
                const searchingText = t('searching', 'Searching...');
                const expectedText = testTranslations[lang]['searching'];
                
                const isCorrect = searchingText === expectedText;
                console.log(`  t('searching'): "${searchingText}" ${isCorrect ? '✅' : '❌'}`);
                console.log(`  ожидалось: "${expectedText}"`);
                
                if (!isCorrect) {
                    console.error(`  ❌ ОШИБКА: перевод не соответствует ожидаемому!`);
                }
            } else {
                console.warn(`  ⚠️ Функция t() недоступна`);
            }
        });
    }

    // Функция симуляции поиска
    function simulateSearch() {
        console.log('[Searching Translation Test] === Симуляция поиска ===');
        
        // Устанавливаем русские переводы
        window.WheelFitI18n = testTranslations.ru;
        
        // Ищем элементы поиска
        const searchText = document.getElementById('search-text');
        const searchLoader = document.getElementById('search-loader');
        
        if (!searchText) {
            console.warn('[Searching Translation Test] ⚠️ Элемент #search-text не найден');
            return;
        }
        
        console.log('[Searching Translation Test] Найден элемент search-text');
        console.log('[Searching Translation Test] Текущий текст:', searchText.textContent);
        
        // Симулируем начало поиска
        console.log('[Searching Translation Test] Симулируем начало поиска...');
        
        // Применяем логику из showSearchLoader()
        if (searchLoader) {
            searchLoader.classList.remove('hidden');
        }
        
        if (searchText) {
            const searchingText = typeof t === 'function' ? t('searching', 'Searching...') : 'Searching...';
            searchText.textContent = searchingText;
            console.log('[Searching Translation Test] Установлен текст поиска:', searchingText);
        }
        
        // Через 3 секунды симулируем окончание поиска
        setTimeout(() => {
            console.log('[Searching Translation Test] Симулируем окончание поиска...');
            
            if (searchLoader) {
                searchLoader.classList.add('hidden');
            }
            
            if (searchText) {
                const buttonText = typeof t === 'function' ? t('button_search', 'Find Sizes') : 'Find Sizes';
                searchText.textContent = buttonText;
                console.log('[Searching Translation Test] Восстановлен текст кнопки:', buttonText);
            }
        }, 3000);
    }

    // Функция тестирования виджета
    function testWidgetSearching() {
        console.log('[Searching Translation Test] === Тестирование виджета ===');
        
        if (window.wheelFitWidget && typeof window.wheelFitWidget.showSearchLoader === 'function') {
            console.log('[Searching Translation Test] Найден виджет, тестируем showSearchLoader...');
            
            // Устанавливаем русские переводы
            window.WheelFitI18n = testTranslations.ru;
            
            // Вызываем showSearchLoader
            window.wheelFitWidget.showSearchLoader();
            console.log('[Searching Translation Test] Вызван showSearchLoader()');
            
            // Проверяем результат
            const searchText = document.getElementById('search-text');
            if (searchText) {
                const currentText = searchText.textContent.trim();
                const expectedText = testTranslations.ru['searching'];
                
                const isCorrect = currentText === expectedText;
                console.log(`[Searching Translation Test] Текст после showSearchLoader: "${currentText}" ${isCorrect ? '✅' : '❌'}`);
                console.log(`[Searching Translation Test] Ожидался: "${expectedText}"`);
                
                if (isCorrect) {
                    console.log('[Searching Translation Test] ✅ УСПЕХ: перевод "Searching..." работает правильно!');
                } else {
                    console.error('[Searching Translation Test] ❌ ОШИБКА: перевод не работает!');
                }
            }
            
            // Через 2 секунды вызываем hideSearchLoader
            setTimeout(() => {
                window.wheelFitWidget.hideSearchLoader();
                console.log('[Searching Translation Test] Вызван hideSearchLoader()');
                
                const searchText = document.getElementById('search-text');
                if (searchText) {
                    const currentText = searchText.textContent.trim();
                    console.log(`[Searching Translation Test] Текст после hideSearchLoader: "${currentText}"`);
                }
            }, 2000);
            
        } else {
            console.warn('[Searching Translation Test] ⚠️ Виджет или функция showSearchLoader недоступны');
        }
    }

    // Функция проверки всех языков
    function testAllLanguages() {
        console.log('[Searching Translation Test] === Тестирование всех языков ===');
        
        const languages = Object.keys(testTranslations);
        let currentLangIndex = 0;
        
        function testNextLanguage() {
            if (currentLangIndex >= languages.length) {
                console.log('[Searching Translation Test] ✅ Тестирование всех языков завершено');
                return;
            }
            
            const lang = languages[currentLangIndex];
            console.log(`\n[Searching Translation Test] Тестируем ${lang.toUpperCase()}...`);
            
            // Устанавливаем переводы
            window.WheelFitI18n = testTranslations[lang];
            
            // Симулируем поиск
            const searchText = document.getElementById('search-text');
            if (searchText && window.wheelFitWidget) {
                window.wheelFitWidget.showSearchLoader();
                
                setTimeout(() => {
                    const currentText = searchText.textContent.trim();
                    const expectedText = testTranslations[lang]['searching'];
                    const isCorrect = currentText === expectedText;
                    
                    console.log(`  ${lang}: "${currentText}" ${isCorrect ? '✅' : '❌'}`);
                    
                    window.wheelFitWidget.hideSearchLoader();
                    
                    currentLangIndex++;
                    setTimeout(testNextLanguage, 1000);
                }, 500);
            } else {
                currentLangIndex++;
                setTimeout(testNextLanguage, 100);
            }
        }
        
        testNextLanguage();
    }

    // Основная функция тестирования
    function runSearchingTest() {
        console.log('[Searching Translation Test] 🚀 ЗАПУСК ТЕСТА ПЕРЕВОДА "Searching..."');
        
        // 1. Тестируем функцию перевода
        testSearchingTranslation();
        
        // 2. Симулируем поиск
        setTimeout(() => {
            simulateSearch();
        }, 1000);
        
        // 3. Тестируем виджет
        setTimeout(() => {
            testWidgetSearching();
        }, 2000);
        
        // 4. Тестируем все языки
        setTimeout(() => {
            testAllLanguages();
        }, 5000);
    }

    // Глобальные функции для ручного тестирования
    window.testSearchingTranslation = {
        runTest: runSearchingTest,
        testFunction: testSearchingTranslation,
        simulate: simulateSearch,
        testWidget: testWidgetSearching,
        testAll: testAllLanguages
    };

    // Автоматический запуск через 2 секунды
    setTimeout(() => {
        console.log('[Searching Translation Test] Автоматический запуск теста...');
        runSearchingTest();
    }, 2000);

    console.log('[Searching Translation Test] Тест загружен. Доступные функции:');
    console.log('- testSearchingTranslation.runTest() - полный тест');
    console.log('- testSearchingTranslation.simulate() - симуляция поиска');
    console.log('- testSearchingTranslation.testWidget() - тест виджета');

})();
