// Test script to verify garage scoping fix
console.log('=== Testing Garage Scoping Fix ===');

// Test 1: Check if performGarageLoad is accessible
console.log('1. Function accessibility test:');
console.log('   performGarageLoad (should be undefined - now internal):', typeof performGarageLoad);
console.log('   window.debugGarageWidget (should be function):', typeof window.debugGarageWidget);

// Test 2: Check garage initialization
console.log('2. Garage initialization test:');
console.log('   __garageInitiated flag:', !!window.__garageInitiated);

// Test 3: Check DOM elements
console.log('3. DOM elements test:');
const garageElements = [
    'garage-drawer',
    'garage-overlay', 
    'garage-items-list',
    'garage-close-btn'
];

garageElements.forEach(id => {
    const element = document.getElementById(id);
    console.log(`   ${id}: ${element ? '✅ found' : '❌ missing'}`);
});

// Test 4: Check if garage can be opened/closed
console.log('4. Garage drawer functionality test:');
const garageDrawer = document.getElementById('garage-drawer');
const garageOverlay = document.getElementById('garage-overlay');

if (garageDrawer && garageOverlay) {
    console.log('   ✅ Garage drawer elements available');
    
    // Test opening
    const triggerBtn = document.querySelector('[data-garage-trigger]');
    if (triggerBtn) {
        console.log('   ✅ Garage trigger button found');
        
        // Simulate click to test if drawer opens
        console.log('   Testing drawer open...');
        triggerBtn.click();
        
        setTimeout(() => {
            const isOpen = !garageOverlay.classList.contains('hidden');
            console.log(`   Drawer opened: ${isOpen ? '✅' : '❌'}`);
            
            if (isOpen) {
                // Test closing
                console.log('   Testing drawer close...');
                const closeBtn = document.getElementById('garage-close-btn');
                if (closeBtn) {
                    closeBtn.click();
                    setTimeout(() => {
                        const isClosed = garageOverlay.classList.contains('hidden');
                        console.log(`   Drawer closed: ${isClosed ? '✅' : '❌'}`);
                    }, 100);
                } else {
                    console.log('   ❌ Close button not found');
                }
            }
        }, 100);
    } else {
        console.log('   ❌ Garage trigger button not found');
    }
} else {
    console.log('   ❌ Garage drawer elements missing');
}

// Test 5: Mock garage load test (if storage available)
console.log('5. Mock garage load test:');

if (typeof LocalStorageHandler !== 'undefined') {
    const storage = new LocalStorageHandler();
    const garage = storage.getGarage();
    
    console.log(`   Garage items count: ${garage.length}`);
    
    if (garage.length > 0) {
        console.log('   Testing load functionality...');
        
        // Find a load button
        const loadBtn = document.querySelector('.garage-load');
        if (loadBtn) {
            console.log('   ✅ Load button found');
            
            // Check if widget is ready
            if (window.wheelFitWidget && typeof window.wheelFitWidget.loadFromHistory === 'function') {
                console.log('   ✅ Widget ready for loading');
                
                // Simulate click - this should now work without ReferenceError
                console.log('   Simulating load button click...');
                try {
                    loadBtn.click();
                    console.log('   ✅ Load button clicked without immediate error');
                    
                    // Check for success after a delay
                    setTimeout(() => {
                        console.log('   Checking if drawer closed after load...');
                        const isClosed = garageOverlay && garageOverlay.classList.contains('hidden');
                        console.log(`   Drawer auto-closed: ${isClosed ? '✅' : '❌'}`);
                    }, 2000);
                    
                } catch (error) {
                    console.log('   ❌ Error during load:', error.message);
                }
            } else {
                console.log('   ❌ Widget not ready for loading');
            }
        } else {
            console.log('   ⚠️ No load button found (garage may be empty)');
        }
    } else {
        console.log('   ⚠️ No garage items to test with');
    }
} else {
    console.log('   ❌ LocalStorageHandler not available');
}

// Test 6: Check for ReferenceError in console
console.log('6. Error monitoring test:');
let hasReferenceError = false;
const originalError = console.error;

console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('ReferenceError') && message.includes('closeDrawer')) {
        hasReferenceError = true;
        console.log('   ❌ FOUND ReferenceError: closeDrawer is not defined');
    }
    originalError.apply(console, args);
};

// Restore after 5 seconds
setTimeout(() => {
    console.error = originalError;
    console.log(`   ReferenceError detected: ${hasReferenceError ? '❌ YES' : '✅ NO'}`);
}, 5000);

console.log('\n=== Scoping Fix Summary ===');
console.log('The fix moved performGarageLoad inside initGarageFeature scope');
console.log('This gives it access to closeDrawer function');
console.log('Expected result: No more "ReferenceError: closeDrawer is not defined"');
console.log('Expected behavior: Garage drawer closes after successful load');

console.log('\n=== Test completed ===');
