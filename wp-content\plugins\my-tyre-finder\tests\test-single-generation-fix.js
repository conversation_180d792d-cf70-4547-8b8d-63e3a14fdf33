/**
 * Specialized Test Script for Single Generation Display Fix
 * Tests the critical bug where generation ID is displayed instead of human-readable name
 * when only one generation exists for a vehicle
 */

console.log('=== Single Generation Display Fix Test ===');

// Test configuration
const SINGLE_GEN_TEST_CONFIG = {
    testSelectorDisplay: true,
    testResultsDisplay: true,
    testInternalIdDetection: true,
    testWizardDisplay: true,
    verbose: true
};

// Helper function for logging
function logSingleGen(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [SINGLE-GEN-${type.toUpperCase()}]`;
    
    switch(type) {
        case 'error':
            console.error(`${prefix} ${message}`);
            break;
        case 'warn':
            console.warn(`${prefix} ${message}`);
            break;
        case 'success':
            console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');
            break;
        default:
            console.log(`${prefix} ${message}`);
    }
}

// Mock generation data that simulates the real API response
const MOCK_GENERATION_DATA = {
    // Case 1: Generation with internal ID as slug but proper name
    withInternalId: {
        id: "561918f13a",
        slug: "561918f13a", 
        name: "GB",
        title: null,
        range: null
    },
    
    // Case 2: Generation with readable slug and name
    withReadableSlug: {
        id: "gb-gen",
        slug: "gb",
        name: "GB",
        title: null,
        range: null
    },
    
    // Case 3: Generation with only internal ID (worst case)
    onlyInternalId: {
        id: "561918f13a",
        slug: "561918f13a",
        name: null,
        title: null,
        range: null
    },
    
    // Case 4: Generation with title instead of name
    withTitle: {
        id: "8x-facelift",
        slug: "8x-facelift",
        name: null,
        title: "8X Facelift",
        range: null
    }
};

// Test 1: Internal ID Detection
function testInternalIdDetection() {
    logSingleGen('Testing internal ID detection...', 'info');
    
    const tests = [];
    
    // Test cases for internal ID detection
    const testCases = [
        { input: "561918f13a", expected: true, description: "Long hex string" },
        { input: "gb", expected: false, description: "Short readable string" },
        { input: "8x-facelift", expected: false, description: "Readable slug with dash" },
        { input: "a1b2c3d4e5f6", expected: true, description: "Long alphanumeric" },
        { input: "GB", expected: false, description: "Short uppercase" },
        { input: "Generation1", expected: false, description: "Readable with number" },
        { input: "", expected: false, description: "Empty string" },
        { input: null, expected: false, description: "Null value" }
    ];
    
    testCases.forEach(testCase => {
        tests.push({
            name: `Internal ID detection: ${testCase.description}`,
            test: () => {
                // Mock the isInternalId function logic
                const isInternalId = (str) => {
                    if (!str || typeof str !== 'string') return false;
                    return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
                };
                
                const result = isInternalId(testCase.input);
                logSingleGen(`Testing "${testCase.input}": ${result} (expected: ${testCase.expected})`, 'info');
                return result === testCase.expected;
            },
            expected: true
        });
    });
    
    return runSingleGenTests('Internal ID Detection', tests);
}

// Test 2: Selector Display with Single Generation
function testSelectorDisplayWithSingleGeneration() {
    logSingleGen('Testing selector display with single generation...', 'info');
    
    const tests = [];
    
    Object.keys(MOCK_GENERATION_DATA).forEach(caseKey => {
        const generationData = MOCK_GENERATION_DATA[caseKey];
        
        tests.push({
            name: `Selector display: ${caseKey}`,
            test: () => {
                // Create mock selector
                const mockSelect = document.createElement('select');
                mockSelect.id = 'test-generation-select';
                document.body.appendChild(mockSelect);
                
                try {
                    // Mock the populateGenerations logic
                    const isInternalId = (str) => {
                        if (!str || typeof str !== 'string') return false;
                        return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
                    };
                    
                    const label = generationData.name || generationData.title || generationData.range || generationData.year_range || generationData.gen || generationData.slug || generationData.id || 'Unknown Generation';
                    const value = generationData.slug || generationData.id || generationData.name || generationData.title || generationData.range || label;
                    const displayLabel = isInternalId(label) ? (generationData.name || generationData.title || generationData.range || generationData.year_range || generationData.gen || 'Generation') : label;
                    
                    mockSelect.innerHTML = '<option value="">Choose a generation</option>';
                    mockSelect.add(new Option(displayLabel, value));
                    
                    // Auto-select for single generation
                    mockSelect.value = value;
                    
                    const selectedText = mockSelect.options[mockSelect.selectedIndex]?.text;
                    
                    logSingleGen(`Case ${caseKey}: "${selectedText}" (value: "${value}")`, 'info');
                    
                    // Check that the displayed text is not an internal ID
                    const isDisplayingInternalId = isInternalId(selectedText);
                    
                    return !isDisplayingInternalId && selectedText !== 'Unknown Generation';
                } finally {
                    document.body.removeChild(mockSelect);
                }
            },
            expected: true
        });
    });
    
    return runSingleGenTests('Selector Display', tests);
}

// Test 3: Results Display with Single Generation
function testResultsDisplayWithSingleGeneration() {
    logSingleGen('Testing results display with single generation...', 'info');
    
    const tests = [];
    
    Object.keys(MOCK_GENERATION_DATA).forEach(caseKey => {
        const generationData = MOCK_GENERATION_DATA[caseKey];
        
        tests.push({
            name: `Results display: ${caseKey}`,
            test: () => {
                // Mock the getGenerationName logic
                const isInternalId = (str) => {
                    if (!str || typeof str !== 'string') return false;
                    return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
                };
                
                const generationSlug = generationData.slug || generationData.id;
                
                // Simulate finding generation in cache
                let name = generationData.name || generationData.title || generationData.range || generationData.year_range || generationData.gen || generationData.slug || generationData.id || 'Unknown Generation';
                
                // If the name looks like an internal ID, try to find a better alternative
                if (isInternalId(name)) {
                    name = generationData.name || generationData.title || generationData.range || generationData.year_range || generationData.gen || 'Generation';
                }
                
                // Build vehicle label
                const vehicleLabel = `Audi A1 ${name}`;
                
                logSingleGen(`Case ${caseKey}: "${vehicleLabel}"`, 'info');
                
                // Check that the label doesn't contain internal IDs
                const containsInternalId = isInternalId(name) || vehicleLabel.includes('561918f13a');
                
                return !containsInternalId && name !== 'Unknown Generation';
            },
            expected: true
        });
    });
    
    return runSingleGenTests('Results Display', tests);
}

// Test 4: Wizard Display with Single Generation
function testWizardDisplayWithSingleGeneration() {
    logSingleGen('Testing wizard display with single generation...', 'info');
    
    const tests = [];
    
    Object.keys(MOCK_GENERATION_DATA).forEach(caseKey => {
        const generationData = MOCK_GENERATION_DATA[caseKey];
        
        tests.push({
            name: `Wizard display: ${caseKey}`,
            test: () => {
                // Mock the getGenerationDisplayName logic from wizard
                const getGenerationDisplayName = (generation) => {
                    if (!generation) return 'Generation';
                    
                    // If it's already an object with name
                    if (typeof generation === 'object' && generation.name) {
                        return generation.name;
                    }
                    
                    // If it's a string (ID/slug), check if it looks like an internal ID
                    if (typeof generation === 'string') {
                        const isInternalId = (str) => {
                            return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
                        };
                        
                        if (isInternalId(generation)) {
                            return 'Generation';
                        }
                        
                        return generation.charAt(0).toUpperCase() + generation.slice(1);
                    }
                    
                    // Fallback for object without name
                    if (typeof generation === 'object') {
                        return generation.title || generation.range || generation.year_range || generation.gen || 'Generation';
                    }
                    
                    return 'Generation';
                };
                
                // Test with object
                const displayNameFromObject = getGenerationDisplayName(generationData);
                
                // Test with string (slug/id)
                const displayNameFromString = getGenerationDisplayName(generationData.slug || generationData.id);
                
                const vehicleLabel = `Audi A1 ${displayNameFromObject}`;
                
                logSingleGen(`Case ${caseKey}: Object -> "${displayNameFromObject}", String -> "${displayNameFromString}", Label -> "${vehicleLabel}"`, 'info');
                
                // Check that neither display name contains internal IDs
                const isInternalId = (str) => /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
                
                return !isInternalId(displayNameFromObject) && 
                       !isInternalId(displayNameFromString) && 
                       displayNameFromObject !== 'Unknown Generation';
            },
            expected: true
        });
    });
    
    return runSingleGenTests('Wizard Display', tests);
}

// Test 5: Real Widget Integration
function testRealWidgetIntegration() {
    logSingleGen('Testing real widget integration...', 'info');
    
    const tests = [];
    
    // Test if real widget functions exist and work correctly
    tests.push({
        name: 'Real widget getGenerationName function',
        test: () => {
            if (!window.wheelFitWidget || typeof window.wheelFitWidget.getGenerationName !== 'function') {
                logSingleGen('Real widget not available, skipping test', 'warn');
                return true; // Skip test if widget not available
            }
            
            // Mock cache with internal ID generation
            const mockGenerations = [MOCK_GENERATION_DATA.withInternalId];
            window.wheelFitWidget.cache.set('generations_audi_a1', mockGenerations);
            window.wheelFitWidget.selectedData = { make: 'audi', model: 'a1' };
            
            const result = window.wheelFitWidget.getGenerationName('561918f13a');
            logSingleGen(`Real widget getGenerationName result: "${result}"`, 'info');
            
            // Should return "GB", not "561918f13a"
            return result === 'GB' || result === 'Generation';
        },
        expected: true
    });
    
    tests.push({
        name: 'Real widget getVehicleLabel function',
        test: () => {
            if (!window.wheelFitWidget || typeof window.wheelFitWidget.getVehicleLabel !== 'function') {
                logSingleGen('Real widget not available, skipping test', 'warn');
                return true; // Skip test if widget not available
            }
            
            // Set up widget state
            window.wheelFitWidget.mode = 'byGeneration';
            window.wheelFitWidget.selectedData = {
                make: 'audi',
                model: 'a1',
                generation: '561918f13a'
            };
            
            const result = window.wheelFitWidget.getVehicleLabel();
            logSingleGen(`Real widget getVehicleLabel result: "${result}"`, 'info');
            
            // Should not contain internal ID
            return !result.includes('561918f13a');
        },
        expected: true
    });
    
    return runSingleGenTests('Real Widget Integration', tests);
}

// Helper function to run tests
function runSingleGenTests(suiteName, tests) {
    logSingleGen(`Running ${suiteName} tests...`, 'info');
    
    let passed = 0;
    let failed = 0;
    
    tests.forEach(test => {
        try {
            const result = test.test();
            if (result === test.expected) {
                logSingleGen(`✓ ${test.name}`, 'success');
                passed++;
            } else {
                logSingleGen(`✗ ${test.name} (expected: ${test.expected}, got: ${result})`, 'error');
                failed++;
            }
        } catch (error) {
            logSingleGen(`✗ ${test.name} (error: ${error.message})`, 'error');
            failed++;
        }
    });
    
    const total = passed + failed;
    const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    logSingleGen(`${suiteName} Results: ${passed}/${total} passed (${percentage}%)`, 
        percentage === 100 ? 'success' : 'warn');
    
    return { passed, failed, total, percentage };
}

// Main test runner
function runAllSingleGenerationTests() {
    logSingleGen('Starting single generation fix test suite...', 'info');
    
    const results = [];
    
    if (SINGLE_GEN_TEST_CONFIG.testInternalIdDetection) {
        results.push(testInternalIdDetection());
    }
    
    if (SINGLE_GEN_TEST_CONFIG.testSelectorDisplay) {
        results.push(testSelectorDisplayWithSingleGeneration());
    }
    
    if (SINGLE_GEN_TEST_CONFIG.testResultsDisplay) {
        results.push(testResultsDisplayWithSingleGeneration());
    }
    
    if (SINGLE_GEN_TEST_CONFIG.testWizardDisplay) {
        results.push(testWizardDisplayWithSingleGeneration());
    }
    
    results.push(testRealWidgetIntegration());
    
    // Summary
    const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);
    const totalTests = results.reduce((sum, r) => sum + r.total, 0);
    const overallPercentage = totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0;
    
    logSingleGen('=== SINGLE GENERATION TEST SUMMARY ===', 'info');
    logSingleGen(`Overall Results: ${totalPassed}/${totalTests} passed (${overallPercentage}%)`, 
        overallPercentage >= 80 ? 'success' : 'warn');
    
    if (overallPercentage >= 80) {
        logSingleGen('Single generation fixes appear to be working correctly!', 'success');
    } else {
        logSingleGen('Some issues detected with single generation fixes. Please review the failed tests above.', 'warn');
    }
    
    return results;
}

// Debug information
function showSingleGenerationDebugInfo() {
    logSingleGen('=== SINGLE GENERATION DEBUG INFORMATION ===', 'info');
    
    logSingleGen('Document ready state: ' + document.readyState, 'info');
    
    // Check widget availability
    logSingleGen(`Widget available: ${!!window.wheelFitWidget}`, 'info');
    
    if (window.wheelFitWidget) {
        logSingleGen(`Widget mode: ${window.wheelFitWidget.mode || 'unknown'}`, 'info');
        logSingleGen(`Selected data: ${JSON.stringify(window.wheelFitWidget.selectedData || {})}`, 'info');
        
        // Test with mock data
        const testGenerations = [MOCK_GENERATION_DATA.withInternalId];
        window.wheelFitWidget.cache.set('generations_test_test', testGenerations);
        window.wheelFitWidget.selectedData = { make: 'test', model: 'test' };
        
        const testResult = window.wheelFitWidget.getGenerationName('561918f13a');
        logSingleGen(`Test getGenerationName('561918f13a'): "${testResult}"`, 'info');
    }
    
    // Check DOM elements
    const genSelect = document.getElementById('wf-generation');
    logSingleGen(`Generation selector: ${genSelect ? 'found' : 'missing'}`, 'info');
    
    if (genSelect) {
        logSingleGen(`Generation selector value: "${genSelect.value}"`, 'info');
        logSingleGen(`Generation selector text: "${genSelect.options[genSelect.selectedIndex]?.text || 'none'}"`, 'info');
        logSingleGen(`Generation options count: ${genSelect.options.length}`, 'info');
        
        // Check if any option contains internal ID
        for (let i = 0; i < genSelect.options.length; i++) {
            const option = genSelect.options[i];
            const isInternalId = /^[a-f0-9]{8,}$/i.test(option.text) || /^[a-z0-9]{10,}$/i.test(option.text);
            if (isInternalId) {
                logSingleGen(`WARNING: Option ${i} contains internal ID: "${option.text}" (value: "${option.value}")`, 'warn');
            }
        }
    }
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            showSingleGenerationDebugInfo();
            runAllSingleGenerationTests();
        }, 1000);
    });
} else {
    setTimeout(() => {
        showSingleGenerationDebugInfo();
        runAllSingleGenerationTests();
    }, 1000);
}

// Export functions for manual testing
window.testSingleGenerationFix = {
    runAll: runAllSingleGenerationTests,
    internalIdDetection: testInternalIdDetection,
    selectorDisplay: testSelectorDisplayWithSingleGeneration,
    resultsDisplay: testResultsDisplayWithSingleGeneration,
    wizardDisplay: testWizardDisplayWithSingleGeneration,
    realWidget: testRealWidgetIntegration,
    debug: showSingleGenerationDebugInfo,
    mockData: MOCK_GENERATION_DATA
};

logSingleGen('Single generation test script loaded. Use window.testSingleGenerationFix.runAll() to run tests manually.', 'info');
