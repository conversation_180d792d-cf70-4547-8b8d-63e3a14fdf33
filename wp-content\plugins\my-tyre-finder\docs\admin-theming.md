# Admin Theme Panel Customization

This document describes the CSS custom properties available for customizing the admin theme panel appearance.

## Theme Card Variables

The following CSS custom properties control the visual design of theme cards in the admin panel:

### Layout Variables

#### `--wsf-ring`
- **Default**: `4px`
- **Purpose**: Controls the width of the active theme selection ring
- **Usage**: Applied to `.wsf-theme-card--active` for visual feedback
- **Example**: 
  ```css
  :root {
    --wsf-ring: 6px; /* Thicker ring for better visibility */
  }
  ```

#### `--wsf-gap-swatch`
- **Default**: `6px`
- **Purpose**: Sets consistent spacing between color swatches
- **Usage**: Applied to `.wsf-theme-card__colors` flex gap
- **Example**:
  ```css
  :root {
    --wsf-gap-swatch: 8px; /* More spacing between swatches */
  }
  ```

#### `--wsf-card-padding`
- **Default**: `12px`
- **Purpose**: Ensures uniform internal padding across all theme cards
- **Usage**: Applied to card padding, badge positioning, and action positioning
- **Example**:
  ```css
  :root {
    --wsf-card-padding: 16px; /* More breathing room */
  }
  ```

### Color Variables

#### `--wsf-accent-neutral-dark`
- **Default**: `#334155` (slate-700)
- **Purpose**: Primary color for active states and emphasis
- **Usage**: Active ring color, badge text color, button primary background

## Responsive Behavior

### Mobile Breakpoints

At `768px` and below:
- Action icons move below the badge to prevent overlap
- Card padding adjusts automatically via CSS custom properties
- Text wrapping is handled gracefully with dynamic icon positioning

### Dynamic Icon Positioning

The JavaScript handler (`admin-theme-cards.js`) automatically adjusts icon positioning:

- **Short names** (≤14 characters): Icons positioned normally
- **Long names** (>14 characters): Icons shifted 16px left to prevent text overlap

## Browser Support

- **Modern browsers**: Full CSS custom property support
- **Legacy browsers**: Graceful fallback to hardcoded values
- **IE11**: Limited support, basic functionality maintained

## Customization Examples

### Corporate Branding
```css
:root {
  --wsf-accent-neutral-dark: #0066cc; /* Brand blue */
  --wsf-ring: 3px;                    /* Subtle ring */
  --wsf-card-padding: 16px;           /* Spacious layout */
}
```

### Compact Layout
```css
:root {
  --wsf-card-padding: 8px;   /* Tighter spacing */
  --wsf-gap-swatch: 4px;     /* Closer swatches */
  --wsf-ring: 2px;           /* Minimal ring */
}
```

### High Contrast
```css
:root {
  --wsf-accent-neutral-dark: #000000; /* Pure black */
  --wsf-ring: 6px;                    /* Thick ring */
}
```

## Performance Notes

- CSS custom properties are computed once and cached
- JavaScript positioning runs only on card creation/modification
- No layout thrashing due to efficient event handling
- Minimal DOM queries with cached selectors

## Accessibility

- Ring width (`--wsf-ring`) should be at least 2px for visibility
- Color contrast maintained with `--wsf-accent-neutral-dark`
- Focus states inherit from active states for consistency
- Screen reader compatibility maintained through semantic markup

## Testing

Run the admin theme panel tests to verify customizations:

```bash
npm run test:admin-themes
```

Key test scenarios:
- Variable inheritance and fallbacks
- Responsive breakpoint behavior  
- Dynamic icon positioning accuracy
- Cross-browser compatibility
