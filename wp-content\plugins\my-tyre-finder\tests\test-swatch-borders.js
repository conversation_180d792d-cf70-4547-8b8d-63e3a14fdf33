/**
 * Тест для проверки рамок цветовых свотчей
 * Запустите в консоли браузера на странице админки с темами
 */

(function() {
    'use strict';

    console.log('🎨 === ТЕСТ РАМОК ЦВЕТОВЫХ СВОТЧЕЙ ===');

    // Проверяем наличие свотчей
    function checkSwatches() {
        console.log('\n1️⃣ Проверка наличия свотчей...');
        
        const swatches = document.querySelectorAll('.wsf-theme-card__swatch');
        if (swatches.length === 0) {
            console.warn('⚠️ Свотчи не найдены. Убедитесь, что вы на странице с темами.');
            return false;
        }
        
        console.log(`✅ Найдено ${swatches.length} свотчей`);
        return swatches;
    }

    // Проверяем стили рамок (теперь через ::before псевдоэлемент)
    function checkBorders(swatches) {
        console.log('\n2️⃣ Проверка стилей рамок (::before псевдоэлемент)...');

        let bordersCorrect = 0;
        let bordersIncorrect = 0;

        swatches.forEach((swatch, index) => {
            const styles = window.getComputedStyle(swatch);
            const beforeStyles = window.getComputedStyle(swatch, '::before');

            const border = styles.border;
            const boxShadow = beforeStyles.boxShadow;
            const beforeContent = beforeStyles.content;

            console.log(`Свотч ${index + 1}:`, {
                border: border,
                backgroundColor: styles.backgroundColor,
                beforeContent: beforeContent,
                beforeBoxShadow: boxShadow,
                position: styles.position
            });

            // Проверяем наличие псевдоэлемента с box-shadow
            if (beforeContent && beforeContent !== 'none' && boxShadow && boxShadow !== 'none') {
                bordersCorrect++;
                console.log(`  ✅ Свотч ${index + 1} имеет псевдоэлемент с рамкой`);
            } else {
                bordersIncorrect++;
                console.log(`  ❌ Свотч ${index + 1} НЕ имеет псевдоэлемента с рамкой`);
            }
        });

        console.log(`\n📊 Результат: ${bordersCorrect} с псевдоэлементом, ${bordersIncorrect} без псевдоэлемента`);
        return bordersCorrect > 0;
    }

    // Создаем тестовые свотчи с разными цветами
    function createTestSwatches() {
        console.log('\n3️⃣ Создание тестовых свотчей...');
        
        const testColors = [
            { name: 'Белый', color: '#ffffff' },
            { name: 'Очень светлый серый', color: '#f8f9fa' },
            { name: 'Светлый серый', color: '#e9ecef' },
            { name: 'Средний серый', color: '#6c757d' },
            { name: 'Темный', color: '#212529' },
            { name: 'Синий', color: '#007bff' },
            { name: 'Красный', color: '#dc3545' }
        ];
        
        // Создаем контейнер для тестов
        const testContainer = document.createElement('div');
        testContainer.id = 'swatch-test-container';
        testContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 300px;
        `;
        
        testContainer.innerHTML = `
            <h3 style="margin: 0 0 12px 0; font-size: 14px; color: #333;">
                🧪 Тест рамок свотчей
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="float: right; border: none; background: #dc3545; color: white; 
                               border-radius: 4px; padding: 2px 6px; cursor: pointer;">✕</button>
            </h3>
            <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 12px;">
                ${testColors.map(({name, color}) => `
                    <div style="text-align: center;">
                        <div class="wsf-theme-card__swatch" 
                             style="background-color: ${color}; margin: 0 auto 4px;" 
                             title="${name}: ${color}"></div>
                        <div style="font-size: 10px; color: #666; max-width: 40px; word-wrap: break-word;">
                            ${name}
                        </div>
                    </div>
                `).join('')}
            </div>
            <div style="font-size: 12px; color: #666; line-height: 1.4;">
                Все свотчи должны иметь псевдоэлемент ::before с inset box-shadow для универсальной рамки поверх заливки.
                <br><strong>Преимущество:</strong> рамка видна даже на чисто белом цвете!
            </div>
        `;
        
        document.body.appendChild(testContainer);
        console.log('✅ Тестовые свотчи созданы в правом верхнем углу');
        
        // Проверяем рамки у тестовых свотчей
        setTimeout(() => {
            const testSwatches = testContainer.querySelectorAll('.wsf-theme-card__swatch');
            console.log('\n4️⃣ Проверка тестовых свотчей...');
            checkBorders(testSwatches);
        }, 100);
    }

    // Основная функция тестирования
    function runTest() {
        console.log('🚀 Запуск теста рамок свотчей...\n');
        
        const swatches = checkSwatches();
        if (!swatches) {
            console.log('⚠️ Создаем тестовые свотчи для демонстрации...');
            createTestSwatches();
            return;
        }
        
        const bordersOk = checkBorders(swatches);
        
        console.log('\n📋 === РЕЗУЛЬТАТЫ ТЕСТА ===');
        console.log(`Свотчи найдены: ${swatches ? '✅ ДА' : '❌ НЕТ'}`);
        console.log(`Рамки применены: ${bordersOk ? '✅ ДА' : '❌ НЕТ'}`);
        
        if (bordersOk) {
            console.log('\n🎉 Тест пройден! Рамки применяются ко всем свотчам.');
        } else {
            console.log('\n⚠️ Возможные проблемы с применением рамок.');
        }
        
        // Создаем тестовые свотчи для визуальной проверки
        createTestSwatches();
        
        console.log('\n💡 Советы:');
        console.log('1. Проверьте тестовые свотчи в правом верхнем углу');
        console.log('2. Все цвета должны иметь тонкую рамку');
        console.log('3. Рамка должна быть видна даже на белом фоне');
    }

    // Запускаем тест
    runTest();

})();
