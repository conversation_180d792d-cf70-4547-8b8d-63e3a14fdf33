{# Inline Wheel-Fit Form Template #}
<div class="wheel-fit-widget bg-wsf-bg wsf-root-font max-w-4xl mx-auto p-4 md:p-0{% if garage_enabled %} garage-enabled{% endif %}" data-flow-order="{{ flow_order_json|e }}">
    {% set has_vehicle = show_by_vehicle %}
    {% set has_size = show_by_size %}
    {% set multiple_tabs = has_vehicle and has_size %}
    {% set widget_disabled = (not has_vehicle and not has_size) %}

    {% if multiple_tabs %}
    <!-- Tab buttons -->
    <div class="flex gap-4 mb-4 w-full" role="tablist" aria-label="Режим поиска">
        {% if has_vehicle %}
        <button data-tab="by-car" class="select-tab flex-1" role="tab" aria-selected="true" data-i18n="tab_by_vehicle">Tyres by vehicle</button>
        {% endif %}
        {% if has_size %}
        <button data-tab="by-size" class="select-tab flex-1" role="tab" aria-selected="false" data-i18n="tab_by_size">Tyres by size</button>
        {% endif %}
    </div>
    {% endif %}

    {# Hidden stepper indicator (kept for JS compatibility) #}
    <div class="hidden">
        <div id="step-indicator-1"></div>
        <div id="step-indicator-2"></div>
        <div id="step-indicator-3"></div>
        <div id="step-indicator-4"></div>
        <div id="step-text-1"></div>
        <div id="step-text-2"></div>
        <div id="step-text-3"></div>
        <div id="step-text-4"></div>
        <div id="progress-1"></div>
        <div id="progress-2"></div>
        <div id="progress-3"></div>
    </div>

    {% if has_vehicle %}
    {# Inline Form #}
    <div class="w-full">
        <div class="wsf-form-wrapper">
            {# Widget Title - moved inside form wrapper #}
            <div class="wsf-widget__header mb-6">
                <h1 class="wsf-widget__title text-center text-2xl md:text-3xl font-extrabold tracking-tight text-wsf-text m-0" data-i18n="widget_title">{{ widget_title|e }}</h1>
            </div>

            <form id="tab-by-car" class="tab-panel grid-2x2 grid grid-cols-1 sm:grid-cols-2 gap-3 w-full" role="tabpanel" aria-hidden="false">
                {# Dynamic Field Blocks based on flow_order #}
                {% set order = flow_order is not null ? flow_order : ['make', 'model', 'year', 'mod'] %}
                {% for field in order %}
                    {% include 'fields/' ~ field ~ '.twig' %}
                {% endfor %}

            {% if garage_enabled %}
            <div class="flex flex-col w-full sm:col-span-2 items-end">
              <button type="button" data-garage-trigger class="inline-flex items-center gap-2 text-sm text-wsf-text px-3 py-1.5 rounded-md hover:bg-wsf-surface hover:text-wsf-text transition">
                  <i data-lucide="car" class="w-5 h-5"></i>
                  <span class="font-semibold" data-i18n="label_garage">Garage</span>
                  <span class="wsf-garage-count-badge garage-count-dup hidden"></span>
              </button>
            </div>
            {% endif %}

            {% if not auto_search %}
            <div class="ws-submit flex flex-col basis-full w-full sm:col-span-2">
                <label class="block text-xs font-semibold text-transparent select-none" style="height: 0; line-height: 0; margin: 0; padding: 0;">&nbsp;</label>
                <button type="submit" class="btn-primary" disabled>
                    <span id="search-text" data-i18n="button_search">Find Sizes</span>
                    <div id="search-loader" class="hidden inline-block ml-2"><div class="animate-spin rounded-full h-4 w-4 border-b border-wsf-border border-wsf-border border-wsf-border border-wsf-border border-wsf-border-2 border-white"></div></div>
                </button>
            </div>
            {% endif %}
        </form>
        </div>
    </div>
    {% endif %}

    {% if has_size %}
    <div class="w-full">
        <div class="wsf-form-wrapper">
            {# Widget Title - for tire size search #}
            {% if not multiple_tabs %}
            <div class="wsf-widget__header mb-6">
                <h1 class="wsf-widget__title text-center text-2xl md:text-3xl font-extrabold tracking-tight text-wsf-text m-0" data-i18n="widget_title">{{ widget_title|e }}</h1>
            </div>
            {% endif %}

            <!-- ====== Форма: поиск по размеру ====== -->
            <form id="tab-by-size" class="tab-panel {% if multiple_tabs %}hidden{% endif %} flex flex-col gap-4 w-full" role="tabpanel" aria-hidden="{{ multiple_tabs ? 'true' : 'false' }}">
            <div class="flex flex-col w-full min-w-0">
                <label for="tire-width" class="block text-xs font-semibold uppercase tracking-wide mb-1 text-wsf-muted" data-i18n="label_width">Width</label>
                <select id="tire-width" name="section_width" class="select-field" required>
                    <option value="" data-i18n="select_width_first_placeholder">Select width</option>
                </select>
            </div>
            <div class="flex flex-col w-full min-w-0">
                <label for="tire-profile" class="block text-xs font-semibold uppercase tracking-wide mb-1 text-wsf-muted" data-i18n="label_profile">Profile</label>
                <select id="tire-profile" name="aspect_ratio" class="select-field" required>
                    <option value="" data-i18n="select_profile_first_placeholder">Select profile</option>
                </select>
            </div>
            <div class="flex flex-col w-full min-w-0">
                <label for="tire-diameter" class="block text-xs font-semibold uppercase tracking-wide mb-1 text-wsf-muted" data-i18n="label_diameter">Diameter</label>
                <select id="tire-diameter" name="rim_diameter" class="select-field" required>
                    <option value="" data-i18n="select_diameter_first_placeholder">Select diameter</option>
                </select>
            </div>
            {% if garage_enabled %}
            <div class="flex flex-col w-full sm:col-span-2 items-end">
              <button type="button" data-garage-trigger class="inline-flex items-center gap-2 text-sm text-wsf-muted px-3 py-1.5 rounded-md hover:bg-wsf-surface hover:text-wsf-text transition mt-1">
                  <i data-lucide="car" class="w-5 h-5"></i>
                  <span class="font-semibold" data-i18n="label_garage">Garage</span>
                  <span class="wsf-garage-count-badge garage-count-dup hidden"></span>
              </button>
            </div>
            {% endif %}
            {% if not auto_search %}
            <div class="ws-submit basis-full w-full mt-4">
                <button type="submit" class="btn-primary w-full">
                    <span data-i18n="button_search">Find Vehicles</span>
                </button>
            </div>
            {% endif %}
        </form>
        </div>

        <!-- Results for "By Size" will appear here, inside the same max-width container -->
        <div class="w-full">
            <div id="tire-search-results" class="mt-8 transform transition-all duration-300 ease-in-out"></div>
        </div>
    </div>
    {% endif %}

    {% if not has_vehicle and not has_size %}
        <p class="text-center text-wsf-muted italic mt-8">Widget disabled in Appearance settings.</p>
    {% endif %}

    <!-- Results Section for "By Vehicle" -->
    <div class="w-full">
    <section id="search-results" class="hidden mt-12 bg-wsf-bg shadow-lg rounded-xl p-6 md:p-8 transform transition-all duration-300 ease-in-out">
        <h2 class="text-lg font-semibold text-gray-900 mb-1" data-i18n="section_results">Search Results</h2>
        <p id="vehicle-label" class="text-base font-medium text-gray-900"></p>
        <div id="selected-modification-info" class="mt-1"></div>
        <div class="border-t border-wsf-border my-4"></div>
        <!-- Factory block -->
        <div id="factory-section" class="mb-8 hidden">
            <h3 class="text-sm font-bold text-gray-900 mb-3" data-i18n="section_factory">Factory Sizes</h3>
            <div id="factory-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
        </div>
        <!-- Optional block -->
        {% if not enable_oe_filter %}
        <div id="optional-section" class="mb-8 hidden">
            <h3 class="text-sm font-bold text-gray-900 mb-3" data-i18n="section_optional">Optional Sizes</h3>
            <div id="optional-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
        </div>
        {% endif %}
        <!-- No Results Message -->
        <div id="no-results" class="hidden text-center py-8">
            <h3 class="text-lg font-medium text-gray-900 mb-2" data-i18n="text_no_results_header">Sizes not found</h3>
            <p class="text-wsf-muted" data-i18n="text_no_results_body">Try selecting another modification or check your selection.</p>
        </div>
    </section>
    </div>

    <!-- Saved Searches (localStorage) -->
    <div id="saved-searches" class="hidden mt-12 w-full">
        <h4 class="mb-4 flex items-center gap-2 text-sm font-semibold text-wsf-muted">
            <i data-lucide="history" class="w-4 h-4 text-wsf-muted" aria-hidden="true"></i>
            Your recent searches
        </h4>
        <ul id="history-list" class="space-y-3"></ul>
        <p id="history-empty" class="hidden text-sm text-wsf-muted">No recent searches yet</p>
    </div>

    {# Garage button moved into forms #}
    {% if garage_enabled %}
      <span id="garage-btn-inline-placeholder" class="hidden"></span>
    {% endif %}
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
.wheel-fit-widget, .wheel-fit-widget button, .wheel-fit-widget input, .wheel-fit-widget select, .wheel-fit-widget option, #garage-drawer, #garage-drawer * {font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif}
:root{--ws-control-height:44px}
/* CONTROLS */
.wheel-fit-widget select{
  width:100%;
  min-width:0;
  height:var(--ws-control-height);
  background-color: var(--wsf-input-bg);
  color: var(--wsf-input-text);
  font:500 14px/1.3 var(--wsf-font-base, 'Inter', sans-serif);
  padding:0 12px;
  border:1px solid var(--wsf-input-border);
  border-radius:6px;
}
.wheel-fit-widget select option[value=""]{font-style:normal;color:var(--wsf-input-placeholder)}
.wheel-fit-widget select[disabled]{opacity:.35;pointer-events:none;transition:.25s}
.ws-submit>button{width:100%}
/* BUTTON */
.ws-submit>button{width:100%}
/* Two-column grid for >=640px handled by Tailwind (sm:grid-cols-2) */
/* Keep full width on mobile via default 1-col grid */
/* Ensure hidden steps remain visible but disabled */
#step-3.hidden,#step-4.hidden{display:flex!important}
.select-field{
  width:100%;
  height:44px;
  background-color: var(--wsf-input-bg);
  color: var(--wsf-input-text);
  border:1px solid var(--wsf-input-border);
  border-radius:6px;
  padding:0 12px;
  box-shadow:0 0 0 1px var(--wsf-shadow);
}
.checkbox{width:16px;height:16px;accent-color:var(--wsf-primary);}
.select-tab {
  height: 44px;
  background-color: var(--wsf-input-bg);
  color: var(--wsf-input-text);
  border: 1px solid var(--wsf-input-border);
  border-radius: 6px;
  padding: 0 12px;
  font: 500 14px var(--wsf-font-base, 'Inter', sans-serif);
  box-shadow: 0 0 0 1px var(--wsf-shadow);
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.select-tab:hover {
  border-color: var(--wsf-primary);
}
.select-tab[aria-selected="true"] {
  font-weight: 700;
  border-color: var(--wsf-primary);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--wsf-primary) 25%, transparent);
}
.garage-float{position:absolute;right:0;top:100%;margin-top:.25rem;}
@media (max-width:639px){.garage-float{position:static;margin-top:.25rem;}}
#garage-wrapper{line-height:1;}
/* Убираем наследуемые ограничения ширины */
.wheel-fit-widget .flex-col,
.wheel-fit-widget .relative {
  min-width: 0 !important;
}

/* Принудительное растягивание select на 100% */
.wheel-fit-widget select {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box;
}

/* Фикс для WebKit (Safari/Chrome) */
.wheel-fit-widget select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
</style>

{% if is_admin %}
<style>
  /* Переопределение стилей админки */
  #widget-preview .wheel-fit-widget select {
    width: 100% !important;
    max-width: 100% !important;
  }
  #widget-preview .wheel-fit-widget .flex-col {
    flex: 1 1 100% !important;
  }
</style>
{% endif %}

{% if garage_enabled %}
<script src="{{ plugin_url }}/assets/js/garage.js"></script>
<div id="garage-overlay" class="hidden fixed inset-0 z-40 bg-black/25 backdrop-blur-md transition-opacity cursor-pointer"></div>
<aside id="garage-drawer" class="fixed right-0 top-0 h-full w-full max-w-sm bg-wsf-surface shadow-2xl transform translate-x-full transition-transform duration-300 z-50 flex flex-col" style="top: var(--wp-admin--admin-bar--height, 0); height: calc(100% - var(--wp-admin--admin-bar--height, 0));">
  <header class="flex items-center justify-between p-4 border-b border-wsf-border border-wsf-border border-wsf-border border-wsf-border border-wsf-border">
    <h2 class="text-xl font-bold text-wsf-text" data-i18n="garage_title">My Garage</h2>
    <button id="garage-close-btn" aria-label="Close" class="text-wsf-muted hover:text-wsf-muted">
      <svg viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
        <line x1="18" y1="6" x2="6" y2="18"/>
        <line x1="6" y1="6" x2="18" y2="18"/>
      </svg>
    </button>
  </header>
  <ul id="garage-items-list" class="flex-1 min-h-0 overflow-y-auto p-4 space-y-4"></ul>
  <footer id="garage-footer" class="sticky bottom-4 flex-shrink-0 p-4 flex justify-end">
    <button id="garage-clear-all" class="btn-secondary">
      <i data-lucide="trash-2"></i>
      <span data-i18n="garage_clear_all">Clear&nbsp;all</span>
    </button>
  </footer>
</aside>
<div id="garage-toast" class="fixed left-1/2 -translate-x-1/2 bottom-6 bg-wsf-primary text-white text-sm font-medium px-4 py-2 rounded-lg shadow-lg transition opacity-0 translate-y-4 z-50">
  <span data-i18n="garage_saved_notification">Saved to Garage!</span>
</div>
{% endif %}

<script>
// Delegated click handler works for dynamically injected previews
document.addEventListener('click',function(e){
  const btn=e.target.closest('.select-tab');
  if(!btn) return;
  const id=btn.dataset.tab;
  const scope=document;
  scope.querySelectorAll('.select-tab').forEach(b=>b.setAttribute('aria-selected',b===btn?'true':'false'));
  scope.querySelectorAll('.tab-panel').forEach(p=>{
    const show=p.id==='tab-'+id;
    p.classList.toggle('hidden',!show);
    p.setAttribute('aria-hidden',!show);
  });
  // Hide existing results on tab switch
  const sr=scope.getElementById('search-results');
  const tr=scope.getElementById('tire-search-results');
  if(typeof fadeOut==='function'){
    fadeOut(sr);
    fadeOut(tr);
  } else {
    if(sr) sr.classList.add('hidden');
    if(tr) tr.classList.add('hidden');
  }
});
</script> 