// Comprehensive test for garage modal behavior fixes
console.log('=== Garage Modal Behavior Fix Test ===');

// Test configuration
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function logResult(test, status, message) {
    const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${test}: ${message}`);
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Test 1: Check garage elements availability
console.log('\n1. Garage Elements Availability:');
const garageOverlay = document.getElementById('garage-overlay');
const garageDrawer = document.getElementById('garage-drawer');
const garageCloseBtn = document.getElementById('garage-close-btn');
const garageTriggers = document.querySelectorAll('[data-garage-trigger]');

logResult('Garage Overlay', garageOverlay ? 'pass' : 'fail', 
    garageOverlay ? 'Found' : 'Not found');
logResult('Garage Drawer', garageDrawer ? 'pass' : 'fail', 
    garageDrawer ? 'Found' : 'Not found');
logResult('Close Button', garageCloseBtn ? 'pass' : 'fail', 
    garageCloseBtn ? 'Found' : 'Not found');
logResult('Garage Triggers', garageTriggers.length > 0 ? 'pass' : 'warning', 
    `Found ${garageTriggers.length} trigger(s)`);

// Test 2: Test proper garage trigger behavior
console.log('\n2. Garage Trigger Behavior:');

if (garageTriggers.length > 0) {
    // Test clicking on garage trigger
    let triggerWorked = false;
    const originalOpen = window.openDrawer;
    
    // Mock openDrawer to detect calls
    window.openDrawer = function() {
        triggerWorked = true;
        console.log('[Test] openDrawer called via trigger');
    };
    
    // Click the first garage trigger
    garageTriggers[0].click();
    
    logResult('Garage Trigger Click', triggerWorked ? 'pass' : 'fail',
        triggerWorked ? 'Trigger correctly opens garage' : 'Trigger failed to open garage');
    
    // Restore original function
    window.openDrawer = originalOpen;
} else {
    logResult('Garage Trigger Test', 'warning', 'No triggers found to test');
}

// Test 3: Test unintended click prevention
console.log('\n3. Unintended Click Prevention:');

// Create test elements that should NOT trigger garage
const testElements = [
    { tag: 'div', text: 'Some random div', class: 'test-element' },
    { tag: 'span', text: 'Random span', class: 'test-span' },
    { tag: 'p', text: 'Paragraph with garage word', class: 'test-paragraph' },
    { tag: 'button', text: 'Regular button', class: 'test-button' }
];

testElements.forEach((elementConfig, index) => {
    const element = document.createElement(elementConfig.tag);
    element.textContent = elementConfig.text;
    element.className = elementConfig.class;
    element.style.position = 'absolute';
    element.style.top = '-1000px'; // Hide off-screen
    document.body.appendChild(element);
    
    let unintendedTrigger = false;
    const originalOpen = window.openDrawer;
    
    window.openDrawer = function() {
        unintendedTrigger = true;
        console.log('[Test] Unintended openDrawer call from:', element);
    };
    
    // Click the test element
    element.click();
    
    logResult(`Unintended Click ${index + 1}`, !unintendedTrigger ? 'pass' : 'fail',
        !unintendedTrigger ? `${elementConfig.tag} correctly ignored` : `${elementConfig.tag} incorrectly triggered garage`);
    
    // Cleanup
    window.openDrawer = originalOpen;
    document.body.removeChild(element);
});

// Test 4: Test text-based trigger validation
console.log('\n4. Text-based Trigger Validation:');

// Test button with "garage" text (should trigger)
const validGarageButton = document.createElement('button');
validGarageButton.textContent = 'Open Garage';
validGarageButton.style.position = 'absolute';
validGarageButton.style.top = '-1000px';
document.body.appendChild(validGarageButton);

let validTrigger = false;
const originalOpen2 = window.openDrawer;

window.openDrawer = function() {
    validTrigger = true;
    console.log('[Test] Valid text-based trigger worked');
};

validGarageButton.click();

logResult('Valid Text Trigger', validTrigger ? 'pass' : 'fail',
    validTrigger ? 'Button with garage text correctly triggers' : 'Valid garage button failed to trigger');

// Cleanup
window.openDrawer = originalOpen2;
document.body.removeChild(validGarageButton);

// Test invalid text trigger (span with garage text - should NOT trigger)
const invalidGarageSpan = document.createElement('span');
invalidGarageSpan.textContent = 'garage information';
invalidGarageSpan.style.position = 'absolute';
invalidGarageSpan.style.top = '-1000px';
document.body.appendChild(invalidGarageSpan);

let invalidTrigger = false;
const originalOpen3 = window.openDrawer;

window.openDrawer = function() {
    invalidTrigger = true;
    console.log('[Test] Invalid text-based trigger incorrectly fired');
};

invalidGarageSpan.click();

logResult('Invalid Text Trigger', !invalidTrigger ? 'pass' : 'fail',
    !invalidTrigger ? 'Span with garage text correctly ignored' : 'Invalid garage span incorrectly triggered');

// Cleanup
window.openDrawer = originalOpen3;
document.body.removeChild(invalidGarageSpan);

// Test 5: Test overlay click behavior
console.log('\n5. Overlay Click Behavior:');

if (garageOverlay) {
    // Test clicking directly on overlay
    let overlayCloseWorked = false;
    const originalClose = window.closeDrawer;
    
    window.closeDrawer = function() {
        overlayCloseWorked = true;
        console.log('[Test] closeDrawer called via overlay click');
    };
    
    // Simulate clicking directly on overlay
    const overlayEvent = new MouseEvent('click', { bubbles: true });
    Object.defineProperty(overlayEvent, 'target', { value: garageOverlay });
    garageOverlay.dispatchEvent(overlayEvent);
    
    logResult('Overlay Direct Click', overlayCloseWorked ? 'pass' : 'fail',
        overlayCloseWorked ? 'Overlay click correctly closes garage' : 'Overlay click failed to close garage');
    
    // Restore original function
    window.closeDrawer = originalClose;
} else {
    logResult('Overlay Click Test', 'warning', 'No overlay found to test');
}

// Test 6: Test close button behavior
console.log('\n6. Close Button Behavior:');

if (garageCloseBtn) {
    let closeBtnWorked = false;
    const originalClose2 = window.closeDrawer;
    
    window.closeDrawer = function() {
        closeBtnWorked = true;
        console.log('[Test] closeDrawer called via close button');
    };
    
    garageCloseBtn.click();
    
    logResult('Close Button Click', closeBtnWorked ? 'pass' : 'fail',
        closeBtnWorked ? 'Close button correctly closes garage' : 'Close button failed to close garage');
    
    // Restore original function
    window.closeDrawer = originalClose2;
} else {
    logResult('Close Button Test', 'warning', 'No close button found to test');
}

// Test 7: Test backdrop persistence prevention
console.log('\n7. Backdrop Persistence Prevention:');

if (garageOverlay) {
    // Test the enhanced closeDrawer function
    console.log('   Testing enhanced closeDrawer function...');
    
    // First, simulate opening the garage
    garageOverlay.classList.remove('hidden');
    garageOverlay.style.display = '';
    
    // Then close it using the enhanced function
    if (typeof closeDrawer === 'function') {
        closeDrawer();
        
        // Check after a delay to see if cleanup worked
        setTimeout(() => {
            const isHidden = garageOverlay.classList.contains('hidden');
            const displayStyle = garageOverlay.style.display;
            
            logResult('Backdrop Cleanup', isHidden ? 'pass' : 'fail',
                isHidden ? 'Overlay properly hidden after close' : 'Overlay not properly hidden');
                
            logResult('Display Style Reset', displayStyle === '' ? 'pass' : 'warning',
                displayStyle === '' ? 'Display style properly reset' : `Display style: ${displayStyle}`);
        }, 100);
    } else {
        logResult('Enhanced Close Function', 'warning', 'closeDrawer function not accessible for testing');
    }
} else {
    logResult('Backdrop Test', 'warning', 'No overlay found to test');
}

// Final summary
setTimeout(() => {
    console.log('\n=== Test Summary ===');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⚠️ Warnings: ${testResults.warnings}`);
    
    const totalTests = testResults.passed + testResults.failed + testResults.warnings;
    const successRate = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
    
    console.log(`\n📊 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 All critical tests passed! Garage modal fixes are working correctly.');
    } else {
        console.log('\n⚠️ Some tests failed. Please review the issues above.');
    }
    
    console.log('\n📋 Expected Behavior After Fixes:');
    console.log('1. ✅ Garage only opens when clicking explicit garage triggers');
    console.log('2. ✅ Random clicks on page elements do not open garage');
    console.log('3. ✅ Text-based triggers are validated (buttons only)');
    console.log('4. ✅ Overlay closes garage with single click');
    console.log('5. ✅ Close button works properly');
    console.log('6. ✅ No gray backdrop persistence after closing');
    
}, 200);

console.log('\n=== Garage modal fix test initiated ===');
