<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Generation Fixes</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography&preflight=false"></script>
    <script src="test-single-generation-fix.js"></script>
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button.danger {
            background: #ef4444;
        }
        .test-button.danger:hover {
            background: #dc2626;
        }
        .test-button.success {
            background: #10b981;
        }
        .test-button.success:hover {
            background: #059669;
        }
        .test-results {
            background: #f9fafb;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
        
        .mock-widget {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            background: #f9fafb;
        }
        
        .mock-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="test-container">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Generation Display Fixes Test</h1>
        
        <!-- Test Status Overview -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Test Status Overview</h2>
            <div id="status-overview">
                <div class="flex items-center mb-2">
                    <span class="status-indicator status-warning"></span>
                    <span>Tests not yet run</span>
                </div>
            </div>
        </div>

        <!-- Bug Simulation Section -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Bug Simulation</h2>
            <p class="text-gray-600 mb-4">
                Simulate the reported bugs to test the fixes.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Bug 1: Generation ID vs Name -->
                <div class="mock-widget">
                    <h3 class="font-medium mb-3">🐞 Bug 1: Generation ID in Results</h3>
                    <p class="text-sm text-gray-600 mb-3">
                        Results should show "Audi A1 GB" not "Audi A1 561918f13a"
                    </p>
                    
                    <div class="mb-3">
                        <label class="block text-sm font-medium mb-1">Mock Generation Data:</label>
                        <select id="mock-generation-data" class="mock-select">
                            <option value='{"slug":"561918f13a","name":"GB"}'>GB (ID: 561918f13a)</option>
                            <option value='{"slug":"8x","name":"8X"}'>8X (ID: 8x)</option>
                            <option value='{"slug":"8x-facelift","name":"8X Facelift"}'>8X Facelift (ID: 8x-facelift)</option>
                        </select>
                    </div>
                    
                    <button onclick="testGenerationNameInResults()" class="test-button">Test Generation Name Display</button>
                    <div id="generation-name-result" class="text-sm mt-2"></div>
                </div>
                
                <!-- Bug 2: Single Generation Auto-fill -->
                <div class="mock-widget">
                    <h3 class="font-medium mb-3">🐞 Bug 2: Single Generation Auto-fill</h3>
                    <p class="text-sm text-gray-600 mb-3">
                        When only one generation exists, selector should show generation name, not modification
                    </p>
                    
                    <div class="mb-3">
                        <label class="block text-sm font-medium mb-1">Generation Selector:</label>
                        <select id="mock-generation-select" class="mock-select">
                            <option value="">Choose a generation</option>
                        </select>
                    </div>
                    
                    <button onclick="testSingleGenerationAutoFill()" class="test-button">Test Single Generation Auto-fill</button>
                    <div id="single-generation-result" class="text-sm mt-2"></div>
                </div>
            </div>
            
            <div id="bug-simulation-results" class="test-results" style="display: none;"></div>
        </div>

        <!-- Function Tests -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Function Tests</h2>
            <p class="text-gray-600 mb-4">
                Test the specific functions that were fixed.
            </p>
            
            <button onclick="testGenerationNameResolution()" class="test-button">Test getGenerationName()</button>
            <button onclick="testVehicleLabelGeneration()" class="test-button">Test getVehicleLabel()</button>
            <button onclick="testPopulateGenerations()" class="test-button">Test populateGenerations()</button>
            
            <div id="function-results" class="test-results" style="display: none;"></div>
        </div>

        <!-- Single Generation Critical Test -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">🚨 Critical: Single Generation Tests</h2>
            <p class="text-gray-600 mb-4">
                Specialized tests for the critical bug where generation ID is displayed instead of human-readable name when only one generation exists.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 class="font-medium text-red-800 mb-2">❌ Bug Example</h3>
                    <p class="text-sm text-red-700">Shows: "Audi A1 561918f13a"</p>
                    <p class="text-xs text-red-600">Internal ID displayed to user</p>
                </div>
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="font-medium text-green-800 mb-2">✅ Expected Result</h3>
                    <p class="text-sm text-green-700">Shows: "Audi A1 GB"</p>
                    <p class="text-xs text-green-600">Human-readable name displayed</p>
                </div>
            </div>

            <button onclick="runSingleGenerationTests()" class="test-button bg-red-600 hover:bg-red-700">🚨 Test Single Generation Fix</button>
            <button onclick="testInternalIdDetection()" class="test-button">Test ID Detection</button>
            <button onclick="showSingleGenDebugInfo()" class="test-button">Single Gen Debug</button>

            <div id="single-generation-results" class="test-results" style="display: none;"></div>
        </div>

        <!-- Comprehensive Test -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Comprehensive Tests</h2>
            <p class="text-gray-600 mb-4">
                Run all generation fix tests.
            </p>

            <button onclick="runAllGenerationTests()" class="test-button bg-green-600 hover:bg-green-700">Run All Tests</button>
            <button onclick="showDebugInfo()" class="test-button">Show Debug Info</button>
            <button onclick="clearResults()" class="test-button bg-gray-600 hover:bg-gray-700">Clear Results</button>

            <div id="comprehensive-results" class="test-results" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Mock widget functions for testing
        window.mockWheelFitWidget = {
            mode: 'byGeneration',
            selectedData: {
                make: 'audi',
                model: 'a1',
                generation: 'gb'
            },
            cache: new Map(),
            
            getGenerationName: function(generationSlug) {
                if (!generationSlug) return 'Unknown Generation';
                
                const { make, model } = this.selectedData;
                const generationsCacheKey = `generations_${make}_${model}`;
                const generations = this.cache.get(generationsCacheKey) || [];
                
                const generation = generations.find(g => {
                    const value = g.slug || g.name || g.title || g.range;
                    return value === generationSlug;
                });
                
                if (generation) {
                    return generation.name || generation.title || generation.range || generation.slug || 'Unknown Generation';
                }
                
                return generationSlug;
            },
            
            getVehicleLabel: function() {
                const { make, model, year, generation } = this.selectedData;
                
                let label;
                if (this.mode === 'byGeneration' && generation) {
                    const generationName = this.getGenerationName(generation);
                    label = `${capitalize(make)} ${capitalize(model)} ${capitalize(generationName)}`;
                } else {
                    label = `${capitalize(make)} ${capitalize(model)} (${year})`;
                }
                
                return label;
            },
            
            populateGenerations: function(generations) {
                const select = document.getElementById('mock-generation-select');
                if (!select) return;
                
                select.innerHTML = '<option value="">Choose a generation</option>';
                
                generations.forEach(g => {
                    const label = g.name || g.title || g.range || g.slug || 'Unknown Generation';
                    const value = g.slug || g.name || g.title || g.range || label;
                    select.add(new Option(label, value));
                });
                
                if (generations.length === 1) {
                    select.value = select.options[1].value;
                    logToResults('bug-simulation-results', `Auto-selected single generation: ${select.options[1].text}`);
                }
            }
        };
        
        // Helper functions
        function capitalize(str) {
            return str ? str.charAt(0).toUpperCase() + str.slice(1).toLowerCase() : '';
        }
        
        function logToResults(elementId, message) {
            const results = document.getElementById(elementId);
            results.style.display = 'block';
            results.textContent += message + '\n';
        }
        
        function clearResults() {
            document.querySelectorAll('.test-results').forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
            
            document.getElementById('status-overview').innerHTML = `
                <div class="flex items-center mb-2">
                    <span class="status-indicator status-warning"></span>
                    <span>Tests not yet run</span>
                </div>
            `;
        }
        
        function updateStatusOverview(testType, passed) {
            const overview = document.getElementById('status-overview');
            const statusClass = passed ? 'status-success' : 'status-error';
            const statusText = passed ? 'Passed' : 'Failed';
            
            overview.innerHTML += `
                <div class="flex items-center mb-2">
                    <span class="status-indicator ${statusClass}"></span>
                    <span>${testType.charAt(0).toUpperCase() + testType.slice(1)} Test: ${statusText}</span>
                </div>
            `;
        }
        
        // Bug simulation tests
        function testGenerationNameInResults() {
            const select = document.getElementById('mock-generation-data');
            const generationData = JSON.parse(select.value);
            
            // Mock cache data
            window.mockWheelFitWidget.cache.set('generations_audi_a1', [generationData]);
            window.mockWheelFitWidget.selectedData.generation = generationData.slug;
            
            const vehicleLabel = window.mockWheelFitWidget.getVehicleLabel();
            
            const resultDiv = document.getElementById('generation-name-result');
            const usesName = vehicleLabel.includes(generationData.name);
            const usesId = vehicleLabel.includes(generationData.slug);
            
            if (usesName && !usesId) {
                resultDiv.innerHTML = `<span class="text-green-600">✅ Correct: "${vehicleLabel}"</span>`;
                updateStatusOverview('generation-name', true);
            } else {
                resultDiv.innerHTML = `<span class="text-red-600">❌ Wrong: "${vehicleLabel}" (should use "${generationData.name}", not "${generationData.slug}")</span>`;
                updateStatusOverview('generation-name', false);
            }
            
            logToResults('bug-simulation-results', `Generation name test: ${vehicleLabel}`);
        }
        
        function testSingleGenerationAutoFill() {
            const singleGeneration = [{ slug: 'gb', name: 'GB' }];
            
            // Clear and populate
            const select = document.getElementById('mock-generation-select');
            window.mockWheelFitWidget.populateGenerations(singleGeneration);
            
            const resultDiv = document.getElementById('single-generation-result');
            const selectedText = select.options[select.selectedIndex]?.text;
            const selectedValue = select.value;
            
            if (selectedValue === 'gb' && selectedText === 'GB') {
                resultDiv.innerHTML = `<span class="text-green-600">✅ Correct: Auto-selected "${selectedText}"</span>`;
                updateStatusOverview('single-generation', true);
            } else {
                resultDiv.innerHTML = `<span class="text-red-600">❌ Wrong: Selected "${selectedText}" (value: "${selectedValue}")</span>`;
                updateStatusOverview('single-generation', false);
            }
            
            logToResults('bug-simulation-results', `Single generation auto-fill: "${selectedText}" (${selectedValue})`);
        }
        
        // Function tests
        function testGenerationNameResolution() {
            const results = document.getElementById('function-results');
            results.style.display = 'block';
            results.textContent = 'Testing getGenerationName function...\n';
            
            // Setup mock data
            const mockGenerations = [
                { slug: 'gb', name: 'GB' },
                { slug: '8x', name: '8X' },
                { slug: '8x-facelift', name: '8X Facelift' }
            ];
            
            window.mockWheelFitWidget.cache.set('generations_audi_a1', mockGenerations);
            
            let passed = 0;
            let total = 0;
            
            // Test each generation
            mockGenerations.forEach(gen => {
                total++;
                const result = window.mockWheelFitWidget.getGenerationName(gen.slug);
                if (result === gen.name) {
                    passed++;
                    results.textContent += `✓ ${gen.slug} → ${result}\n`;
                } else {
                    results.textContent += `✗ ${gen.slug} → ${result} (expected: ${gen.name})\n`;
                }
            });
            
            // Test fallback
            total++;
            const fallbackResult = window.mockWheelFitWidget.getGenerationName('non-existent');
            if (fallbackResult === 'non-existent') {
                passed++;
                results.textContent += `✓ Fallback test passed\n`;
            } else {
                results.textContent += `✗ Fallback test failed: ${fallbackResult}\n`;
            }
            
            results.textContent += `\ngetGenerationName Tests: ${passed}/${total} passed\n`;
            updateStatusOverview('function-tests', passed === total);
        }
        
        function testVehicleLabelGeneration() {
            const results = document.getElementById('function-results');
            results.style.display = 'block';
            results.textContent += '\nTesting getVehicleLabel function...\n';
            
            // Setup mock data
            window.mockWheelFitWidget.cache.set('generations_audi_a1', [{ slug: 'gb', name: 'GB' }]);
            window.mockWheelFitWidget.selectedData = {
                make: 'audi',
                model: 'a1',
                generation: 'gb'
            };
            
            const label = window.mockWheelFitWidget.getVehicleLabel();
            const expected = 'Audi A1 GB';
            
            if (label === expected) {
                results.textContent += `✓ Vehicle label: "${label}"\n`;
                updateStatusOverview('vehicle-label', true);
            } else {
                results.textContent += `✗ Vehicle label: "${label}" (expected: "${expected}")\n`;
                updateStatusOverview('vehicle-label', false);
            }
        }
        
        function testPopulateGenerations() {
            const results = document.getElementById('function-results');
            results.style.display = 'block';
            results.textContent += '\nTesting populateGenerations function...\n';
            
            const mockGenerations = [
                { slug: 'gb', name: 'GB' },
                { slug: '8x', name: '8X' }
            ];
            
            window.mockWheelFitWidget.populateGenerations(mockGenerations);
            
            const select = document.getElementById('mock-generation-select');
            const optionCount = select.options.length - 1; // Exclude placeholder
            
            if (optionCount === mockGenerations.length) {
                results.textContent += `✓ Populated ${optionCount} generations\n`;
                
                // Check option labels
                let labelsCorrect = true;
                for (let i = 1; i < select.options.length; i++) {
                    const option = select.options[i];
                    const expectedGen = mockGenerations[i - 1];
                    if (option.text !== expectedGen.name || option.value !== expectedGen.slug) {
                        labelsCorrect = false;
                        break;
                    }
                }
                
                if (labelsCorrect) {
                    results.textContent += `✓ All generation labels correct\n`;
                    updateStatusOverview('populate-generations', true);
                } else {
                    results.textContent += `✗ Some generation labels incorrect\n`;
                    updateStatusOverview('populate-generations', false);
                }
            } else {
                results.textContent += `✗ Expected ${mockGenerations.length} generations, got ${optionCount}\n`;
                updateStatusOverview('populate-generations', false);
            }
        }
        
        // Single Generation Critical Tests
        function runSingleGenerationTests() {
            const results = document.getElementById('single-generation-results');
            results.style.display = 'block';
            results.textContent = 'Running single generation critical tests...\n';

            if (window.testSingleGenerationFix) {
                const testResults = window.testSingleGenerationFix.runAll();

                testResults.forEach(result => {
                    results.textContent += `${result.passed}/${result.total} tests passed\n`;
                });

                const totalPassed = testResults.reduce((sum, r) => sum + r.passed, 0);
                const totalTests = testResults.reduce((sum, r) => sum + r.total, 0);
                const percentage = totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0;

                results.textContent += `\nOverall: ${totalPassed}/${totalTests} (${percentage}%)\n`;

                if (percentage >= 80) {
                    results.textContent += '✅ Single generation fix is working!\n';
                    updateStatusOverview('single-generation-critical', true);
                } else {
                    results.textContent += '❌ Single generation fix needs attention!\n';
                    updateStatusOverview('single-generation-critical', false);
                }
            } else {
                results.textContent += 'Single generation test script not loaded!\n';
                updateStatusOverview('single-generation-critical', false);
            }
        }

        function testInternalIdDetection() {
            const results = document.getElementById('single-generation-results');
            results.style.display = 'block';
            results.textContent += '\nTesting internal ID detection...\n';

            if (window.testSingleGenerationFix) {
                const result = window.testSingleGenerationFix.internalIdDetection();
                results.textContent += `ID Detection: ${result.passed}/${result.total} passed\n`;
            }
        }

        function showSingleGenDebugInfo() {
            const results = document.getElementById('single-generation-results');
            results.style.display = 'block';
            results.textContent += '\n=== SINGLE GENERATION DEBUG ===\n';

            if (window.testSingleGenerationFix) {
                window.testSingleGenerationFix.debug();
                results.textContent += 'Debug info logged to console.\n';
            }

            // Show mock data
            if (window.testSingleGenerationFix && window.testSingleGenerationFix.mockData) {
                results.textContent += '\nMock Generation Data:\n';
                Object.keys(window.testSingleGenerationFix.mockData).forEach(key => {
                    const data = window.testSingleGenerationFix.mockData[key];
                    results.textContent += `${key}: slug="${data.slug}", name="${data.name}"\n`;
                });
            }
        }

        function runAllGenerationTests() {
            clearResults();

            // Run critical single generation tests first
            runSingleGenerationTests();

            // Then run other tests
            testGenerationNameInResults();
            testSingleGenerationAutoFill();
            setTimeout(() => {
                testGenerationNameResolution();
                testVehicleLabelGeneration();
                testPopulateGenerations();
            }, 500);
        }
        
        function showDebugInfo() {
            const results = document.getElementById('comprehensive-results');
            results.style.display = 'block';
            results.textContent = 'Debug Information:\n';
            results.textContent += `Mock widget available: ${!!window.mockWheelFitWidget}\n`;
            results.textContent += `Mock widget mode: ${window.mockWheelFitWidget?.mode}\n`;
            results.textContent += `Mock selected data: ${JSON.stringify(window.mockWheelFitWidget?.selectedData)}\n`;
            results.textContent += `Mock cache size: ${window.mockWheelFitWidget?.cache?.size || 0}\n`;
            
            // Check if real widget is available
            results.textContent += `Real widget available: ${!!window.wheelFitWidget}\n`;
            if (window.wheelFitWidget) {
                results.textContent += `Real widget mode: ${window.wheelFitWidget.mode}\n`;
                results.textContent += `Real widget functions: ${Object.getOwnPropertyNames(window.wheelFitWidget).filter(name => typeof window.wheelFitWidget[name] === 'function').join(', ')}\n`;
            }
        }
        
        // Initialize mock data
        window.mockWheelFitWidget.cache.set('generations_audi_a1', [
            { slug: 'gb', name: 'GB' },
            { slug: '8x', name: '8X' },
            { slug: '8x-facelift', name: '8X Facelift' }
        ]);
    </script>
</body>
</html>
