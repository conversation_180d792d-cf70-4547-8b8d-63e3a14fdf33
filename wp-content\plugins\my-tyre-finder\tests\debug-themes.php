<?php
/**
 * Debug and Force Update Themes
 */

// Ensure WordPress is loaded
if (!defined('ABSPATH')) {
    require_once dirname(__DIR__, 3) . '/wp-load.php';
}

use MyTyreFinder\Includes\ThemeManager;

echo "🔍 Theme Debug and Force Update\n";
echo "===============================\n\n";

try {
    // 1. Check current themes
    echo "1️⃣ Current themes in database:\n";
    $current_themes = get_option('wsf_theme_presets', []);
    
    if (empty($current_themes)) {
        echo "   ❌ No themes found in database\n";
    } else {
        foreach ($current_themes as $slug => $theme) {
            $name = $theme['name'] ?? $slug;
            $accent = $theme['--wsf-accent'] ?? 'not set';
            echo "   • {$name} ({$slug}): accent = {$accent}\n";
        }
    }
    
    echo "\n2️⃣ Active theme: " . get_option('wsf_active_theme', 'not set') . "\n\n";
    
    // 2. Force clear and regenerate
    echo "3️⃣ Force clearing themes...\n";
    delete_option('wsf_theme_presets');
    echo "   ✅ Cleared database\n";
    
    // 3. Get fresh themes (this will regenerate them)
    echo "4️⃣ Regenerating themes...\n";
    $new_themes = ThemeManager::get_themes();
    echo "   ✅ Regenerated themes\n";
    
    // 4. Verify new themes
    echo "5️⃣ New themes:\n";
    foreach ($new_themes as $slug => $theme) {
        $name = $theme['name'] ?? $slug;
        $accent = $theme['--wsf-accent'] ?? 'not set';
        echo "   • {$name} ({$slug}): accent = {$accent}\n";
        
        if ($slug === 'light' && $accent === '#1E40AF') {
            echo "     ✅ Light theme has correct dark blue accent\n";
        } elseif ($slug === 'light') {
            echo "     ❌ Light theme accent should be #1E40AF\n";
        }
        
        if ($slug === 'dark' && $accent === '#E5E7EB') {
            echo "     ✅ Dark theme has correct light gray accent\n";
        } elseif ($slug === 'dark') {
            echo "     ❌ Dark theme accent should be #E5E7EB\n";
        }
    }
    
    // 5. Set light theme as active
    echo "\n6️⃣ Setting light theme as active...\n";
    $result = ThemeManager::set_active_theme('light');
    echo "   " . ($result ? '✅' : '❌') . " Result: " . ($result ? 'success' : 'failed') . "\n";
    
    echo "\n🎉 Update completed! Check your admin panel now.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
