/**
 * Critical Font Overrides for Wheel Size Finder Widget
 * 
 * This file contains high-specificity CSS rules to ensure Inter font
 * and custom font size are applied to all widget elements, including labels and selectors.
 * 
 * Load this file AFTER wheel-fit-shared.css to override any conflicting styles.
 */

/* ================================================================ */
/* CRITICAL FONT OVERRIDES - Maximum specificity */
/* ================================================================ */

/* Enhanced label styling with maximum specificity to override all theme styles */
.wheel-fit-widget .step-container label,
.wheel-fit-widget label[for^="wf-"],
.wsf-finder-widget .step-container label,
.wsf-finder-widget label[for^="wf-"],
.wheel-fit-widget label.text-sm,
.wheel-fit-widget label.text-xs,
.wsf-finder-widget label.text-sm,
.wsf-finder-widget label.text-xs,
.wheel-fit-widget div label,
.wsf-finder-widget div label,
.wheel-fit-widget form label,
.wsf-finder-widget form label {
  font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  font-size: var(--wsf-font-size, 1rem) !important;
  font-weight: 600 !important;
  color: var(--wsf-text, #1f2937) !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Enhanced wizard step heading styling with maximum specificity */
.wheel-fit-widget .wizard-step h2,
.wsf-finder-widget .wizard-step h2,
.wheel-fit-widget h2.text-2xl,
.wheel-fit-widget h2.font-bold,
.wsf-finder-widget h2.text-2xl,
.wsf-finder-widget h2.font-bold,
.wheel-fit-widget h2.wsf-text-primary,
.wsf-finder-widget h2.wsf-text-primary,
.wheel-fit-widget div h2,
.wsf-finder-widget div h2 {
  font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  font-size: calc(var(--wsf-font-size, 1rem) * 1.5) !important; /* 1.5x base size for headings */
  font-weight: 700 !important;
  color: var(--wsf-primary, #2563eb) !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Enhanced select styling with maximum specificity */
.wheel-fit-widget select.wsf-input,
.wsf-finder-widget select.wsf-input,
.wheel-fit-widget .step-container select,
.wsf-finder-widget .step-container select,
.wheel-fit-widget div select,
.wsf-finder-widget div select,
.wheel-fit-widget form select,
.wsf-finder-widget form select,
.wheel-fit-widget select[id^="wf-"],
.wsf-finder-widget select[id^="wf-"] {
  font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  font-size: var(--wsf-font-size, 1rem) !important;
  font-weight: 500 !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Widget title styling */
.wheel-fit-widget .wsf-widget__title,
.wsf-finder-widget .wsf-widget__title {
  font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  font-size: calc(var(--wsf-font-size, 1rem) * 1.875) !important; /* 1.875x base size for main title */
  font-weight: 700 !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Button styling */
.wheel-fit-widget button,
.wsf-finder-widget button,
.wheel-fit-widget .btn-primary,
.wsf-finder-widget .btn-primary {
  font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  font-size: var(--wsf-font-size, 1rem) !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Input styling */
.wheel-fit-widget input,
.wsf-finder-widget input {
  font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  font-size: var(--wsf-font-size, 1rem) !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Support for variable fonts when available */
@supports (font-variation-settings: normal) {
  .wheel-fit-widget .step-container label,
  .wheel-fit-widget label[for^="wf-"],
  .wsf-finder-widget .step-container label,
  .wsf-finder-widget label[for^="wf-"],
  .wheel-fit-widget label.text-sm,
  .wheel-fit-widget label.text-xs,
  .wsf-finder-widget label.text-sm,
  .wsf-finder-widget label.text-xs,
  .wheel-fit-widget div label,
  .wsf-finder-widget div label,
  .wheel-fit-widget form label,
  .wsf-finder-widget form label {
    font-family: var(--wsf-font-variable, 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  }

  .wheel-fit-widget .wizard-step h2,
  .wsf-finder-widget .wizard-step h2,
  .wheel-fit-widget h2.text-2xl,
  .wheel-fit-widget h2.font-bold,
  .wsf-finder-widget h2.text-2xl,
  .wsf-finder-widget h2.font-bold,
  .wheel-fit-widget h2.wsf-text-primary,
  .wsf-finder-widget h2.wsf-text-primary,
  .wheel-fit-widget div h2,
  .wsf-finder-widget div h2 {
    font-family: var(--wsf-font-variable, 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  }

  .wheel-fit-widget select.wsf-input,
  .wsf-finder-widget select.wsf-input,
  .wheel-fit-widget .step-container select,
  .wsf-finder-widget .step-container select,
  .wheel-fit-widget div select,
  .wsf-finder-widget div select,
  .wheel-fit-widget form select,
  .wsf-finder-widget form select,
  .wheel-fit-widget select[id^="wf-"],
  .wsf-finder-widget select[id^="wf-"] {
    font-family: var(--wsf-font-variable, 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  }

  .wheel-fit-widget .wsf-widget__title,
  .wsf-finder-widget .wsf-widget__title,
  .wheel-fit-widget button,
  .wsf-finder-widget button,
  .wheel-fit-widget .btn-primary,
  .wsf-finder-widget .btn-primary,
  .wheel-fit-widget input,
  .wsf-finder-widget input {
    font-family: var(--wsf-font-variable, 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  }
}

/* Additional overrides for specific WordPress themes that might interfere */
body .wheel-fit-widget label,
body .wsf-finder-widget label,
body .wheel-fit-widget select,
body .wsf-finder-widget select,
body .wheel-fit-widget h2,
body .wsf-finder-widget h2 {
  font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  font-size: var(--wsf-font-size, 1rem) !important;
}

/* Ensure font applies to all nested elements */
.wheel-fit-widget *,
.wsf-finder-widget * {
  font-family: inherit !important;
  font-size: inherit !important;
}

/* Override any theme-specific font declarations */
.wheel-fit-widget,
.wsf-finder-widget {
  font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  font-size: var(--wsf-font-size, 1rem) !important;
}
