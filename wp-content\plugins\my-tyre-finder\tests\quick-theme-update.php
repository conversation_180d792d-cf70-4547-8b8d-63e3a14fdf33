<?php
/**
 * Quick theme update script - run this to immediately update default themes
 */

// Ensure WordPress is loaded
if (!defined('ABSPATH')) {
    die('This script must be run within WordPress context');
}

// Load the ThemeManager class
require_once __DIR__ . '/../src/includes/ThemeManager.php';

use MyTyreFinder\Includes\ThemeManager;

echo "🚀 Quick Theme Update\n";
echo "====================\n\n";

try {
    // Force update default themes
    echo "Updating default themes...\n";
    $result = ThemeManager::force_update_default_themes();
    
    if ($result) {
        echo "✅ Default themes updated successfully!\n\n";
        
        // Verify the update
        $themes = ThemeManager::get_themes();
        
        echo "📊 Current themes:\n";
        foreach ($themes as $slug => $theme) {
            $name = $theme['name'] ?? $slug;
            $primary = $theme['--wsf-primary'] ?? 'not set';
            echo "  • {$name} ({$slug}): {$primary}\n";
        }
        
        echo "\n🎉 Update completed! Refresh your admin panel to see the new colors.\n";
        
    } else {
        echo "❌ Failed to update themes\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
