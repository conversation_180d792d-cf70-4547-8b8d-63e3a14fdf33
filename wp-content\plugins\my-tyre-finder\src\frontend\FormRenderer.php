<?php

declare(strict_types=1);

namespace MyTyreFinder\Frontend;

use Twig\Environment;
use Twig\Loader\FilesystemLoader;
use MyTyreFinder\Translations\TranslationManager;

class FormRenderer
{
    private Environment $templateRenderer;

    public function __construct()
    {
        $templatePath = trailingslashit(dirname(__DIR__, 2)) . 'templates';
        $loader = new FilesystemLoader($templatePath);
        $this->templateRenderer = new Environment($loader, [
            'cache' => false, // Disable cache for development
        ]);
    }

    /**
     * Renders the search form.
     */
    public function render(): string
    {
        $options = [
            'flow'   => get_option('wheel_size_active_flow', 'none'),
            'layout' => get_option('wheel_size_form_layout', 'stepper'),
        ];

        $template_name = 'form-container.twig';
        if ($options['layout'] === 'wizard') {
            $template_name = 'wizard-flow.twig';
        }

        $template_name = apply_filters('wheel_size_template_for_layout', $template_name, $options);

        $widget_title = TranslationManager::get('widget_title') ?: 'Wheel & Tyre Finder';

        // Check if template exists, fallback to finder-form.twig if not
        try {
            $this->templateRenderer->getLoader()->getSourceContext($template_name);
        } catch (\Twig\Error\LoaderError $e) {
            // form-container.twig doesn't exist, use finder-form.twig as fallback
            $template_name = 'finder-form.twig';
        }

        return $this->templateRenderer->render($template_name, [
            'nonce' => wp_create_nonce('wheel_fit_nonce'),
            'options' => $options,
            'widget_title' => $widget_title,
            'garage_enabled' => \MyTyreFinder\Admin\Admin::garage_enabled(),
            'form_layout' => $options['layout'],
            'active_flow' => $options['flow'],
            'flow_order' => null, // Will be set by template if needed
            'primary_color' => get_option('wheel_size_primary_color', '#3b82f6'),
            'show_by_vehicle' => true,
            'show_by_size' => false
        ]);
    }
}
