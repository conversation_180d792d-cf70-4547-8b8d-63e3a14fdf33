/**
 * Test script to verify garage button alignment
 * Run in browser console on admin appearance page
 */

console.log('📐 === GARAGE BUTTON ALIGNMENT TEST ===');

const checkbox = document.getElementById('auto_search_on_last_input');
const previewContainer = document.getElementById('widget-preview');

if (!checkbox || !previewContainer) {
    console.log('❌ Required elements not found');
    return;
}

function testGarageAlignment(autoSearchEnabled) {
    const previewWidget = previewContainer.querySelector('.wheel-fit-widget');
    if (!previewWidget) {
        console.log('   ❌ Preview widget not found');
        return;
    }

    const garageButtons = previewWidget.querySelectorAll('[data-garage-trigger]');
    
    console.log(`\n${autoSearchEnabled ? 'Auto-search ON' : 'Auto-search OFF'} - Garage Button Alignment:`);
    console.log('   Garage buttons found:', garageButtons.length);
    
    garageButtons.forEach((button, index) => {
        const container = button.parentElement;
        const containerStyle = window.getComputedStyle(container);
        
        console.log(`   Button ${index + 1}:`);
        console.log('     Container classes:', container.className);
        console.log('     Container display:', containerStyle.display);
        console.log('     Container justify-content:', containerStyle.justifyContent);
        console.log('     Container text-align:', containerStyle.textAlign);
        
        // Check if properly aligned to the right
        const isFlexEnd = containerStyle.justifyContent === 'flex-end';
        const isTextRight = containerStyle.textAlign === 'right';
        const hasJustifyEnd = container.classList.contains('justify-end');
        
        console.log('     Is right-aligned:', (isFlexEnd || isTextRight || hasJustifyEnd) ? '✅' : '❌');
        
        // Get button position
        const buttonRect = button.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        const rightOffset = containerRect.right - buttonRect.right;
        
        console.log('     Right offset from container:', Math.round(rightOffset) + 'px');
        console.log('     Properly positioned:', rightOffset < 20 ? '✅' : '❌'); // Allow small margin
    });
}

console.log('\n1. Current State:');
console.log('   Auto-search enabled:', checkbox.checked);
testGarageAlignment(checkbox.checked);

// Test toggle
console.log('\n🔄 Testing toggle...');
const originalState = checkbox.checked;

checkbox.checked = !originalState;
checkbox.dispatchEvent(new Event('change', { bubbles: true }));

setTimeout(() => {
    console.log('\n2. After Toggle:');
    testGarageAlignment(checkbox.checked);
    
    // Restore
    checkbox.checked = originalState;
    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
    
    setTimeout(() => {
        console.log('\n3. Restored State:');
        testGarageAlignment(originalState);
        
        console.log('\n✅ Test completed. Garage buttons should be right-aligned in both modes.');
    }, 1500);
    
}, 1500);

console.log('\n⏳ Running alignment test...');
