<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wizard Structure Test - No Nested Forms</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .wizard-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .brand-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        
        .brand-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
            min-height: 80px;
        }
        
        .brand-button:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }
        
        .brand-button.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 3px;
            width: 20%;
            transition: width 0.3s;
        }
        
        .step-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .step-name {
            color: #9ca3af;
        }
        
        .step-name.active {
            color: #3b82f6;
        }
        
        .diagnostic-result {
            padding: 12px;
            border-radius: 6px;
            margin: 8px 0;
            font-family: monospace;
        }
        
        .diagnostic-pass {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .diagnostic-fail {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .diagnostic-info {
            background: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #bfdbfe;
        }
        
        button {
            font-family: inherit;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 4px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <h1>🧙 Wizard Structure Test - No Nested Forms</h1>
    
    <div class="test-section">
        <h2>📋 Test Overview</h2>
        <p>This page demonstrates the correct wizard structure without nested forms. The wizard uses buttons instead of form inputs for brand/model selection.</p>
        
        <div id="diagnostic-results">
            <h3>🔍 Diagnostic Results</h3>
            <div id="results-container">
                <!-- Results will be populated by JavaScript -->
            </div>
        </div>
        
        <button class="test-button" onclick="runDiagnostic()">🔍 Run Diagnostic</button>
        <button class="test-button" onclick="simulateNestedForm()">⚠️ Simulate Nested Form Issue</button>
        <button class="test-button" onclick="fixIssues()">🔧 Fix Issues</button>
    </div>
    
    <div class="test-section">
        <h2>✅ Correct Wizard Structure (No Forms)</h2>
        
        <!-- This is the CORRECT structure - no form elements -->
        <div id="wheel-fit-wizard" class="wizard-container">
            <h1>Wheel & Tyre Finder</h1>
            
            <!-- Progress indicator -->
            <div class="step-header">
                <span class="step-name active">Make</span>
                <span class="step-name">Model</span>
                <span class="step-name">Year</span>
                <span class="step-name">Modification</span>
                <span class="step-name">Results</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            
            <!-- Step content - NO FORMS -->
            <div class="wsf-form-wrapper">
                <div id="wizard-step-1" class="wizard-step">
                    <h2>Select Manufacturer</h2>
                    <div id="wizard-makes-grid" class="brand-grid">
                        <!-- These are BUTTONS, not form inputs -->
                        <button type="button" class="brand-button" data-brand="audi">
                            <div>🚗</div>
                            <span>Audi</span>
                        </button>
                        <button type="button" class="brand-button" data-brand="bmw">
                            <div>🚗</div>
                            <span>BMW</span>
                        </button>
                        <button type="button" class="brand-button" data-brand="mercedes">
                            <div>🚗</div>
                            <span>Mercedes</span>
                        </button>
                        <button type="button" class="brand-button" data-brand="volkswagen">
                            <div>🚗</div>
                            <span>Volkswagen</span>
                        </button>
                        <button type="button" class="brand-button" data-brand="toyota">
                            <div>🚗</div>
                            <span>Toyota</span>
                        </button>
                        <button type="button" class="brand-button" data-brand="honda">
                            <div>🚗</div>
                            <span>Honda</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>❌ Incorrect Structure (Nested Forms) - FOR TESTING ONLY</h2>
        <p><strong>This is what NOT to do:</strong></p>
        
        <!-- This will be created by the simulate function to show the problem -->
        <div id="nested-form-example" style="display: none;">
            <!-- This would be WRONG - nested forms -->
        </div>
    </div>

    <script>
        // Diagnostic functions
        function checkForNestedForms() {
            const allForms = document.querySelectorAll('form');
            let nestedCount = 0;
            
            allForms.forEach(form => {
                const nested = form.querySelectorAll('form');
                nestedCount += nested.length;
            });
            
            return {
                totalForms: allForms.length,
                nestedForms: nestedCount,
                isValid: nestedCount === 0
            };
        }
        
        function checkWizardStructure() {
            const wizard = document.getElementById('wheel-fit-wizard');
            if (!wizard) {
                return { found: false, isValid: false, message: 'Wizard not found' };
            }
            
            const formsInWizard = wizard.querySelectorAll('form');
            const buttonsInWizard = wizard.querySelectorAll('button[type="button"]');
            
            return {
                found: true,
                isValid: formsInWizard.length === 0,
                formsCount: formsInWizard.length,
                buttonsCount: buttonsInWizard.length,
                message: formsInWizard.length === 0 ? 'Wizard structure is correct' : 'Forms found in wizard'
            };
        }
        
        function checkButtonTypes() {
            const wizard = document.getElementById('wheel-fit-wizard');
            if (!wizard) return { isValid: false, message: 'Wizard not found' };
            
            const buttons = wizard.querySelectorAll('button');
            let invalidButtons = 0;
            
            buttons.forEach(button => {
                if (button.type !== 'button') {
                    invalidButtons++;
                }
            });
            
            return {
                isValid: invalidButtons === 0,
                totalButtons: buttons.length,
                invalidButtons: invalidButtons,
                message: invalidButtons === 0 ? 'All buttons properly configured' : `${invalidButtons} buttons need type="button"`
            };
        }
        
        function displayResult(test, result) {
            const className = result.isValid ? 'diagnostic-pass' : 'diagnostic-fail';
            const icon = result.isValid ? '✅' : '❌';
            
            return `<div class="${className}">${icon} ${test}: ${result.message || JSON.stringify(result)}</div>`;
        }
        
        function runDiagnostic() {
            console.log('🔍 Running wizard structure diagnostic...');
            
            const results = {
                nestedForms: checkForNestedForms(),
                wizardStructure: checkWizardStructure(),
                buttonTypes: checkButtonTypes()
            };
            
            let html = '<h4>🔍 Diagnostic Results:</h4>';
            html += displayResult('Nested Forms Check', results.nestedForms);
            html += displayResult('Wizard Structure Check', results.wizardStructure);
            html += displayResult('Button Types Check', results.buttonTypes);
            
            const allValid = results.nestedForms.isValid && 
                           results.wizardStructure.isValid && 
                           results.buttonTypes.isValid;
            
            if (allValid) {
                html += '<div class="diagnostic-pass">🎉 All tests passed! Wizard structure is correct.</div>';
            } else {
                html += '<div class="diagnostic-fail">⚠️ Issues detected. Use the fix button to resolve them.</div>';
            }
            
            document.getElementById('results-container').innerHTML = html;
            
            console.log('Diagnostic results:', results);
            return results;
        }
        
        function simulateNestedForm() {
            const container = document.getElementById('nested-form-example');
            container.style.display = 'block';
            container.innerHTML = `
                <div style="border: 2px solid #dc2626; padding: 16px; border-radius: 8px; background: #fef2f2;">
                    <h4 style="color: #dc2626; margin-top: 0;">❌ WRONG: Nested Form Structure</h4>
                    <form id="outer-form" style="border: 1px solid #999; padding: 12px; margin: 8px 0;">
                        <p>Outer Form</p>
                        <form id="inner-form" style="border: 1px solid #666; padding: 8px; background: #f9f9f9;">
                            <p>❌ Nested Form (Invalid HTML)</p>
                            <input type="radio" name="brand" value="audi"> Audi<br>
                            <input type="radio" name="brand" value="bmw"> BMW<br>
                        </form>
                    </form>
                    <p style="color: #dc2626; font-size: 14px;">This structure causes HTML validation errors and breaks functionality!</p>
                </div>
            `;
            
            setTimeout(() => {
                runDiagnostic();
            }, 100);
        }
        
        function fixIssues() {
            // Remove any nested forms
            const allForms = document.querySelectorAll('form');
            let fixesApplied = 0;
            
            allForms.forEach(form => {
                const nestedForms = form.querySelectorAll('form');
                nestedForms.forEach(nestedForm => {
                    const div = document.createElement('div');
                    div.innerHTML = nestedForm.innerHTML;
                    div.className = nestedForm.className;
                    nestedForm.parentNode.replaceChild(div, nestedForm);
                    fixesApplied++;
                });
            });
            
            // Fix button types
            const wizard = document.getElementById('wheel-fit-wizard');
            if (wizard) {
                const buttons = wizard.querySelectorAll('button');
                buttons.forEach(button => {
                    if (button.type !== 'button') {
                        button.type = 'button';
                        fixesApplied++;
                    }
                });
            }
            
            if (fixesApplied > 0) {
                alert(`✅ Applied ${fixesApplied} fix(es). Running diagnostic...`);
                setTimeout(runDiagnostic, 100);
            } else {
                alert('ℹ️ No issues found to fix.');
            }
        }
        
        // Add click handlers to brand buttons
        document.addEventListener('DOMContentLoaded', function() {
            const brandButtons = document.querySelectorAll('.brand-button');
            brandButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove previous selection
                    brandButtons.forEach(btn => btn.classList.remove('selected'));
                    // Add selection to clicked button
                    this.classList.add('selected');
                    
                    console.log('Brand selected:', this.dataset.brand);
                });
            });
            
            // Run initial diagnostic
            setTimeout(runDiagnostic, 500);
        });
    </script>
</body>
</html>
