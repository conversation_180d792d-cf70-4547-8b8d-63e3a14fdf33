// Debug script for API validation issues
console.log('=== API Validation Debug Test ===');

// Test results tracking
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function logResult(test, status, message) {
    const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${test}: ${message}`);
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Test 1: Check current API configuration status
console.log('\n1. Current API Configuration Status:');
const apiKeyInput = document.getElementById('api_key');
const statusIndicator = document.querySelector('p[style*="color: #46b450"], p[style*="color: #dc3232"]');

if (statusIndicator) {
    const isConfigured = statusIndicator.textContent.includes('validated and active');
    const isNotConfigured = statusIndicator.textContent.includes('not validated');
    
    logResult('Status indicator found', 'pass', `Status: ${statusIndicator.textContent.trim()}`);
    
    if (isConfigured) {
        logResult('API configured status', 'pass', 'API appears to be configured');
    } else if (isNotConfigured) {
        logResult('API configured status', 'warning', 'API appears to be NOT configured');
    } else {
        logResult('API configured status', 'warning', 'Status unclear from indicator');
    }
} else {
    logResult('Status indicator', 'fail', 'No status indicator found');
}

// Test 2: Check admin notices
console.log('\n2. Admin Notices Check:');
const adminNotices = document.querySelectorAll('.notice');
const apiNotice = Array.from(adminNotices).find(notice => 
    notice.textContent.includes('Plugin functionality is disabled') ||
    notice.textContent.includes('API key') ||
    notice.textContent.includes('inactive')
);

if (apiNotice) {
    const isErrorNotice = apiNotice.classList.contains('notice-error');
    const isSuccessNotice = apiNotice.classList.contains('notice-success');
    
    logResult('Admin notice found', 'warning', `Notice: ${apiNotice.textContent.trim()}`);
    logResult('Notice type', isErrorNotice ? 'warning' : isSuccessNotice ? 'pass' : 'warning', 
        `Type: ${isErrorNotice ? 'error' : isSuccessNotice ? 'success' : 'other'}`);
} else {
    logResult('Admin notices', 'pass', 'No API-related admin notices found (good if configured)');
}

// Test 3: Check menu items availability
console.log('\n3. Admin Menu Items Check:');
const menuItems = document.querySelectorAll('a[href*="wheel-size"]');
const apiSettingsItem = Array.from(menuItems).find(item => 
    item.href.includes('page=wheel-size') && !item.href.includes('page=wheel-size-')
);
const otherItems = Array.from(menuItems).filter(item => 
    item.href.includes('page=wheel-size-')
);

logResult('API Settings menu item', apiSettingsItem ? 'pass' : 'fail', 
    apiSettingsItem ? 'API Settings accessible' : 'API Settings not found');

if (otherItems.length > 0) {
    logResult('Other menu items', 'pass', `Found ${otherItems.length} other menu items: ${otherItems.map(i => i.textContent.trim()).join(', ')}`);
} else {
    logResult('Other menu items', 'warning', 'No other menu items found (expected if API not configured)');
}

// Test 4: Test API validation functionality
console.log('\n4. API Test Functionality:');
const testButton = document.getElementById('test-api-key');
const statusSpan = document.getElementById('api-test-status');
const resultDiv = document.getElementById('api-test-result');

if (testButton && apiKeyInput) {
    logResult('Test elements present', 'pass', 'Test button and API key input found');
    
    // Check if there's an API key to test
    const currentApiKey = apiKeyInput.value.trim();
    if (currentApiKey) {
        logResult('API key present', 'pass', `API key length: ${currentApiKey.length} characters`);
        
        // Simulate test button click for debugging
        console.log('\n   🔧 Manual Test Instructions:');
        console.log('   1. Click the "Test Connection" button');
        console.log('   2. Watch for status changes in the console');
        console.log('   3. Check if page reloads after successful validation');
        
        // Add event listener to monitor test button clicks
        if (!testButton.hasAttribute('data-debug-listener')) {
            testButton.setAttribute('data-debug-listener', 'true');
            testButton.addEventListener('click', function() {
                console.log('🔄 Test Connection button clicked');
                console.log('📤 API Key being tested:', apiKeyInput.value.substring(0, 10) + '...');
                
                // Monitor status changes
                const originalStatus = statusSpan ? statusSpan.textContent : '';
                const checkStatusChange = setInterval(() => {
                    const newStatus = statusSpan ? statusSpan.textContent : '';
                    if (newStatus !== originalStatus) {
                        console.log('📊 Status changed:', originalStatus, '→', newStatus);
                        clearInterval(checkStatusChange);
                    }
                }, 100);
                
                // Stop monitoring after 10 seconds
                setTimeout(() => clearInterval(checkStatusChange), 10000);
            });
        }
        
    } else {
        logResult('API key present', 'warning', 'No API key entered for testing');
    }
} else {
    logResult('Test functionality', 'warning', 'Test elements not found (may not be on API settings page)');
}

// Test 5: Check for JavaScript errors
console.log('\n5. JavaScript Error Check:');
let jsErrors = [];
const originalError = window.onerror;
window.onerror = function(msg, url, line, col, error) {
    jsErrors.push({msg, url, line, col, error});
    if (originalError) originalError.apply(this, arguments);
};

setTimeout(() => {
    if (jsErrors.length === 0) {
        logResult('JavaScript errors', 'pass', 'No JavaScript errors detected');
    } else {
        logResult('JavaScript errors', 'fail', `${jsErrors.length} errors detected`);
        jsErrors.forEach((err, i) => {
            console.log(`   Error ${i+1}: ${err.msg} at ${err.url}:${err.line}`);
        });
    }
}, 1000);

// Test 6: Check AJAX functionality
console.log('\n6. AJAX Functionality Test:');
if (typeof ajaxurl !== 'undefined') {
    logResult('AJAX URL available', 'pass', `AJAX URL: ${ajaxurl}`);
    
    // Test a simple AJAX call to check if API validation endpoint works
    console.log('   🔧 Testing AJAX endpoint availability...');
    
    fetch(ajaxurl, {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: new URLSearchParams({
            action: 'wheel_size_test_api',
            nonce: document.querySelector('input[name="_wpnonce"]')?.value || '',
            api_key: 'test-key-for-endpoint-check'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success === false) {
            logResult('AJAX endpoint', 'pass', 'AJAX endpoint responding (returned expected error for test key)');
        } else {
            logResult('AJAX endpoint', 'warning', 'AJAX endpoint response: ' + JSON.stringify(data));
        }
    })
    .catch(error => {
        logResult('AJAX endpoint', 'fail', 'AJAX endpoint error: ' + error.message);
    });
} else {
    logResult('AJAX functionality', 'warning', 'AJAX URL not available');
}

// Test 7: Database flag simulation
console.log('\n7. Database Flag Simulation:');
console.log('   🔧 To manually test database flag:');
console.log('   1. Open browser developer tools');
console.log('   2. Go to Application/Storage → Local Storage');
console.log('   3. Check if any wheel-size related data is cached');
console.log('   4. Clear cache and reload page');
console.log('   5. Check if status changes');

// Test 8: URL parameters check
console.log('\n8. URL Parameters Check:');
const urlParams = new URLSearchParams(window.location.search);
const hasApiValidated = urlParams.has('api_validated');
const hasApiError = urlParams.has('api_error');
const hasSaved = urlParams.has('saved');

logResult('URL parameters', 'pass', `Parameters: saved=${hasSaved}, api_validated=${hasApiValidated}, api_error=${hasApiError}`);

if (hasApiValidated) {
    logResult('API validation redirect', 'pass', 'Page was redirected after successful validation');
} else if (hasApiError) {
    logResult('API validation redirect', 'warning', 'Page was redirected after validation error');
} else {
    logResult('API validation redirect', 'warning', 'No validation redirect parameters found');
}

// Test 9: Page reload simulation
console.log('\n9. Page Reload Test:');
console.log('   🔧 Manual reload test:');
console.log('   1. Note current status indicators');
console.log('   2. Reload the page (F5 or Ctrl+R)');
console.log('   3. Check if status indicators change');
console.log('   4. Check if menu items appear/disappear');

// Final summary and recommendations
setTimeout(() => {
    console.log('\n=== API Validation Debug Summary ===');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⚠️ Warnings: ${testResults.warnings}`);
    
    console.log('\n🔧 Debugging Recommendations:');
    
    if (testResults.failed > 0) {
        console.log('❌ Critical issues found:');
        console.log('   - Check browser console for JavaScript errors');
        console.log('   - Verify AJAX endpoints are working');
        console.log('   - Check WordPress admin permissions');
    }
    
    if (testResults.warnings > testResults.passed) {
        console.log('⚠️ Potential issues detected:');
        console.log('   - API may not be properly configured');
        console.log('   - Database flag may not be updating');
        console.log('   - Page may need manual reload after validation');
    }
    
    console.log('\n🔍 Manual Testing Steps:');
    console.log('1. Clear any existing API key');
    console.log('2. Save settings (should show "not configured" status)');
    console.log('3. Enter a valid API key');
    console.log('4. Click "Test Connection"');
    console.log('5. Watch for status changes and page reload');
    console.log('6. Check if menu items appear after reload');
    
    console.log('\n📊 Expected Behavior:');
    console.log('✅ Before validation: Red status, limited menu, error notices');
    console.log('✅ During validation: Loading spinner, "Testing..." status');
    console.log('✅ After validation: Green status, page reload, full menu, success notices');
    
}, 2000);

console.log('\n=== Debug test initiated - watch for results above ===');
