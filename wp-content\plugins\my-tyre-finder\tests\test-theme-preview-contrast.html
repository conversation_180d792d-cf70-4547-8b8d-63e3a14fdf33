<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Preview Contrast Test - Улучшение контрастности превью тем</title>
    
    <!-- Подключаем CSS файлы -->
    <link rel="stylesheet" href="../assets/css/fonts.css">
    <link rel="stylesheet" href="../assets/css/admin-theme-panel.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-section {
            margin: 40px 0;
            padding: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #fafafa;
        }
        
        .comparison-section h2 {
            margin-top: 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            padding: 10px;
            background: #e5e7eb;
            border-radius: 8px;
        }
        
        .before-section {
            border-color: #dc2626;
        }
        
        .before-section h2 {
            background: #dc2626;
            color: white;
        }
        
        .after-section {
            border-color: #16a34a;
        }
        
        .after-section h2 {
            background: #16a34a;
            color: white;
        }
        
        .theme-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .old-swatch {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: none;
            display: inline-block;
            margin-right: 8px;
        }
        
        .analysis {
            margin: 30px 0;
            padding: 20px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
        }
        
        .analysis h3 {
            margin-top: 0;
            color: #0c4a6e;
        }
        
        .improvement-list {
            list-style: none;
            padding: 0;
        }
        
        .improvement-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e0f2fe;
        }
        
        .improvement-list li:before {
            content: "✅ ";
            color: #16a34a;
            font-weight: bold;
        }
        
        .color-info {
            font-family: monospace;
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">Theme Preview Contrast Test</h1>
            <p style="color: #6b7280; font-size: 18px;">Тестирование улучшений контрастности превью тем в Theme Presets</p>
        </header>

        <!-- До исправления -->
        <div class="comparison-section before-section">
            <h2>❌ До исправления - Проблемы с контрастностью</h2>
            
            <div class="theme-grid">
                <!-- Light Theme - старый вариант -->
                <div class="wsf-theme-card" style="background: white;">
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">Light Theme</h3>
                    </div>
                    <div class="wsf-theme-card__colors">
                        <div class="old-swatch" style="background-color: #ffffff" title="Background: #ffffff"></div>
                        <div class="old-swatch" style="background-color: #1f2937" title="Text: #1f2937"></div>
                        <div class="old-swatch" style="background-color: #2563eb" title="Primary: #2563eb"></div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280; margin: 10px 0 0;">
                        <span class="color-info">Bg: #ffffff</span> - Чисто белый, не различим от фона карточки
                    </p>
                </div>

                <!-- Dark Theme - старый вариант -->
                <div class="wsf-theme-card" style="background: white;">
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">Dark Theme</h3>
                    </div>
                    <div class="wsf-theme-card__colors">
                        <div class="old-swatch" style="background-color: #ffffff" title="Background: #ffffff"></div>
                        <div class="old-swatch" style="background-color: #1f2937" title="Text: #1f2937"></div>
                        <div class="old-swatch" style="background-color: #2563eb" title="Primary: #2563eb"></div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280; margin: 10px 0 0;">
                        <span class="color-info">Bg: #ffffff</span> - Белый фон для темной темы! Неправильно!
                    </p>
                </div>

                <!-- Green Theme - старый вариант -->
                <div class="wsf-theme-card" style="background: white;">
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">Green Theme</h3>
                    </div>
                    <div class="wsf-theme-card__colors">
                        <div class="old-swatch" style="background-color: #ffffff" title="Background: #ffffff"></div>
                        <div class="old-swatch" style="background-color: #1f2937" title="Text: #1f2937"></div>
                        <div class="old-swatch" style="background-color: #16a34a" title="Primary: #16a34a"></div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280; margin: 10px 0 0;">
                        <span class="color-info">Bg: #ffffff</span> - Белый фон не показывает зеленую тему
                    </p>
                </div>
            </div>
        </div>

        <!-- После исправления -->
        <div class="comparison-section after-section">
            <h2>✅ После исправления - Улучшенная контрастность</h2>
            
            <div class="theme-grid">
                <!-- Light Theme - новый вариант -->
                <div class="wsf-theme-card" data-theme-id="light" style="background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);">
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">Light Theme</h3>
                    </div>
                    <div class="wsf-theme-card__colors">
                        <div class="wsf-theme-card__swatch" style="background-color: #F8FAFC" title="Background: #F8FAFC"></div>
                        <div class="wsf-theme-card__swatch" style="background-color: #1E293B" title="Text: #1E293B"></div>
                        <div class="wsf-theme-card__swatch" style="background-color: #2563EB" title="Primary: #2563EB"></div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280; margin: 10px 0 0;">
                        <span class="color-info">Bg: #F8FAFC</span> - Slate-50, видимый контраст
                    </p>
                </div>

                <!-- Dark Theme - новый вариант -->
                <div class="wsf-theme-card" data-theme-id="dark" style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);">
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">Dark Theme</h3>
                    </div>
                    <div class="wsf-theme-card__colors">
                        <div class="wsf-theme-card__swatch" style="background-color: #0F172A" title="Background: #0F172A"></div>
                        <div class="wsf-theme-card__swatch" style="background-color: #F1F5F9" title="Text: #F1F5F9"></div>
                        <div class="wsf-theme-card__swatch" style="background-color: #3B82F6" title="Primary: #3B82F6"></div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280; margin: 10px 0 0;">
                        <span class="color-info">Bg: #0F172A</span> - Slate-900, реальный темный фон!
                    </p>
                </div>

                <!-- Green Theme - новый вариант -->
                <div class="wsf-theme-card" data-theme-id="green">
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">Green Theme</h3>
                    </div>
                    <div class="wsf-theme-card__colors">
                        <div class="wsf-theme-card__swatch" style="background-color: #F0FDF4" title="Background: #F0FDF4"></div>
                        <div class="wsf-theme-card__swatch" style="background-color: #14532D" title="Text: #14532D"></div>
                        <div class="wsf-theme-card__swatch" style="background-color: #16A34A" title="Primary: #16A34A"></div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280; margin: 10px 0 0;">
                        <span class="color-info">Bg: #F0FDF4</span> - Green-50, зеленоватый оттенок
                    </p>
                </div>

                <!-- Blue Theme - новый вариант -->
                <div class="wsf-theme-card" data-theme-id="blue">
                    <div class="wsf-theme-card__header">
                        <h3 class="wsf-theme-card__name">Blue Theme</h3>
                    </div>
                    <div class="wsf-theme-card__colors">
                        <div class="wsf-theme-card__swatch" style="background-color: #EFF6FF" title="Background: #EFF6FF"></div>
                        <div class="wsf-theme-card__swatch" style="background-color: #1E3A8A" title="Text: #1E3A8A"></div>
                        <div class="wsf-theme-card__swatch" style="background-color: #2563EB" title="Primary: #2563EB"></div>
                    </div>
                    <p style="font-size: 12px; color: #6b7280; margin: 10px 0 0;">
                        <span class="color-info">Bg: #EFF6FF</span> - Blue-50, голубоватый оттенок
                    </p>
                </div>
            </div>
        </div>

        <div class="analysis">
            <h3>📊 Анализ улучшений</h3>
            
            <h4>Основные проблемы, которые были решены:</h4>
            <ul class="improvement-list">
                <li><strong>Белый фон для всех тем:</strong> Заменен на тематически подходящие цвета</li>
                <li><strong>Dark Theme с белым фоном:</strong> Теперь использует реальный темный цвет #0F172A</li>
                <li><strong>Отсутствие различий между темами:</strong> Каждая тема теперь визуально уникальна</li>
                <li><strong>Плохая видимость светлых цветов:</strong> Добавлены тонкие рамки для контраста</li>
                <li><strong>Неинформативные превью:</strong> Теперь превью реально отражают цветовую схему</li>
            </ul>

            <h4>Технические улучшения:</h4>
            <ul class="improvement-list">
                <li><strong>Умные fallback цвета:</strong> Метод getThemeColorWithFallback() с контекстными значениями</li>
                <li><strong>Улучшенные рамки:</strong> Тонкие рамки rgba(0,0,0,0.08) для лучшего определения</li>
                <li><strong>Специальные стили для темных тем:</strong> Светлые рамки для темных цветов</li>
                <li><strong>Градиентные фоны карточек:</strong> Тонкие градиенты для дополнительной глубины</li>
                <li><strong>Тематические цвета:</strong> Green-50, Blue-50 для соответствующих тем</li>
            </ul>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f0fdf4; border: 1px solid #16a34a; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #15803d;">🎉 Результат</h3>
            <p><strong>Проблема решена:</strong> Теперь превью тем в Theme Presets визуально различимы и информативны.</p>
            <p><strong>Пользователи могут:</strong></p>
            <ul>
                <li>Легко различать темы по цветовым превью</li>
                <li>Понимать, какие цвета использует каждая тема</li>
                <li>Видеть реальные цвета фона, а не белые заглушки</li>
                <li>Быстро выбирать подходящую тему для своего сайта</li>
            </ul>
        </div>
    </div>

    <script>
        // Добавляем интерактивность для демонстрации
        document.addEventListener('DOMContentLoaded', function() {
            const swatches = document.querySelectorAll('.wsf-theme-card__swatch');
            
            swatches.forEach(swatch => {
                swatch.addEventListener('click', function() {
                    const color = this.style.backgroundColor;
                    const title = this.getAttribute('title');
                    alert(`Цвет: ${title}\nRGB: ${color}`);
                });
            });
        });
    </script>
</body>
</html>
