<?php

declare(strict_types=1);

namespace MyTyreFinder\Admin;

use MyTyreFinder\Services\WheelSizeApi;

/**
 * Manages API key validation and configuration status
 */
final class ApiValidator
{
    private const API_CONFIGURED_OPTION = 'wheel_size_api_configured';
    private const API_KEY_OPTION = 'wheel_size_api_key';
    
    /**
     * Check if API is properly configured and validated
     */
    public static function is_api_configured(): bool
    {
        return (bool) get_option(self::API_CONFIGURED_OPTION, false);
    }
    
    /**
     * Set API configuration status
     */
    public static function set_api_configured(bool $configured): void
    {
        update_option(self::API_CONFIGURED_OPTION, $configured);
    }
    
    /**
     * Get current API key
     */
    public static function get_api_key(): string
    {
        return get_option(self::API_KEY_OPTION, '');
    }
    
    /**
     * Validate API key by making a test call
     */
    public static function validate_api_key(string $api_key): array
    {
        if (empty($api_key)) {
            return [
                'success' => false,
                'message' => 'API key cannot be empty'
            ];
        }

        // Basic format validation - API keys should be 32 character hex strings
        if (!preg_match('/^[a-f0-9]{32}$/i', $api_key)) {
            return [
                'success' => false,
                'message' => 'Invalid API key format. API key should be a 32-character hexadecimal string.'
            ];
        }

        try {
            // Create API instance with strict validation (no fallback)
            $api = self::create_api_instance_for_validation($api_key);
            $makes = $api->getMakes();

            if (!empty($makes) && is_array($makes)) {
                // Additional validation - check if we got real data structure
                $first_make = reset($makes);
                if (is_array($first_make) && isset($first_make['name']) && isset($first_make['slug'])) {
                    return [
                        'success' => true,
                        'message' => 'API key validated successfully',
                        'data' => ['makes_count' => count($makes)]
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'API key validation failed - invalid data structure returned'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'API key validation failed - no data returned from API'
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'API key validation failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Validate and save API key if valid
     */
    public static function validate_and_save_api_key(string $api_key): array
    {
        $validation_result = self::validate_api_key($api_key);
        
        if ($validation_result['success']) {
            // Save the API key and mark as configured
            update_option(self::API_KEY_OPTION, $api_key);
            self::set_api_configured(true);
            
            // Clear any cached data that might be invalid
            self::clear_api_cache();
        } else {
            // Mark as not configured if validation fails
            self::set_api_configured(false);
        }
        
        return $validation_result;
    }
    
    /**
     * Clear API-related cache
     */
    public static function clear_api_cache(): void
    {
        global $wpdb;

        // Clear specific transients that might contain cached API data
        $cache_keys = [
            'wheel_size_makes',
            'wheel_size_api_test',
            'wheel_size_brand_filters_makes'
        ];

        foreach ($cache_keys as $key) {
            delete_transient($key);
        }

        // Clear all wheel_fit related transients using SQL for efficiency
        $like_patterns = [
            '_transient_wheel_fit_makes%',
            '_transient_timeout_wheel_fit_makes%',
            '_transient_wheel_fit_models%',
            '_transient_timeout_wheel_fit_models%',
            '_transient_wheel_fit_modifications%',
            '_transient_timeout_wheel_fit_modifications%',
            '_transient_wheel_fit_years%',
            '_transient_timeout_wheel_fit_years%',
            '_transient_wheel_fit_all_years%',
            '_transient_timeout_wheel_fit_all_years%',
            '_transient_models_%',
            '_transient_timeout_models_%',
            '_transient_mods_%',
            '_transient_timeout_mods_%'
        ];

        $where_conditions = array_map(function($pattern) {
            return "option_name LIKE '{$pattern}'";
        }, $like_patterns);

        $where = implode(' OR ', $where_conditions);
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE {$where}");

        // Clear any other API-related cache
        wp_cache_flush();
    }
    
    /**
     * Reset API configuration (mark as unconfigured)
     */
    public static function reset_api_configuration(): void
    {
        self::set_api_configured(false);
        self::clear_api_cache();
    }
    
    /**
     * Check if current stored API key is still valid
     */
    public static function verify_current_api_key(): bool
    {
        $api_key = self::get_api_key();
        
        if (empty($api_key)) {
            self::set_api_configured(false);
            return false;
        }
        
        $validation_result = self::validate_api_key($api_key);
        
        if (!$validation_result['success']) {
            self::set_api_configured(false);
            return false;
        }
        
        return true;
    }
    
    /**
     * Get user-friendly message for unconfigured API
     */
    public static function get_unconfigured_message(): string
    {
        return 'Please configure your API key in the Wheel-Size settings to use this feature.';
    }
    
    /**
     * Get admin URL for API settings page
     */
    public static function get_api_settings_url(): string
    {
        return admin_url('admin.php?page=wheel-size');
    }
    
    /**
     * Check if we should show admin notices about API configuration
     */
    public static function should_show_admin_notice(): bool
    {
        // Only show on admin pages
        if (!is_admin()) {
            return false;
        }
        
        // Don't show if API is already configured
        if (self::is_api_configured()) {
            return false;
        }
        
        // Only show to users who can manage options
        if (!current_user_can('manage_options')) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if current page is the API settings page
     */
    public static function is_api_settings_page(): bool
    {
        global $pagenow;
        
        return $pagenow === 'admin.php' && 
               isset($_GET['page']) && 
               $_GET['page'] === 'wheel-size';
    }
    
    /**
     * Get list of admin pages that should be accessible when API is not configured
     */
    public static function get_allowed_admin_pages(): array
    {
        return [
            'wheel-size', // API Settings page
        ];
    }
    
    /**
     * Check if a specific admin page should be accessible when API is not configured
     */
    public static function is_admin_page_allowed(string $page_slug): bool
    {
        return in_array($page_slug, self::get_allowed_admin_pages(), true);
    }
    
    /**
     * Get activation redirect flag and clear it
     */
    public static function get_and_clear_activation_redirect(): bool
    {
        $should_redirect = (bool) get_option('wheel_size_activation_redirect', false);

        if ($should_redirect) {
            delete_option('wheel_size_activation_redirect');
        }

        return $should_redirect;
    }

    /**
     * Create API instance for validation without fallback key
     */
    private static function create_api_instance_for_validation(string $api_key): WheelSizeApi
    {
        // Temporarily disable the fallback key by creating a custom API instance
        // We'll need to modify WheelSizeApi to support strict validation mode

        // For now, we'll use reflection to bypass the fallback
        $api = new WheelSizeApi($api_key);

        // Use reflection to set the user_key directly and bypass fallback
        $reflection = new \ReflectionClass($api);
        $userKeyProperty = $reflection->getProperty('user_key');
        $userKeyProperty->setAccessible(true);
        $userKeyProperty->setValue($api, $api_key);

        return $api;
    }
}
