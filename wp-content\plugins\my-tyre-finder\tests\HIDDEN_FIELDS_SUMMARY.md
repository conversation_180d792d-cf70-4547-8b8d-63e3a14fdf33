# Hidden Fields Summary

## Changes Made ✅

### 1. Hidden Wizard Layout Option
**File**: `src/admin/AppearancePage.php` (line 470)

**Before**:
```php
'wizard' => __('Wizard', 'my-tyre-finder'),
```

**After**:
```php
// 'wizard' => __('Wizard', 'my-tyre-finder'), // Hidden but not removed - may be added back later
```

**Result**: Wizard option no longer appears in Form Layout dropdown, but all code preserved.

### 2. Hidden Automatic Search Field
**File**: `src/admin/AppearancePage.php` (lines 514-521)

**Before**:
```html
<div class="flex items-center">
```

**After**:
```html
<div class="flex items-center" style="display: none;">
```

**Result**: Field hidden from UI but form still submits the value (always checked).

### 3. Always Enable Automatic Search
**File**: `src/admin/AppearancePage.php`

**Save function** (line 977):
```php
update_option('auto_search_on_last_input', true); // Always enabled
```

**Default value** (line 413):
```php
$auto_search = get_option('auto_search_on_last_input', true); // Always enabled by default
```

**Result**: Automatic search always enabled regardless of UI state.

## Current Form Layout ✅

**Left Column**:
- Search Flow
- Form Layout (3 options: Inline, Grid, Step-by-Step)

**Right Column**:
- Font Family
- Font Size

**Hidden but Functional**:
- Wizard layout (commented out, easy to restore)
- Automatic Search (hidden with CSS, always enabled)

## How to Restore ✅

**Wizard**: Uncomment line 470 in AppearancePage.php
**Automatic Search**: Remove `style="display: none;"` from line 515

All your hard work is preserved and ready for quick restoration! 🎯
