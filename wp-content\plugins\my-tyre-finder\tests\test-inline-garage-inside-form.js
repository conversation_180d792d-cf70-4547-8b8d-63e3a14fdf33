/**
 * Test script for Inline Layout Garage Inside Form
 * Проверяет, что блок Garage находится внутри формы в лейауте Inline (1×4)
 */

console.log('🧪 Testing Inline Layout Garage Inside Form...');

function testGarageInsideForm() {
    console.log('\n📍 Testing if Garage is inside form...');
    
    // Find the widget container
    const widget = document.querySelector('.wheel-fit-widget');
    if (!widget) {
        console.error('❌ Widget container not found');
        return false;
    }
    
    // Check if garage is enabled
    const garageEnabled = widget.classList.contains('garage-enabled');
    if (!garageEnabled) {
        console.warn('⚠️ Garage is not enabled - skipping tests');
        return true;
    }
    
    console.log('✅ Garage is enabled');
    
    // Find garage buttons
    const garageButtons = widget.querySelectorAll('[data-garage-trigger]');
    if (garageButtons.length === 0) {
        console.error('❌ No garage buttons found');
        return false;
    }
    
    console.log(`✅ Found ${garageButtons.length} garage button(s)`);
    
    let allTestsPassed = true;
    
    garageButtons.forEach((button, index) => {
        console.log(`\n🔍 Testing garage button ${index + 1}:`);
        
        // Test 1: Button is inside .ws-submit container
        const submitContainer = button.closest('.ws-submit');
        if (submitContainer) {
            console.log('  ✅ Button is inside .ws-submit container (part of form)');
        } else {
            console.error('  ❌ Button is NOT inside .ws-submit container');
            allTestsPassed = false;
        }
        
        // Test 2: Button is positioned after the Find Sizes button
        if (submitContainer) {
            const findSizesButton = submitContainer.querySelector('button[type="submit"]');
            const garageContainer = button.closest('.flex.justify-center');
            
            if (findSizesButton && garageContainer) {
                const findSizesRect = findSizesButton.getBoundingClientRect();
                const garageRect = garageContainer.getBoundingClientRect();
                
                if (garageRect.top > findSizesRect.bottom) {
                    console.log('  ✅ Garage button is positioned below Find Sizes button');
                } else {
                    console.error('  ❌ Garage button is NOT positioned below Find Sizes button');
                    allTestsPassed = false;
                }
            }
        }
        
        // Test 3: Proper spacing (8-12px)
        const computedStyle = window.getComputedStyle(button);
        const marginTop = parseFloat(computedStyle.marginTop);
        
        if (marginTop >= 8 && marginTop <= 16) {
            console.log(`  ✅ Proper spacing from button: ${marginTop}px`);
        } else {
            console.warn(`  ⚠️ Spacing might be off: ${marginTop}px (expected 8-16px)`);
        }
        
        // Test 4: Centered alignment
        const centerContainer = button.closest('.flex.justify-center');
        if (centerContainer) {
            console.log('  ✅ Button is in centered container');
        } else {
            console.error('  ❌ Button is NOT in centered container');
            allTestsPassed = false;
        }
        
        // Test 5: Button is visually part of the form
        const formContainer = button.closest('form');
        if (formContainer) {
            console.log('  ✅ Button is inside form element');
        } else {
            console.error('  ❌ Button is NOT inside form element');
            allTestsPassed = false;
        }
    });
    
    return allTestsPassed;
}

function testFormIntegration() {
    console.log('\n🔗 Testing form integration...');
    
    const widget = document.querySelector('.wheel-fit-widget');
    if (!widget) return false;
    
    // Test that garage buttons are part of the same visual container as form fields
    const forms = widget.querySelectorAll('form');
    let integrationPassed = true;
    
    forms.forEach((form, index) => {
        console.log(`\n📝 Testing form ${index + 1}:`);
        
        const garageButton = form.querySelector('[data-garage-trigger]');
        const submitButton = form.querySelector('button[type="submit"]');
        
        if (garageButton && submitButton) {
            // Check if they're in the same visual container
            const garageParent = garageButton.closest('.ws-submit');
            const submitParent = submitButton.closest('.ws-submit');
            
            if (garageParent === submitParent) {
                console.log('  ✅ Garage and submit buttons are in same container');
            } else {
                console.error('  ❌ Garage and submit buttons are in different containers');
                integrationPassed = false;
            }
            
            // Check visual alignment
            const garageRect = garageButton.getBoundingClientRect();
            const submitRect = submitButton.getBoundingClientRect();
            const formRect = form.getBoundingClientRect();
            
            // Check if garage button is within form bounds
            if (garageRect.left >= formRect.left && garageRect.right <= formRect.right) {
                console.log('  ✅ Garage button is within form horizontal bounds');
            } else {
                console.warn('  ⚠️ Garage button might be outside form horizontal bounds');
            }
        }
    });
    
    return integrationPassed;
}

function runAllTests() {
    console.log('🚀 Running all Inline Layout Garage Inside Form tests...\n');
    
    const results = {
        insideForm: testGarageInsideForm(),
        integration: testFormIntegration()
    };
    
    console.log('\n📊 Test Results Summary:');
    console.log(`  Garage Inside Form: ${results.insideForm ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Form Integration: ${results.integration ? '✅ PASS' : '❌ FAIL'}`);
    
    const allPassed = Object.values(results).every(result => result);
    console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    if (allPassed) {
        console.log('\n🎉 Success! Garage block is now properly integrated inside the form!');
        console.log('   ✓ Garage is part of the form structure');
        console.log('   ✓ Positioned after Find Sizes button');
        console.log('   ✓ Proper spacing and alignment');
        console.log('   ✓ Centered within form width');
    }
    
    return allPassed;
}

// Auto-run tests when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
} else {
    runAllTests();
}

// Export for manual testing
window.testInlineGarageInsideForm = {
    runAllTests,
    testGarageInsideForm,
    testFormIntegration
};
