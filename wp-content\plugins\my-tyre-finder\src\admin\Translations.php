<?php

declare(strict_types=1);

namespace MyTyreFinder\Admin;

use MyTyreFinder\Translations\TranslationManager;

/**
 * Adds "Translations" submenu and handles saving / uploading locale files.
 */
final class Translations
{
    private const SLUG          = 'wheel-size-translations';
    private const NONCE_ACTION  = 'wheel_size_translations_action';
    private const NONCE_NAME    = 'wheel_size_translations_nonce';

    public function register(): void
    {
        add_action('admin_menu', [$this, 'add_menu']);
        add_action('admin_post_wheel_size_save_translations', [$this, 'save']);
        add_action('admin_enqueue_scripts', [$this, 'assets']);
    }

    public function add_menu(): void
    {
        add_submenu_page(
            'wheel-size',
            __('Translations', 'my-tyre-finder'),
            __('Translations', 'my-tyre-finder'),
            'manage_options',
            self::SLUG,
            [$this, 'render']
        );
    }

    public function assets(string $hook): void
    {
        if ($hook !== 'wheel-size_page_' . self::SLUG) {
            return;
        }
        $plugin_file = dirname(__DIR__, 2) . '/my-tyre-finder.php';
        wp_enqueue_style(
            'wheel-size-translations-admin',
            plugins_url('assets/css/admin-translations.css', $plugin_file),
            [],
            file_exists(plugin_dir_path($plugin_file) . 'assets/css/admin-translations.css') ? filemtime(plugin_dir_path($plugin_file) . 'assets/css/admin-translations.css') : '1.0.0'
        );
        wp_enqueue_script(
            'wheel-size-translations-admin',
            plugins_url('assets/js/admin-translations.js', $plugin_file),
            [],
            file_exists(plugin_dir_path($plugin_file) . 'assets/js/admin-translations.js') ? filemtime(plugin_dir_path($plugin_file) . 'assets/js/admin-translations.js') : '1.0.0',
            true
        );
    }

    public function render(): void
    {
        if (!current_user_can('manage_options')) {
            return;
        }
        TranslationManager::bootstrap();
        $locale   = sanitize_key($_GET['locale'] ?? get_option('wheel_size_active_language', 'en'));
        $locales  = TranslationManager::get_available_locales();
        $data     = in_array($locale, $locales, true) ? TranslationManager::get_locale_data($locale) : [];
        $enData   = TranslationManager::get_locale_data('en');
        $desc     = self::get_key_descriptions();

        // Combine keys from the English default and the current locale to ensure all keys are visible
        $display_keys = array_keys(array_replace($enData, $data));
        sort($display_keys);
        ?>
        <div class="wrap" id="wheel-size-translations-page">
            <style>
                /* Table container with natural height */
                .table-container {
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    margin-bottom: 20px;
                    padding-bottom: 60px; /* Prevent overlap with sticky footer */
                }

                /* Sticky header for table */
                .table-container .sticky-header {
                    position: sticky;
                    top: 0;
                    background: #f9f9f9;
                    z-index: 10;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }

                /* Sticky form actions - always visible at bottom */
                .form-actions {
                    position: sticky;
                    bottom: 0;
                    z-index: 20;
                    background: #fff;
                    padding: 12px 0;
                    border-top: 1px solid #ddd;
                    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
                    margin-top: 10px;
                }

                /* Compact table styling - reduce excessive padding */
                .wp-list-table th,
                .wp-list-table td {
                    padding: 6px 12px !important; /* Reduced from default 12px 15px */
                    line-height: 1.3;
                }

                .wp-list-table th {
                    font-size: 14px;
                    font-weight: 600;
                }

                .wp-list-table td {
                    font-size: 14px;
                }

                /* Compact group headers */
                .group-header th {
                    padding: 4px 12px !important;
                    font-size: 13px;
                    background: #f8f9fa !important;
                    border-bottom: 1px solid #ddd;
                    font-weight: 600;
                    color: #555;
                }

                /* Reduce spacing between rows */
                .wp-list-table tbody tr {
                    border-bottom: 1px solid #f0f0f0;
                }

                /* Compact input fields in table */
                .wp-list-table input[type="text"] {
                    padding: 4px 8px;
                    font-size: 14px;
                    line-height: 1.3;
                    width: 100%;
                    box-sizing: border-box;
                }
            </style>

            <h1><?php esc_html_e('Manage UI Translations', 'my-tyre-finder'); ?></h1>

            <?php if (isset($_GET['msg'])) : ?>
                <div class="notice notice-<?php echo esc_attr(sanitize_key($_GET['status'] ?? 'success')); ?> is-dismissible"><p><?php echo esc_html($_GET['msg']); ?></p></div>
            <?php endif; ?>

            <!-- Two Column Layout with Aligned Heights -->
            <div style="display: flex; gap: 30px; margin-bottom: 20px; align-items: stretch;">
                <!-- Left Column: Instructions -->
                <div style="flex: 1; display: flex; flex-direction: column; gap: 15px;">
                    <!-- How to Use Block -->
                    <div style="background: #f0f6ff; padding: 20px; border-left: 4px solid #0073aa; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); flex: 1;">
                        <h3 style="margin-top: 0; color: #0073aa; font-size: 16px; margin-bottom: 12px;">💡 How to Use This Page</h3>
                        <p style="margin-bottom: 8px; font-size: 14px;"><strong>You can do two things here:</strong></p>
                        <ol style="margin-left: 20px; font-size: 14px; line-height: 1.4; margin-bottom: 12px;">
                            <li><strong>Switch Languages:</strong> Use the dropdown to select different languages</li>
                            <li><strong>Edit Text Values:</strong> Click any text in the table to customize it</li>
                        </ol>
                        <p style="margin-bottom: 0; font-size: 14px;"><strong>💾 Important:</strong> Click "Save Changes" before switching languages!</p>
                    </div>

                    <!-- Tip Block -->
                    <div style="background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <p style="margin: 0; font-size: 14px;">
                            <strong>✏️ Tip:</strong> Click on any text in the "Translation" column below to edit it.
                            You can customize button text, labels, placeholders, and any other text that users see.
                        </p>
                    </div>
                </div>

                <!-- Right Column: Controls -->
                <div style="flex: 0 0 280px; display: flex; flex-direction: column; gap: 15px;">
                    <!-- Language Settings Block -->
                    <div style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); flex: 1;">
                        <h3 style="margin-top: 0; margin-bottom: 15px; font-size: 16px; color: #333;">Language Settings</h3>

                        <form method="get" class="locale-selector-form" style="margin-bottom: 15px;">
                            <label for="locale-selector" style="display: block; margin-bottom: 8px; font-weight: 600; font-size: 14px;">Select language:</label>
                            <input type="hidden" name="page" value="<?php echo esc_attr(self::SLUG); ?>">
                            <select name="locale" id="locale-selector" onchange="this.form.submit()" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                                <?php foreach ($locales as $loc) : ?>
                                    <option value="<?php echo esc_attr($loc); ?>" <?php selected($loc, $locale); ?>><?php echo esc_html(strtoupper($loc)); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </form>

                        <?php if ('en' !== $locale && empty($data)) : ?>
                            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
                                <?php wp_nonce_field(self::NONCE_ACTION, self::NONCE_NAME); ?>
                                <input type="hidden" name="action" value="wheel_size_duplicate_from_en">
                                <input type="hidden" name="locale" value="<?php echo esc_attr($locale); ?>">
                                <?php submit_button(__('Duplicate from EN', 'my-tyre-finder'), 'secondary', 'duplicate-from-en', false, ['style' => 'width: 100%; font-size: 14px;']); ?>
                            </form>
                        <?php endif; ?>
                    </div>

                    <!-- Search Block -->
                    <div style="background: #f8f9fa; padding: 15px; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <label for="translation-search-input" style="display: block; margin-bottom: 8px; font-weight: 600; font-size: 14px; color: #333;">Search translations:</label>
                        <input type="search" id="translation-search-input" placeholder="<?php esc_attr_e('Search keys...', 'my-tyre-finder'); ?>" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    </div>
                </div>
            </div>

            <form id="translations-form" method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">
                <?php wp_nonce_field(self::NONCE_ACTION, self::NONCE_NAME); ?>
                <input type="hidden" name="action" value="wheel_size_save_translations">
                <input type="hidden" name="locale" value="<?php echo esc_attr($locale); ?>">

                <div class="table-container">
                    <table class="wp-list-table widefat striped">
                        <thead class="sticky-header">
                            <tr><th><?php esc_html_e('Key', 'my-tyre-finder'); ?></th><th><?php esc_html_e('Translation', 'my-tyre-finder'); ?></th><th></th></tr>
                        </thead>
                        <tbody id="translations-body">
                        <?php if (empty($display_keys)) : ?>
                            <tr class="no-items"><td colspan="3"><?php esc_html_e('No translation keys found. Check your default language file.', 'my-tyre-finder'); ?></td></tr>
                        <?php else : ?>
                            <?php
                            // --- Grouping logic -------------------------------------------------
                            $groups = [
                                'Buttons'       => static fn($key)=> str_starts_with($key,'button_'),
                                'Fuel types'    => static fn($key)=> str_starts_with($key,'fuel_'),
                                'Garage'        => static fn($key)=> str_starts_with($key,'garage_') || str_starts_with($key,'tooltip_'),
                                'Sections'      => static fn($key)=> str_starts_with($key,'section_'),
                                'Placeholders'  => static fn($key)=> str_contains($key,'placeholder_') || str_contains($key,'_placeholder') || str_starts_with($key,'select_'),
                                'Labels'        => static fn($key)=> str_starts_with($key,'label_') || str_starts_with($key,'heading_'),
                                'Other'         => static fn($key)=> true,
                            ];

                            $grouped = [];
                            foreach ($display_keys as $kk) {
                                foreach ($groups as $gLabel=>$matcher) {
                                    if ($matcher($kk)) { $grouped[$gLabel][]=$kk; break; }
                                }
                            }
                            $idx=0;
                            foreach ($grouped as $gLabel=>$keysArr) :
                            ?>
                            <tr class="translation-group-header"><th colspan="3" style="background:#f6f7f7;font-weight:600;"><?php echo esc_html($gLabel); ?></th></tr>
                                <?php foreach ($keysArr as $k) : ?>
                                <tr>
                                    <td>
                                        <div class="key-cell">
                                            <input type="text" class="key-input" name="translations[<?php echo $idx;?>][key]" value="<?php echo esc_attr($k); ?>" readonly>
                                            <span class="dashicons dashicons-info-outline" title="<?php
                                                echo esc_attr(($desc[$k] ?? '') . (isset($enData[$k]) ? ' — &quot;' . $enData[$k] . '&quot;' : ''));
                                            ?>"></span>
                                        </div>
                                    </td>
                                    <td><input type="text" class="value-input widefat" name="translations[<?php echo $idx;?>][value]" value="<?php echo esc_attr($data[$k] ?? ''); ?>" placeholder="<?php echo esc_attr($enData[$k] ?? 'No default value'); ?>"></td>
                                    <td class="actions-cell"><button type="button" class="button-link-delete delete-row" aria-label="Delete key"><span class="dashicons dashicons-trash"></span></button></td>
                                </tr>
                                <?php $idx++; endforeach; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="form-actions">
                    <button type="button" id="add-new-row" class="button button-secondary"><?php esc_html_e('Add New Key', 'my-tyre-finder'); ?></button>
                    <button type="submit" id="save-translations" class="button button-primary" title="<?php esc_attr_e('Save all translation changes for the selected language.', 'my-tyre-finder'); ?>"><?php esc_html_e('Save Changes', 'my-tyre-finder'); ?></button>
                </div>
            </form>
        </div>
        <?php
    }

    public function save(): void
    {
        check_admin_referer(self::NONCE_ACTION, self::NONCE_NAME);
        if (!current_user_can('manage_options')) {
            wp_die('permission');
        }
        $locale = sanitize_key($_POST['locale'] ?? 'en');
        $rows   = $_POST['translations'] ?? [];
        $out    = [];
        foreach ($rows as $row) {
            $k = sanitize_text_field($row['key'] ?? '');
            $raw_val = isset($row['value']) ? wp_unslash($row['value']) : '';
            $v = sanitize_text_field($raw_val);
            if ($k !== '') {
                $out[$k] = $v;
            }
        }
        $ok  = TranslationManager::save_locale_data($locale, $out);
        if ($ok) {
            delete_transient("wheel_size_locale_{$locale}");
            update_option('wheel_size_active_language', $locale);
        }
        $msg = $ok ? __('Translations saved successfully.', 'my-tyre-finder') : __('Failed to save translations.', 'my-tyre-finder');
        wp_safe_redirect(add_query_arg(['page' => self::SLUG, 'locale' => $locale, 'status' => $ok ? 'success' : 'error', 'msg' => $msg], admin_url('admin.php')));
        exit;
    }

    private static function get_key_descriptions(): array
    {
        return [
            'label_make'      => 'Dropdown label for car make selection.',
            'label_model'     => 'Dropdown label for car model selection.',
            'label_year'      => 'Dropdown label for car year selection.',
            'label_mods'      => 'Dropdown label for car modification selection.',
            'button_search'   => 'Text on main search button.',
            'button_clear'    => 'Text on clear form button.',
            'button_reset'    => 'Text on reset form button.',
            'section_results' => 'Heading for search results.',
            'section_factory' => 'Heading for factory sizes.',
            'fuel_petrol'     => 'Badge for petrol fuel.',
            'fuel_diesel'     => 'Badge for diesel fuel.',
            'fuel_electric'   => 'Badge for electric vehicles.',
        ];
    }
} 