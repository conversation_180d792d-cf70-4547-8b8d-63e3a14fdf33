# Wheel Size Finder - Theme System Documentation

## Overview

The Wheel Size Finder plugin includes a comprehensive theme system built on CSS custom properties (CSS variables) and CSS cascade layers. This system allows for easy customization and theming of the widget appearance.

## Architecture

### CSS Cascade Layers

The theme system uses CSS cascade layers in the following order (lowest to highest specificity):

1. `@layer wsf-base` - Reset and base styles, CSS custom properties
2. `@layer wsf-components` - Component-specific styles
3. `@layer wsf-utilities` - Utility classes and overrides
4. `@layer wsf-overrides` - Site-specific customizations (for developers)

### Theme Classes

Each widget instance includes theme classes:
- `.wsf-finder-widget` - Base widget class
- `.wsf-theme-{slug}` - Theme-specific class (e.g., `.wsf-theme-light`, `.wsf-theme-dark`)
- `data-wsf-theme="{slug}"` - Theme data attribute

## CSS Custom Properties (Design Tokens)

### Core Color Tokens

| Property | Description | Light Default | Dark Default |
|----------|-------------|---------------|--------------|
| `--wsf-bg` | Background color | `#ffffff` | `#1e1e1e` |
| `--wsf-text` | Primary text color | `#1f2937` | `#f3f4f6` |
| `--wsf-primary` | Primary accent color | `#2563eb` | `#7dd3fc` |
| `--wsf-border` | Border color | `#e5e7eb` | `#374151` |
| `--wsf-hover` | Hover state color | `#1d4ed8` | `#0ea5e9` |

### Semantic Color Tokens

| Property | Description | Light Default | Dark Default |
|----------|-------------|---------------|--------------|
| `--wsf-secondary` | Secondary text/elements | `#6b7280` | `#9ca3af` |
| `--wsf-accent` | Accent color | `#3b82f6` | `#60a5fa` |
| `--wsf-muted` | Muted text | `#9ca3af` | `#6b7280` |
| `--wsf-success` | Success state | `#10b981` | `#34d399` |
| `--wsf-warning` | Warning state | `#f59e0b` | `#fbbf24` |
| `--wsf-error` | Error state | `#ef4444` | `#f87171` |

### Surface and Interaction Tokens

| Property | Description | Light Default | Dark Default |
|----------|-------------|---------------|--------------|
| `--wsf-surface` | Surface background | `#f9fafb` | `#2d2d2d` |
| `--wsf-surface-hover` | Surface hover state | `#f3f4f6` | `#3d3d3d` |
| `--wsf-border-light` | Light border | `#f3f4f6` | `#4b5563` |
| `--wsf-border-focus` | Focus border | `var(--wsf-primary)` | `var(--wsf-primary)` |
| `--wsf-shadow` | Box shadow | `rgba(0, 0, 0, 0.1)` | `rgba(0, 0, 0, 0.3)` |
| `--wsf-shadow-hover` | Hover shadow | `rgba(0, 0, 0, 0.15)` | `rgba(0, 0, 0, 0.4)` |

### Typography Tokens

| Property | Description | Light Default | Dark Default |
|----------|-------------|---------------|--------------|
| `--wsf-text-primary` | Primary text | `#1f2937` | `#f3f4f6` |
| `--wsf-text-secondary` | Secondary text | `#6b7280` | `#9ca3af` |
| `--wsf-text-muted` | Muted text | `#9ca3af` | `#6b7280` |
| `--wsf-text-inverse` | Inverse text | `#ffffff` | `#1f2937` |

### Spacing and Sizing Tokens

| Property | Description | Default Value |
|----------|-------------|---------------|
| `--wsf-radius` | Border radius | `0.375rem` |
| `--wsf-radius-lg` | Large border radius | `0.5rem` |
| `--wsf-spacing-xs` | Extra small spacing | `0.25rem` |
| `--wsf-spacing-sm` | Small spacing | `0.5rem` |
| `--wsf-spacing-md` | Medium spacing | `1rem` |
| `--wsf-spacing-lg` | Large spacing | `1.5rem` |
| `--wsf-spacing-xl` | Extra large spacing | `2rem` |

## Creating Custom Themes

### Method 1: Using the Admin Panel

1. Navigate to **Wheel-Size → Appearance** in WordPress admin
2. Use the Theme Presets panel to create new themes
3. Customize colors using the color pickers
4. Save and apply your custom theme

### Method 2: Programmatic Theme Creation

```php
// Create a custom theme programmatically
$custom_theme_properties = [
    'name' => 'Brand Theme',
    '--wsf-bg' => '#ffffff',
    '--wsf-text' => '#333333',
    '--wsf-primary' => '#ff6b35',
    '--wsf-border' => '#e1e5e9',
    '--wsf-hover' => '#e55a2b',
    // ... other properties
];

$theme_slug = \MyTyreFinder\Includes\ThemeManager::create_theme(
    'Brand Theme',
    $custom_theme_properties
);

// Set as active theme
\MyTyreFinder\Includes\ThemeManager::set_active_theme($theme_slug);
```

### Method 3: CSS Override Layer

Use the `wsf-overrides` layer for site-specific customizations:

```css
@layer wsf-overrides {
    .wsf-finder-widget.wsf-theme-custom {
        --wsf-primary: #your-brand-color;
        --wsf-bg: #your-background;
    }
    
    /* Specific component overrides */
    .wsf-finder-widget .ws-submit > button {
        border-radius: 0; /* Remove border radius */
        text-transform: uppercase;
    }
}
```

## Developer Hooks

### Action Hooks

#### `wsf_inline_styles`

Allows developers to inject custom CSS after the main theme styles are loaded.

**Parameters:**
- `$handle` (string) - The CSS handle being used
- `$active_theme_slug` (string) - Current active theme slug
- `$active_theme_properties` (array) - Current theme properties

**Example:**

```php
add_action('wsf_inline_styles', function($handle, $theme_slug, $theme_props) {
    $custom_css = "
        .wsf-finder-widget.wsf-theme-{$theme_slug} {
            /* Your custom styles here */
            border: 2px solid var(--wsf-primary);
        }
    ";
    wp_add_inline_style($handle, $custom_css);
}, 10, 3);
```

## Tailwind CSS Integration

The theme system is fully integrated with Tailwind CSS using the `wsf-` prefix:

```html
<!-- Use theme-aware Tailwind classes -->
<div class="wsf-bg-wsf-bg wsf-text-wsf-text wsf-border-wsf-border">
    <button class="wsf-bg-wsf-primary wsf-text-wsf-text-inverse">
        Button
    </button>
</div>
```

### Available Tailwind Classes

- Colors: `wsf-bg-wsf-primary`, `wsf-text-wsf-text`, `wsf-border-wsf-border`, etc.
- Spacing: `wsf-p-wsf-md`, `wsf-m-wsf-lg`, etc.
- Border radius: `wsf-rounded-wsf`, `wsf-rounded-wsf-lg`
- Shadows: `wsf-shadow-wsf`, `wsf-shadow-wsf-hover`

## Dark Mode Support

The theme system includes built-in dark mode support:

```javascript
// Toggle dark mode programmatically
const widget = document.querySelector('.wsf-finder-widget');
widget.classList.remove('wsf-theme-light');
widget.classList.add('wsf-theme-dark');
widget.setAttribute('data-wsf-theme', 'dark');
```

## Best Practices

### 1. Use CSS Custom Properties

Always use the provided CSS custom properties instead of hardcoded values:

```css
/* ✅ Good */
.my-custom-element {
    background-color: var(--wsf-primary);
    color: var(--wsf-text-inverse);
}

/* ❌ Bad */
.my-custom-element {
    background-color: #2563eb;
    color: #ffffff;
}
```

### 2. Respect the Layer System

Place your customizations in the appropriate layer:

```css
/* ✅ Good - using override layer */
@layer wsf-overrides {
    .wsf-finder-widget {
        /* Your overrides */
    }
}

/* ❌ Bad - no layer specified */
.wsf-finder-widget {
    /* This might be overridden */
}
```

### 3. Test Both Light and Dark Themes

Always test your customizations with both light and dark themes to ensure proper contrast and readability.

### 4. Use Semantic Tokens

Prefer semantic tokens over core color tokens when possible:

```css
/* ✅ Good - semantic meaning */
.error-message {
    color: var(--wsf-error);
}

/* ❌ Less ideal - generic color */
.error-message {
    color: var(--wsf-primary);
}
```

## Troubleshooting

### Theme Not Applying

1. Check that the theme class is present on the widget element
2. Verify CSS custom properties are defined
3. Ensure no conflicting CSS is overriding the theme styles
4. Check browser console for JavaScript errors

### Custom Styles Not Working

1. Verify you're using the `wsf-overrides` layer
2. Check CSS specificity - you may need `!important`
3. Ensure the theme system is properly loaded
4. Test with the diagnostic script provided

### Performance Considerations

- CSS custom properties are efficiently handled by modern browsers
- The layer system prevents specificity wars
- Minimal JavaScript is used for theme switching
- Themes are cached in WordPress options for performance

## Migration Guide

### From Legacy Color System

If you were using the old `--wf-primary-color` system:

```css
/* Old system */
.my-element {
    color: var(--wf-primary-color);
}

/* New system */
.my-element {
    color: var(--wsf-primary);
}
```

The old system is still supported for backward compatibility, but new development should use the new token system.
