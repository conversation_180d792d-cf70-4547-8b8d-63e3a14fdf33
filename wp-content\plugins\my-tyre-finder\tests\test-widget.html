<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget CSS Variables Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .theme-switcher {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .theme-button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        .theme-button.active {
            box-shadow: 0 0 0 2px #007cba;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Widget CSS Variables Test</h1>
        
        <div class="theme-switcher">
            <h3>Theme Presets Test:</h3>
            <button class="theme-button active" onclick="applyTheme('light')" style="background: #ffffff; color: #1f2937;">Light</button>
            <button class="theme-button" onclick="applyTheme('dark')" style="background: #1e1e1e; color: #f3f4f6;">Dark</button>
            <button class="theme-button" onclick="applyTheme('blue')" style="background: #2563eb; color: white;">Blue</button>
            <button class="theme-button" onclick="applyTheme('green')" style="background: #10b981; color: white;">Green</button>
            <button class="theme-button" onclick="applyTheme('purple')" style="background: #8b5cf6; color: white;">Purple</button>
        </div>

        <!-- Test Widget -->
        <div class="wsf-finder-widget" id="test-widget" data-wsf-theme="light">
            <div style="padding: 20px; border: 1px solid var(--wsf-border); border-radius: 8px; background: var(--wsf-bg);">
                <h3 style="color: var(--wsf-text); margin-top: 0;">Test Widget</h3>
                
                <!-- Search Button Test -->
                <div style="margin: 15px 0;">
                    <button type="submit" class="wsf-bg-wsf-primary wsf-text-white wsf-px-6 wsf-py-3 wsf-rounded-lg wsf-font-semibold hover:wsf-bg-wsf-hover wsf-transition">
                        Find Tire & Wheel Sizes
                    </button>
                    <button type="button" class="wsf-bg-wsf-primary wsf-text-white wsf-px-6 wsf-py-3 wsf-rounded-lg wsf-font-semibold wsf-ml-2" disabled>
                        Disabled Button
                    </button>
                </div>

                <!-- Spinner Test -->
                <div style="margin: 15px 0;">
                    <div class="wsf-inline-flex wsf-items-center wsf-gap-2">
                        <span style="color: var(--wsf-text);">Loading:</span>
                        <div class="wsf-animate-spin wsf-rounded-full wsf-h-5 wsf-w-5 wsf-border-b-2 wsf-border-wsf-primary"></div>
                    </div>
                </div>

                <!-- Form Elements Test -->
                <div style="margin: 15px 0;">
                    <select class="wsf-block wsf-w-full wsf-bg-wsf-bg wsf-border-wsf-border wsf-rounded-lg wsf-px-4 wsf-py-3 wsf-shadow-sm focus:wsf-ring-2 focus:wsf-ring-wsf-primary/40 focus:wsf-border-wsf-primary wsf-transition">
                        <option value="">Select an option</option>
                        <option value="test">Test Option</option>
                    </select>
                </div>

                <!-- Text Colors Test -->
                <div style="margin: 15px 0;">
                    <p style="color: var(--wsf-text);">Primary text color</p>
                    <p style="color: var(--wsf-muted);">Muted text color</p>
                    <p style="color: var(--wsf-primary);">Primary accent color</p>
                </div>
            </div>
        </div>

        <div style="margin-top: 20px;">
            <button onclick="runTests()" style="background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: 500;">
                🧪 Run CSS Variables Test
            </button>
        </div>

        <div id="test-results" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px; display: none;">
            <h4>Test Results:</h4>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        // Theme definitions
        const themes = {
            light: {
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#1f2937',
                '--wsf-primary': '#2563eb',
                '--wsf-border': '#e5e7eb',
                '--wsf-hover': '#1d4ed8',
                '--wsf-secondary': '#6b7280',
                '--wsf-accent': '#3b82f6',
                '--wsf-muted': '#9ca3af',
                '--wsf-surface': '#f9fafb'
            },
            dark: {
                '--wsf-bg': '#1e1e1e',
                '--wsf-text': '#f3f4f6',
                '--wsf-primary': '#7dd3fc',
                '--wsf-border': '#374151',
                '--wsf-hover': '#0ea5e9',
                '--wsf-secondary': '#9ca3af',
                '--wsf-accent': '#60a5fa',
                '--wsf-muted': '#6b7280',
                '--wsf-surface': '#2d2d2d'
            },
            blue: {
                '--wsf-bg': '#eff6ff',
                '--wsf-text': '#1e3a8a',
                '--wsf-primary': '#1d4ed8',
                '--wsf-border': '#bfdbfe',
                '--wsf-hover': '#1e40af',
                '--wsf-secondary': '#64748b',
                '--wsf-accent': '#3b82f6',
                '--wsf-muted': '#64748b',
                '--wsf-surface': '#dbeafe'
            },
            green: {
                '--wsf-bg': '#f0fdf4',
                '--wsf-text': '#14532d',
                '--wsf-primary': '#16a34a',
                '--wsf-border': '#bbf7d0',
                '--wsf-hover': '#15803d',
                '--wsf-secondary': '#64748b',
                '--wsf-accent': '#22c55e',
                '--wsf-muted': '#64748b',
                '--wsf-surface': '#dcfce7'
            },
            purple: {
                '--wsf-bg': '#faf5ff',
                '--wsf-text': '#581c87',
                '--wsf-primary': '#8b5cf6',
                '--wsf-border': '#d8b4fe',
                '--wsf-hover': '#7c3aed',
                '--wsf-secondary': '#64748b',
                '--wsf-accent': '#a855f7',
                '--wsf-muted': '#64748b',
                '--wsf-surface': '#ede9fe'
            }
        };

        function applyTheme(themeName) {
            const widget = document.getElementById('test-widget');
            const theme = themes[themeName];
            
            // Remove active class from all buttons
            document.querySelectorAll('.theme-button').forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            event.target.classList.add('active');
            
            // Apply theme variables
            Object.entries(theme).forEach(([property, value]) => {
                widget.style.setProperty(property, value);
            });
            
            widget.setAttribute('data-wsf-theme', themeName);
            console.log(`🎨 Applied ${themeName} theme`);
        }

        function runTests() {
            console.log('🧪 Starting CSS Variables Tests...');
            
            // Load and execute the test script
            fetch('test-css-variables-fix.js')
                .then(response => response.text())
                .then(scriptContent => {
                    // Execute the test script
                    eval(scriptContent);
                    
                    // Show results
                    document.getElementById('test-results').style.display = 'block';
                    document.getElementById('results-content').innerHTML = 'Check browser console for detailed test results.';
                })
                .catch(error => {
                    console.error('❌ Failed to load test script:', error);
                    document.getElementById('test-results').style.display = 'block';
                    document.getElementById('results-content').innerHTML = '❌ Failed to load test script. Check console for details.';
                });
        }

        // Initialize with light theme
        document.addEventListener('DOMContentLoaded', function() {
            applyTheme('light');
        });
    </script>
</body>
</html>
