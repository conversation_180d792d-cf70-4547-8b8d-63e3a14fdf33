# Configuration Change Fixes

## Проблемы, которые были исправлены

### 1. Потеря локализации при изменении настроек
**Проблема**: При изменении настроек в админ-панели (Wheel-Size → Appearance) русские переводы сбрасывались на английские.

**Причины**:
- Кеширование переводов через `get_transient()` без очистки при изменении настроек
- Неправильная обработка переводов в AJAX preview
- Отсутствие реинициализации переводов после обновления DOM

**Исправления**:
- Добавлена функция `TranslationManager::clear_cache()` для очистки кеша переводов
- Обновлена `AppearancePage::save_settings()` для очистки кеша при сохранении настроек
- Улучшена функция `ajax_render_preview()` для правильной обработки переводов
- Расширена функция `applyStaticTranslations()` для поддержки placeholder-атрибутов
- Добавлено логирование для отладки процесса применения переводов

### 2. Исчезновение блока "Гараж" после изменения настроек
**Проблема**: Блок "Гараж" переставал отображаться после изменения конфигурации в админке.

**Причины**:
- Защита от двойной инициализации блокировала повторную инициализацию после AJAX-обновления
- Зависимость от `wheelFitWidget` без учета его пересоздания
- Отсутствие механизма реинициализации для AJAX-обновлений

**Исправления**:
- Добавлен параметр `forceReinit` в функцию `initGarageFeature()`
- Созданы функции `resetGarageState()` и `reinitGarage()` для управления состоянием
- Обновлен код в `AppearancePage.php` для вызова реинициализации гаража
- Добавлена реинициализация гаража в `admin-appearance.js`

## Файлы, которые были изменены

### Backend (PHP)
1. **src/Translations/TranslationManager.php**
   - Добавлена функция `clear_cache()` (строки 278-290)

2. **src/admin/AppearancePage.php**
   - Добавлена очистка кеша переводов в `save_settings()` (строки 571-573)
   - Добавлена очистка кеша в `ajax_render_preview()` (строки 587-589)
   - Обновлена логика реинициализации гаража (строки 451-475)

### Frontend (JavaScript)
1. **assets/js/finder.js**
   - Расширена функция `applyStaticTranslations()` (строки 1860-1916)
   - Добавлена поддержка `data-i18n-placeholder` атрибутов

2. **assets/js/garage.js**
   - Добавлен параметр `forceReinit` в `initGarageFeature()` (строки 5-13)
   - Добавлены функции `resetGarageState()` и `reinitGarage()` (строки 656-691)

3. **assets/js/admin-appearance.js**
   - Улучшена обработка переводов в live preview (строки 44-94)
   - Добавлена реинициализация гаража (строки 87-102)

## Тестирование исправлений

### Автоматическое тестирование
1. **Загрузите тестовый скрипт в консоль браузера**:
   ```javascript
   // Скопируйте содержимое test-config-change-fixes.js в консоль
   ```

2. **Запустите тесты**:
   ```javascript
   window.testConfigFixes.runAll();
   ```

### Ручное тестирование
1. **Откройте файл `test-config-fixes.html` в браузере** для интерактивного тестирования

2. **Тестирование в WordPress админке**:
   - Перейдите в Wheel-Size → Appearance
   - Измените настройки (Search Flow, Form Layout)
   - Сохраните изменения
   - Проверьте, что переводы остались русскими
   - Проверьте, что блок "Гараж" продолжает работать

### Проверка в live preview
1. Откройте Wheel-Size → Appearance
2. Измените настройки в форме
3. Наблюдайте за live preview справа
4. Убедитесь, что:
   - Переводы применяются корректно
   - Блок "Гараж" отображается и функционирует
   - Нет ошибок в консоли браузера

## Отладка

### Включение отладочного режима
Добавьте в `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Полезные команды в консоли браузера
```javascript
// Проверка состояния переводов
console.log(window.WheelFitI18n);

// Проверка состояния гаража
window.debugGarageWidget();

// Ручное применение переводов
applyStaticTranslations();

// Ручная реинициализация гаража
window.reinitGarage();

// Запуск конкретных тестов
window.testConfigFixes.translations();
window.testConfigFixes.garage();
```

## Логи и мониторинг

### Логи переводов
Ищите в консоли браузера сообщения с префиксом:
- `[applyStaticTranslations]`
- `[Admin Preview]`
- `[Wheel-Size i18n]`

### Логи гаража
Ищите в консоли браузера сообщения с префиксом:
- `[Garage]`
- `[Admin Preview]`

### Файлы логов WordPress
Проверьте файлы в папке `logs/` плагина для серверных логов.

## Совместимость

Исправления совместимы с:
- WordPress 5.0+
- PHP 8.1+
- Всеми существующими настройками плагина
- Всеми layout-ами форм (inline, popup-horizontal, stepper, wizard)
- Всеми search flow вариантами (by_vehicle, by_year, by_generation)

## Поддержка

При возникновении проблем:
1. Проверьте консоль браузера на наличие ошибок
2. Запустите автоматические тесты
3. Проверьте логи WordPress
4. Убедитесь, что все файлы были обновлены корректно
