// Диагностический скрипт для анализа конфликтов стилей в wizard
// Запустить в консоли браузера на странице с wizard

console.log('🔍 ДИАГНОСТИКА КОНФЛИКТОВ СТИЛЕЙ WIZARD');
console.log('=====================================');

function analyzeElement(element, label) {
    if (!element) {
        console.log(`❌ ${label}: элемент не найден`);
        return;
    }
    
    console.log(`\n📋 ${label}:`);
    console.log('Element:', element);
    console.log('Classes:', element.className);
    
    const computedStyles = window.getComputedStyle(element);
    const relevantStyles = {
        'white-space': computedStyles.whiteSpace,
        'word-break': computedStyles.wordBreak,
        'overflow': computedStyles.overflow,
        'text-overflow': computedStyles.textOverflow,
        'display': computedStyles.display,
        'flex-direction': computedStyles.flexDirection,
        'align-items': computedStyles.alignItems,
        'justify-content': computedStyles.justifyContent,
        'width': computedStyles.width,
        'min-width': computedStyles.minWidth,
        'max-width': computedStyles.maxWidth,
        'height': computedStyles.height,
        'min-height': computedStyles.minHeight,
        'padding': computedStyles.padding,
        'margin': computedStyles.margin,
        'text-align': computedStyles.textAlign,
        'line-height': computedStyles.lineHeight,
        'font-size': computedStyles.fontSize
    };
    
    console.log('Computed styles:', relevantStyles);
    
    // Проверяем CSS правила
    const rules = [];
    for (let sheet of document.styleSheets) {
        try {
            for (let rule of sheet.cssRules || sheet.rules) {
                if (rule.style && element.matches && element.matches(rule.selectorText)) {
                    rules.push({
                        selector: rule.selectorText,
                        styles: rule.style.cssText,
                        sheet: sheet.href || 'inline'
                    });
                }
            }
        } catch (e) {
            // Cross-origin или другие ошибки доступа
        }
    }
    
    if (rules.length > 0) {
        console.log('Matching CSS rules:');
        rules.forEach(rule => {
            console.log(`  ${rule.selector} (${rule.sheet})`);
            console.log(`    ${rule.styles}`);
        });
    }
}

function checkWizardElements() {
    console.log('\n🎯 АНАЛИЗ ЭЛЕМЕНТОВ WIZARD');
    console.log('==========================');
    
    // Проверяем заголовки
    const headers = document.querySelectorAll('#widget-preview .wizard-step h2');
    headers.forEach((header, index) => {
        analyzeElement(header, `Заголовок шага ${index + 1}: "${header.textContent}"`);
    });
    
    // Проверяем кнопки моделей
    const modelButtons = document.querySelectorAll('#wizard-models-list .list-item');
    console.log(`\n📱 Найдено ${modelButtons.length} кнопок моделей`);
    modelButtons.forEach((button, index) => {
        if (index < 3) { // Анализируем первые 3 кнопки
            analyzeElement(button, `Кнопка модели ${index + 1}: "${button.textContent}"`);
        }
    });
    
    // Проверяем кнопки модификаций
    const modButtons = document.querySelectorAll('#wizard-modifications-list .list-item');
    console.log(`\n🔧 Найдено ${modButtons.length} кнопок модификаций`);
    modButtons.forEach((button, index) => {
        if (index < 3) { // Анализируем первые 3 кнопки
            analyzeElement(button, `Кнопка модификации ${index + 1}: "${button.textContent}"`);
        }
    });
    
    // Проверяем навигационные кнопки
    const nextBtn = document.querySelector('#wizard-next-btn');
    const backBtn = document.querySelector('#wizard-back-btn');
    analyzeElement(nextBtn, 'Кнопка Next');
    analyzeElement(backBtn, 'Кнопка Back');
    
    // Проверяем поисковое поле
    const searchInput = document.querySelector('.wizard-search-wrapper input');
    analyzeElement(searchInput, 'Поисковое поле');
}

function checkConflictingRules() {
    console.log('\n⚠️ ПОИСК КОНФЛИКТУЮЩИХ ПРАВИЛ');
    console.log('==============================');
    
    const problematicSelectors = [
        '#widget-preview button',
        '#widget-preview .list-item',
        '#widget-preview .wizard-step h2',
        '#widget-preview .wizard-step-name',
        '.wsf-truncate',
        '.wsf-whitespace-nowrap',
        '.wsf-break-all',
        '.wsf-overflow-hidden'
    ];
    
    problematicSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
            console.log(`\n🎯 Селектор: ${selector} (${elements.length} элементов)`);
            
            // Проверяем первый элемент
            const element = elements[0];
            const computedStyles = window.getComputedStyle(element);
            
            const criticalStyles = {
                'white-space': computedStyles.whiteSpace,
                'word-break': computedStyles.wordBreak,
                'overflow': computedStyles.overflow,
                'text-overflow': computedStyles.textOverflow,
                'display': computedStyles.display
            };
            
            console.log('Critical styles:', criticalStyles);
        }
    });
}

function checkSpecificity() {
    console.log('\n📊 АНАЛИЗ СПЕЦИФИЧНОСТИ CSS');
    console.log('============================');
    
    const testElement = document.querySelector('#wizard-models-list .list-item');
    if (testElement) {
        console.log('Тестовый элемент:', testElement);
        console.log('Classes:', testElement.className);
        
        // Проверяем, какие правила применяются
        const styles = window.getComputedStyle(testElement);
        console.log('Final white-space:', styles.whiteSpace);
        console.log('Final word-break:', styles.wordBreak);
        console.log('Final text-align:', styles.textAlign);
        console.log('Final display:', styles.display);
        console.log('Final min-width:', styles.minWidth);
        console.log('Final min-height:', styles.minHeight);
    }
}

function suggestFixes() {
    console.log('\n💡 ПРЕДЛОЖЕНИЯ ПО ИСПРАВЛЕНИЮ');
    console.log('==============================');
    
    const issues = [];
    
    // Проверяем заголовки на разбитый текст
    const headers = document.querySelectorAll('#widget-preview .wizard-step h2');
    headers.forEach(header => {
        const computedStyles = window.getComputedStyle(header);
        if (computedStyles.whiteSpace === 'nowrap' && header.scrollWidth > header.clientWidth) {
            issues.push(`❌ Заголовок "${header.textContent}" обрезается из-за white-space: nowrap`);
        }
        if (computedStyles.wordBreak === 'break-all') {
            issues.push(`❌ Заголовок "${header.textContent}" разбивается по буквам из-за word-break: break-all`);
        }
    });
    
    // Проверяем кнопки на неровность
    const buttons = document.querySelectorAll('#wizard-modifications-list .list-item');
    if (buttons.length > 1) {
        const heights = Array.from(buttons).map(btn => btn.offsetHeight);
        const minHeight = Math.min(...heights);
        const maxHeight = Math.max(...heights);
        
        if (maxHeight - minHeight > 5) {
            issues.push(`❌ Кнопки модификаций имеют разную высоту: ${minHeight}px - ${maxHeight}px`);
        }
        
        const widths = Array.from(buttons).map(btn => btn.offsetWidth);
        const minWidth = Math.min(...widths);
        const maxWidth = Math.max(...widths);
        
        if (maxWidth - minWidth > 20) {
            issues.push(`❌ Кнопки модификаций имеют сильно разную ширину: ${minWidth}px - ${maxWidth}px`);
        }
    }
    
    if (issues.length === 0) {
        console.log('✅ Критических проблем не обнаружено');
    } else {
        console.log('Обнаруженные проблемы:');
        issues.forEach(issue => console.log(issue));
        
        console.log('\n🔧 Рекомендуемые исправления:');
        console.log('1. Добавить более специфичные CSS правила для заголовков');
        console.log('2. Унифицировать стили кнопок модификаций');
        console.log('3. Проверить порядок загрузки CSS файлов');
        console.log('4. Использовать !important только для критических правил');
    }
}

// Запускаем диагностику
checkWizardElements();
checkConflictingRules();
checkSpecificity();
suggestFixes();

console.log('\n🎉 ДИАГНОСТИКА ЗАВЕРШЕНА');
console.log('========================');
console.log('Скопируйте результаты и отправьте разработчику для анализа.');
