<?php

declare(strict_types=1);

namespace MyTyreFinder;

final class Installer
{
    /** Ensure wp_wsf_search_stats table exists */
    public static function maybe_create_stats_table(): void
    {
        global $wpdb;
        $table = $wpdb->prefix . 'wsf_search_stats';

        // If table exists – exit
        if ($wpdb->get_var($wpdb->prepare('SHOW TABLES LIKE %s', $table)) === $table) {
            return;
        }

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        $charset = $wpdb->get_charset_collate();
        $sql = "CREATE TABLE {$table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            searched_at datetime NOT NULL,
            make   varchar(64)  NOT NULL DEFAULT '',
            model  varchar(128) NOT NULL DEFAULT '',
            rim    tinyint(3) unsigned NOT NULL DEFAULT 0,
            width  smallint(5) unsigned NOT NULL DEFAULT 0,
            PRIMARY KEY (id),
            <PERSON><PERSON><PERSON> searched_at (searched_at),
            <PERSON><PERSON><PERSON> make (make),
            KEY model (model)
        ) {$charset};";
        dbDelta($sql);
    }
} 