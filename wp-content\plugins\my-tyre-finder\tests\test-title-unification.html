<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Title Unification Test - Admin vs Frontend</title>
    
    <!-- Подключаем CSS файлы -->
    <link rel="stylesheet" href="../assets/css/fonts.css">
    <link rel="stylesheet" href="../assets/css/wheel-fit-shared.css">
    <link rel="stylesheet" href="../assets/css/admin-theme-panel.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }
        
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            background: #fafafa;
        }
        
        .comparison-section h2 {
            margin-top: 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            padding: 10px;
            background: #e5e7eb;
            border-radius: 8px;
        }
        
        .frontend-section {
            border-color: #3b82f6;
        }
        
        .frontend-section h2 {
            background: #3b82f6;
            color: white;
        }
        
        .admin-section {
            border-color: #10b981;
        }
        
        .admin-section h2 {
            background: #10b981;
            color: white;
        }
        
        .widget-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* Simulate WordPress admin environment */
        .admin-environment {
            background: #f0f0f1;
            padding: 20px;
            border-radius: 8px;
        }
        
        .admin-environment .widget-preview {
            background: white;
        }
        
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .controls h3 {
            margin-top: 0;
            color: #374151;
        }
        
        .control-group {
            margin: 15px 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        
        .control-group select,
        .control-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .analysis {
            margin: 30px 0;
            padding: 20px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
        }
        
        .analysis h3 {
            margin-top: 0;
            color: #0c4a6e;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
        }
        
        .metric-label {
            font-weight: 500;
        }
        
        .metric-value {
            font-family: monospace;
            color: #059669;
        }
        
        .difference {
            color: #dc2626;
        }
        
        .match {
            color: #059669;
        }
        
        @media (max-width: 1024px) {
            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">Widget Title Unification Test</h1>
            <p style="color: #6b7280; font-size: 18px;">Сравнение отображения заголовка "Wheel & Tyre Finder" в админке и на фронте</p>
        </header>

        <div class="controls">
            <h3>Настройки тестирования</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div class="control-group">
                    <label for="theme-select">Тема виджета:</label>
                    <select id="theme-select">
                        <option value="light">Light Theme</option>
                        <option value="dark">Dark Theme</option>
                        <option value="custom">Custom Theme</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="font-size">Размер шрифта:</label>
                    <select id="font-size">
                        <option value="mobile">Mobile (24px)</option>
                        <option value="desktop" selected>Desktop (30px)</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="primary-color">Основной цвет:</label>
                    <input type="color" id="primary-color" value="#2563eb">
                </div>
            </div>
        </div>

        <div class="comparison-grid">
            <!-- Frontend Version -->
            <div class="comparison-section frontend-section">
                <h2>🌐 Frontend (Фронт сайта)</h2>
                <div class="widget-preview">
                    <div class="wheel-fit-widget bg-wsf-bg wsf-root-font max-w-4xl mx-auto rounded-lg shadow-lg">
                        <div class="wsf-form-wrapper">
                            <div class="wsf-widget__header mb-6">
                                <h1 class="wsf-widget__title text-center text-2xl md:text-3xl font-extrabold tracking-tight text-wsf-text m-0">Wheel & Tyre Finder</h1>
                            </div>
                            <div style="padding: 20px; text-align: center; color: #6b7280;">
                                <p>Здесь будет форма поиска...</p>
                                <div style="display: flex; gap: 10px; justify-content: center; margin-top: 15px;">
                                    <div style="width: 100px; height: 40px; background: #e5e7eb; border-radius: 6px;"></div>
                                    <div style="width: 100px; height: 40px; background: #e5e7eb; border-radius: 6px;"></div>
                                    <div style="width: 100px; height: 40px; background: #2563eb; border-radius: 6px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Version -->
            <div class="comparison-section admin-section">
                <h2>⚙️ Admin (Live Preview)</h2>
                <div class="admin-environment">
                    <div class="widget-preview" id="widget-preview">
                        <div class="wheel-fit-widget bg-wsf-bg wsf-root-font max-w-4xl mx-auto rounded-lg shadow-lg">
                            <div class="wsf-form-wrapper">
                                <div class="wsf-widget__header mb-6">
                                    <h1 class="wsf-widget__title text-center text-2xl md:text-3xl font-extrabold tracking-tight text-wsf-text m-0">Wheel & Tyre Finder</h1>
                                </div>
                                <div style="padding: 20px; text-align: center; color: #6b7280;">
                                    <p>Здесь будет форма поиска...</p>
                                    <div style="display: flex; gap: 10px; justify-content: center; margin-top: 15px;">
                                        <div style="width: 100px; height: 40px; background: #e5e7eb; border-radius: 6px;"></div>
                                        <div style="width: 100px; height: 40px; background: #e5e7eb; border-radius: 6px;"></div>
                                        <div style="width: 100px; height: 40px; background: #2563eb; border-radius: 6px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="analysis">
            <h3>📊 Анализ соответствия стилей</h3>
            <div id="analysis-results">
                <div class="metric">
                    <span class="metric-label">Шрифт (font-family):</span>
                    <span class="metric-value" id="font-family-status">Проверяется...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Размер шрифта (font-size):</span>
                    <span class="metric-value" id="font-size-status">Проверяется...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Вес шрифта (font-weight):</span>
                    <span class="metric-value" id="font-weight-status">Проверяется...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Высота строки (line-height):</span>
                    <span class="metric-value" id="line-height-status">Проверяется...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Выравнивание (text-align):</span>
                    <span class="metric-value" id="text-align-status">Проверяется...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Цвет текста (color):</span>
                    <span class="metric-value" id="color-status">Проверяется...</span>
                </div>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f0fdf4; border: 1px solid #16a34a; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #15803d;">✅ Результат унификации</h3>
            <p><strong>Цель:</strong> Обеспечить идентичное отображение заголовка "Wheel & Tyre Finder" в админке (Live Preview) и на фронте сайта.</p>
            <p><strong>Реализация:</strong></p>
            <ul>
                <li>Добавлены специфичные CSS стили в админку с <code>!important</code></li>
                <li>Принудительное использование шрифта Inter во всех контекстах</li>
                <li>Унифицированы размеры, веса и другие свойства заголовка</li>
                <li>Обеспечена адаптивность (24px на мобильных, 30px на десктопе)</li>
            </ul>
        </div>
    </div>

    <script>
        // Анализ стилей заголовков
        function analyzeStyles() {
            const frontendTitle = document.querySelector('.frontend-section .wsf-widget__title');
            const adminTitle = document.querySelector('.admin-section .wsf-widget__title');
            
            if (!frontendTitle || !adminTitle) {
                console.error('Заголовки не найдены');
                return;
            }
            
            const frontendStyles = window.getComputedStyle(frontendTitle);
            const adminStyles = window.getComputedStyle(adminTitle);
            
            const properties = [
                'fontFamily',
                'fontSize', 
                'fontWeight',
                'lineHeight',
                'textAlign',
                'color'
            ];
            
            properties.forEach(prop => {
                const frontendValue = frontendStyles[prop];
                const adminValue = adminStyles[prop];
                const statusElement = document.getElementById(`${prop.replace(/([A-Z])/g, '-$1').toLowerCase()}-status`);
                
                if (statusElement) {
                    if (frontendValue === adminValue) {
                        statusElement.textContent = `✅ Совпадает (${frontendValue})`;
                        statusElement.className = 'metric-value match';
                    } else {
                        statusElement.textContent = `❌ Различается (F: ${frontendValue} | A: ${adminValue})`;
                        statusElement.className = 'metric-value difference';
                    }
                }
            });
        }
        
        // Обработчики изменений
        document.getElementById('theme-select').addEventListener('change', function(e) {
            const theme = e.target.value;
            document.querySelectorAll('.wheel-fit-widget').forEach(widget => {
                widget.setAttribute('data-wsf-theme', theme);
            });
        });
        
        document.getElementById('font-size').addEventListener('change', function(e) {
            const size = e.target.value;
            document.querySelectorAll('.wsf-widget__title').forEach(title => {
                if (size === 'mobile') {
                    title.style.fontSize = '1.5rem';
                } else {
                    title.style.fontSize = '1.875rem';
                }
            });
            setTimeout(analyzeStyles, 100);
        });
        
        document.getElementById('primary-color').addEventListener('change', function(e) {
            const color = e.target.value;
            document.documentElement.style.setProperty('--wsf-primary', color);
        });
        
        // Запуск анализа после загрузки
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(analyzeStyles, 500);
        });
    </script>
</body>
</html>
