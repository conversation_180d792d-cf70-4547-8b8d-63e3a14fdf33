<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Garage Accent Fix Test</title>
    <link rel="stylesheet" href="../assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .color-controls {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .color-field {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .color-input {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .color-label {
            flex: 1;
            font-weight: 500;
            color: #374151;
        }
        
        .demo-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        /* Demo garage button in interface */
        .demo-garage-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--wsf-accent, #3b82f6);
            padding: 6px 12px;
            border-radius: 6px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .demo-garage-button:hover {
            background: var(--wsf-surface, #f9fafb);
            color: var(--wsf-text, #1f2937);
        }
        
        .demo-garage-icon {
            width: 20px;
            height: 20px;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }
        
        /* Demo size card with add to garage button */
        .demo-size-card {
            position: relative;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 15px;
            text-align: center;
            width: 180px;
            display: inline-block;
        }
        
        .demo-diameter {
            font-size: 2rem;
            font-weight: 900;
            color: var(--wsf-primary, #2563eb);
            line-height: 1;
            margin-bottom: 8px;
        }
        
        .demo-tire-size {
            color: #374151;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* Demo add to garage button - using new classes */
        .demo-add-to-garage {
            position: absolute;
            bottom: 8px;
            right: 8px;
            padding: 4px;
            border-radius: 50%;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .demo-add-to-garage.wsf-garage-hover:hover {
            background-color: var(--wsf-accent);
        }
        
        .demo-add-to-garage.wsf-garage-hover:hover .wsf-text-accent {
            color: var(--wsf-bg);
        }
        
        .demo-add-icon {
            width: 24px;
            height: 24px;
            stroke: currentColor;
            fill: none;
            stroke-width: 2;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .status-item {
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .status-pass {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }
        
        .status-fail {
            background: #fef2f2;
            border-color: #fecaca;
        }
        
        .code-block {
            background: #1e293b;
            color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .instructions {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #92400e;
        }
        
        .highlight-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Complete Garage Accent Fix Test</h1>
        <p>Полная проверка исправлений для кнопок гаража - все элементы должны использовать цвет Accent.</p>
        
        <!-- Color Controls -->
        <div class="test-section">
            <h3>🎛️ Управление цветами</h3>
            <p>Измените цвет "Accent" и убедитесь, что ВСЕ элементы гаража меняются:</p>
            
            <div class="color-controls">
                <div class="color-field">
                    <input type="color" class="color-input" value="#3b82f6" data-token="--wsf-accent">
                    <label class="color-label">Accent (для всех элементов гаража)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#2563eb" data-token="--wsf-primary">
                    <label class="color-label">Primary (для диаметров и основных кнопок)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#ffffff" data-token="--wsf-bg">
                    <label class="color-label">Background (для фона при наведении)</label>
                </div>
                
                <div class="color-field">
                    <input type="color" class="color-input" value="#1f2937" data-token="--wsf-text">
                    <label class="color-label">Text (НЕ влияет на гараж)</label>
                </div>
            </div>
        </div>
        
        <!-- Live Demo -->
        <div class="test-section">
            <h3>🎮 Живая демонстрация</h3>
            
            <div class="demo-widget" id="demo-widget">
                <h4 style="color: var(--wsf-text); margin-bottom: 20px;">1. Кнопка "Garage" в интерфейсе</h4>
                <div style="margin-bottom: 30px;">
                    <button class="demo-garage-button">
                        <svg class="demo-garage-icon" viewBox="0 0 24 24">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                        </svg>
                        <span style="font-weight: 600;">Garage</span>
                    </button>
                    <p style="font-size: 12px; color: #6b7280; margin-top: 8px;">
                        ↑ Использует <code>wsf-text-accent</code> (цвет Accent)
                    </p>
                </div>
                
                <h4 style="color: var(--wsf-text); margin-bottom: 20px;">2. Кнопки "Add to Garage" на карточках</h4>
                <div style="display: flex; gap: 20px; flex-wrap: wrap; justify-content: center;">
                    <div class="demo-size-card">
                        <div class="demo-diameter">17"</div>
                        <div class="demo-tire-size">225/45 R17</div>
                        <button class="demo-add-to-garage wsf-garage-hover" title="Add to Garage">
                            <svg class="demo-add-icon wsf-text-accent" viewBox="0 0 24 24">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="demo-size-card">
                        <div class="demo-diameter">18"</div>
                        <div class="demo-tire-size">LT185R14</div>
                        <button class="demo-add-to-garage wsf-garage-hover" title="Add to Garage">
                            <svg class="demo-add-icon wsf-text-accent" viewBox="0 0 24 24">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                        </button>
                    </div>
                </div>
                <p style="font-size: 12px; color: #6b7280; margin-top: 15px; text-align: center;">
                    ↑ Используют <code>wsf-text-accent</code> и <code>wsf-garage-hover</code><br>
                    При наведении: фон = Accent, иконка = Background
                </p>
            </div>
        </div>
        
        <!-- Status Check -->
        <div class="test-section">
            <h3>✅ Статус исправлений</h3>
            
            <div class="status-grid">
                <div class="status-item status-pass">
                    <h4>✅ Кнопка "Garage" в интерфейсе</h4>
                    <p><strong>Исправлено:</strong> <code>text-wsf-muted</code> → <code>wsf-text-accent</code></p>
                    <p><strong>Файлы:</strong> 5 шаблонов (.twig)</p>
                    <p><strong>Результат:</strong> Использует цвет Accent</p>
                </div>
                
                <div class="status-item status-pass">
                    <h4>✅ Кнопка "Add to Garage"</h4>
                    <p><strong>Исправлено:</strong> <code>text-wsf-primary</code> → <code>wsf-text-accent</code></p>
                    <p><strong>Файл:</strong> finder.js</p>
                    <p><strong>Результат:</strong> Использует цвет Accent</p>
                </div>
                
                <div class="status-item status-pass">
                    <h4>✅ Hover состояние</h4>
                    <p><strong>Исправлено:</strong> <code>hover:bg-wsf-primary</code> → <code>wsf-garage-hover</code></p>
                    <p><strong>Добавлено:</strong> CSS классы для настраиваемого hover</p>
                    <p><strong>Результат:</strong> Фон = Accent, иконка = Background</p>
                </div>
                
                <div class="status-item status-pass">
                    <h4>✅ CSS классы</h4>
                    <p><strong>Добавлено:</strong> <code>.wsf-garage-hover</code></p>
                    <p><strong>Функция:</strong> Настраиваемые hover состояния</p>
                    <p><strong>Результат:</strong> Полный контроль через Theme Presets</p>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="test-section">
            <h3>🔧 Технические детали</h3>
            
            <h4>Новые CSS классы:</h4>
            <div class="code-block">
/* Добавлено в wheel-fit-shared.src.css */
.wsf-garage-hover:hover { 
    background-color: var(--wsf-accent); 
}
.wsf-garage-hover:hover .wsf-text-accent { 
    color: var(--wsf-bg); 
}
            </div>
            
            <h4>Исправленный код кнопки "Add to Garage":</h4>
            <div class="code-block">
// ДО (проблемный код):
&lt;button class="hover:bg-wsf-primary"&gt;
    &lt;i class="text-wsf-primary group-hover:text-white"&gt;&lt;/i&gt;
&lt;/button&gt;

// ПОСЛЕ (исправленный код):
&lt;button class="wsf-garage-hover"&gt;
    &lt;i class="wsf-text-accent"&gt;&lt;/i&gt;
&lt;/button&gt;
            </div>
            
            <h4>Все элементы, использующие Accent:</h4>
            <ul>
                <li>✅ Кнопка "Garage" в интерфейсе (5 шаблонов)</li>
                <li>✅ Иконка "Add to Garage" на карточках (finder.js)</li>
                <li>✅ Ссылки "Show more" / "Show less"</li>
                <li>✅ Year ranges в результатах поиска</li>
            </ul>
        </div>
        
        <!-- Instructions -->
        <div class="instructions">
            <h4>📋 Как проверить в WordPress</h4>
            <ol>
                <li><strong>Очистите кэш</strong> WordPress и браузера</li>
                <li>Перейдите в <strong>WordPress Admin → Wheel-Size → Appearance</strong></li>
                <li>Создайте новую тему или отредактируйте существующую</li>
                <li>Измените цвет <strong>"Accent"</strong> на яркий (например, красный #ff0000)</li>
                <li>Сохраните и примените тему</li>
                <li>Перейдите на страницу с виджетом и выполните поиск</li>
                <li>Убедитесь, что изменились:
                    <ul>
                        <li>Кнопка "Garage" в интерфейсе ✅</li>
                        <li>Иконки "Add to Garage" на карточках ✅</li>
                        <li>Ссылки "Show more" ✅</li>
                        <li>Year ranges ✅</li>
                    </ul>
                </li>
                <li>Проверьте hover состояния:
                    <ul>
                        <li>При наведении на "Add to Garage" фон становится цветом Accent ✅</li>
                        <li>При наведении иконка становится цветом Background ✅</li>
                    </ul>
                </li>
            </ol>
            
            <div class="highlight-box">
                <strong>🎯 Ожидаемый результат:</strong>
                <ul>
                    <li>Accent цвет полностью функционален</li>
                    <li>Все элементы гаража используют Accent</li>
                    <li>Hover состояния настраиваются через Theme Presets</li>
                    <li>Логичное разделение между Primary и Accent</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Color picker functionality
        document.querySelectorAll('.color-input').forEach(input => {
            input.addEventListener('change', function() {
                const token = this.dataset.token;
                const value = this.value;
                const demoWidget = document.getElementById('demo-widget');
                
                demoWidget.style.setProperty(token, value);
                console.log(`Updated ${token} to ${value}`);
            });
        });
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            const demoWidget = document.getElementById('demo-widget');
            
            // Set default values
            demoWidget.style.setProperty('--wsf-bg', '#ffffff');
            demoWidget.style.setProperty('--wsf-text', '#1f2937');
            demoWidget.style.setProperty('--wsf-accent', '#3b82f6');
            demoWidget.style.setProperty('--wsf-primary', '#2563eb');
            demoWidget.style.setProperty('--wsf-border', '#e5e7eb');
            demoWidget.style.setProperty('--wsf-surface', '#f9fafb');
            
            console.log('Complete garage accent fix demo initialized');
        });
    </script>
</body>
</html>
