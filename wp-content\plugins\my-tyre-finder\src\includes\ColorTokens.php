<?php
declare(strict_types=1);

namespace MyTyreFinder\Includes;

/**
 * Color Tokens Metadata
 * Single source of truth for color token definitions
 */
final class ColorTokens
{
    /**
     * Get color tokens metadata
     * 
     * @return array Array of color token definitions with labels, help text, and CSS variables
     */
    public static function get_tokens(): array
    {
        return [
            // Base colors (foundation)
            'background' => [
                'label' => 'Background',
                'help' => 'Global widget/page background',
                'vars' => ['--wsf-bg'],
                'property' => '--wsf-bg',
                'order' => 1
            ],
            'text' => [
                'label' => 'Text',
                'help' => 'Default body text',
                'vars' => ['--wsf-text', '--wsf-text-primary'],
                'property' => '--wsf-text',
                'order' => 2
            ],
            'border' => [
                'label' => 'Border',
                'help' => 'Default borders for inputs & cards',
                'vars' => ['--wsf-border'],
                'property' => '--wsf-border',
                'order' => 3
            ],
            // Primary colors (main actions)
            'primary' => [
                'label' => 'Primary',
                'help' => 'Main action color (buttons & focus)',
                'vars' => ['--wsf-primary'],
                'property' => '--wsf-primary',
                'order' => 4
            ],
            'hover' => [
                'label' => 'Hover State',
                'help' => 'Hover color for primary elements',
                'vars' => ['--wsf-hover'],
                'property' => '--wsf-hover',
                'order' => 5
            ],
            'accent' => [
                'label' => 'Accent',
                'help' => 'Links, icons, accent highlights',
                'vars' => ['--wsf-accent'],
                'property' => '--wsf-accent',
                'order' => 6
            ],
            // Secondary colors (supporting elements)
            'secondary' => [
                'label' => 'Secondary',
                'help' => 'Secondary text & UI elements',
                'vars' => ['--wsf-text-secondary', '--wsf-secondary'],
                'property' => '--wsf-secondary',
                'order' => 7
            ],
            'muted' => [
                'label' => 'Muted',
                'help' => 'Placeholders & subtle/disabled UI',
                'vars' => ['--wsf-muted', '--wsf-text-muted'],
                'property' => '--wsf-muted',
                'order' => 8
            ],
            // Surface & Input colors (form elements)
            'surface' => [
                'label' => 'Surface',
                'help' => 'Background for cards, inputs & form elements',
                'vars' => ['--wsf-surface'],
                'property' => '--wsf-surface',
                'order' => 9
            ],
            'input_background' => [
                'label' => 'Input Background',
                'help' => 'Background color for select elements & inputs',
                'vars' => ['--wsf-input-bg'],
                'property' => '--wsf-input-bg',
                'order' => 10
            ],
            'input_text' => [
                'label' => 'Input Text',
                'help' => 'Text color inside select elements & inputs',
                'vars' => ['--wsf-input-text'],
                'property' => '--wsf-input-text',
                'order' => 11
            ],
            'input_border' => [
                'label' => 'Input Border',
                'help' => 'Border color for select elements & inputs',
                'vars' => ['--wsf-input-border'],
                'property' => '--wsf-input-border',
                'order' => 12
            ],
            // State colors (feedback)
            'success' => [
                'label' => 'Success',
                'help' => 'Positive/confirmed states',
                'vars' => ['--wsf-success'],
                'property' => '--wsf-success',
                'order' => 13
            ],
            'warning' => [
                'label' => 'Warning',
                'help' => 'Attention-needed states',
                'vars' => ['--wsf-warning'],
                'property' => '--wsf-warning',
                'order' => 14
            ],
            'error' => [
                'label' => 'Error',
                'help' => 'Errors & invalid input states',
                'vars' => ['--wsf-error'],
                'property' => '--wsf-error',
                'order' => 15
            ]
        ];
    }

    /**
     * Get color tokens formatted for JavaScript
     * 
     * @return array Array formatted for wp_localize_script
     */
    public static function get_tokens_for_js(): array
    {
        $tokens = self::get_tokens();

        // Sort by order field
        uasort($tokens, function($a, $b) {
            return ($a['order'] ?? 999) <=> ($b['order'] ?? 999);
        });

        $js_tokens = [];

        foreach ($tokens as $key => $token) {
            $js_tokens[$token['property']] = [
                'key' => $key,
                'label' => $token['label'],
                'help' => $token['help'],
                'vars' => $token['vars'],
                'property' => $token['property'],
                'order' => $token['order'] ?? 999
            ];
        }

        return $js_tokens;
    }

    /**
     * Get example elements for each token
     * 
     * @return array Array of example elements for each token
     */
    public static function get_examples(): array
    {
        return [
            'background' => 'Widget wrapper',
            'text' => 'Body text, headings',
            'border' => 'Input borders, cards',
            'primary' => 'Primary buttons, focus rings',
            'hover' => 'Button hovers, active links',
            'accent' => 'Links, small badges',
            'secondary' => 'Subtitles, labels',
            'muted' => 'Placeholders, disabled fields',
            'surface' => 'Card backgrounds, input containers',
            'input_background' => 'Select dropdowns, text inputs',
            'input_text' => 'Text inside select elements',
            'input_border' => 'Select borders, input outlines',
            'success' => 'Success messages',
            'warning' => 'Warnings, notices',
            'error' => 'Validation errors'
        ];
    }
}
