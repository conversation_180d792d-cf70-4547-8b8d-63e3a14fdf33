/**
 * Test Live Preview functionality
 * Run in browser console on admin theme panel page
 */

(function() {
    'use strict';

    console.log('🎬 === LIVE PREVIEW TEST ===');

    // Find preview container
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.log('❌ Live preview container not found');
        return;
    }

    console.log('✅ Live preview container found');

    // Find widgets in preview
    const widgets = previewContainer.querySelectorAll('.wsf-finder-widget, .wheel-fit-widget');
    console.log(`📊 Found ${widgets.length} widget(s) in preview`);

    if (widgets.length === 0) {
        console.log('⚠️ No widgets found in preview container');
        return;
    }

    const widget = widgets[0];
    console.log('🎯 Testing with first widget');

    // Test themes
    const testThemes = {
        light: {
            '--wsf-bg': '#ffffff',
            '--wsf-text': '#1f2937',
            '--wsf-primary': '#2563eb',
            '--wsf-border': '#e5e7eb'
        },
        dark: {
            '--wsf-bg': '#1e1e1e',
            '--wsf-text': '#f3f4f6',
            '--wsf-primary': '#7dd3fc',
            '--wsf-border': '#374151'
        },
        blue: {
            '--wsf-bg': '#f0f9ff',
            '--wsf-text': '#0c4a6e',
            '--wsf-primary': '#0284c7',
            '--wsf-border': '#7dd3fc'
        },
        green: {
            '--wsf-bg': '#f0fdf4',
            '--wsf-text': '#14532d',
            '--wsf-primary': '#16a34a',
            '--wsf-border': '#86efac'
        }
    };

    // Apply theme function
    function applyTheme(themeName, theme) {
        console.log(`🎨 Applying ${themeName} theme...`);
        
        Object.entries(theme).forEach(([property, value]) => {
            previewContainer.style.setProperty(property, value);
        });
        
        // Log current styles
        const styles = getComputedStyle(widget);
        console.log(`  Background: ${styles.backgroundColor}`);
        console.log(`  Text: ${styles.color}`);
    }

    // Test each theme
    let currentTheme = 0;
    const themeNames = Object.keys(testThemes);
    
    function cycleThemes() {
        const themeName = themeNames[currentTheme];
        const theme = testThemes[themeName];
        
        applyTheme(themeName, theme);
        
        currentTheme = (currentTheme + 1) % themeNames.length;
        
        if (currentTheme === 0) {
            console.log('🔄 Theme cycle completed');
            clearInterval(themeInterval);
            
            // Reset to light theme
            setTimeout(() => {
                applyTheme('light (reset)', testThemes.light);
            }, 1000);
        }
    }

    // Start theme cycling
    console.log('🚀 Starting theme cycle test...');
    const themeInterval = setInterval(cycleThemes, 2000);

    // Manual test functions
    window.testLightTheme = () => applyTheme('light', testThemes.light);
    window.testDarkTheme = () => applyTheme('dark', testThemes.dark);
    window.testBlueTheme = () => applyTheme('blue', testThemes.blue);
    window.testGreenTheme = () => applyTheme('green', testThemes.green);

    // Check current CSS variables
    function checkCurrentVariables() {
        console.log('\n📋 Current CSS Variables:');
        Object.keys(testThemes.light).forEach(prop => {
            const value = previewContainer.style.getPropertyValue(prop);
            console.log(`  ${prop}: ${value || 'not set'}`);
        });
    }

    // Status indicator
    const indicator = document.createElement('div');
    indicator.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: #007cba;
        color: white;
        padding: 12px 16px;
        border-radius: 6px;
        font-size: 12px;
        z-index: 9999;
        font-family: monospace;
        max-width: 200px;
    `;
    indicator.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 4px;">🎬 Live Preview Test</div>
        <div>Widgets: ${widgets.length}</div>
        <div>Container: ${previewContainer.tagName}</div>
        <div style="margin-top: 8px; font-size: 10px;">
            Manual tests available:<br>
            testLightTheme()<br>
            testDarkTheme()<br>
            testBlueTheme()<br>
            testGreenTheme()
        </div>
    `;
    document.body.appendChild(indicator);

    setTimeout(() => indicator.remove(), 10000);

    console.log('\n💡 Manual test functions available:');
    console.log('  testLightTheme() - Apply light theme');
    console.log('  testDarkTheme() - Apply dark theme');
    console.log('  testBlueTheme() - Apply blue theme');
    console.log('  testGreenTheme() - Apply green theme');

    // Check variables after 8 seconds
    setTimeout(checkCurrentVariables, 8000);

})();
