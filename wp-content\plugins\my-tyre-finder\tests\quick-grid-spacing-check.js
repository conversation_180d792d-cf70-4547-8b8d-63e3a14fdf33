/**
 * Quick Grid Spacing Check
 * 
 * Copy and paste this entire script into browser console on:
 * /wp-admin/admin.php?page=wheel-size-appearance
 * 
 * Make sure to select "Grid (2×2)" layout first and enable Garage!
 */

console.log('📐 Quick Grid Garage Spacing Check...');

// Check if we're in grid layout
const gridForm = document.querySelector('#tab-by-car.grid-2x2');
const garageButton = document.querySelector('[data-garage-trigger]');
const submitButton = document.querySelector('.ws-submit .btn-primary');

if (!gridForm || !garageButton || !submitButton) {
    console.error('❌ Grid layout or required elements not detected');
    console.log('💡 Make sure to:');
    console.log('   1. Select "Grid (2×2)" in Form Layout dropdown');
    console.log('   2. Enable Garage feature');
    console.log('   3. Refresh the Live Preview');
    console.log('   4. Run this test again');
    console.log('');
    console.log('Found elements:');
    console.log('   Grid form:', !!gridForm);
    console.log('   Garage button:', !!garageButton);
    console.log('   Submit button:', !!submitButton);
} else {
    console.log('✅ Grid (2×2) layout with Garage detected');
    
    console.log('\n📊 GARAGE SPACING CHECK:');
    
    // Get element positions
    const garageRect = garageButton.getBoundingClientRect();
    const submitRect = submitButton.getBoundingClientRect();
    
    // Calculate spacing
    const spacing = submitRect.top - garageRect.bottom;
    
    console.log(`Garage button bottom: ${garageRect.bottom.toFixed(1)}px`);
    console.log(`Submit button top: ${submitRect.top.toFixed(1)}px`);
    console.log(`Vertical spacing: ${spacing.toFixed(1)}px`);
    
    // Check if spacing is reasonable
    const minSpacing = 8; // 8px minimum
    const maxSpacing = 24; // 24px maximum (reduced from larger value)
    const isReasonableSpacing = spacing >= minSpacing && spacing <= maxSpacing;
    
    console.log(`\n📊 SPACING ANALYSIS:`);
    console.log(`1. Spacing range: ${minSpacing}-${maxSpacing}px (target)`);
    console.log(`2. Actual spacing: ${spacing.toFixed(1)}px`);
    console.log(`3. Spacing reasonable: ${isReasonableSpacing ? '✅' : '❌'}`);
    
    if (!isReasonableSpacing) {
        if (spacing < minSpacing) {
            console.log(`   → Too tight (minimum: ${minSpacing}px)`);
        } else {
            console.log(`   → Too large (maximum: ${maxSpacing}px)`);
        }
    }
    
    // Check grid gap
    const gridStyle = window.getComputedStyle(gridForm);
    const gridGap = parseFloat(gridStyle.gap) || parseFloat(gridStyle.rowGap);
    const expectedGap = 12; // 0.75rem
    const gapCorrect = Math.abs(gridGap - expectedGap) <= 2;
    
    console.log(`4. Grid gap: ${gridGap}px ${gapCorrect ? '✅' : '❌'} (expected ~${expectedGap}px)`);
    
    // Check responsive behavior
    const viewportWidth = window.innerWidth;
    const isMobile = viewportWidth <= 639;
    
    console.log(`5. Viewport: ${viewportWidth}px (${isMobile ? 'Mobile' : 'Desktop'})`);
    
    if (isMobile) {
        const expectedMobileGap = 8; // 0.5rem
        const mobileGapCorrect = Math.abs(gridGap - expectedMobileGap) <= 2;
        console.log(`   Mobile gap correct: ${mobileGapCorrect ? '✅' : '⚠️'} (expected ~${expectedMobileGap}px)`);
    }
    
    // Check visual order
    const gridChildren = Array.from(gridForm.children);
    let garageIndex = -1;
    let submitIndex = -1;
    
    gridChildren.forEach((child, index) => {
        if (child.querySelector('[data-garage-trigger]')) {
            garageIndex = index;
        }
        if (child.classList.contains('ws-submit')) {
            submitIndex = index;
        }
    });
    
    const correctOrder = garageIndex < submitIndex && garageIndex >= 0 && submitIndex >= 0;
    
    console.log(`6. Element order: ${correctOrder ? '✅' : '❌'}`);
    console.log(`   Garage position: ${garageIndex + 1} of ${gridChildren.length}`);
    console.log(`   Submit position: ${submitIndex + 1} of ${gridChildren.length}`);
    
    // Overall status
    const allGood = isReasonableSpacing && gapCorrect && correctOrder;
    
    console.log(`\n🎯 OVERALL STATUS: ${allGood ? '✅ PERFECT SPACING' : '❌ NEEDS ATTENTION'}`);
    
    if (!allGood) {
        console.log('\n💡 ISSUES TO FIX:');
        if (!isReasonableSpacing) {
            console.log(`   - Adjust spacing between Garage and Submit (currently ${spacing.toFixed(1)}px)`);
        }
        if (!gapCorrect) {
            console.log(`   - Adjust grid gap (currently ${gridGap}px, should be ~${expectedGap}px)`);
        }
        if (!correctOrder) {
            console.log('   - Check element order in grid');
        }
    }
    
    // Check CSS classes and margins
    console.log('\n🔍 CSS ANALYSIS:');
    
    // Check garage button margins
    const garageStyle = window.getComputedStyle(garageButton);
    console.log(`Garage button margins: ${garageStyle.marginTop} ${garageStyle.marginRight} ${garageStyle.marginBottom} ${garageStyle.marginLeft}`);
    
    // Check submit container margins
    const submitContainer = document.querySelector('.ws-submit');
    if (submitContainer) {
        const submitContainerStyle = window.getComputedStyle(submitContainer);
        console.log(`Submit container margins: ${submitContainerStyle.marginTop} ${submitContainerStyle.marginRight} ${submitContainerStyle.marginBottom} ${submitContainerStyle.marginLeft}`);
    }
    
    // Check submit button margins
    const submitStyle = window.getComputedStyle(submitButton);
    console.log(`Submit button margins: ${submitStyle.marginTop} ${submitStyle.marginRight} ${submitStyle.marginBottom} ${submitStyle.marginLeft}`);
    
    // Visual highlight
    console.log('\n🎨 Highlighting elements for 5 seconds...');
    
    // Highlight garage in green
    garageButton.style.outline = '3px solid green';
    garageButton.style.backgroundColor = 'rgba(0, 255, 0, 0.2)';
    
    // Highlight submit in red
    submitButton.style.outline = '3px solid red';
    submitButton.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
    
    // Highlight grid in blue
    gridForm.style.outline = '2px solid blue';
    
    setTimeout(() => {
        garageButton.style.outline = '';
        garageButton.style.backgroundColor = '';
        submitButton.style.outline = '';
        submitButton.style.backgroundColor = '';
        gridForm.style.outline = '';
        console.log('🧹 Highlights removed');
    }, 5000);
    
    // Summary
    console.log('\n📋 SUMMARY:');
    console.log('Green outline = Garage button');
    console.log('Red outline = Submit button');
    console.log('Blue outline = Grid container');
    console.log('The spacing between green and red should be reduced but comfortable');
    
    if (allGood) {
        console.log('🎉 Perfect! Garage and Submit button spacing is optimized');
    } else {
        console.log('🔧 Needs adjustment - check CSS rules for Grid (2×2) layout');
    }
}
