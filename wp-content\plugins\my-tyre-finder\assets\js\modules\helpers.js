// Helper utilities module (restored)
(function(global){
    const t = (k, fallback = null) => {
        if (global.WheelFitI18n && typeof global.WheelFitI18n === 'object' && k in global.WheelFitI18n) {
            return global.WheelFitI18n[k];
        }
        if (global.WheelFitData && global.WheelFitData.debug) {
            if (!global.__missingKeys) global.__missingKeys = new Set();
            if (!__missingKeys.has(k)) {
                console.warn('[Wheel-Size i18n] missing key:', k);
                __missingKeys.add(k);
            }
        }
        return fallback ?? k;
    };

    const capitalize = str => {
        if (!str || typeof str !== 'string') return '';
        return str.split(' ').map(w => w.toUpperCase() === w ? w : w.charAt(0).toUpperCase() + w.slice(1).toLowerCase()).join(' ');
    };

    const shortMod = str => !str ? '' : str.split(/[ ,-—]+/).slice(0,2).join(' ');

    const fuelIcon = fuel => {
        switch((fuel||'').toLowerCase()){
            case 'electric': return '⚡';
            case 'diesel':
            case 'petrol': return '⛽';
            case 'hybrid': return '⚡';
            default: return '🏎';
        }
    };

    function fadeOut(el, cb){
        if(!el||el.classList.contains('hidden')){cb&&cb();return;}
        el.classList.add('transition-all','duration-300','ease-in-out','transform','opacity-0','scale-95');
        el.addEventListener('transitionend',()=>{el.classList.add('hidden');el.classList.remove('opacity-0','scale-95');cb&&cb();},{once:true});
    }
    function fadeIn(el){
        if(!el) return;
        el.classList.add('transition-all','duration-300','ease-in-out','transform','opacity-0','translate-y-2');
        el.classList.remove('hidden');
        requestAnimationFrame(()=>el.classList.remove('opacity-0','translate-y-2'));
    }

    global.t = global.t || t;
    global.capitalize = global.capitalize || capitalize;
    global.shortMod = global.shortMod || shortMod;
    global.fuelIcon = global.fuelIcon || fuelIcon;
    global.fadeOut = global.fadeOut || fadeOut;
    global.fadeIn  = global.fadeIn  || fadeIn;
})(typeof window!=='undefined'?window:this); 