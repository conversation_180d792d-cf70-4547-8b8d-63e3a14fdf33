<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Title Integration Test</title>
    
    <!-- Подключаем CSS файлы -->
    <link rel="stylesheet" href="../assets/css/wheel-fit-shared.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #111827;
            font-size: 24px;
            font-weight: 600;
        }
        
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .controls button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .controls button.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        /* Имитация виджета - ДО исправления */
        .widget-before {
            max-width: 768px;
            margin: 0 auto;
        }
        
        .widget-before .title-outside {
            text-align: center;
            font-size: 24px;
            font-weight: 800;
            margin: 32px 0;
            color: #1f2937;
            /* Заголовок висит отдельно */
        }
        
        .widget-before .form-container {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        /* Имитация виджета - ПОСЛЕ исправления */
        .widget-after {
            max-width: 768px;
            margin: 0 auto;
        }
        
        .widget-after .wheel-fit-widget {
            background: var(--wsf-bg, #ffffff);
            border: 1px solid var(--wsf-border, #e5e7eb);
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .widget-after .wsf-widget__header {
            padding: 24px 32px 16px;
        }
        
        .widget-after .wsf-widget__title {
            margin: 0;
            color: var(--wsf-text, #1f2937);
            font-size: 24px;
            font-weight: 700;
            line-height: 1.3;
            text-align: center;
        }
        
        .widget-after .wsf-widget__content {
            padding: 0 32px 32px;
        }
        
        @media (min-width: 768px) {
            .widget-after .wsf-widget__title {
                font-size: 30px;
            }
            
            .widget-after .wsf-widget__header {
                padding: 32px 40px 20px;
            }
            
            .widget-after .wsf-widget__content {
                padding: 0 40px 40px;
            }
        }
        
        /* Форма внутри виджета */
        .form-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--wsf-text, #374151);
            font-size: 14px;
        }
        
        .form-select, .form-input {
            padding: 12px 16px;
            border: 2px solid var(--wsf-border, #e5e7eb);
            border-radius: 8px;
            background: var(--wsf-bg, #ffffff);
            color: var(--wsf-text, #1f2937);
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .form-select:focus, .form-input:focus {
            outline: none;
            border-color: var(--wsf-primary, #2563eb);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-button {
            grid-column: 1 / -1;
            padding: 14px 24px;
            background: var(--wsf-primary, #2563eb);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .form-button:hover {
            background: var(--wsf-hover, #1d4ed8);
            transform: translateY(-1px);
        }
        
        /* Темы */
        .theme-light {
            --wsf-bg: #F8FAFC;
            --wsf-text: #1E293B;
            --wsf-border: #E2E8F0;
            --wsf-primary: #2563EB;
            --wsf-hover: #1D4ED8;
        }
        
        .theme-dark {
            --wsf-bg: #0F172A;
            --wsf-text: #F1F5F9;
            --wsf-border: #334155;
            --wsf-primary: #3B82F6;
            --wsf-hover: #2563EB;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }
        
        .comparison-item {
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .comparison-item h3 {
            margin-top: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .form-content {
                grid-template-columns: 1fr;
            }
            
            .test-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Widget Title Integration Test</h1>
        <p>Тест интеграции заголовка виджета внутрь контейнера с общим фоном и стилизацией.</p>
        
        <!-- Контролы -->
        <div class="controls">
            <h3>🎛️ Контролы тестирования</h3>
            <button onclick="switchTheme('light')" class="active" id="btn-light">☀️ Light Theme</button>
            <button onclick="switchTheme('dark')" id="btn-dark">🌙 Dark Theme</button>
            <button onclick="toggleComparison()">🔄 Toggle Comparison</button>
            <button onclick="testMobile()">📱 Mobile View</button>
        </div>
        
        <!-- Сравнение ДО и ПОСЛЕ -->
        <div class="demo-section">
            <h2>📊 Сравнение: До и После интеграции</h2>
            
            <div class="comparison" id="comparison">
                <!-- ДО: Заголовок вне контейнера -->
                <div class="comparison-item">
                    <h3>❌ ДО: Заголовок вне контейнера</h3>
                    <div class="widget-before theme-light" id="widget-before">
                        <h1 class="title-outside">Поиск дисков и шин</h1>
                        <div class="form-container">
                            <div class="form-content">
                                <div class="form-group">
                                    <label class="form-label">Марка автомобиля</label>
                                    <select class="form-select">
                                        <option>Выберите марку</option>
                                        <option>BMW</option>
                                        <option>Mercedes</option>
                                        <option>Audi</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Модель</label>
                                    <select class="form-select">
                                        <option>Выберите модель</option>
                                        <option>X5</option>
                                        <option>X3</option>
                                        <option>3 Series</option>
                                    </select>
                                </div>
                                
                                <button class="form-button">🔍 Найти размеры</button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 16px; padding: 12px; background: #fef2f2; border-radius: 6px; font-size: 14px; color: #dc2626;">
                        <strong>Проблемы:</strong>
                        <ul style="margin: 8px 0 0 16px;">
                            <li>Заголовок "висит" отдельно</li>
                            <li>Нет общего фона</li>
                            <li>Разные радиусы скругления</li>
                            <li>Лишние отступы</li>
                        </ul>
                    </div>
                </div>
                
                <!-- ПОСЛЕ: Заголовок внутри контейнера -->
                <div class="comparison-item">
                    <h3>✅ ПОСЛЕ: Заголовок внутри контейнера</h3>
                    <div class="widget-after theme-light" id="widget-after">
                        <div class="wheel-fit-widget">
                            <div class="wsf-widget__header">
                                <h1 class="wsf-widget__title">Поиск дисков и шин</h1>
                            </div>
                            <div class="wsf-widget__content">
                                <div class="form-content">
                                    <div class="form-group">
                                        <label class="form-label">Марка автомобиля</label>
                                        <select class="form-select">
                                            <option>Выберите марку</option>
                                            <option>BMW</option>
                                            <option>Mercedes</option>
                                            <option>Audi</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Модель</label>
                                        <select class="form-select">
                                            <option>Выберите модель</option>
                                            <option>X5</option>
                                            <option>X3</option>
                                            <option>3 Series</option>
                                        </select>
                                    </div>
                                    
                                    <button class="form-button">🔍 Найти размеры</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 16px; padding: 12px; background: #f0fdf4; border-radius: 6px; font-size: 14px; color: #166534;">
                        <strong>Улучшения:</strong>
                        <ul style="margin: 8px 0 0 16px;">
                            <li>Заголовок внутри общего контейнера</li>
                            <li>Единый фон и стилизация</li>
                            <li>Правильные отступы и радиусы</li>
                            <li>Адаптивная типографика</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Технические детали -->
        <div class="demo-section">
            <h2>🔧 Технические изменения</h2>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div>
                    <h3>HTML структура</h3>
                    <div style="background: #f8fafc; padding: 16px; border-radius: 8px; font-family: monospace; font-size: 12px;">
                        <div style="color: #dc2626; margin-bottom: 12px;"><strong>ДО:</strong></div>
                        <div style="color: #6b7280;">
                            &lt;div class="wheel-fit-widget"&gt;<br>
                            &nbsp;&nbsp;&lt;h1&gt;Заголовок&lt;/h1&gt;<br>
                            &nbsp;&nbsp;&lt;!-- форма --&gt;<br>
                            &lt;/div&gt;
                        </div>
                        
                        <div style="color: #16a34a; margin: 16px 0 8px;"><strong>ПОСЛЕ:</strong></div>
                        <div style="color: #6b7280;">
                            &lt;div class="wheel-fit-widget"&gt;<br>
                            &nbsp;&nbsp;&lt;div class="wsf-widget__header"&gt;<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&lt;h1 class="wsf-widget__title"&gt;Заголовок&lt;/h1&gt;<br>
                            &nbsp;&nbsp;&lt;/div&gt;<br>
                            &nbsp;&nbsp;&lt;div class="wsf-widget__content"&gt;<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&lt;!-- форма --&gt;<br>
                            &nbsp;&nbsp;&lt;/div&gt;<br>
                            &lt;/div&gt;
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3>CSS стили</h3>
                    <div style="background: #f8fafc; padding: 16px; border-radius: 8px; font-family: monospace; font-size: 12px;">
                        <div style="color: #16a34a; margin-bottom: 8px;"><strong>Новые классы:</strong></div>
                        <div style="color: #6b7280;">
                            .wsf-widget__header {<br>
                            &nbsp;&nbsp;padding: 24px 32px 16px;<br>
                            }<br><br>
                            
                            .wsf-widget__title {<br>
                            &nbsp;&nbsp;margin: 0;<br>
                            &nbsp;&nbsp;color: var(--wsf-text);<br>
                            &nbsp;&nbsp;font-size: 1.5rem;<br>
                            &nbsp;&nbsp;font-weight: 700;<br>
                            &nbsp;&nbsp;text-align: center;<br>
                            }<br><br>
                            
                            .wsf-widget__content {<br>
                            &nbsp;&nbsp;padding: 0 32px 32px;<br>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Acceptance Criteria -->
        <div class="demo-section">
            <h2>✅ Acceptance Criteria</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>Визуальные критерии:</h4>
                    <ul>
                        <li>✅ Заголовок внутри блока формы</li>
                        <li>✅ Общий фон с --wsf-bg</li>
                        <li>✅ Единые радиусы скругления</li>
                        <li>✅ Правильные отступы</li>
                        <li>✅ Цвет через --wsf-text</li>
                    </ul>
                </div>
                <div>
                    <h4>Функциональные критерии:</h4>
                    <ul>
                        <li>✅ Адаптивная типографика</li>
                        <li>✅ Корректная смена тем</li>
                        <li>✅ Мобильная адаптивность</li>
                        <li>✅ Нет лишних отступов</li>
                        <li>✅ Одинаковое поведение везде</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentTheme = 'light';
        let comparisonVisible = true;
        
        function switchTheme(theme) {
            currentTheme = theme;
            
            // Обновляем кнопки
            document.getElementById('btn-light').classList.toggle('active', theme === 'light');
            document.getElementById('btn-dark').classList.toggle('active', theme === 'dark');
            
            // Применяем тему к виджетам
            const widgetBefore = document.getElementById('widget-before');
            const widgetAfter = document.getElementById('widget-after');
            
            widgetBefore.className = `widget-before theme-${theme}`;
            widgetAfter.className = `widget-after theme-${theme}`;
            
            showNotification(`${theme.charAt(0).toUpperCase() + theme.slice(1)} theme applied`, 'success');
        }
        
        function toggleComparison() {
            const comparison = document.getElementById('comparison');
            comparisonVisible = !comparisonVisible;
            
            if (comparisonVisible) {
                comparison.style.display = 'grid';
                showNotification('Comparison view enabled', 'info');
            } else {
                comparison.style.display = 'none';
                showNotification('Comparison view disabled', 'info');
            }
        }
        
        function testMobile() {
            const container = document.querySelector('.test-container');
            const isMobile = container.style.maxWidth === '375px';
            
            if (isMobile) {
                container.style.maxWidth = '1200px';
                showNotification('Desktop view restored', 'info');
            } else {
                container.style.maxWidth = '375px';
                showNotification('Mobile view activated', 'info');
            }
        }
        
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 500;
                z-index: 1000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
        
        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 Widget Title Integration Test загружен');
            showNotification('Widget title integration test ready!', 'success');
        });
    </script>
</body>
</html>
