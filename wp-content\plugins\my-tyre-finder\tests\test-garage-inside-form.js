/**
 * Test script to verify Garage button functionality after moving inside form
 * for Inline (1x4) and Step-by-Step layouts
 */

console.log('🧪 Testing Garage button functionality inside form...');

// Test configuration
const testConfig = {
    layouts: ['popup-horizontal', 'stepper'],
    layoutNames: ['Inline (1x4)', 'Step-by-Step'],
    garageEnabled: true
};

// Test results storage
const testResults = {
    passed: 0,
    failed: 0,
    details: []
};

function logTest(testName, passed, details = '') {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status}: ${testName}`);
    if (details) console.log(`   ${details}`);
    
    testResults.details.push({ testName, passed, details });
    if (passed) testResults.passed++;
    else testResults.failed++;
}

function testGarageButtonPresence() {
    console.log('\n📍 Testing Garage button presence...');
    
    // Check if garage button exists
    const garageButtons = document.querySelectorAll('[data-garage-trigger]');
    logTest('Garage button exists', garageButtons.length > 0, 
        `Found ${garageButtons.length} garage button(s)`);
    
    // Check if garage button is inside form
    let insideFormCount = 0;
    garageButtons.forEach((btn, index) => {
        const form = btn.closest('form');
        if (form) {
            insideFormCount++;
            logTest(`Garage button ${index + 1} is inside form`, true, 
                `Form ID: ${form.id || 'no-id'}`);
        } else {
            logTest(`Garage button ${index + 1} is inside form`, false, 
                'Button not found inside any form element');
        }
    });
    
    return garageButtons;
}

function testGarageButtonFunctionality(garageButtons) {
    console.log('\n🔧 Testing Garage button functionality...');
    
    if (garageButtons.length === 0) {
        logTest('Garage button functionality', false, 'No garage buttons found');
        return;
    }
    
    // Test garage drawer elements
    const garageDrawer = document.getElementById('garage-drawer');
    const garageOverlay = document.getElementById('garage-overlay');
    const garageCount = document.getElementById('garage-count');
    
    logTest('Garage drawer exists', !!garageDrawer, 
        garageDrawer ? 'Found garage drawer' : 'Garage drawer not found');
    logTest('Garage overlay exists', !!garageOverlay, 
        garageOverlay ? 'Found garage overlay' : 'Garage overlay not found');
    logTest('Garage count element exists', !!garageCount, 
        garageCount ? 'Found garage count element' : 'Garage count element not found');
    
    // Test button click functionality
    const firstButton = garageButtons[0];
    if (firstButton) {
        try {
            // Simulate click event
            const clickEvent = new Event('click', { bubbles: true });
            firstButton.dispatchEvent(clickEvent);
            
            // Check if drawer opens (should have transform: translateX(0))
            setTimeout(() => {
                if (garageDrawer) {
                    const transform = window.getComputedStyle(garageDrawer).transform;
                    const isOpen = !transform.includes('translateX') || transform.includes('matrix(1, 0, 0, 1, 0, 0)');
                    logTest('Garage drawer opens on click', isOpen, 
                        `Transform: ${transform}`);
                }
            }, 100);
            
            logTest('Garage button click event', true, 'Click event dispatched successfully');
        } catch (error) {
            logTest('Garage button click event', false, `Error: ${error.message}`);
        }
    }
}

function testFormSubmissionIntegrity() {
    console.log('\n📝 Testing form submission integrity...');
    
    const forms = document.querySelectorAll('form');
    forms.forEach((form, index) => {
        const garageButton = form.querySelector('[data-garage-trigger]');
        if (garageButton) {
            // Check if garage button has type="button" to prevent form submission
            const buttonType = garageButton.getAttribute('type');
            logTest(`Form ${index + 1}: Garage button type is "button"`, 
                buttonType === 'button', 
                `Button type: ${buttonType || 'not set'}`);
            
            // Check if garage button has proper event handling
            const hasDataAttribute = garageButton.hasAttribute('data-garage-trigger');
            logTest(`Form ${index + 1}: Garage button has data-garage-trigger`, 
                hasDataAttribute, 
                hasDataAttribute ? 'Attribute found' : 'Attribute missing');
        }
    });
}

function testLayoutSpecificPositioning() {
    console.log('\n📐 Testing layout-specific positioning...');
    
    // Check current layout
    const widget = document.querySelector('.wheel-fit-widget');
    if (!widget) {
        logTest('Widget container found', false, 'No widget container found');
        return;
    }
    
    // Test for inline layout (popup-horizontal)
    const inlineForm = document.getElementById('tab-by-car');
    if (inlineForm && inlineForm.classList.contains('grid')) {
        const garageButton = inlineForm.querySelector('[data-garage-trigger]');
        if (garageButton) {
            const buttonContainer = garageButton.parentElement;
            const hasRightAlignment = buttonContainer.classList.contains('justify-end');
            logTest('Inline layout: Garage button right-aligned', hasRightAlignment, 
                `Container classes: ${buttonContainer.className}`);
            
            // Check grid column span
            const hasFullWidth = buttonContainer.classList.contains('w-full');
            logTest('Inline layout: Garage button full width container', hasFullWidth, 
                `Container classes: ${buttonContainer.className}`);
        }
    }
    
    // Test for step-by-step layout (stepper)
    const stepperForm = document.getElementById('wheel-fit-form');
    if (stepperForm && stepperForm.classList.contains('space-y-6')) {
        const garageButton = stepperForm.querySelector('[data-garage-trigger]');
        if (garageButton) {
            const buttonContainer = garageButton.parentElement;
            const hasRightAlignment = buttonContainer.classList.contains('justify-end');
            logTest('Step-by-step layout: Garage button right-aligned', hasRightAlignment, 
                `Container classes: ${buttonContainer.className}`);
        }
    }
}

function runTests() {
    console.log('🚀 Starting Garage inside form tests...\n');
    
    // Run all tests
    const garageButtons = testGarageButtonPresence();
    testGarageButtonFunctionality(garageButtons);
    testFormSubmissionIntegrity();
    testLayoutSpecificPositioning();
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    if (testResults.failed > 0) {
        console.log('\n❌ Failed Tests:');
        testResults.details.filter(t => !t.passed).forEach(test => {
            console.log(`   • ${test.testName}: ${test.details}`);
        });
    }
    
    return testResults;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runTests);
} else {
    runTests();
}

// Export for manual testing
window.testGarageInsideForm = runTests;
