/** @type {import('tailwindcss').Config} */
module.exports = {
  prefix: 'wsf-',
  purge: [
    // Only admin theme panel files
    './assets/css/admin-theme-panel.src.css',
    './assets/js/admin-theme-panel.js',
    './src/admin/AppearancePage.php',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Neutral theme colors - no corporate blue
        'primary': {
          DEFAULT: '#64748b', // slate-500 - neutral primary
          'dark': '#475569',   // slate-600
        },
        'primary-dark': '#475569',

        // Standard color palette for @apply directives
        'slate': {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        'gray': {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        'red': {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        'white': '#ffffff',
        'black': '#000000',

        // Theme token system with neutral colors
        'wsf': {
          'bg': 'var(--wsf-bg, #ffffff)',
          'text': 'var(--wsf-text, #1e293b)',
          'primary': 'var(--wsf-primary, #64748b)',
          'border': 'var(--wsf-border, #e2e8f0)',
          'hover': 'var(--wsf-hover, #f1f5f9)',
          'secondary': 'var(--wsf-secondary, #94a3b8)',
          'accent': 'var(--wsf-accent, #64748b)',
          'accent-neutral': 'var(--wsf-accent-neutral, #64748b)',
          'muted': 'var(--wsf-muted, #94a3b8)',
          'success': 'var(--wsf-success, #10b981)',
          'warning': 'var(--wsf-warning, #f59e0b)',
          'error': 'var(--wsf-error, #ef4444)',
          'surface': 'var(--wsf-surface, #f8fafc)',
          'surface-hover': 'var(--wsf-surface-hover, #f1f5f9)',
          'border-light': 'var(--wsf-border-light, #f1f5f9)',
          'border-focus': 'var(--wsf-border-focus, #94a3b8)',
          'text-primary': 'var(--wsf-text-primary, #1e293b)',
          'text-secondary': 'var(--wsf-text-secondary, #475569)',
          'text-muted': 'var(--wsf-text-muted, #94a3b8)',
          'text-inverse': 'var(--wsf-text-inverse, #ffffff)',
        }
      },
      spacing: {
        'wsf-xs': 'var(--wsf-spacing-xs, 0.25rem)',
        'wsf-sm': 'var(--wsf-spacing-sm, 0.5rem)',
        'wsf-md': 'var(--wsf-spacing-md, 1rem)',
        'wsf-lg': 'var(--wsf-spacing-lg, 1.5rem)',
        'wsf-xl': 'var(--wsf-spacing-xl, 2rem)',
      },
      borderRadius: {
        'wsf': 'var(--wsf-radius, 0.375rem)',
        'wsf-lg': 'var(--wsf-radius-lg, 0.5rem)',
      },
      boxShadow: {
        'wsf': '0 1px 3px var(--wsf-shadow, rgba(0, 0, 0, 0.1))',
        'wsf-hover': '0 4px 6px var(--wsf-shadow-hover, rgba(0, 0, 0, 0.1))',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
