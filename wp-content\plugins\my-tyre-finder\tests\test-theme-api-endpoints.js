/**
 * Test script for Theme API endpoints
 * Run this in browser console on the Appearance admin page
 */

(function() {
    'use strict';

    console.log('=== Theme API Endpoints Test ===');

    // Check if wpApiSettings is available
    if (typeof wpApiSettings === 'undefined') {
        console.error('❌ wpApiSettings not found - API endpoints may not be properly configured');
        return;
    }

    console.log('✅ wpApiSettings found:', wpApiSettings);

    const apiBase = wpApiSettings.root + 'wheel-size/v1/themes';
    const nonce = wpApiSettings.nonce;

    // Test 1: Get all themes
    async function testGetThemes() {
        console.log('\n--- Test 1: GET /themes ---');
        try {
            const response = await fetch(apiBase, {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('✅ GET /themes successful:', data);
            
            if (data.themes && typeof data.themes === 'object') {
                console.log('✅ Themes data structure is valid');
                console.log('Available themes:', Object.keys(data.themes));
            } else {
                console.warn('⚠️ Themes data structure unexpected');
            }

            if (data.active_theme) {
                console.log('✅ Active theme:', data.active_theme);
            } else {
                console.warn('⚠️ No active theme set');
            }

            return data;
        } catch (error) {
            console.error('❌ GET /themes failed:', error);
            return null;
        }
    }

    // Test 2: Get active theme
    async function testGetActiveTheme() {
        console.log('\n--- Test 2: GET /themes/active ---');
        try {
            const response = await fetch(apiBase + '/active', {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('✅ GET /themes/active successful:', data);
            return data;
        } catch (error) {
            console.error('❌ GET /themes/active failed:', error);
            return null;
        }
    }

    // Test 3: Get specific theme
    async function testGetSpecificTheme(slug = 'light') {
        console.log(`\n--- Test 3: GET /themes/${slug} ---`);
        try {
            const response = await fetch(apiBase + '/' + slug, {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`✅ GET /themes/${slug} successful:`, data);
            return data;
        } catch (error) {
            console.error(`❌ GET /themes/${slug} failed:`, error);
            return null;
        }
    }

    // Test 4: Set active theme
    async function testSetActiveTheme(slug = 'light') {
        console.log(`\n--- Test 4: PUT /themes/active (set to ${slug}) ---`);
        try {
            const response = await fetch(apiBase + '/active', {
                method: 'PUT',
                headers: {
                    'X-WP-Nonce': nonce,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ slug: slug })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`✅ PUT /themes/active (${slug}) successful:`, data);
            return data;
        } catch (error) {
            console.error(`❌ PUT /themes/active (${slug}) failed:`, error);
            return null;
        }
    }

    // Test 5: Check ThemeManager class availability
    function testThemeManagerAvailability() {
        console.log('\n--- Test 5: ThemeManager Class Availability ---');
        
        // Check if window.wsfActiveTheme is available
        if (typeof window.wsfActiveTheme !== 'undefined') {
            console.log('✅ window.wsfActiveTheme found:', window.wsfActiveTheme);
        } else {
            console.warn('⚠️ window.wsfActiveTheme not found');
        }

        // Check if WSF_THEME_PRESETS flag is set
        if (typeof window.WSF_THEME_PRESETS !== 'undefined') {
            console.log('✅ WSF_THEME_PRESETS flag found:', window.WSF_THEME_PRESETS);
        } else {
            console.warn('⚠️ WSF_THEME_PRESETS flag not found');
        }
    }

    // Test 6: Check theme panel elements
    function testThemePanelElements() {
        console.log('\n--- Test 6: Theme Panel Elements ---');
        
        const panel = document.querySelector('.wsf-theme-panel');
        if (panel) {
            console.log('✅ Theme panel element found');
        } else {
            console.warn('⚠️ Theme panel element not found');
        }

        const previewContainer = document.getElementById('widget-preview');
        if (previewContainer) {
            console.log('✅ Preview container found');
            
            const widget = previewContainer.querySelector('.wheel-fit-widget, .wsf-finder-widget, [data-wsf-theme]');
            if (widget) {
                console.log('✅ Widget element found in preview');
            } else {
                console.warn('⚠️ Widget element not found in preview');
            }
        } else {
            console.warn('⚠️ Preview container not found');
        }
    }

    // Run all tests
    async function runAllTests() {
        console.log('Starting comprehensive theme API tests...\n');

        // Synchronous tests first
        testThemeManagerAvailability();
        testThemePanelElements();

        // Asynchronous API tests
        const themesData = await testGetThemes();
        await testGetActiveTheme();
        await testGetSpecificTheme('light');
        await testGetSpecificTheme('dark');
        
        // Test setting active theme if we have themes
        if (themesData && themesData.themes) {
            const availableThemes = Object.keys(themesData.themes);
            if (availableThemes.length > 0) {
                await testSetActiveTheme(availableThemes[0]);
            }
        }

        console.log('\n=== Theme API Tests Complete ===');
    }

    // Export functions to window for manual testing
    window.themeApiTests = {
        runAllTests,
        testGetThemes,
        testGetActiveTheme,
        testGetSpecificTheme,
        testSetActiveTheme,
        testThemeManagerAvailability,
        testThemePanelElements
    };

    // Auto-run tests
    runAllTests();

})();
