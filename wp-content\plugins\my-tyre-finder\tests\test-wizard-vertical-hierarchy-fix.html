<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wizard Vertical Hierarchy Fix Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .comparison-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            height: 600px;
            overflow-y: auto;
        }
        
        .before-section {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .after-section {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .widget-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        /* Wizard styles simulation */
        .wheel-fit-widget {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }
        
        /* BEFORE: Broken horizontal layout */
        .wizard-step.broken {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .wizard-step.broken h2 {
            flex: 0 0 auto;
            margin: 0;
            font-size: 20px;
        }
        
        .wizard-step.broken nav {
            flex: 1 1 auto;
            margin: 0;
            text-align: center;
            font-size: 12px;
        }
        
        .wizard-step.broken p {
            flex: 0 0 auto;
            margin: 0;
            font-size: 11px;
        }
        
        .wizard-step.broken .search-wrapper {
            flex: 1 1 100%;
            margin: 5px 0;
        }
        
        /* AFTER: Proper vertical hierarchy */
        .wizard-step.fixed {
            display: block;
        }
        
        .wizard-step.fixed h2 {
            display: block;
            margin-bottom: 20px;
            font-size: 28px;
            font-weight: 700;
            line-height: 1.2;
            color: #2563eb;
        }
        
        .wizard-step.fixed nav {
            display: block;
            margin-bottom: 12px;
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
        }
        
        .wizard-step.fixed p {
            display: block;
            margin-bottom: 24px;
            font-size: 13px;
            color: #9ca3af;
            font-weight: 400;
        }
        
        .wizard-step.fixed .search-wrapper {
            display: block;
            margin-bottom: 24px;
            max-width: 400px;
        }
        
        .search-input {
            width: 100%;
            height: 48px;
            padding: 12px 48px 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s ease;
        }
        
        .search-input:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }
        
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .model-item {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 16px;
            text-align: center;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .model-item:hover {
            background: #e5e7eb;
            border-color: #2563eb;
        }
        
        .issue-highlight {
            background: #fef2f2;
            border: 2px dashed #ef4444;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
        }
        
        .fix-highlight {
            background: #f0fdf4;
            border: 2px dashed #10b981;
            border-radius: 4px;
            padding: 8px;
            margin: 4px 0;
        }
        
        .hierarchy-flow {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .hierarchy-flow h4 {
            margin: 0 0 12px 0;
            color: #1d4ed8;
            font-size: 16px;
        }
        
        .hierarchy-flow ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .hierarchy-flow li {
            margin-bottom: 8px;
            color: #1e40af;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: #1f2937; margin-bottom: 10px;">📐 Wizard Vertical Hierarchy Fix Test</h1>
            <p style="color: #6b7280; font-size: 18px;">Исправление горизонтального расположения элементов</p>
        </header>

        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h3 style="margin-top: 0; color: #92400e;">🐛 Анализ проблемы иерархии</h3>
            <p><strong>Выявленная проблема:</strong></p>
            <ul>
                <li>❌ <strong>Горизонтальное расположение:</strong> Элементы выстроены в линию вместо вертикальной иерархии</li>
                <li>❌ <strong>Нарушена логика:</strong> "Choose a model" → Models count → Breadcrumbs → Search</li>
                <li>❌ <strong>Плохая читаемость:</strong> Сложно воспринимать структуру</li>
                <li>❌ <strong>Потерянный заголовок:</strong> Заголовок теряется среди других элементов</li>
                <li>❌ <strong>Нарушен визуальный ритм:</strong> Поле поиска "вбито" посередине</li>
            </ul>
            
            <p><strong>Источник проблемы:</strong></p>
            <ul>
                <li>🔍 <code>finder-wizard.twig</code> - использует <code>flex items-center justify-between</code></li>
                <li>🔍 <code>live-preview-width-fix.css</code> - принудительные flex правила</li>
                <li>🔍 CSS конфликты между горизонтальными и вертикальными лейаутами</li>
            </ul>
        </div>

        <div class="hierarchy-flow">
            <h4>✅ Правильная вертикальная иерархия (сверху вниз):</h4>
            <ol>
                <li><strong>Заголовок шага</strong> — "Choose a model" (крупно, заметно)</li>
                <li><strong>Breadcrumbs</strong> — "Aion" (контекст выбора)</li>
                <li><strong>Метка счетчика</strong> — "Models count: 14" (вторичная информация)</li>
                <li><strong>Поле поиска</strong> — Search input (инструмент фильтрации)</li>
                <li><strong>Основной контент</strong> — Кнопки с моделями (главное содержимое)</li>
            </ol>
        </div>

        <div class="comparison-grid">
            <!-- Before -->
            <div class="comparison-section before-section">
                <h2 style="color: #dc2626; margin-top: 0;">❌ До исправления</h2>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <div class="wizard-step broken issue-highlight">
                            <h2>Choose a model</h2>
                            <nav>Aion</nav>
                            <p>Models count: 14</p>
                            <div class="search-wrapper">
                                <input type="text" class="search-input" placeholder="Search models...">
                            </div>
                        </div>
                        <div class="models-grid">
                            <div class="model-item">Hyper HT</div>
                            <div class="model-item">Hyper SSR</div>
                            <div class="model-item">S Plus</div>
                            <div class="model-item">Y Plus</div>
                            <div class="model-item">Pro</div>
                            <div class="model-item">Sport</div>
                        </div>
                    </div>
                </div>
                <p style="color: #7f1d1d; margin-top: 15px; font-size: 14px;">
                    <strong>Проблемы:</strong> Элементы расположены горизонтально, нарушена логическая иерархия
                </p>
            </div>

            <!-- After -->
            <div class="comparison-section after-section">
                <h2 style="color: #059669; margin-top: 0;">✅ После исправления</h2>
                <div class="widget-preview">
                    <div class="wheel-fit-widget">
                        <div class="wizard-step fixed fix-highlight">
                            <h2>Choose a model</h2>
                            <nav>Aion</nav>
                            <p>Models count: 14</p>
                            <div class="search-wrapper">
                                <input type="text" class="search-input" placeholder="Search models...">
                            </div>
                        </div>
                        <div class="models-grid">
                            <div class="model-item">Hyper HT</div>
                            <div class="model-item">Hyper SSR</div>
                            <div class="model-item">S Plus</div>
                            <div class="model-item">Y Plus</div>
                            <div class="model-item">Pro</div>
                            <div class="model-item">Sport</div>
                        </div>
                    </div>
                </div>
                <p style="color: #065f46; margin-top: 15px; font-size: 14px;">
                    <strong>Решение:</strong> Правильная вертикальная иерархия с логичным порядком элементов
                </p>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f0fdf4; border: 1px solid #16a34a; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #15803d;">✅ Выполненные исправления</h3>
            
            <p><strong>1. Исправлена структура в finder-wizard.twig:</strong></p>
            <ul>
                <li>✅ Убран <code>flex items-center justify-between</code></li>
                <li>✅ Добавлены комментарии с нумерацией элементов</li>
                <li>✅ Правильные отступы между элементами</li>
                <li>✅ Логичная последовательность: заголовок → breadcrumbs → счетчик → поиск → контент</li>
            </ul>
            
            <p><strong>2. Обновлены CSS правила в live-preview-width-fix.css:</strong></p>
            <ul>
                <li>✅ Убраны принудительные flex правила</li>
                <li>✅ Добавлено <code>display: block</code> для wizard шагов</li>
                <li>✅ Специальные правила для вертикальной иерархии</li>
                <li>✅ Правильные отступы для каждого типа элемента</li>
            </ul>
            
            <p><strong>3. Защита от CSS конфликтов:</strong></p>
            <ul>
                <li>✅ Высокая специфичность селекторов</li>
                <li>✅ Переопределение проблемных flex правил</li>
                <li>✅ Сохранение grid лейаутов для контента</li>
                <li>✅ Исключения для элементов, которые должны оставаться flex</li>
            </ul>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #eff6ff; border: 1px solid #3b82f6; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1d4ed8;">🔧 Для тестирования</h3>
            <p>Чтобы проверить исправления:</p>
            <ol>
                <li>Перейдите в админку WordPress → Wheel-Size → Appearance</li>
                <li>Установите <strong>Form Layout: Wizard</strong></li>
                <li>В Live Preview пройдите до шага "Select Model"</li>
                <li>Проверьте порядок элементов сверху вниз:
                    <ul>
                        <li>1. Заголовок "Choose a model"</li>
                        <li>2. Breadcrumbs (например, "Aion")</li>
                        <li>3. Счетчик "Models count: X"</li>
                        <li>4. Поле поиска</li>
                        <li>5. Кнопки с моделями</li>
                    </ul>
                </li>
                <li>Убедитесь, что элементы НЕ расположены в одной горизонтальной линии</li>
            </ol>
            
            <p><strong>Ожидаемый результат:</strong></p>
            <ul>
                <li>✅ Четкая вертикальная иерархия</li>
                <li>✅ Заголовок выделяется и хорошо читается</li>
                <li>✅ Логичный порядок элементов</li>
                <li>✅ Правильные отступы между блоками</li>
                <li>✅ Поле поиска на своем месте</li>
                <li>✅ Профессиональный внешний вид</li>
            </ul>
        </div>
    </div>
</body>
</html>
