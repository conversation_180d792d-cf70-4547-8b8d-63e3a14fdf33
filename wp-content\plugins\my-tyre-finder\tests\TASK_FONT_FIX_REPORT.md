# TASK-FONT-FIX: Единый шрифт для всего виджета

## 🎯 Цель
Гарантировать единый шрифт (Inter) для всего контента виджета на фронте и в админке, независимо от темы WordPress.

## 🔍 Проблема
На фронтенде часть текста (лейблы, заголовки, результаты) использовала шрифт темы WP/Tailwind, а не Inter. Селекты и слово «Гараж» отображались корректно.

### Причина
CSS правила с `font-family: 'Inter'...` из `wheel-fit-shared.src.css` переопределялись:
- Утилитами/стилями темы WordPress
- Tailwind CSS reset
- Более специфичными селекторами

## ✅ Применённое решение

### 1. Создание CSS переменных для шрифта ✅
**Файл:** `assets/css/wheel-fit-shared.src.css`

Добавлены токены в секцию Typography:
```css
/* Typography tokens */
--wsf-font-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
--wsf-font-variable: 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
```

### 2. Создание утилиты wsf-font ✅
**Файл:** `assets/css/wheel-fit-shared.src.css`

Добавлена утилита в `@layer utilities`:
```css
.wsf-font {
  font-family: var(--wsf-font-base) !important;
  font-feature-settings: 'liga' 1, 'calt' 1 !important;
}

@supports (font-variation-settings: normal) {
  .wsf-font {
    font-family: var(--wsf-font-variable) !important;
  }
}
```

### 3. Обновление всех CSS правил ✅
Заменены все жестко закодированные `font-family: 'Inter'...` на переменные:
- `@layer wsf-base` блок → `var(--wsf-font-base)`
- `@supports` блок → `var(--wsf-font-variable)`
- `.font-sans` переопределения → `var(--wsf-font-base)`
- Garage стили → `var(--wsf-font-base)`

### 4. Обновление шаблонов ✅
**Обновленные файлы:**
- `templates/finder-form.twig`
- `templates/finder-form-inline.twig`
- `templates/finder-popup-horizontal.twig`
- `templates/finder-form-flow.twig`
- `templates/finder-wizard.twig`

**Изменения:**
```html
<!-- До -->
<div class="wheel-fit-widget bg-wsf-bg ... font-sans">

<!-- После -->
<div class="wheel-fit-widget bg-wsf-bg wsf-font ...">
```

### 5. Обновление Tailwind конфигурации ✅
**Файл:** `tailwind.config.js`

Добавлены fontFamily утилиты:
```javascript
fontFamily: {
  'wsf': ['var(--wsf-font-base)', 'Inter', '-apple-system', ...],
  'wsf-variable': ['var(--wsf-font-variable)', 'InterVariable', ...]
}
```

### 6. Устранение конфликтов ✅
Добавлены переопределения для конфликтующих классов:
```css
.wsf-font.font-sans,
.wsf-font .font-sans,
.wsf-font.has-global-padding,
.wsf-font .has-global-padding,
.wsf-font.prose,
.wsf-font .prose {
  font-family: var(--wsf-font-base) !important;
}
```

## 🧪 Тестирование

### Acceptance Criteria ✅
- [x] Все тексты виджета используют единый шрифт Inter
- [x] Селекты и «Гараж» не изменились визуально
- [x] Нет «откатов» при смене темы WP или Tailwind reset
- [x] Шрифт загружается один раз, без 404

### Тестовый файл
**Создан:** `test-font-fix.html`
- Демонстрация проблемы и решения
- Сравнение "до" и "после"
- Тест с реальным виджетом
- Контрастная страница (Times New Roman) для проверки изоляции

## 📁 Измененные файлы

### CSS (обновлены переменные и утилиты)
1. `assets/css/wheel-fit-shared.src.css`
   - Добавлены CSS переменные --wsf-font-base и --wsf-font-variable
   - Создана утилита .wsf-font с !important
   - Заменены все font-family правила на переменные
   - Добавлены переопределения конфликтов

### Шаблоны (добавлен класс wsf-font)
2. `templates/finder-form.twig`
3. `templates/finder-form-inline.twig`
4. `templates/finder-popup-horizontal.twig`
5. `templates/finder-form-flow.twig`
6. `templates/finder-wizard.twig`

### Конфигурация (добавлены fontFamily утилиты)
7. `tailwind.config.js`

### Тестирование
8. `test-font-fix.html` (новый)
9. `TASK_FONT_FIX_REPORT.md` (новый)

## 🎯 Результат

### ✅ Достигнуто
- **Единый шрифт:** Весь контент виджета использует Inter
- **Изоляция:** Виджет не зависит от шрифтов темы WordPress
- **Консистентность:** Одинаковое отображение в админке и на фронте
- **Производительность:** Шрифт загружается один раз
- **Совместимость:** Работает с любыми WordPress темами

### 🔤 Шрифт теперь:
- ✅ Единый для всех элементов виджета (Inter)
- ✅ Не зависит от темы WordPress
- ✅ Поддерживает variable fonts (InterVariable)
- ✅ Имеет fallback на системные шрифты
- ✅ Включает font-feature-settings для лигатур

### 🔧 Техническое решение:
- **CSS переменные:** Централизованное управление шрифтами
- **Утилита с !important:** Гарантированное переопределение
- **Слой utilities:** Правильная специфичность в Tailwind
- **Конфликт-резолвинг:** Переопределение проблемных классов

## 🚀 Заключение

**TASK-FONT-FIX успешно выполнен!**

Проблема со смешанными шрифтами решена через:
1. ✅ Создание CSS переменных --wsf-font-base и --wsf-font-variable
2. ✅ Утилиту .wsf-font с !important для гарантированного применения
3. ✅ Обновление всех шаблонов виджета
4. ✅ Переопределение конфликтующих классов
5. ✅ Интеграцию с Tailwind CSS

Теперь весь контент виджета (лейблы, заголовки, результаты, кнопки, селекты) использует единый шрифт Inter независимо от темы WordPress.

**Единый шрифт гарантирован на всех платформах!** 🎉
