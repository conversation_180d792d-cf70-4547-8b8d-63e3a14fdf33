{# Wheel-Fit Form Template - Premium Redesign #}
{% set is_stepper = (form_layout ?? 'stepper') == 'stepper' %}

{% if is_stepper %}
  {% set has_vehicle = true %}
  {% set has_size    = false %}
{% else %}
  {% set has_vehicle = show_by_vehicle %}
  {% set has_size    = show_by_size %}
{% endif %}

{% set widget_disabled = (not is_stepper) and (not has_vehicle and not has_size) %}

{% if not widget_disabled %}
<div class="wheel-fit-widget bg-wsf-bg wsf-root_font max-w-4xl mx-auto rounded-lg shadow-lg{% if garage_enabled %} garage-enabled{% endif %}" data-flow-order="{{ flow_order_json|e }}">
    {# Main Widget Title - now properly inside the themed container #}
    <div class="wsf-widget__header p-6 md:p-8 pb-4 md:pb-6">
        <h1 class="wsf-widget__title text-center text-2xl md:text-3xl font-extrabold tracking-tight text-wsf-text m-0" data-i18n="widget_title">{{ widget_title|e }}</h1>
    </div>

    {# Widget Content - with consistent padding #}
    <div class="wsf-widget__content px-6 md:px-8 pb-6 md:pb-8">
        {# Stepper Progress Indicator #}
        <div class="flex items-center gap-3 mb-8 font-semibold">
        <div class="flex-1">
            <div class="flex items-center gap-2 md:gap-4">
            {% set order = flow_order is not null ? flow_order : ['make', 'model', 'year', 'mod'] %}
            {% set label_keys = {
              'make': 'label_make',
              'model': 'label_model',
              'year': 'label_year',
              'mod':  'label_mods'
            }%}
            {% set label_fallback = {
              'make':'Make',
              'model':'Model',
              'year':'Year',
              'mod':'Modification'
            }%}
            {% for field in order %}
            <div class="flex items-center gap-2">
                    <div id="step-indicator-{{ loop.index }}" class="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold transition-colors">{{ loop.index }}</div>
                    <span id="step-text-{{ loop.index }}" class="text-xs md:text-sm font-semibold transition-colors" data-i18n="{{ label_keys[field] }}">{{ label_fallback[field] }}</span>
            </div>
                {% if not loop.last %}<div id="progress-{{ loop.index }}" class="h-px flex-1 transition-colors"></div>{% endif %}
            {% endfor %}
            </div>
        </div>
        {# Garage button moved below selects #}
        {% if garage_enabled %}
        <span id="garage-btn-placeholder" class="hidden"></span>
        {% endif %}
    </div>

    <div class="wsf-form-wrapper">
        <form id="wheel-fit-form" class="space-y-6">
            {# Dynamic Field Blocks based on flow_order #}
            {% set order = flow_order is not null ? flow_order : ['make', 'model', 'year', 'mod'] %}
            {% for field in order %}
                {% include 'fields/' ~ field ~ '.twig' %}
            {% endfor %}
        </form>
    </div>

    {# Results container for "By Tire Size" search #}
    <div id="tire-search-results" class="mt-8"></div>

    {# Results Section #}
    <div class="max-w-4xl mx-auto">
    <section id="search-results" class="hidden mt-12 bg-wsf-bg shadow-lg rounded-xl p-6 md:p-8 transform transition-all duration-300 ease-in-out">
        <h2 class="text-lg font-semibold text-gray-900 mb-1" data-i18n="section_results">Search Results</h2>
        <p id="vehicle-label" class="text-base font-medium text-gray-900"></p>
        <div id="selected-modification-info" class="mt-1"></div>
        
        <div class="border-t border-wsf-border my-4"></div>
        
        {# Factory block #}
        <div id="factory-section" class="mb-8 hidden">
            <h3 class="text-sm font-bold text-gray-900 mb-3" data-i18n="section_factory">Factory Sizes</h3>
            <div id="factory-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
        </div>

        {# Optional block #}
        {% if not enable_oe_filter %}
        <div id="optional-section" class="mb-8 hidden">
            <h3 class="text-sm font-bold text-gray-900 mb-3" data-i18n="section_optional">Optional Sizes</h3>
            <div id="optional-grid" class="grid gap-6 grid-cols-[repeat(auto-fill,minmax(150px,1fr))] auto-rows-fr"></div>
        </div>
        {% endif %}

        {# No Results Message #}
        <div id="no-results" class="hidden text-center py-8">
            <h3 class="text-lg font-medium text-gray-900 mb-2" data-i18n="text_no_results_header">Sizes not found</h3>
            <p class="text-wsf-muted" data-i18n="text_no_results_body">Try selecting another modification or check your selection.</p>
        </div>
    </section>
    </div>

    {# Saved Searches (localStorage) #}
    <div id="saved-searches" class="hidden mt-12">
        <h4 class="mb-4 flex items-center gap-2 text-sm font-semibold text-wsf-muted">
            <i data-lucide="history" class="w-4 h-4 text-wsf-muted" aria-hidden="true"></i>
            Your recent searches
        </h4>
        <ul id="history-list" class="space-y-3"></ul>
        <p id="history-empty" class="hidden text-sm text-wsf-muted">No recent searches yet</p>
    </div>
    </div> {# Close .wsf-widget__content #}
</div> {# Close .wheel-fit-widget #}

<style>
.wheel-fit-widget, .wheel-fit-widget button, .wheel-fit-widget input, .wheel-fit-widget select, .wheel-fit-widget option, #garage-drawer, #garage-drawer *, #garage-overlay, #garage-toast {font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif}
:root{--wsf-primary:{{ primary_color|e('css') }};--ws-control-height:44px}
.wheel-fit-widget select{width:100%;height:var(--ws-control-height);font:500 14px/1.3 'Inter',sans-serif;padding:0 12px;border:1px solid var(--wsf-input-border);border-radius:6px;background:var(--wsf-input-bg);color:var(--wsf-input-text)}
.wheel-fit-widget select option[value=""]{font-style:normal;color:var(--wsf-input-placeholder)}
.wheel-fit-widget button[type="submit"] {
    font-size: 16px;
    font-weight: 600;
    padding: .9rem 2.5rem;
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
.animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
}
#garage-items-list::before {
  content: '';
  position: sticky;
  top: 0;
  display: block;
  height: 16px;
  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
  z-index: 1;
}

/* Prevent WP-admin flex/grid from forcing 50% width */
.wheel-fit-widget .flex-col,
.wheel-fit-widget .relative {
  min-width: 0 !important;
}
.wheel-fit-widget select {
  max-width: 100% !important;
}
</style>

{% endif %}  {# closes outer widget_disabled block #}

{# Placeholder & fallback trigger when widget disabled #}
{% if widget_disabled %}
    <p class="text-center text-wsf-muted italic mt-8">Widget disabled in Appearance settings.</p>
    {% if garage_enabled %}
    <div class="flex justify-end my-6">
      <button type="button" data-garage-trigger class="inline-flex items-center gap-1 text-sm text-wsf-text px-3 py-1.5 rounded-md hover:bg-wsf-surface hover:text-wsf-primary transition cursor-pointer select-none">
        <i data-lucide="car" class="w-5 h-5"></i>
        <span data-i18n="label_garage">Garage</span>
        <span id="garage-count" class="wsf-garage-count-badge hidden"></span>
      </button>
    </div>
    {% endif %}
{% endif %}

{# Garage overlay / drawer / toast always included if feature enabled #}
{% if garage_enabled %}
<script src="{{ plugin_url }}/assets/js/garage.js"></script>
<!-- overlay, drawer, toast keep same markup -->
<!-- 2. Overlay -->
<div id="garage-overlay" class="hidden fixed inset-0 z-40 bg-black/25 backdrop-blur-md transition-opacity cursor-pointer"></div>
<!-- 3. Drawer -->
<aside id="garage-drawer" class="fixed right-0 top-0 h-full w-full max-w-sm bg-wsf-surface shadow-2xl transform translate-x-full transition-transform duration-300 z-50 flex flex-col" style="top: var(--wp-admin--admin-bar--height, 0); height: calc(100% - var(--wp-admin--admin-bar--height, 0));">
  <header class="flex items-center justify-between p-4 border-b border-wsf-border border-wsf-border border-wsf-border border-wsf-border border-wsf-border">
    <h2 class="text-xl font-bold text-wsf-text" data-i18n="garage_title">My Garage</h2>
    <button id="garage-close-btn" aria-label="Close" class="text-wsf-muted hover:text-wsf-muted">
      <svg viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>
    </button>
  </header>
  <ul id="garage-items-list" class="flex-1 min-h-0 overflow-y-auto p-4 space-y-4"></ul>
  <footer id="garage-footer" class="flex-shrink-0 p-4 flex justify-end mt-6">
    <button id="garage-clear-all" class="btn-secondary">
      <i data-lucide="trash-2"></i>
      <span data-i18n="garage_clear_all">Clear&nbsp;all</span>
    </button>
  </footer>
</aside>
<!-- 4. Toast -->
<div id="garage-toast" class="fixed left-1/2 -translate-x-1/2 bottom-6 bg-wsf-primary text-white text-sm font-medium px-4 py-2 rounded-lg shadow-lg transition opacity-0 translate-y-4 z-50">
  <span data-i18n="garage_saved_notification">Saved to Garage!</span>
</div>
{% endif %} 