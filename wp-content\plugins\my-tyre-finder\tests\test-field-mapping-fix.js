// Test script for API field mapping fix
console.log('=== Testing API Field Mapping Fix ===');

// Test 1: Check if widget is available
console.log('1. Widget availability check:');
if (!window.wheelFitWidget) {
    console.log('   ❌ Widget not found');
    return;
}
console.log('   ✅ Widget found');

const widget = window.wheelFitWidget;

// Helper function to create test select element
function createTestSelect(id) {
    let select = document.getElementById(id);
    if (!select) {
        select = document.createElement('select');
        select.id = id;
        document.body.appendChild(select);
    }
    return select;
}

// Helper function to check select options
function checkSelectOptions(selectId, testName, expectedCount) {
    const select = document.getElementById(selectId);
    if (!select) {
        console.log(`   ❌ ${testName}: Select not found`);
        return;
    }
    
    const options = [...select.options];
    const nonEmptyOptions = options.filter(o => o.text.trim() !== '');
    const emptyOptions = options.filter(o => o.text.trim() === '');
    
    console.log(`   ${testName}:`);
    console.log(`     Total options: ${options.length}`);
    console.log(`     Non-empty options: ${nonEmptyOptions.length}`);
    console.log(`     Empty options: ${emptyOptions.length}`);
    console.log(`     Expected non-empty: ${expectedCount}`);
    
    if (emptyOptions.length > 1) { // placeholder is allowed
        console.log(`     ❌ Found ${emptyOptions.length} empty options (should be max 1 placeholder)`);
    } else {
        console.log(`     ✅ Empty options count is acceptable`);
    }
    
    // Show option texts
    nonEmptyOptions.forEach((option, index) => {
        console.log(`     Option ${index + 1}: "${option.text}" (value: "${option.value}")`);
    });
    
    return { nonEmptyOptions, emptyOptions };
}

// Test 2: Test generations with different field names
console.log('2. Testing generations with different field names:');

createTestSelect('wf-generation');

// Test with 'name' field
console.log('   Testing with name field:');
const generationsWithName = [
    { name: 'Generation 1', slug: 'gen-1' },
    { name: 'Generation 2', slug: 'gen-2' }
];
widget.populateGenerations(generationsWithName);
checkSelectOptions('wf-generation', 'Generations with name', 2);

// Test with 'title' field
console.log('   Testing with title field:');
const generationsWithTitle = [
    { title: 'EV 570 HP', slug: 'ev-570-hp' }
];
widget.populateGenerations(generationsWithTitle);
const titleResult = checkSelectOptions('wf-generation', 'Generations with title', 1);

// Test with 'range' field
console.log('   Testing with range field:');
const generationsWithRange = [
    { range: '2020-2023', slug: 'range-2020-2023' }
];
widget.populateGenerations(generationsWithRange);
checkSelectOptions('wf-generation', 'Generations with range', 1);

// Test with 'year_range' field
console.log('   Testing with year_range field:');
const generationsWithYearRange = [
    { year_range: '2018-2022', slug: 'yr-2018-2022' }
];
widget.populateGenerations(generationsWithYearRange);
checkSelectOptions('wf-generation', 'Generations with year_range', 1);

// Test with missing all fields (fallback to slug)
console.log('   Testing with only slug field:');
const generationsWithOnlySlug = [
    { slug: 'mysterious-generation' }
];
widget.populateGenerations(generationsWithOnlySlug);
checkSelectOptions('wf-generation', 'Generations with only slug', 1);

// Test 3: Test models with different field names
console.log('3. Testing models with different field names:');

createTestSelect('wf-model');

// Test with 'title' field
console.log('   Testing models with title field:');
const modelsWithTitle = [
    { title: 'Model X', slug: 'model-x' }
];
widget.populateModels(modelsWithTitle);
checkSelectOptions('wf-model', 'Models with title', 1);

// Test 4: Test years with different field names
console.log('4. Testing years with different field names:');

createTestSelect('wf-year');

// Test with 'year' field
console.log('   Testing years with year field:');
const yearsWithYear = [
    { year: '2023', slug: '2023' }
];
widget.populateYears(yearsWithYear);
checkSelectOptions('wf-year', 'Years with year field', 1);

// Test with 'title' field
console.log('   Testing years with title field:');
const yearsWithTitle = [
    { title: '2022 Model Year', slug: '2022' }
];
widget.populateYears(yearsWithTitle);
checkSelectOptions('wf-year', 'Years with title field', 1);

// Test 5: Test modifications with different field names
console.log('5. Testing modifications with different field names:');

createTestSelect('wf-modification');

// Test with 'title' field
console.log('   Testing modifications with title field:');
const modificationsWithTitle = [
    { 
        title: 'Electric Motor', 
        slug: 'electric-motor',
        engine: { power: { hp: 570 }, type: 'Electric' }
    }
];
widget.populateModifications(modificationsWithTitle);
checkSelectOptions('wf-modification', 'Modifications with title', 1);

// Test 6: Test auto-selection for single items
console.log('6. Testing auto-selection for single items:');

// Test single generation auto-selection
console.log('   Testing single generation auto-selection:');
const singleGeneration = [{ title: 'Avatr 11 EV 570 HP', slug: 'avatr-11-ev-570-hp' }];
widget.populateGenerations(singleGeneration);

setTimeout(() => {
    const select = document.getElementById('wf-generation');
    const isSelected = select.value !== '';
    const selectedText = select.options[select.selectedIndex]?.text;
    
    console.log(`     Auto-selected: ${isSelected ? '✅ YES' : '❌ NO'}`);
    console.log(`     Selected text: "${selectedText}"`);
    console.log(`     Selected value: "${select.value}"`);
    
    if (isSelected && selectedText === 'Avatr 11 EV 570 HP') {
        console.log('     🎉 SUCCESS: Single generation auto-selected with correct text!');
    } else {
        console.log('     ❌ ISSUE: Auto-selection not working correctly');
    }
}, 100);

// Test 7: Real-world API response simulation
console.log('7. Real-world API response simulation:');

// Simulate problematic API response that caused empty options
const problematicApiResponse = [
    {
        // No 'name' field - this would cause empty option before fix
        title: 'EV 570 HP',
        slug: 'ev-570-hp',
        year_range: '2022-2024'
    }
];

console.log('   Simulating problematic API response...');
console.log('   Response data:', problematicApiResponse);

widget.populateGenerations(problematicApiResponse);

setTimeout(() => {
    const select = document.getElementById('wf-generation');
    const options = [...select.options];
    const nonEmptyOptions = options.filter(o => o.text.trim() !== '');
    const hasEmptyNonPlaceholder = options.some((o, i) => i > 0 && o.text.trim() === '');
    
    console.log('   Results after fix:');
    console.log(`     Total options: ${options.length}`);
    console.log(`     Non-empty options: ${nonEmptyOptions.length}`);
    console.log(`     Empty non-placeholder options: ${hasEmptyNonPlaceholder ? '❌ YES' : '✅ NO'}`);
    
    if (nonEmptyOptions.length > 0) {
        console.log(`     First option text: "${nonEmptyOptions[0].text}"`);
        console.log(`     First option value: "${nonEmptyOptions[0].value}"`);
    }
    
    if (!hasEmptyNonPlaceholder && nonEmptyOptions.length > 0) {
        console.log('     🎉 SUCCESS: No empty options, proper field mapping working!');
    } else {
        console.log('     ❌ ISSUE: Still has empty options or no options generated');
    }
}, 200);

// Test 8: Clean up
setTimeout(() => {
    console.log('8. Cleaning up test elements...');
    ['wf-model', 'wf-year', 'wf-generation', 'wf-modification'].forEach(id => {
        const element = document.getElementById(id);
        if (element && element.parentNode === document.body) {
            document.body.removeChild(element);
        }
    });
    
    console.log('\n=== Test Summary ===');
    console.log('Field mapping priority order:');
    console.log('- Generations: name → title → range → year_range → gen → slug → fallback');
    console.log('- Models: name → title → model → slug → fallback');
    console.log('- Years: name → year → title → slug → fallback');
    console.log('- Modifications: name → title → modification → slug → fallback');
    console.log('');
    console.log('Expected behavior:');
    console.log('✅ No empty options (except placeholder)');
    console.log('✅ Auto-selection for single items');
    console.log('✅ Proper text display from any field name');
    console.log('✅ Fallback to slug if all else fails');
    
}, 1000);

console.log('\n=== Field Mapping Fix Test Running ===');
