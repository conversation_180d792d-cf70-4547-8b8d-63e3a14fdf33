// Test script to verify API validation gate functionality
console.log('=== API Validation Gate Test ===');

// Test results tracking
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function logResult(test, status, message) {
    const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${test}: ${message}`);
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Test 1: Check if widget shows API configuration message when not configured
console.log('\n1. Frontend Widget Protection Test:');
const widgetContainer = document.querySelector('.wheel-fit-widget');

if (widgetContainer) {
    const isApiNotConfigured = widgetContainer.classList.contains('api-not-configured');
    const hasConfigMessage = widgetContainer.textContent.includes('Configuration Required') || 
                            widgetContainer.textContent.includes('configure API settings');
    
    if (isApiNotConfigured || hasConfigMessage) {
        logResult('Widget shows API config message', 'pass', 'Widget correctly shows configuration required message');
        
        // Check if admin link is present for admin users
        const hasAdminLink = widgetContainer.querySelector('a[href*="wheel-size"]');
        if (hasAdminLink) {
            logResult('Admin link present', 'pass', 'Configuration link available for admin users');
        } else {
            logResult('Admin link present', 'warning', 'No admin link found (may be normal for non-admin users)');
        }
    } else {
        // Widget might be functional, check if API is actually configured
        const hasFunctionalElements = widgetContainer.querySelector('#wf-make, #wf-model, #wf-year');
        if (hasFunctionalElements) {
            logResult('Widget functionality', 'warning', 'Widget appears functional - API may be configured');
        } else {
            logResult('Widget shows API config message', 'fail', 'Widget not showing config message but also not functional');
        }
    }
} else {
    logResult('Widget container exists', 'warning', 'No widget container found on this page');
}

// Test 2: Check admin menu restrictions (if in admin)
console.log('\n2. Admin Menu Restrictions Test:');
if (window.location.href.includes('/wp-admin/')) {
    const wheelSizeMenuItems = document.querySelectorAll('a[href*="wheel-size"]');
    
    if (wheelSizeMenuItems.length > 0) {
        logResult('Admin menu items found', 'pass', `Found ${wheelSizeMenuItems.length} Wheel-Size menu items`);
        
        // Check if only API Settings is available when not configured
        const apiSettingsItem = Array.from(wheelSizeMenuItems).find(item => 
            item.href.includes('page=wheel-size') && !item.href.includes('page=wheel-size-')
        );
        
        const otherItems = Array.from(wheelSizeMenuItems).filter(item => 
            item.href.includes('page=wheel-size-')
        );
        
        if (apiSettingsItem) {
            logResult('API Settings page accessible', 'pass', 'API Settings menu item found');
        } else {
            logResult('API Settings page accessible', 'fail', 'API Settings menu item not found');
        }
        
        // If API is not configured, other items should be hidden
        if (otherItems.length === 0) {
            logResult('Other admin pages hidden', 'pass', 'No other admin menu items found (API likely not configured)');
        } else {
            logResult('Other admin pages hidden', 'warning', 
                `Found ${otherItems.length} other menu items (API may be configured)`);
        }
    } else {
        logResult('Admin menu items', 'warning', 'No Wheel-Size menu items found');
    }
} else {
    logResult('Admin menu test', 'warning', 'Not in admin area - cannot test admin menu restrictions');
}

// Test 3: Check admin notices (if in admin)
console.log('\n3. Admin Notices Test:');
if (window.location.href.includes('/wp-admin/')) {
    const adminNotices = document.querySelectorAll('.notice');
    const apiNotice = Array.from(adminNotices).find(notice => 
        notice.textContent.includes('API key') || 
        notice.textContent.includes('Plugin functionality is disabled') ||
        notice.textContent.includes('configure')
    );
    
    if (apiNotice) {
        const isErrorNotice = apiNotice.classList.contains('notice-error');
        const hasConfigLink = apiNotice.querySelector('a[href*="wheel-size"]');
        
        logResult('API configuration notice shown', 'pass', 'Admin notice about API configuration found');
        logResult('Notice is error type', isErrorNotice ? 'pass' : 'warning', 
            `Notice type: ${isErrorNotice ? 'error' : 'other'}`);
        logResult('Notice has config link', hasConfigLink ? 'pass' : 'warning', 
            hasConfigLink ? 'Configuration link present' : 'No configuration link');
    } else {
        logResult('API configuration notice', 'warning', 
            'No API configuration notice found (API may be configured or not on relevant page)');
    }
} else {
    logResult('Admin notices test', 'warning', 'Not in admin area - cannot test admin notices');
}

// Test 4: Test AJAX endpoint protection
console.log('\n4. AJAX Endpoint Protection Test:');
if (typeof ajaxurl !== 'undefined') {
    // Test a wizard endpoint
    fetch(ajaxurl, {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: new URLSearchParams({
            action: 'wizard_get_makes'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success === false && data.data && data.data.includes('API key not configured')) {
            logResult('AJAX endpoint protection', 'pass', 'AJAX endpoint correctly blocked when API not configured');
        } else if (data.success === true) {
            logResult('AJAX endpoint protection', 'warning', 'AJAX endpoint returned data (API may be configured)');
        } else {
            logResult('AJAX endpoint protection', 'warning', 'AJAX endpoint response unclear: ' + JSON.stringify(data));
        }
    })
    .catch(error => {
        logResult('AJAX endpoint test', 'warning', 'AJAX test failed: ' + error.message);
    });
} else {
    logResult('AJAX endpoint test', 'warning', 'ajaxurl not available - cannot test AJAX protection');
}

// Test 5: Check API test functionality (if on API settings page)
console.log('\n5. API Test Functionality Test:');
const apiTestButton = document.getElementById('test-api-key');
const apiKeyInput = document.getElementById('api_key');

if (apiTestButton && apiKeyInput) {
    logResult('API test elements present', 'pass', 'API test button and input field found');
    
    // Check if test button is functional
    const testButtonWorks = typeof apiTestButton.onclick === 'function' || 
                           apiTestButton.getAttribute('onclick') !== null;
    
    logResult('API test button functional', testButtonWorks ? 'pass' : 'warning', 
        testButtonWorks ? 'Test button has click handler' : 'Test button may not be functional');
    
    // Check for status display elements
    const statusSpan = document.getElementById('api-test-status');
    const resultDiv = document.getElementById('api-test-result');
    
    logResult('API test feedback elements', (statusSpan && resultDiv) ? 'pass' : 'warning',
        (statusSpan && resultDiv) ? 'Status and result elements found' : 'Some feedback elements missing');
        
} else {
    logResult('API test functionality', 'warning', 'Not on API settings page or elements not found');
}

// Test 6: Check for proper error handling in shortcodes
console.log('\n6. Shortcode Error Handling Test:');
const shortcodeElements = document.querySelectorAll('[class*="wheel-fit"], [class*="tyre-finder"]');

if (shortcodeElements.length > 0) {
    let hasProperErrorHandling = false;
    
    shortcodeElements.forEach(element => {
        const hasErrorMessage = element.textContent.includes('configure') || 
                               element.textContent.includes('API') ||
                               element.textContent.includes('settings');
        const hasErrorStyling = element.style.border || element.classList.contains('api-not-configured');
        
        if (hasErrorMessage || hasErrorStyling) {
            hasProperErrorHandling = true;
        }
    });
    
    logResult('Shortcode error handling', hasProperErrorHandling ? 'pass' : 'warning',
        hasProperErrorHandling ? 'Shortcodes show proper error messages' : 'Shortcodes may not show error messages');
} else {
    logResult('Shortcode error handling', 'warning', 'No shortcode elements found on this page');
}

// Test 7: Check database flag status (if accessible)
console.log('\n7. Database Flag Status Test:');
// This would require server-side data, but we can check if any client-side indicators exist
const configIndicators = document.querySelectorAll('[data-api-configured], .api-configured, .api-not-configured');

if (configIndicators.length > 0) {
    const isConfigured = Array.from(configIndicators).some(el => 
        el.dataset.apiConfigured === 'true' || 
        el.classList.contains('api-configured')
    );
    
    const isNotConfigured = Array.from(configIndicators).some(el => 
        el.dataset.apiConfigured === 'false' || 
        el.classList.contains('api-not-configured')
    );
    
    if (isConfigured) {
        logResult('API configuration status', 'warning', 'Indicators suggest API is configured');
    } else if (isNotConfigured) {
        logResult('API configuration status', 'pass', 'Indicators suggest API is not configured');
    } else {
        logResult('API configuration status', 'warning', 'Configuration status unclear from indicators');
    }
} else {
    logResult('API configuration status', 'warning', 'No configuration status indicators found');
}

// Test 8: Check activation redirect behavior (if applicable)
console.log('\n8. Activation Redirect Test:');
const currentUrl = window.location.href;
const isApiSettingsPage = currentUrl.includes('page=wheel-size') && !currentUrl.includes('page=wheel-size-');

if (isApiSettingsPage) {
    logResult('On API settings page', 'pass', 'Currently on API settings page (may indicate redirect worked)');
} else if (currentUrl.includes('/wp-admin/')) {
    logResult('Activation redirect', 'warning', 'In admin but not on API settings page');
} else {
    logResult('Activation redirect test', 'warning', 'Not in admin area - cannot test activation redirect');
}

// Final summary
setTimeout(() => {
    console.log('\n=== API Validation Gate Test Summary ===');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⚠️ Warnings: ${testResults.warnings}`);
    
    const totalTests = testResults.passed + testResults.failed + testResults.warnings;
    const successRate = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
    
    console.log(`\n📊 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 All critical tests passed! API validation gate appears to be working correctly.');
        
        console.log('\n📋 Validation Gate Status:');
        if (testResults.warnings > testResults.passed) {
            console.log('⚠️ Many warnings suggest API may already be configured');
            console.log('🔧 To test unconfigured state:');
            console.log('   1. Go to Wheel-Size → API Settings');
            console.log('   2. Clear the API key field');
            console.log('   3. Save settings');
            console.log('   4. Refresh and run this test again');
        } else {
            console.log('✅ API validation gate is properly blocking functionality');
            console.log('✅ Frontend shows configuration messages');
            console.log('✅ Admin restrictions appear to be in place');
        }
    } else {
        console.log('\n⚠️ Some tests failed. Please review the failures above.');
    }
    
    console.log('\n🔧 Implementation Features Tested:');
    console.log('- Frontend widget protection with user-friendly messages');
    console.log('- Admin menu restrictions (API Settings only when not configured)');
    console.log('- Admin notices for configuration requirements');
    console.log('- AJAX endpoint protection');
    console.log('- API key testing functionality');
    console.log('- Shortcode error handling');
    console.log('- Configuration status indicators');
    console.log('- Activation redirect behavior');
    
}, 100);

console.log('\n=== API validation gate test initiated ===');
