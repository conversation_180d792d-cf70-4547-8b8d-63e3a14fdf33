# Live Preview Width Management System

## 🎯 Цель
Создать единую систему управления шириной элементов в Live Preview админки WordPress, обеспечивающую:
- Соответствие ширины виджета блоку результатов поиска (56rem/896px)
- Отсутствие горизонтального скролла
- Правильное отображение на всех разрешениях экрана
- Корректную обработку длинных локализованных текстов

## 📋 Источники CSS правил (до исправления)

### 1. AppearancePage.php (УДАЛЕНО)
```css
/* Конфликтующие правила - УДАЛЕНЫ */
#widget-preview .wheel-fit-widget { max-width: none !important; }
#widget-preview .step-container { max-width: none !important; }
```

### 2. live-preview-width-fix.css (ЕДИНСТВЕННЫЙ ИСТОЧНИК)
```css
/* Основные правила ширины */
#widget-preview .wheel-fit-widget { max-width: 56rem !important; }
```

### 3. wheel-fit-shared.src.css (Базовые стили)
```css
/* Общие стили для всех контекстов */
.wheel-fit-widget select { width: 100% !important; }
```

## 🏗️ Архитектура единой системы

### Уровень 1: Контейнер Live Preview
```css
#widget-preview {
  display: flex !important;
  justify-content: center !important;
  overflow-x: hidden !important; /* Предотвращение горизонтального скролла */
  width: 100% !important;
}
```

### Уровень 2: Виджет-контейнер
```css
#widget-preview .wheel-fit-widget,
#widget-preview .wsf-finder-widget {
  width: 100% !important;
  max-width: 56rem !important; /* 896px - как у блока результатов */
  margin: 0 auto !important; /* Центрирование */
}
```

### Уровень 3: Элементы формы
```css
#widget-preview select,
#widget-preview input,
#widget-preview .wsf-input {
  width: 100% !important; /* 100% от контейнера виджета */
  max-width: 100% !important;
  box-sizing: border-box !important;
}
```

### Уровень 4: Wizard элементы
```css
/* Заголовок и навигация */
#widget-preview #wizard-header {
  width: 100% !important;
  max-width: 100% !important;
}

/* Сетка брендов */
#widget-preview #wizard-makes-grid {
  width: 100% !important;
  max-width: 100% !important;
  justify-content: center !important;
}

/* Названия шагов */
#widget-preview .wizard-step-name {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-height: 2.4em !important; /* 2 строки максимум */
}
```

## 📐 Размеры и ограничения

### Основные размеры
- **Виджет-контейнер**: max-width: 56rem (896px)
- **Блок результатов**: max-width: 56rem (896px) - СООТВЕТСТВИЕ ✅
- **Элементы формы**: width: 100% от контейнера
- **Сетка брендов**: 4 колонки с gap: 1rem

### Адаптивность
```css
/* Все разрешения используют одинаковую ширину */
@media (min-width: 768px) {
  #widget-preview .wheel-fit-widget { max-width: 56rem !important; }
}

@media (min-width: 1024px) {
  #widget-preview .wheel-fit-widget { max-width: 56rem !important; }
}
```

## 🌐 Обработка локализации

### Длинные тексты
```css
/* Названия шагов */
#widget-preview .wizard-step-name {
  white-space: normal !important;
  line-height: 1.2 !important;
  max-height: 2.4em !important;
  overflow: hidden !important;
}

/* Кнопки */
#widget-preview button {
  word-break: break-word !important;
  white-space: normal !important;
  max-width: 100% !important;
}
```

### Переносы текста
- **Лейблы**: ellipsis для коротких элементов
- **Кнопки**: word-break для длинного текста
- **Названия шагов**: 2 строки максимум с overflow: hidden

## 🔧 Тестирование

### Автоматические тесты
1. **comprehensive-width-audit.js** - полный аудит ширины
2. **test-widget-results-width-match.js** - соответствие результатам поиска
3. **quick-width-test.js** - быстрая проверка

### Ручные тесты
1. Проверка в тёмной/светлой теме
2. Тестирование на разных разрешениях (768px, 1024px+)
3. Проверка длинных локализованных текстов
4. Отсутствие горизонтального скролла

### Контрольные точки
- ✅ Виджет = 896px максимум
- ✅ Результаты поиска = 896px максимум
- ✅ Нет горизонтального скролла
- ✅ Элементы центрированы
- ✅ Формы занимают 100% ширины контейнера

## 🚫 Что НЕ делать

### Избегать глобальных переопределений
```css
/* ПЛОХО - глобальное переопределение */
.w-full { width: 100% !important; }
.max-w-full { max-width: none !important; }

/* ХОРОШО - локализованное под Live Preview */
#widget-preview .w-full { width: 100% !important; }
```

### Избегать конфликтующих правил
```css
/* ПЛОХО - конфликт */
#widget-preview .widget { max-width: none !important; }
#widget-preview .widget { max-width: 56rem !important; }

/* ХОРОШО - единое правило */
#widget-preview .widget { max-width: 56rem !important; }
```

## 📁 Файловая структура

### Основные файлы
- `assets/css/live-preview-width-fix.css` - **ЕДИНСТВЕННЫЙ ИСТОЧНИК** правил ширины
- `src/admin/AppearancePage.php` - инлайновые стили удалены
- `assets/css/wheel-fit-shared.src.css` - базовые стили (не затрагивают Live Preview)

### Тестовые файлы
- `tests/comprehensive-width-audit.js` - полный аудит
- `tests/test-widget-results-width-match.js` - проверка соответствия
- `tests/quick-width-test.js` - быстрая диагностика
- `tests/test-width-preview.html` - визуальный тест

## 🔄 Процесс изменений

### При добавлении новых элементов
1. Добавить правила в `live-preview-width-fix.css`
2. Использовать селектор `#widget-preview`
3. Установить `width: 100%` и `max-width: 100%`
4. Запустить тесты

### При изменении ширины
1. Изменить только в `live-preview-width-fix.css`
2. Обновить переменную `expectedMaxWidth` в тестах
3. Проверить соответствие блоку результатов

## ✅ Результат

### До исправления
- Конфликтующие правила в 2+ файлах
- Виджет: 600-800px (переменная ширина)
- Результаты: 896px
- Горизонтальный скролл в некоторых случаях

### После исправления
- Единый источник правил: `live-preview-width-fix.css`
- Виджет: 896px (фиксированная ширина)
- Результаты: 896px
- **ПОЛНОЕ СООТВЕТСТВИЕ** ✅
- Нет горизонтального скролла ✅
- Корректная локализация ✅
