/**
 * Test script to verify garage button positioning in Inline layout
 * Run in browser console on admin appearance page
 */

console.log('📍 === INLINE GARAGE POSITION TEST ===');

const layoutSelect = document.getElementById('form_layout');
const autoSearchCheckbox = document.getElementById('auto_search_on_last_input');
const previewContainer = document.getElementById('widget-preview');

if (!layoutSelect || !autoSearchCheckbox || !previewContainer) {
    console.log('❌ Required elements not found');
    return;
}

function testInlineGaragePosition() {
    const previewWidget = previewContainer.querySelector('.wheel-fit-widget');
    if (!previewWidget) {
        console.log('   ❌ Preview widget not found');
        return;
    }

    const form = previewWidget.querySelector('form');
    const garageButtons = previewWidget.querySelectorAll('[data-garage-trigger]');
    
    console.log('\nInline Layout - Garage Position Test:');
    console.log('   Layout:', layoutSelect.value);
    console.log('   Auto-search enabled:', autoSearchCheckbox.checked);
    console.log('   Form found:', !!form);
    console.log('   Garage buttons found:', garageButtons.length);
    
    if (garageButtons.length > 0) {
        garageButtons.forEach((button, index) => {
            const container = button.parentElement;
            const isInsideForm = form && form.contains(button);
            
            console.log(`   Button ${index + 1}:`);
            console.log('     Inside form:', isInsideForm ? '❌ (should be outside)' : '✅');
            console.log('     Container classes:', container.className);
            
            // Check if button is positioned below form
            if (form && !isInsideForm) {
                const formRect = form.getBoundingClientRect();
                const buttonRect = button.getBoundingClientRect();
                const isBelow = buttonRect.top > formRect.bottom;
                
                console.log('     Positioned below form:', isBelow ? '✅' : '❌');
                console.log('     Form bottom:', Math.round(formRect.bottom));
                console.log('     Button top:', Math.round(buttonRect.top));
            }
            
            // Check right alignment
            const containerStyle = window.getComputedStyle(container);
            const isRightAligned = containerStyle.justifyContent === 'flex-end' || 
                                 container.classList.contains('justify-end');
            
            console.log('     Right aligned:', isRightAligned ? '✅' : '❌');
            
            // Check if button is in grid layout
            const formStyle = form ? window.getComputedStyle(form) : null;
            const isFormGrid = formStyle && formStyle.display === 'grid';
            
            if (isFormGrid && isInsideForm) {
                console.log('     ⚠️  Button is inside grid form - may cause layout issues');
            }
        });
    } else {
        console.log('   ❌ No garage buttons found');
    }
}

// Test current state
console.log('\n1. Current State:');
testInlineGaragePosition();

// Test with Inline layout and auto-search
console.log('\n🔄 Testing Inline + Auto-search combination...');

// Set to Inline layout
const originalLayout = layoutSelect.value;
const originalAutoSearch = autoSearchCheckbox.checked;

layoutSelect.value = 'popup-horizontal'; // Inline (1x4)
autoSearchCheckbox.checked = true;

// Trigger change events
layoutSelect.dispatchEvent(new Event('change', { bubbles: true }));
autoSearchCheckbox.dispatchEvent(new Event('change', { bubbles: true }));

setTimeout(() => {
    console.log('\n2. After setting Inline + Auto-search:');
    testInlineGaragePosition();
    
    // Test without auto-search
    autoSearchCheckbox.checked = false;
    autoSearchCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
    
    setTimeout(() => {
        console.log('\n3. Inline without auto-search:');
        testInlineGaragePosition();
        
        // Restore original settings
        layoutSelect.value = originalLayout;
        autoSearchCheckbox.checked = originalAutoSearch;
        layoutSelect.dispatchEvent(new Event('change', { bubbles: true }));
        autoSearchCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
        
        setTimeout(() => {
            console.log('\n4. Restored original settings:');
            testInlineGaragePosition();
            
            console.log('\n✅ Test Summary:');
            console.log('   - In Inline + Auto-search: Garage button should be below form, right-aligned');
            console.log('   - In Inline without Auto-search: Garage button should be in form, right-aligned');
            console.log('   - Button should never be inside grid when auto-search is enabled');
            
        }, 2000);
    }, 2000);
}, 2000);

console.log('\n⏳ Running position test, please wait...');
