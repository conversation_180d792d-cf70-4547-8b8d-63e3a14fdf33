// Отладочный скрипт для проверки API вызовов
// Запустите в консоли браузера: window.debugApiCalls()

window.debugApiCalls = async function() {
    console.log('🔍 [Debug API] Начинаем отладку API вызовов...');
    
    // Функция для AJAX запроса
    async function makeRequest(action, data = {}) {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('nonce', window.WheelFitData?.nonce || 'test');
        
        Object.keys(data).forEach(key => {
            formData.append(key, data[key]);
        });

        const response = await fetch(window.WheelFitData?.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }
    
    console.log('--- Тест 1: Обычный запрос брендов ---');
    const makes1 = await makeRequest('wf_get_makes');
    console.log('Результат wf_get_makes:', makes1);
    console.log('Количество брендов:', makes1.success ? makes1.data.length : 'Ошибка');
    
    console.log('\n--- Тест 2: Бренды по году ---');
    const makes2 = await makeRequest('wf_get_makes_by_year', { year: 2020 });
    console.log('Результат wf_get_makes_by_year:', makes2);
    console.log('Количество брендов:', makes2.success ? makes2.data.length : 'Ошибка');
    
    console.log('\n--- Тест 3: Проверка настроек ---');
    
    // Попробуем получить информацию о текущих настройках через PHP
    console.log('Для проверки настроек откройте админку WordPress и посмотрите:');
    console.log('1. Wheel-Size > Features - какие регионы включены?');
    console.log('2. Wheel-Size > Brand Filters - какие бренды в include/exclude?');
    
    console.log('\n--- Анализ ---');
    if (makes1.success && makes2.success) {
        const count1 = makes1.data.length;
        const count2 = makes2.data.length;
        
        if (count1 === 0 && count2 === 0) {
            console.error('❌ Оба запроса вернули 0 брендов - проблема в фильтрах!');
            console.log('💡 Решение: Проверьте настройки региональных и брендовых фильтров');
        } else if (count1 > 0 && count2 > 0) {
            console.log('✅ Оба запроса работают');
        } else {
            console.warn('⚠️ Один из запросов не работает');
        }
    } else {
        console.error('❌ Один или оба запроса завершились с ошибкой');
    }
    
    return { makes1, makes2 };
};

console.log('🔍 [Debug API] Скрипт загружен. Запустите: window.debugApiCalls()');
