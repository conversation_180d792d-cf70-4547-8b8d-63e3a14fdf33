// Test script for complete API validation cycle
console.log('=== API Validation Cycle Test ===');

function runValidationCycleTest() {
    console.log('\n🔄 Starting API Validation Cycle Test...');
    
    // Step 1: Check initial state
    console.log('\n1️⃣ Checking Initial State:');
    const apiKeyInput = document.getElementById('api_key');
    const testButton = document.getElementById('test-api-key');
    const statusSpan = document.getElementById('api-test-status');
    const resultDiv = document.getElementById('api-test-result');
    
    if (!apiKeyInput || !testButton) {
        console.log('❌ Test elements not found. Make sure you are on the API Settings page.');
        return;
    }
    
    console.log('✅ Test elements found');
    console.log('📝 Current API key length:', apiKeyInput.value.length);
    
    // Step 2: Check current configuration status
    console.log('\n2️⃣ Checking Current Configuration:');
    const statusIndicator = document.querySelector('p[style*="color: #46b450"], p[style*="color: #dc3232"]');
    if (statusIndicator) {
        console.log('📊 Current status:', statusIndicator.textContent.trim());
    }
    
    // Step 3: Check admin notices
    console.log('\n3️⃣ Checking Admin Notices:');
    const notices = document.querySelectorAll('.notice');
    const apiNotices = Array.from(notices).filter(notice => 
        notice.textContent.includes('API') || 
        notice.textContent.includes('inactive') ||
        notice.textContent.includes('functionality')
    );
    
    if (apiNotices.length > 0) {
        console.log('📢 Found', apiNotices.length, 'API-related notices:');
        apiNotices.forEach((notice, i) => {
            console.log(`   ${i+1}. ${notice.textContent.trim()}`);
        });
    } else {
        console.log('📢 No API-related notices found');
    }
    
    // Step 4: Check menu items
    console.log('\n4️⃣ Checking Menu Items:');
    const menuItems = document.querySelectorAll('a[href*="wheel-size"]');
    const otherMenuItems = Array.from(menuItems).filter(item => 
        item.href.includes('page=wheel-size-')
    );
    
    console.log('🔗 Total Wheel-Size menu items:', menuItems.length);
    console.log('🔗 Other menu items (non-API):', otherMenuItems.length);
    if (otherMenuItems.length > 0) {
        console.log('   Available pages:', otherMenuItems.map(item => item.textContent.trim()).join(', '));
    }
    
    // Step 5: Simulate API key test (if API key is present)
    if (apiKeyInput.value.trim()) {
        console.log('\n5️⃣ API Key Test Available:');
        console.log('🔑 API key is present, you can test validation');
        console.log('📋 To test manually:');
        console.log('   1. Click "Test Connection" button');
        console.log('   2. Watch console for debug output');
        console.log('   3. Wait for page reload');
        console.log('   4. Check if menu items appear');
        
        // Add enhanced monitoring
        if (!testButton.hasAttribute('data-cycle-test-listener')) {
            testButton.setAttribute('data-cycle-test-listener', 'true');
            
            testButton.addEventListener('click', function() {
                console.log('\n🚀 API Test Started!');
                console.log('⏰ Timestamp:', new Date().toISOString());
                
                // Monitor status changes
                let statusChecks = 0;
                const statusMonitor = setInterval(() => {
                    statusChecks++;
                    const currentStatus = statusSpan ? statusSpan.textContent : 'N/A';
                    const currentResult = resultDiv ? resultDiv.textContent.substring(0, 100) : 'N/A';
                    
                    console.log(`📊 Status check ${statusChecks}: ${currentStatus}`);
                    if (currentResult.includes('success') || currentResult.includes('error')) {
                        console.log(`📋 Result: ${currentResult}`);
                    }
                    
                    // Stop monitoring after 15 seconds
                    if (statusChecks >= 30) {
                        clearInterval(statusMonitor);
                        console.log('⏹️ Status monitoring stopped');
                    }
                }, 500);
                
                // Monitor for page reload
                let reloadWarningShown = false;
                const reloadMonitor = setInterval(() => {
                    if (resultDiv && resultDiv.textContent.includes('Reloading') && !reloadWarningShown) {
                        console.log('🔄 Page reload initiated - monitoring will stop');
                        reloadWarningShown = true;
                        clearInterval(statusMonitor);
                        clearInterval(reloadMonitor);
                    }
                }, 100);
                
                // Clean up monitors after 20 seconds
                setTimeout(() => {
                    clearInterval(statusMonitor);
                    clearInterval(reloadMonitor);
                }, 20000);
            });
        }
    } else {
        console.log('\n5️⃣ No API Key Present:');
        console.log('🔑 Please enter an API key to test validation');
        console.log('📋 Steps to test:');
        console.log('   1. Enter your API key in the field');
        console.log('   2. Click "Test Connection"');
        console.log('   3. Run this test again to monitor the process');
    }
    
    // Step 6: Check for validation URL parameters
    console.log('\n6️⃣ Checking URL Parameters:');
    const urlParams = new URLSearchParams(window.location.search);
    const validationParams = ['api_validated', 'api_error', 'saved'];
    
    validationParams.forEach(param => {
        if (urlParams.has(param)) {
            console.log(`✅ Found parameter: ${param} = ${urlParams.get(param)}`);
        }
    });
    
    if (!validationParams.some(param => urlParams.has(param))) {
        console.log('📋 No validation parameters in URL (normal for initial load)');
    }
    
    // Step 7: Provide next steps
    console.log('\n7️⃣ Next Steps:');
    
    const hasApiKey = apiKeyInput.value.trim().length > 0;
    const appearsConfigured = statusIndicator && statusIndicator.textContent.includes('validated and active');
    const hasOtherMenuItems = otherMenuItems.length > 0;
    
    if (appearsConfigured && hasOtherMenuItems) {
        console.log('✅ API appears to be properly configured');
        console.log('📋 All functionality should be available');
    } else if (hasApiKey && !appearsConfigured) {
        console.log('⚠️ API key present but not validated');
        console.log('📋 Click "Test Connection" to validate');
    } else if (!hasApiKey) {
        console.log('🔑 Enter your API key and test connection');
    } else {
        console.log('❓ Status unclear - check configuration');
    }
    
    console.log('\n📊 Test Summary:');
    console.log(`🔑 API Key: ${hasApiKey ? 'Present' : 'Missing'}`);
    console.log(`⚙️ Configured: ${appearsConfigured ? 'Yes' : 'No'}`);
    console.log(`📋 Menu Items: ${otherMenuItems.length} available`);
    console.log(`📢 Notices: ${apiNotices.length} API-related`);
}

// Auto-run the test
runValidationCycleTest();

// Provide manual re-run function
window.runValidationCycleTest = runValidationCycleTest;
console.log('\n💡 Tip: Run runValidationCycleTest() again after making changes');

// Monitor for page changes
let lastUrl = window.location.href;
setInterval(() => {
    if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        console.log('\n🔄 Page changed, running validation cycle test...');
        setTimeout(runValidationCycleTest, 1000);
    }
}, 1000);
