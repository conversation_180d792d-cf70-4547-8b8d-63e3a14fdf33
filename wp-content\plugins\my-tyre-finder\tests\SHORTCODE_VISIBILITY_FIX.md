# Shortcode Block Visibility Fix

## 🎯 Задача
Скрывать блок "Shortcode" до активации плагина (пока `wheel_size_api_configured = false`).

## 🐞 Проблема
В правом сайдбаре админ-страницы «API Settings» продолжал показываться метабокс "Shortcode – [wheel_fit]" даже когда API ключ не был валидирован.

## ✅ Решение

### **1. Условное отображение в PHP**

**Файл:** `src/admin/ApiPage.php`

**До (показывался всегда):**
```php
<div class="api-page-header">
    <h1>Wheel-Size – API Settings</h1>
    <div class="shortcode-box">
        <strong>Shortcode</strong>
        <p style="margin:6px 0 0;">Use this to display the finder:</p>
        <code>[wheel_fit]</code>
    </div>
</div>
```

**После (условное отображение):**
```php
<div class="api-page-header">
    <h1>Wheel-Size – API Settings</h1>
    <?php if ($is_configured): ?>
        <div class="shortcode-box">
            <strong>Shortcode</strong>
            <p style="margin:6px 0 0;">Use this to display the finder:</p>
            <code>[wheel_fit]</code>
        </div>
    <?php endif; ?>
</div>
```

### **2. Динамическое управление через JavaScript**

**При успешной валидации API:**
```javascript
// Show shortcode box if it was hidden
const pageHeader = document.querySelector('.api-page-header');
const existingShortcodeBox = pageHeader.querySelector('.shortcode-box');
if (!existingShortcodeBox && pageHeader) {
    const shortcodeBox = document.createElement('div');
    shortcodeBox.className = 'shortcode-box';
    shortcodeBox.innerHTML = `
        <strong>Shortcode</strong>
        <p style="margin:6px 0 0;">Use this to display the finder:</p>
        <code>[wheel_fit]</code>
    `;
    pageHeader.appendChild(shortcodeBox);
}
```

**При неудачной валидации API:**
```javascript
// Hide shortcode box on validation failure
const pageHeader = document.querySelector('.api-page-header');
const existingShortcodeBox = pageHeader?.querySelector('.shortcode-box');
if (existingShortcodeBox) {
    existingShortcodeBox.remove();
}
```

## 📊 Поведение

### **Когда API не настроен (`wheel_size_api_configured = false`):**
- ❌ Блок "Shortcode" **скрыт**
- ⚠️ Показывается баннер "Plugin inactive"
- 🔒 Доступна только страница API Settings

### **Когда API настроен (`wheel_size_api_configured = true`):**
- ✅ Блок "Shortcode" **виден**
- ✅ Показывается "[wheel_fit]" код
- 🔓 Доступны все функции плагина

### **Во время валидации:**
- **Неудачная валидация:** Блок shortcode исчезает немедленно
- **Успешная валидация:** Блок shortcode появляется немедленно, затем страница перезагружается

## 🧪 Тестирование

### **Автоматическое тестирование:**
Создан скрипт `test-shortcode-visibility.js` для проверки:
- Начальной видимости блока
- Поведения во время валидации
- CSS стилизации
- Responsive дизайна
- Поведения при перезагрузке страницы

### **Ручное тестирование:**

**Тест 1: Начальное состояние**
1. Перейти в Wheel-Size → API Settings
2. Если API не настроен → блок shortcode должен отсутствовать
3. Если API настроен → блок shortcode должен быть виден

**Тест 2: Валидация API**
1. Ввести неправильный API ключ → нажать "Test Connection"
2. Блок shortcode должен исчезнуть
3. Ввести правильный API ключ → нажать "Test Connection"
4. Блок shortcode должен появиться

**Тест 3: Деактивация/реактивация**
1. Настроить валидный API ключ (блок shortcode виден)
2. Деактивировать плагин в WordPress Admin → Plugins
3. Реактивировать плагин
4. Вернуться в API Settings → блок shortcode должен быть скрыт
5. Повторно валидировать API ключ → блок должен появиться

## 📋 Приёмочные критерии

### ✅ **Выполнено:**
- **Пока плагин показывает баннер «Plugin inactive», блок "Shortcode" отсутствует**
- **Блок появляется только после успешной валидации ключа**
- **Динамическое управление во время валидации работает корректно**
- **Поведение сохраняется при перезагрузке страницы**
- **Интеграция с системой API validation gate**

### 🔧 **Технические детали:**
- Использует переменную `$is_configured` из `ApiValidator::is_api_configured()`
- JavaScript обновляет DOM в реальном времени
- CSS стили применяются корректно к динамически созданным элементам
- Responsive дизайн сохраняется
- Нет конфликтов с другими элементами интерфейса

## 📁 Измененные файлы

1. **`src/admin/ApiPage.php`**
   - Добавлено условное отображение shortcode блока
   - Обновлен JavaScript для динамического управления

2. **`test-shortcode-visibility.js`**
   - Комплексный тестовый скрипт

3. **`SHORTCODE_VISIBILITY_FIX.md`**
   - Данная документация

## 🎯 Результат

Теперь блок "Shortcode" корректно скрывается до активации плагина и появляется только после успешной валидации API ключа, обеспечивая логичный и последовательный пользовательский опыт.

**До исправления:**
- ❌ Shortcode блок всегда виден
- ❌ Пользователи видели код до настройки API
- ❌ Несоответствие между статусом плагина и доступностью функций

**После исправления:**
- ✅ Shortcode блок скрыт до валидации API
- ✅ Появляется только когда плагин активен
- ✅ Полное соответствие между статусом и функциональностью
- ✅ Улучшенный пользовательский опыт
