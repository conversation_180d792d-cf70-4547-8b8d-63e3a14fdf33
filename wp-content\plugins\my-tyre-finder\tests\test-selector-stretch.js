/**
 * Quick test for selector stretching in auto-search mode
 * Run in browser console on admin appearance page
 */

console.log('🔧 Testing selector stretching in auto-search mode...');

const layoutSelect = document.getElementById('form_layout');
const autoSearchCheckbox = document.getElementById('auto_search_on_last_input');
const previewContainer = document.getElementById('widget-preview');

if (!layoutSelect || !autoSearchCheckbox || !previewContainer) {
    console.log('❌ Required elements not found');
    return;
}

function checkSelectorWidth() {
    const form = previewContainer.querySelector('#tab-by-car');
    if (!form) {
        console.log('❌ Form not found');
        return;
    }
    
    const hasAutoSearchClass = form.classList.contains('auto-search-mode');
    const computedStyle = window.getComputedStyle(form);
    const gridColumns = computedStyle.gridTemplateColumns;
    
    console.log('Form classes:', form.className);
    console.log('Has auto-search-mode class:', hasAutoSearchClass);
    console.log('Grid template columns:', gridColumns);
    console.log('Auto-search enabled:', autoSearchCheckbox.checked);
    
    // Check if selectors fill the width
    const selectors = form.querySelectorAll('select[id^="wf-"]');
    const submitButton = form.querySelector('.ws-submit');
    
    console.log('Selectors found:', selectors.length);
    console.log('Submit button visible:', submitButton ? !submitButton.classList.contains('hidden') : false);
    
    if (selectors.length > 0) {
        const formRect = form.getBoundingClientRect();
        let totalSelectorWidth = 0;
        
        selectors.forEach(selector => {
            const container = selector.closest('[id^="step-"]') || selector.parentElement;
            totalSelectorWidth += container.getBoundingClientRect().width;
        });
        
        const utilization = (totalSelectorWidth / formRect.width) * 100;
        console.log(`Form width: ${Math.round(formRect.width)}px`);
        console.log(`Selector area width: ${Math.round(totalSelectorWidth)}px`);
        console.log(`Utilization: ${Math.round(utilization)}%`);
        
        if (autoSearchCheckbox.checked) {
            console.log(utilization > 90 ? '✅ Selectors properly stretched' : '❌ Selectors not stretched enough');
        } else {
            console.log(utilization < 90 ? '✅ Space left for submit button' : '❌ Selectors taking too much space');
        }
    }
}

// Set to Inline layout
layoutSelect.value = 'popup-horizontal';
layoutSelect.dispatchEvent(new Event('change', { bubbles: true }));

setTimeout(() => {
    console.log('\n1. Testing WITHOUT auto-search:');
    autoSearchCheckbox.checked = false;
    autoSearchCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
    
    setTimeout(() => {
        checkSelectorWidth();
        
        console.log('\n2. Testing WITH auto-search:');
        autoSearchCheckbox.checked = true;
        autoSearchCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
        
        setTimeout(() => {
            checkSelectorWidth();
            console.log('\n✅ Test completed');
        }, 1000);
    }, 1000);
}, 1000);
