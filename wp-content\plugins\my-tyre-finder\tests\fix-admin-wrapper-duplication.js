/**
 * Fix Admin Wrapper Duplication - Diagnostic and Fix Script
 * 
 * This script identifies and fixes duplicate wrapper containers in the admin preview
 * that create the "form in form in form" effect.
 */

console.log('🔍 Starting Admin Wrapper Duplication Diagnostic...');

// 1. Check for duplicate wrapper containers
function checkWrapperDuplication() {
    console.log('\n📦 Checking for duplicate wrapper containers...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.log('ℹ️ Widget preview container not found (not in admin context)');
        return { isValid: true, message: 'Not in admin context' };
    }
    
    console.log('✅ Widget preview container found');
    
    // Check for multiple wsf-form-wrapper elements
    const formWrappers = previewContainer.querySelectorAll('.wsf-form-wrapper');
    console.log(`📊 Found ${formWrappers.length} .wsf-form-wrapper element(s)`);
    
    // Check for multiple wheel-fit-widget elements
    const widgetContainers = previewContainer.querySelectorAll('.wheel-fit-widget');
    console.log(`📊 Found ${widgetContainers.length} .wheel-fit-widget element(s)`);
    
    // Check for nested containers
    let nestedWrappers = 0;
    formWrappers.forEach((wrapper, index) => {
        const nestedInWrapper = wrapper.querySelectorAll('.wsf-form-wrapper');
        if (nestedInWrapper.length > 0) {
            console.error(`❌ Wrapper #${index + 1} contains ${nestedInWrapper.length} nested wrapper(s):`, nestedInWrapper);
            nestedWrappers += nestedInWrapper.length;
        }
    });
    
    // Check for nested widget containers
    let nestedWidgets = 0;
    widgetContainers.forEach((widget, index) => {
        const nestedInWidget = widget.querySelectorAll('.wheel-fit-widget');
        if (nestedInWidget.length > 0) {
            console.error(`❌ Widget #${index + 1} contains ${nestedInWidget.length} nested widget(s):`, nestedInWidget);
            nestedWidgets += nestedInWidget.length;
        }
    });
    
    const isValid = nestedWrappers === 0 && nestedWidgets === 0;
    
    if (isValid) {
        console.log('✅ No nested wrapper containers detected');
    } else {
        console.error(`❌ Found ${nestedWrappers} nested wrappers and ${nestedWidgets} nested widgets`);
    }
    
    return {
        isValid,
        formWrappers: formWrappers.length,
        widgetContainers: widgetContainers.length,
        nestedWrappers,
        nestedWidgets,
        message: isValid ? 'No wrapper duplication' : 'Wrapper duplication detected'
    };
}

// 2. Check for duplicate class names that might indicate duplication
function checkDuplicateClasses() {
    console.log('\n🏷️ Checking for duplicate class patterns...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) return { isValid: true, message: 'Not in admin context' };
    
    // Classes that should appear only once
    const uniqueClasses = [
        'wheel-fit-widget',
        'wsf-finder-widget',
        'wheel-fit-wizard'
    ];
    
    let duplicateClassIssues = 0;
    
    uniqueClasses.forEach(className => {
        const elements = previewContainer.querySelectorAll(`.${className}`);
        if (elements.length > 1) {
            console.warn(`⚠️ Class '${className}' appears ${elements.length} times (should be unique):`, elements);
            duplicateClassIssues++;
        } else if (elements.length === 1) {
            console.log(`✅ Class '${className}' appears once (correct)`);
        }
    });
    
    return {
        isValid: duplicateClassIssues === 0,
        duplicateClassIssues,
        message: duplicateClassIssues === 0 ? 'No duplicate classes' : `${duplicateClassIssues} duplicate class issues`
    };
}

// 3. Check DOM hierarchy for excessive nesting
function checkExcessiveNesting() {
    console.log('\n🌳 Checking DOM hierarchy for excessive nesting...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) return { isValid: true, message: 'Not in admin context' };
    
    // Find the deepest nesting level
    function getMaxDepth(element, currentDepth = 0) {
        let maxDepth = currentDepth;
        for (const child of element.children) {
            const childDepth = getMaxDepth(child, currentDepth + 1);
            maxDepth = Math.max(maxDepth, childDepth);
        }
        return maxDepth;
    }
    
    const maxDepth = getMaxDepth(previewContainer);
    console.log(`📏 Maximum nesting depth: ${maxDepth} levels`);
    
    // Check for specific problematic patterns
    const problematicSelectors = [
        '.wsf-form-wrapper .wsf-form-wrapper',
        '.wheel-fit-widget .wheel-fit-widget',
        'form form',
        '.ws-form .ws-form'
    ];
    
    let problematicNesting = 0;
    problematicSelectors.forEach(selector => {
        const elements = previewContainer.querySelectorAll(selector);
        if (elements.length > 0) {
            console.error(`❌ Problematic nesting pattern '${selector}' found ${elements.length} time(s):`, elements);
            problematicNesting++;
        }
    });
    
    const isValid = maxDepth < 15 && problematicNesting === 0; // Reasonable depth limit
    
    return {
        isValid,
        maxDepth,
        problematicNesting,
        message: isValid ? 'DOM hierarchy is reasonable' : 'Excessive or problematic nesting detected'
    };
}

// 4. Analyze container structure
function analyzeContainerStructure() {
    console.log('\n🏗️ Analyzing container structure...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) return { isValid: true, message: 'Not in admin context' };
    
    // Get the structure path to the main widget
    const widget = previewContainer.querySelector('.wheel-fit-widget, .wsf-finder-widget, #wheel-fit-wizard');
    if (!widget) {
        console.log('ℹ️ No main widget found');
        return { isValid: false, message: 'No main widget found' };
    }
    
    // Build path from preview container to widget
    const path = [];
    let current = widget;
    while (current && current !== previewContainer) {
        const classes = Array.from(current.classList).join(' ');
        const tag = current.tagName.toLowerCase();
        path.unshift(`${tag}${classes ? '.' + classes.replace(/\s+/g, '.') : ''}`);
        current = current.parentElement;
    }
    
    console.log('📍 Path to main widget:', path.join(' > '));
    
    // Check for redundant containers
    const redundantPatterns = [
        /\.wsf-form-wrapper.*\.wsf-form-wrapper/,
        /\.wheel-fit-widget.*\.wheel-fit-widget/,
        /form.*form/
    ];
    
    const pathString = path.join(' > ');
    let redundantContainers = 0;
    
    redundantPatterns.forEach(pattern => {
        if (pattern.test(pathString)) {
            console.error(`❌ Redundant container pattern detected: ${pattern}`);
            redundantContainers++;
        }
    });
    
    return {
        isValid: redundantContainers === 0,
        path,
        redundantContainers,
        message: redundantContainers === 0 ? 'Container structure is clean' : 'Redundant containers detected'
    };
}

// 5. Fix duplicate wrappers
function fixDuplicateWrappers() {
    console.log('\n🔧 Attempting to fix duplicate wrappers...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.log('ℹ️ Not in admin context, no fixes needed');
        return 0;
    }
    
    let fixesApplied = 0;
    
    // Fix nested .wsf-form-wrapper elements
    const outerWrappers = previewContainer.querySelectorAll('.wsf-form-wrapper');
    outerWrappers.forEach(wrapper => {
        const nestedWrappers = wrapper.querySelectorAll('.wsf-form-wrapper');
        nestedWrappers.forEach(nested => {
            console.log('🔧 Removing nested .wsf-form-wrapper');
            
            // Move children to parent and remove nested wrapper
            while (nested.firstChild) {
                wrapper.insertBefore(nested.firstChild, nested);
            }
            nested.remove();
            fixesApplied++;
        });
    });
    
    // Fix nested .wheel-fit-widget elements
    const outerWidgets = previewContainer.querySelectorAll('.wheel-fit-widget');
    outerWidgets.forEach(widget => {
        const nestedWidgets = widget.querySelectorAll('.wheel-fit-widget');
        nestedWidgets.forEach(nested => {
            console.log('🔧 Removing nested .wheel-fit-widget');
            
            // Move children to parent and remove nested widget
            while (nested.firstChild) {
                widget.insertBefore(nested.firstChild, nested);
            }
            nested.remove();
            fixesApplied++;
        });
    });
    
    // Remove empty containers
    const emptyContainers = previewContainer.querySelectorAll('.wsf-form-wrapper:empty, .wheel-fit-widget:empty');
    emptyContainers.forEach(container => {
        console.log('🔧 Removing empty container:', container.className);
        container.remove();
        fixesApplied++;
    });
    
    if (fixesApplied > 0) {
        console.log(`✅ Applied ${fixesApplied} fix(es) to wrapper structure`);
    } else {
        console.log('ℹ️ No wrapper fixes needed');
    }
    
    return fixesApplied;
}

// 6. Main diagnostic function
function runDiagnostic() {
    console.log('🚀 Running admin wrapper duplication diagnostic...\n');
    
    const results = {
        wrapperDuplication: checkWrapperDuplication(),
        duplicateClasses: checkDuplicateClasses(),
        excessiveNesting: checkExcessiveNesting(),
        containerStructure: analyzeContainerStructure()
    };
    
    console.log('\n📊 Diagnostic Results:');
    console.log('- Wrapper Duplication:', results.wrapperDuplication.isValid ? '✅ PASS' : '❌ FAIL');
    console.log('- Duplicate Classes:', results.duplicateClasses.isValid ? '✅ PASS' : '❌ FAIL');
    console.log('- Excessive Nesting:', results.excessiveNesting.isValid ? '✅ PASS' : '❌ FAIL');
    console.log('- Container Structure:', results.containerStructure.isValid ? '✅ PASS' : '❌ FAIL');
    
    const allPassed = Object.values(results).every(result => result.isValid);
    
    if (allPassed) {
        console.log('\n🎉 All checks passed! No wrapper duplication issues detected.');
    } else {
        console.log('\n⚠️ Issues detected. Running auto-fix...');
        runAutoFix();
    }
    
    return results;
}

// 7. Auto-fix function
function runAutoFix() {
    console.log('\n🔧 Running auto-fix procedures...');
    
    const fixes = {
        duplicateWrappers: fixDuplicateWrappers()
    };
    
    const totalFixes = Object.values(fixes).reduce((sum, count) => sum + count, 0);
    
    if (totalFixes > 0) {
        console.log(`\n✅ Applied ${totalFixes} fix(es). Re-running diagnostic...`);
        setTimeout(() => runDiagnostic(), 1000);
    } else {
        console.log('\n⚠️ No automatic fixes available. Manual intervention may be required.');
        console.log('\n💡 Recommendations:');
        console.log('1. Check if admin preview is adding extra wrapper containers');
        console.log('2. Verify template rendering logic in Frontend.php');
        console.log('3. Look for duplicate template includes or renders');
        console.log('4. Check CSS for conflicting wrapper styles');
    }
    
    return fixes;
}

// Export functions for manual use
window.AdminWrapperFixer = {
    runDiagnostic,
    runAutoFix,
    checkWrapperDuplication,
    checkDuplicateClasses,
    checkExcessiveNesting,
    analyzeContainerStructure,
    fixDuplicateWrappers
};

// Auto-run diagnostic when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runDiagnostic);
} else {
    runDiagnostic();
}

console.log('\n💡 Manual usage: AdminWrapperFixer.runDiagnostic() or AdminWrapperFixer.runAutoFix()');
