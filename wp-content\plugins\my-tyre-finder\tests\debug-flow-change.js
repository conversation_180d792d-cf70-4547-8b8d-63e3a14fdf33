/**
 * Детальная диагностика изменения Flow/Layout
 * Показывает точно, что происходит при изменении настроек
 */

(function() {
    'use strict';

    console.log('[Flow Debug] 🔍 Диагностика изменения Flow/Layout...');

    let changeCounter = 0;
    let isMonitoring = false;

    // Функция детальной проверки селекторов
    function detailedSelectorCheck(label) {
        console.log(`\n[Flow Debug] === ${label} ===`);
        
        const selectors = [
            { id: 'wf-make', key: 'select_make_placeholder' },
            { id: 'wf-model', key: 'select_model_placeholder' },
            { id: 'wf-year', key: 'select_year_placeholder' },
            { id: 'wf-modification', key: 'select_mods_placeholder' },
            { id: 'wf-generation', key: 'select_gen_placeholder' }
        ];
        
        const results = {};
        
        selectors.forEach(({ id, key }) => {
            const select = document.getElementById(id);
            if (select) {
                const option = select.querySelector('option[value=""]');
                if (option) {
                    const text = option.textContent.trim();
                    const dataI18n = option.getAttribute('data-i18n');
                    const isRussian = text.includes('Выберите') || text.includes('Сначала') || text.includes('Загрузка');
                    const isEnglish = text.includes('Choose') || text.includes('Select') || text.includes('Loading');
                    
                    results[id] = {
                        text,
                        dataI18n,
                        isRussian,
                        isEnglish,
                        expectedKey: key,
                        hasCorrectDataI18n: dataI18n === key
                    };
                    
                    const status = isRussian ? '✅ RU' : isEnglish ? '❌ EN' : '⚠️ OTHER';
                    const dataStatus = results[id].hasCorrectDataI18n ? '✅' : '❌';
                    
                    console.log(`[Flow Debug] ${id}: ${status} "${text}"`);
                    console.log(`[Flow Debug]   data-i18n: ${dataStatus} "${dataI18n}" (ожидается: "${key}")`);
                }
            }
        });
        
        return results;
    }

    // Функция проверки переводов
    function checkTranslations() {
        console.log('\n[Flow Debug] === Проверка переводов ===');
        
        if (!window.WheelFitI18n) {
            console.error('[Flow Debug] ❌ window.WheelFitI18n недоступен!');
            return false;
        }
        
        const keys = [
            'select_make_placeholder',
            'select_model_placeholder', 
            'select_year_placeholder',
            'select_mods_placeholder',
            'select_gen_placeholder'
        ];
        
        console.log(`[Flow Debug] Всего переводов: ${Object.keys(window.WheelFitI18n).length}`);
        
        keys.forEach(key => {
            const value = window.WheelFitI18n[key];
            const available = !!value;
            const isRussian = value && (value.includes('Выберите') || value.includes('Сначала'));
            
            console.log(`[Flow Debug] ${key}: ${available ? '✅' : '❌'} "${value}" ${isRussian ? '(RU)' : '(EN/OTHER)'}`);
        });
        
        return true;
    }

    // Функция проверки функций виджета
    function checkWidgetFunctions() {
        console.log('\n[Flow Debug] === Проверка функций виджета ===');
        
        if (!window.wheelFitWidget) {
            console.error('[Flow Debug] ❌ window.wheelFitWidget недоступен!');
            return false;
        }
        
        const widget = window.wheelFitWidget;
        const functions = ['populateMakes', 'populateModels', 'populateYears', 'initializeFormState'];
        
        functions.forEach(funcName => {
            if (typeof widget[funcName] === 'function') {
                const code = widget[funcName].toString();
                const hasDirectTranslation = code.includes('window.WheelFitI18n && window.WheelFitI18n[');
                const hasOldTFunction = code.includes('t(\'select_');
                
                console.log(`[Flow Debug] ${funcName}:`);
                console.log(`[Flow Debug]   Прямые переводы: ${hasDirectTranslation ? '✅' : '❌'}`);
                console.log(`[Flow Debug]   Старая функция t(): ${hasOldTFunction ? '❌' : '✅'}`);
            } else {
                console.log(`[Flow Debug] ${funcName}: ❌ не найден`);
            }
        });
        
        return true;
    }

    // Функция отслеживания AJAX запросов
    function interceptAjaxRequests() {
        console.log('\n[Flow Debug] === Перехват AJAX запросов ===');
        
        // Перехватываем fetch
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            const options = args[1] || {};
            
            if (typeof url === 'string' && url.includes('update_preview')) {
                console.log('[Flow Debug] 🌐 AJAX запрос update_preview обнаружен');
                console.log('[Flow Debug] URL:', url);
                console.log('[Flow Debug] Options:', options);
                
                return originalFetch.apply(this, args).then(response => {
                    console.log('[Flow Debug] 📥 AJAX ответ получен');
                    
                    // Клонируем ответ для чтения
                    const clonedResponse = response.clone();
                    clonedResponse.json().then(data => {
                        console.log('[Flow Debug] 📊 Данные ответа:');
                        console.log('[Flow Debug] Success:', data.success);
                        if (data.data) {
                            console.log('[Flow Debug] HTML length:', data.data.html ? data.data.html.length : 'N/A');
                            console.log('[Flow Debug] I18n keys:', data.data.i18n ? Object.keys(data.data.i18n).length : 'N/A');
                            console.log('[Flow Debug] Locale:', data.data.locale);
                        }
                    }).catch(e => console.log('[Flow Debug] Ошибка парсинга JSON:', e));
                    
                    return response;
                });
            }
            
            return originalFetch.apply(this, args);
        };
        
        console.log('[Flow Debug] ✅ AJAX перехват настроен');
    }

    // Функция мониторинга изменений настроек
    function monitorSettingsChanges() {
        if (isMonitoring) return;
        isMonitoring = true;
        
        console.log('\n[Flow Debug] === Мониторинг изменений настроек ===');
        
        document.addEventListener('change', function(e) {
            if (e.target && (e.target.id === 'search_flow' || e.target.id === 'form_layout')) {
                changeCounter++;
                console.log(`\n[Flow Debug] 🔄 ИЗМЕНЕНИЕ #${changeCounter}: ${e.target.id} = "${e.target.value}"`);
                console.log('[Flow Debug] Время:', new Date().toLocaleTimeString());
                
                // Проверяем ДО изменения
                console.log('\n[Flow Debug] 📋 СОСТОЯНИЕ ДО AJAX:');
                checkTranslations();
                detailedSelectorCheck('Селекторы ДО AJAX');
                
                // Проверяем через разные интервалы ПОСЛЕ изменения
                setTimeout(() => {
                    console.log('\n[Flow Debug] 📋 СОСТОЯНИЕ ЧЕРЕЗ 500ms:');
                    checkTranslations();
                    detailedSelectorCheck('Селекторы через 500ms');
                }, 500);
                
                setTimeout(() => {
                    console.log('\n[Flow Debug] 📋 СОСТОЯНИЕ ЧЕРЕЗ 1 СЕКУНДУ:');
                    checkTranslations();
                    detailedSelectorCheck('Селекторы через 1 секунду');
                }, 1000);
                
                setTimeout(() => {
                    console.log('\n[Flow Debug] 📋 СОСТОЯНИЕ ЧЕРЕЗ 2 СЕКУНДЫ:');
                    checkTranslations();
                    const finalResults = detailedSelectorCheck('Селекторы через 2 секунды');
                    
                    // Анализируем результат
                    const englishSelectors = Object.entries(finalResults).filter(([id, data]) => data.isEnglish);
                    const russianSelectors = Object.entries(finalResults).filter(([id, data]) => data.isRussian);
                    
                    console.log(`\n[Flow Debug] 📊 ФИНАЛЬНЫЙ АНАЛИЗ ИЗМЕНЕНИЯ #${changeCounter}:`);
                    console.log(`[Flow Debug] Русских селекторов: ${russianSelectors.length}`);
                    console.log(`[Flow Debug] Английских селекторов: ${englishSelectors.length}`);
                    
                    if (englishSelectors.length > 0) {
                        console.error(`[Flow Debug] ❌ ПРОБЛЕМА: ${englishSelectors.length} селекторов на английском!`);
                        englishSelectors.forEach(([id, data]) => {
                            console.error(`[Flow Debug]   ${id}: "${data.text}"`);
                        });
                    } else {
                        console.log(`[Flow Debug] ✅ ВСЕ СЕЛЕКТОРЫ НА РУССКОМ!`);
                    }
                    
                    console.log(`[Flow Debug] 🏁 ИЗМЕНЕНИЕ #${changeCounter} ЗАВЕРШЕНО\n`);
                }, 2000);
            }
        });
        
        console.log('[Flow Debug] ✅ Мониторинг настроен');
    }

    // Основная функция диагностики
    function runFlowDebug() {
        console.log('\n[Flow Debug] 🚀 ЗАПУСК ДИАГНОСТИКИ ИЗМЕНЕНИЯ FLOW/LAYOUT');
        
        // 1. Начальная проверка
        console.log('\n[Flow Debug] 1. Начальная проверка...');
        checkTranslations();
        checkWidgetFunctions();
        detailedSelectorCheck('Начальное состояние селекторов');
        
        // 2. Настраиваем перехват AJAX
        interceptAjaxRequests();
        
        // 3. Запускаем мониторинг
        monitorSettingsChanges();
        
        console.log('\n[Flow Debug] ✅ Диагностика готова!');
        console.log('[Flow Debug] 👉 Теперь измените Search Flow или Form Layout');
        console.log('[Flow Debug] 📊 Результаты будут показаны в консоли');
    }

    // Глобальные функции
    window.flowDebug = {
        run: runFlowDebug,
        checkSelectors: () => detailedSelectorCheck('Ручная проверка'),
        checkTranslations: checkTranslations,
        checkFunctions: checkWidgetFunctions
    };

    // Автоматический запуск
    setTimeout(() => {
        console.log('[Flow Debug] Автоматический запуск диагностики...');
        runFlowDebug();
    }, 1000);

    console.log('[Flow Debug] Диагностика загружена. Доступные функции:');
    console.log('- flowDebug.run() - полная диагностика');
    console.log('- flowDebug.checkSelectors() - проверка селекторов');

})();
