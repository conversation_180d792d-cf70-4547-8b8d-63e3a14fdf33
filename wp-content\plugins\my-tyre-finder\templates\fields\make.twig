{# Make Field Template - Reusable across layouts #}
{% set is_stepper = (form_layout ?? 'stepper') == 'stepper' %}
{% set is_inline = (form_layout ?? 'inline') == 'inline' %}
{% set is_popup_horizontal = (form_layout ?? 'popup-horizontal') == 'popup-horizontal' %}

{% if is_stepper %}
    {# Step-by-Step layout #}
    <div id="step-1" class="step-container">
        <label for="wf-make" class="block text-sm font-semibold text-wsf-text uppercase tracking-wide mb-2" data-i18n="label_make">Make</label>
        <div class="relative w-full">
            <select id="wf-make" name="make" class="wsf-input block w-full" required>
                <option value="" data-i18n="loading_makes">Loading makes...</option>
            </select>
            <div id="make-loader" class="hidden absolute right-4 top-1/2 -translate-y-1/2"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-wsf-primary"></div></div>
        </div>
    </div>
{% else %}
    {# Inline and Popup-Horizontal layouts #}
    <div id="step-1" class="flex flex-col w-full min-w-0">
        <label for="wf-make" class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1" data-i18n="label_make">Make</label>
        <div class="relative min-w-0">
            <select id="wf-make" name="make" class="wsf-input block w-full" required>
                <option value="" data-i18n="loading_makes">Loading makes...</option>
            </select>
            <div id="make-loader" class="loader hidden absolute right-3 top-1/2 -translate-y-1/2"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-wsf-primary"></div></div>
        </div>
    </div>
{% endif %} 