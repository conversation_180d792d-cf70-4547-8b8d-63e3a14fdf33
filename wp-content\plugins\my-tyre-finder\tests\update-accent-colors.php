<?php
/**
 * Update Accent Colors for Default Themes
 * 
 * This script updates the accent colors for garage button:
 * - Light theme: Dark blue (#1E40AF)
 * - Dark theme: Light gray (#E5E7EB)
 */

// Ensure WordPress is loaded
if (!defined('ABSPATH')) {
    require_once dirname(__DIR__, 3) . '/wp-load.php';
}

// Load the ThemeManager class
use MyTyreFinder\Includes\ThemeManager;

echo "🎨 Updating Accent Colors for Garage Button\n";
echo "==========================================\n\n";

try {
    // Force update default themes with new accent colors
    echo "1️⃣ Updating default themes...\n";
    $result = ThemeManager::force_update_default_themes();
    
    if ($result) {
        echo "✅ Default themes updated successfully!\n\n";
        
        // Verify the update
        echo "2️⃣ Verifying accent colors...\n";
        $themes = ThemeManager::get_themes();
        
        if (isset($themes['light'])) {
            $light_accent = $themes['light']['--wsf-accent'] ?? 'not set';
            echo "  🌞 Light theme accent: {$light_accent}\n";
            if ($light_accent === '#1E40AF') {
                echo "     ✅ Correct dark blue color for garage button\n";
            } else {
                echo "     ❌ Expected #1E40AF (dark blue)\n";
            }
        }
        
        if (isset($themes['dark'])) {
            $dark_accent = $themes['dark']['--wsf-accent'] ?? 'not set';
            echo "  🌙 Dark theme accent: {$dark_accent}\n";
            if ($dark_accent === '#E5E7EB') {
                echo "     ✅ Correct light gray color for garage button\n";
            } else {
                echo "     ❌ Expected #E5E7EB (light gray)\n";
            }
        }
        
        echo "\n🎉 Accent colors updated! Refresh your page to see the new garage button colors.\n";
        echo "\n📋 Summary:\n";
        echo "   • Light theme garage button: Dark blue (#1E40AF)\n";
        echo "   • Dark theme garage button: Light gray (#E5E7EB)\n";
        
    } else {
        echo "❌ Failed to update themes\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
