<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Button Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        /* Simulate WordPress frontend styles that might conflict */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 2rem;
            background: #f0f0f1;
            margin: 0;
        }
        
        /* Simulate some WordPress button styles that might conflict */
        button {
            font-family: inherit;
        }
        
        .wp-block-button__link {
            background-color: #007cba;
            color: #fff;
            padding: 0.667em 1em;
            border-radius: 3px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .widget-container {
            background: #f9fafb;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            margin: 1rem 0;
        }
        
        .garage-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 1rem 0;
            padding: 1rem;
            background: white;
            border-radius: 0.375rem;
            border: 1px solid #e5e7eb;
        }
        
        .garage-items {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .garage-item {
            background: #f3f4f6;
            padding: 0.5rem 0.75rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            color: #374151;
        }
        
        h2 {
            margin-top: 0;
            color: #1f2937;
        }
        
        .mobile-test {
            max-width: 320px;
            border: 2px dashed #3b82f6;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        /* Include the inline styles from Frontend.php */
        .btn-secondary {
            background-color: transparent !important;
            color: var(--wsf-error, #ef4444) !important;
            font-weight: 500 !important;
            font-size: 0.875rem !important;
            padding: 0.5rem 0.75rem !important;
            border-radius: 0.375rem !important;
            border: 1px solid var(--wsf-border-light, #f3f4f6) !important;
            transition: all 0.15s ease !important;
            cursor: pointer !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.375rem !important;
            text-decoration: none !important;
            box-shadow: none !important;
        }

        .btn-secondary:hover:not(:disabled) {
            background-color: var(--wsf-surface-hover, #f3f4f6) !important;
            border-color: var(--wsf-error, #ef4444) !important;
            color: var(--wsf-error, #dc2626) !important;
        }

        .btn-secondary:focus {
            outline: none !important;
            box-shadow: 0 0 0 2px var(--wsf-error, #ef4444), 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
        }

        .btn-secondary:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .btn-secondary i {
            width: 1rem !important;
            height: 1rem !important;
            flex-shrink: 0 !important;
        }

        /* Responsive styles for mobile devices */
        @media (max-width: 640px) {
            .btn-secondary {
                padding: 0.5rem !important;
                min-width: 2.5rem !important;
            }
            
            .btn-secondary span {
                display: none !important;
            }
            
            .btn-secondary i {
                margin: 0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Тест кнопки на фронте</h1>
        
        <div class="widget-container wsf-finder-widget">
            <h2>Виджет поиска шин</h2>
            
            <div class="garage-section">
                <div>
                    <strong>Мой гараж:</strong>
                    <div class="garage-items">
                        <span class="garage-item">BMW X5 2020</span>
                        <span class="garage-item">Mercedes C-Class 2019</span>
                    </div>
                </div>
                
                <button id="garage-clear-all" class="btn-secondary">
                    <i data-lucide="trash-2"></i>
                    <span>Clear all</span>
                </button>
            </div>
            
            <div style="margin-top: 1rem;">
                <button class="btn-primary">
                    <i data-lucide="search"></i>
                    <span>Find Sizes</span>
                </button>
            </div>
        </div>

        <div class="mobile-test">
            <h3>📱 Мобильная версия</h3>
            <p>На экранах ≤640px текст скрывается:</p>
            <button class="btn-secondary">
                <i data-lucide="trash-2"></i>
                <span>Clear all</span>
            </button>
        </div>
        
        <div style="background: #e0f2fe; padding: 1rem; border-radius: 0.375rem; margin-top: 2rem;">
            <h3>✅ Что должно работать:</h3>
            <ul>
                <li>Кнопка "Clear all" должна быть прозрачной с красным текстом</li>
                <li>При hover - светло-серый фон и более яркая красная рамка</li>
                <li>На мобильных устройствах - только иконка без текста</li>
                <li>Размер кнопки должен быть компактным (не как btn-primary)</li>
            </ul>
        </div>

        <div style="background: #fff3cd; padding: 1rem; border-radius: 0.375rem; margin-top: 1rem; border: 1px solid #ffeaa7;">
            <h3>🔧 Диагностика:</h3>
            <p>Если кнопка выглядит как обычная браузерная кнопка, проблема в:</p>
            <ol>
                <li><strong>CSS не загружается</strong> - проверьте Network tab в DevTools</li>
                <li><strong>Селекторы переопределяются</strong> - проверьте Computed styles</li>
                <li><strong>Inline стили не применяются</strong> - проверьте Elements tab</li>
            </ol>
            <p><strong>Решение:</strong> Добавлены максимально специфичные inline стили с !important</p>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Test button functionality
        document.getElementById('garage-clear-all').addEventListener('click', function() {
            alert('Clear all clicked! Стили применяются правильно.');
        });
    </script>
</body>
</html>
