/**
 * Test script to verify that "Disabled" option has been removed from Form Layout
 * and that all fallback logic works correctly
 */

console.log('🧪 Testing Disabled option removal...');

function testFormLayoutOptions() {
    console.log('\n📋 Testing Form Layout dropdown options...');
    
    const layoutSelect = document.getElementById('form_layout');
    if (!layoutSelect) {
        console.log('❌ Form Layout dropdown not found (not on admin page)');
        return false;
    }
    
    console.log('✅ Found Form Layout dropdown');
    
    // Get all options
    const options = Array.from(layoutSelect.options);
    const optionValues = options.map(opt => opt.value);
    const optionTexts = options.map(opt => opt.textContent.trim());
    
    console.log('   Available options:');
    options.forEach((opt, index) => {
        console.log(`     ${index + 1}. "${opt.value}" - "${opt.textContent.trim()}"`);
    });
    
    // Check that "none" value is NOT present
    const hasNoneValue = optionValues.includes('none');
    console.log(`${!hasNoneValue ? '✅' : '❌'} "none" value is not present`);
    
    // Check that "Disabled" text is NOT present
    const hasDisabledText = optionTexts.some(text => text.toLowerCase().includes('disabled'));
    console.log(`${!hasDisabledText ? '✅' : '❌'} "Disabled" text is not present`);
    
    // Check that expected options ARE present
    const expectedOptions = [
        'popup-horizontal', // Inline (1x4)
        'inline',          // Grid (2x2)
        'stepper',         // Step-by-Step
        'wizard'           // Wizard
    ];
    
    const allExpectedPresent = expectedOptions.every(value => optionValues.includes(value));
    console.log(`${allExpectedPresent ? '✅' : '❌'} All expected options are present`);
    
    if (!allExpectedPresent) {
        const missing = expectedOptions.filter(value => !optionValues.includes(value));
        console.log(`   Missing options: ${missing.join(', ')}`);
    }
    
    return !hasNoneValue && !hasDisabledText && allExpectedPresent;
}

function testCurrentSelection() {
    console.log('\n🎯 Testing current selection...');
    
    const layoutSelect = document.getElementById('form_layout');
    if (!layoutSelect) {
        console.log('❌ Form Layout dropdown not found');
        return false;
    }
    
    const currentValue = layoutSelect.value;
    console.log(`   Current selection: "${currentValue}"`);
    
    // Check that current value is not "none"
    const isNotNone = currentValue !== 'none';
    console.log(`${isNotNone ? '✅' : '❌'} Current selection is not "none"`);
    
    // Check that current value is valid
    const validOptions = ['popup-horizontal', 'inline', 'stepper', 'wizard'];
    const isValidSelection = validOptions.includes(currentValue);
    console.log(`${isValidSelection ? '✅' : '❌'} Current selection is valid`);
    
    return isNotNone && isValidSelection;
}

function testFallbackLogic() {
    console.log('\n🔄 Testing fallback logic...');
    
    // This test simulates what happens when someone had "none" selected
    // We can't directly test the PHP logic, but we can check the JavaScript fallback
    
    const layoutSelect = document.getElementById('form_layout');
    if (!layoutSelect) {
        console.log('❌ Form Layout dropdown not found');
        return false;
    }
    
    // Temporarily create a "none" option to test fallback
    const noneOption = document.createElement('option');
    noneOption.value = 'none';
    noneOption.textContent = 'Test Disabled';
    layoutSelect.appendChild(noneOption);
    
    // Select the "none" option
    layoutSelect.value = 'none';
    console.log('   Temporarily set selection to "none"');
    
    // Trigger change event to test any JavaScript fallback logic
    const changeEvent = new Event('change', { bubbles: true });
    layoutSelect.dispatchEvent(changeEvent);
    
    // Check if JavaScript changed it back
    const afterChangeValue = layoutSelect.value;
    console.log(`   Value after change event: "${afterChangeValue}"`);
    
    // Clean up - remove the test option
    layoutSelect.removeChild(noneOption);
    
    // Reset to a valid value
    layoutSelect.value = 'popup-horizontal';
    
    const fallbackWorked = afterChangeValue !== 'none';
    console.log(`${fallbackWorked ? '✅' : 'ℹ️'} JavaScript fallback ${fallbackWorked ? 'worked' : 'not implemented (PHP handles it)'}`);
    
    return true; // This test is informational
}

function testWidgetAlwaysVisible() {
    console.log('\n👁️ Testing widget visibility...');
    
    // Check if widget is visible in Live Preview
    const widgetPreview = document.getElementById('widget-preview');
    if (!widgetPreview) {
        console.log('ℹ️ Widget preview not found (not on admin page)');
        return true;
    }
    
    const widget = widgetPreview.querySelector('.wheel-fit-widget, .wsf-finder-widget');
    if (!widget) {
        console.log('❌ Widget not found in preview');
        return false;
    }
    
    console.log('✅ Widget is present in preview');
    
    // Check if widget shows "disabled" message
    const disabledMessage = widget.querySelector('p:contains("Widget disabled")');
    const hasDisabledMessage = !!disabledMessage;
    
    console.log(`${!hasDisabledMessage ? '✅' : '❌'} Widget does not show disabled message`);
    
    // Check if widget has actual form elements
    const hasFormElements = widget.querySelector('select, input, button[type="submit"]');
    console.log(`${hasFormElements ? '✅' : '❌'} Widget has functional form elements`);
    
    return !hasDisabledMessage && hasFormElements;
}

function runDisabledOptionRemovalTests() {
    console.log('🚀 Starting Disabled option removal tests...\n');
    
    const results = {
        formLayoutOptions: testFormLayoutOptions(),
        currentSelection: testCurrentSelection(),
        fallbackLogic: testFallbackLogic(),
        widgetVisibility: testWidgetAlwaysVisible()
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! "Disabled" option has been successfully removed.');
    } else {
        console.log('⚠️ Some tests failed. Check the details above.');
    }
    
    console.log('\n📋 Removal Summary:');
    console.log('• "Disabled" option removed from Form Layout dropdown');
    console.log('• "none" value no longer available');
    console.log('• Fallback logic ensures widget always displays');
    console.log('• Current selections automatically migrate to valid options');
    console.log('• Widget is always functional regardless of previous settings');
    
    return results;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runDisabledOptionRemovalTests);
} else {
    runDisabledOptionRemovalTests();
}

// Export for manual testing
window.testDisabledOptionRemoval = runDisabledOptionRemovalTests;
