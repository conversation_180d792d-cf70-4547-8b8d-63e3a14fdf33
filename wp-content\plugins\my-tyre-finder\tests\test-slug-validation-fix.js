/**
 * Test Slug Validation Fix
 * Проверка исправления валидации slug для активации тем
 */

(function() {
    'use strict';

    console.log('🔧 === TEST SLUG VALIDATION FIX ===');

    // Проверяем доступность API
    if (typeof wpApiSettings === 'undefined') {
        console.error('❌ wpApiSettings not found - run this in WordPress admin');
        return;
    }

    const apiBase = wpApiSettings.root + 'wheel-size/v1/themes';
    const nonce = wpApiSettings.nonce;

    console.log('✅ API Base:', apiBase);
    console.log('✅ Nonce available:', !!nonce);

    // Тестовые slug для проверки
    const testSlugs = [
        '123',          // Чисто числовой
        '321',          // Чисто числовой
        'abc123',       // Буквы + числа
        'test-theme',   // С дефисом
        'test_theme',   // С подчеркиванием
        'light',        // Стандартная тема
        'dark'          // Стандартная тема
    ];

    // Функция для тестирования активации темы
    async function testActivateTheme(slug) {
        console.log(`\n--- Testing activation of "${slug}" ---`);
        
        try {
            const response = await fetch(apiBase + '/active', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': nonce
                },
                body: JSON.stringify({ slug: slug })
            });

            console.log(`Response status for "${slug}":`, response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.log(`❌ Failed to activate "${slug}":`, errorText);
                
                // Попробуем распарсить JSON ошибку
                try {
                    const errorData = JSON.parse(errorText);
                    console.log(`Error details:`, errorData);
                } catch (e) {
                    console.log(`Raw error:`, errorText);
                }
                
                return false;
            } else {
                const data = await response.json();
                console.log(`✅ Successfully activated "${slug}":`, data);
                return true;
            }
        } catch (error) {
            console.error(`❌ Exception activating "${slug}":`, error);
            return false;
        }
    }

    // Функция для получения всех доступных тем
    async function getAvailableThemes() {
        console.log('\n--- Getting available themes ---');
        
        try {
            const response = await fetch(apiBase, {
                headers: { 'X-WP-Nonce': nonce }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            const themeKeys = Object.keys(data.themes);
            console.log('✅ Available themes:', themeKeys);
            return themeKeys;
        } catch (error) {
            console.error('❌ Failed to get themes:', error);
            return [];
        }
    }

    // Функция для создания тестовой темы с числовым именем
    async function createNumericTheme(name) {
        console.log(`\n--- Creating theme "${name}" ---`);
        
        const themeData = {
            name: name,
            properties: {
                '--wsf-primary': '#ff6b35',
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#333333'
            }
        };

        try {
            const response = await fetch(apiBase, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': nonce
                },
                body: JSON.stringify(themeData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.log(`❌ Failed to create "${name}":`, errorText);
                return null;
            }

            const data = await response.json();
            console.log(`✅ Created theme "${name}" with slug:`, data.slug);
            return data.slug;
        } catch (error) {
            console.error(`❌ Exception creating "${name}":`, error);
            return null;
        }
    }

    // Основная функция тестирования
    async function runTests() {
        console.log('\n🚀 Starting slug validation tests...\n');

        // 1. Получить доступные темы
        const availableThemes = await getAvailableThemes();
        
        // 2. Создать тестовые темы с числовыми именами
        const numericTheme1 = await createNumericTheme('456');
        const numericTheme2 = await createNumericTheme('789');
        
        // 3. Обновить список тем для тестирования
        const allTestSlugs = [...testSlugs];
        if (numericTheme1) allTestSlugs.push(numericTheme1);
        if (numericTheme2) allTestSlugs.push(numericTheme2);

        // 4. Тестировать активацию каждой темы
        const results = {};
        
        for (const slug of allTestSlugs) {
            // Проверяем, существует ли тема
            if (availableThemes.includes(slug) || slug === numericTheme1 || slug === numericTheme2) {
                results[slug] = await testActivateTheme(slug);
                
                // Небольшая пауза между запросами
                await new Promise(resolve => setTimeout(resolve, 500));
            } else {
                console.log(`⏭️ Skipping "${slug}" - theme not found`);
                results[slug] = 'skipped';
            }
        }

        // 5. Показать результаты
        console.log('\n📊 === TEST RESULTS ===');
        Object.entries(results).forEach(([slug, result]) => {
            const status = result === true ? '✅ SUCCESS' : 
                          result === false ? '❌ FAILED' : 
                          '⏭️ SKIPPED';
            console.log(`${status}: ${slug}`);
        });

        // 6. Проверить, исправлена ли проблема с числовыми slug
        const numericResults = Object.entries(results).filter(([slug]) => /^\d+$/.test(slug));
        const numericSuccess = numericResults.filter(([, result]) => result === true);
        
        console.log('\n🔢 Numeric slug test results:');
        console.log(`Total numeric slugs tested: ${numericResults.length}`);
        console.log(`Successful activations: ${numericSuccess.length}`);
        
        if (numericSuccess.length > 0) {
            console.log('🎉 SUCCESS: Numeric slugs are now working!');
        } else if (numericResults.length > 0) {
            console.log('❌ ISSUE: Numeric slugs still failing');
        } else {
            console.log('⚠️ WARNING: No numeric slugs were tested');
        }

        return results;
    }

    // Экспорт для ручного тестирования
    window.slugValidationTest = {
        runTests,
        testActivateTheme,
        getAvailableThemes,
        createNumericTheme
    };

    // Автоматический запуск через 1 секунду
    setTimeout(runTests, 1000);

    console.log('\n💡 Manual testing available via window.slugValidationTest');
    console.log('Example: window.slugValidationTest.testActivateTheme("321")');

})();
