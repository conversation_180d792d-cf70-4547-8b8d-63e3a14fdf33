<?php
/**
 * Fix wizard settings - set correct search flow and layout
 */

// Try different WordPress paths
$wp_paths = [
    '../../../wp-config.php',
    '../../../../wp-config.php',
    '../../../wp-load.php',
    '../../../../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $wp_loaded = true;
        echo "✓ WordPress loaded from: $path\n";
        break;
    }
}

if (!$wp_loaded) {
    echo "❌ WordPress not found. Please run this script from WordPress admin or use WP-CLI.\n";
    echo "Alternative: Go to WordPress Admin > Wheel-Size > Appearance and set:\n";
    echo "- Search Flow: By Vehicle\n";
    echo "- Layout: Wizard\n";
    exit(1);
}

echo "Current settings:\n";
echo "- Layout: " . get_option('wheel_size_form_layout', 'popup-horizontal') . "\n";
echo "- Search Flow: " . get_option('ws_search_flow', 'by_generation') . "\n";
echo "- Active Flow: " . get_option('wheel_size_active_flow', 'none') . "\n";

echo "\nUpdating settings...\n";

// Set search flow to by_vehicle (required for wizard)
update_option('ws_search_flow', 'by_vehicle');
echo "✓ Search flow set to 'by_vehicle'\n";

// Set layout to wizard
update_option('wheel_size_form_layout', 'wizard');
echo "✓ Layout set to 'wizard'\n";

// Set active flow to flow1 (for wizard)
update_option('wheel_size_active_flow', 'flow1');
echo "✓ Active flow set to 'flow1'\n";

echo "\nNew settings:\n";
echo "- Layout: " . get_option('wheel_size_form_layout', 'popup-horizontal') . "\n";
echo "- Search Flow: " . get_option('ws_search_flow', 'by_generation') . "\n";
echo "- Active Flow: " . get_option('wheel_size_active_flow', 'none') . "\n";

echo "\n✅ Settings updated successfully!\n";
echo "Please refresh the admin page to see the wizard.\n";
?>
