// Test script to validate Russian translations for Generation field strings
console.log('=== Russian Generation Translations Test ===');

// Test configuration
const EXPECTED_RUSSIAN_TRANSLATIONS = {
    'label_generation': 'Поколение',
    'select_gen_placeholder': 'Выберите поколение',
    'select_gen_first_placeholder': 'Сначала выберите поколение'
};

const EXPECTED_ENGLISH_FALLBACKS = {
    'label_generation': 'Generation',
    'select_gen_placeholder': 'Choose a generation',
    'select_gen_first_placeholder': 'Select generation first'
};

// Test results tracking
let testResults = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function logResult(test, status, message) {
    const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${test}: ${message}`);
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
}

// Test 1: Check if translation system is available
console.log('\n1. Translation System Availability:');
const hasTranslationFunction = typeof window.t === 'function';
const hasWheelFitI18n = window.WheelFitI18n && typeof window.WheelFitI18n === 'object';

logResult('Translation function (window.t)', hasTranslationFunction ? 'pass' : 'fail',
    hasTranslationFunction ? 'Available' : 'Not available');
logResult('Translation data (WheelFitI18n)', hasWheelFitI18n ? 'pass' : 'fail',
    hasWheelFitI18n ? 'Available' : 'Not available');

// Test 2: Test Russian translations
console.log('\n2. Russian Translation Tests:');
if (hasTranslationFunction) {
    Object.keys(EXPECTED_RUSSIAN_TRANSLATIONS).forEach(key => {
        const translation = window.t(key, 'FALLBACK');
        const expected = EXPECTED_RUSSIAN_TRANSLATIONS[key];
        const isCorrect = translation === expected;
        
        logResult(`Russian "${key}"`, isCorrect ? 'pass' : 'fail',
            `Expected: "${expected}", Got: "${translation}"`);
    });
} else {
    logResult('Russian translations', 'fail', 'Cannot test - translation function not available');
}

// Test 3: Test English fallbacks
console.log('\n3. English Fallback Tests:');
if (hasTranslationFunction) {
    // Temporarily mock missing translations to test fallbacks
    const originalI18n = window.WheelFitI18n;
    window.WheelFitI18n = {}; // Empty to force fallbacks
    
    Object.keys(EXPECTED_ENGLISH_FALLBACKS).forEach(key => {
        const fallback = EXPECTED_ENGLISH_FALLBACKS[key];
        const translation = window.t(key, fallback);
        const isCorrect = translation === fallback;
        
        logResult(`English fallback "${key}"`, isCorrect ? 'pass' : 'fail',
            `Expected: "${fallback}", Got: "${translation}"`);
    });
    
    // Restore original translations
    window.WheelFitI18n = originalI18n;
} else {
    logResult('English fallbacks', 'fail', 'Cannot test - translation function not available');
}

// Test 4: Test DOM elements with data-i18n attributes
console.log('\n4. DOM Element Translation Tests:');
const generationElements = [
    { selector: 'label[for="wf-generation"]', expectedKey: 'label_generation' },
    { selector: '#wf-generation option[value=""]', expectedKey: 'select_gen_placeholder' }
];

generationElements.forEach(({ selector, expectedKey }) => {
    const element = document.querySelector(selector);
    if (element) {
        const dataI18nKey = element.getAttribute('data-i18n');
        const currentText = element.textContent.trim();
        const expectedRussian = EXPECTED_RUSSIAN_TRANSLATIONS[expectedKey];
        
        logResult(`Element "${selector}" data-i18n`, dataI18nKey === expectedKey ? 'pass' : 'warning',
            `Expected key: "${expectedKey}", Found: "${dataI18nKey}"`);
            
        if (hasWheelFitI18n && expectedRussian) {
            const isTranslated = currentText === expectedRussian;
            logResult(`Element "${selector}" translation`, isTranslated ? 'pass' : 'warning',
                `Expected: "${expectedRussian}", Current: "${currentText}"`);
        }
    } else {
        logResult(`Element "${selector}"`, 'warning', 'Element not found in DOM');
    }
});

// Test 5: Test generation field functionality
console.log('\n5. Generation Field Functionality Tests:');
const generationSelect = document.getElementById('wf-generation');
if (generationSelect) {
    logResult('Generation select element', 'pass', 'Found in DOM');
    
    // Check if it has the correct attributes
    const hasCorrectName = generationSelect.name === 'generation';
    const hasCorrectId = generationSelect.id === 'wf-generation';
    
    logResult('Generation select name attribute', hasCorrectName ? 'pass' : 'fail',
        `Expected: "generation", Found: "${generationSelect.name}"`);
    logResult('Generation select ID attribute', hasCorrectId ? 'pass' : 'fail',
        `Expected: "wf-generation", Found: "${generationSelect.id}"`);
        
    // Check placeholder option
    const placeholderOption = generationSelect.querySelector('option[value=""]');
    if (placeholderOption) {
        const hasDataI18n = placeholderOption.hasAttribute('data-i18n');
        const dataI18nValue = placeholderOption.getAttribute('data-i18n');
        
        logResult('Placeholder option data-i18n', hasDataI18n ? 'pass' : 'fail',
            hasDataI18n ? `Found: "${dataI18nValue}"` : 'Missing data-i18n attribute');
    } else {
        logResult('Placeholder option', 'warning', 'Not found');
    }
} else {
    logResult('Generation select element', 'warning', 'Not found in DOM (normal if not in generation flow)');
}

// Test 6: Test widget mode and flow detection
console.log('\n6. Widget Mode and Flow Tests:');
if (window.wheelFitWidget) {
    const mode = window.wheelFitWidget.mode;
    const isGenerationFlow = mode === 'byGeneration';
    
    logResult('Widget mode detection', 'pass', `Mode: "${mode}"`);
    logResult('Generation flow active', isGenerationFlow ? 'pass' : 'warning',
        isGenerationFlow ? 'Generation flow is active' : 'Generation flow is not active');
        
    if (isGenerationFlow) {
        // Test generation-specific functionality
        const hasGenerationMethod = typeof window.wheelFitWidget.onGenerationSelect === 'function';
        const hasPopulateMethod = typeof window.wheelFitWidget.populateGenerations === 'function';
        
        logResult('onGenerationSelect method', hasGenerationMethod ? 'pass' : 'fail',
            hasGenerationMethod ? 'Available' : 'Missing');
        logResult('populateGenerations method', hasPopulateMethod ? 'pass' : 'fail',
            hasPopulateMethod ? 'Available' : 'Missing');
    }
} else {
    logResult('Widget availability', 'warning', 'Widget not found');
}

// Test 7: Test translation file loading
console.log('\n7. Translation File Loading Tests:');
if (hasWheelFitI18n) {
    const hasGenerationKeys = Object.keys(EXPECTED_RUSSIAN_TRANSLATIONS).every(key => 
        key in window.WheelFitI18n
    );
    
    logResult('All generation keys present', hasGenerationKeys ? 'pass' : 'fail',
        hasGenerationKeys ? 'All keys found' : 'Some keys missing');
        
    // Check specific keys
    Object.keys(EXPECTED_RUSSIAN_TRANSLATIONS).forEach(key => {
        const hasKey = key in window.WheelFitI18n;
        const value = window.WheelFitI18n[key];
        
        logResult(`Translation key "${key}"`, hasKey ? 'pass' : 'fail',
            hasKey ? `Value: "${value}"` : 'Missing from WheelFitI18n');
    });
} else {
    logResult('Translation file loading', 'fail', 'WheelFitI18n not available');
}

// Test 8: Manual translation application test
console.log('\n8. Manual Translation Application Test:');
function testManualTranslation() {
    if (!hasTranslationFunction) {
        logResult('Manual translation test', 'fail', 'Translation function not available');
        return;
    }
    
    // Create a test element
    const testElement = document.createElement('div');
    testElement.setAttribute('data-i18n', 'label_generation');
    testElement.textContent = 'Generation';
    document.body.appendChild(testElement);
    
    // Apply translation manually
    const key = testElement.getAttribute('data-i18n');
    const originalText = testElement.textContent;
    const translation = window.t(key, originalText);
    testElement.textContent = translation;
    
    const isTranslated = translation !== originalText;
    logResult('Manual translation application', isTranslated ? 'pass' : 'warning',
        `Original: "${originalText}", Translated: "${translation}"`);
    
    // Cleanup
    document.body.removeChild(testElement);
}

testManualTranslation();

// Final summary
setTimeout(() => {
    console.log('\n=== Test Summary ===');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`⚠️ Warnings: ${testResults.warnings}`);
    
    const totalTests = testResults.passed + testResults.failed + testResults.warnings;
    const successRate = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
    
    console.log(`\n📊 Success Rate: ${successRate}%`);
    
    if (testResults.failed === 0) {
        console.log('\n🎉 All critical tests passed! Russian generation translations should be working.');
    } else {
        console.log('\n⚠️ Some tests failed. Please review the issues above.');
    }
    
    console.log('\n📋 Expected Behavior:');
    console.log('1. ✅ Generation field label shows "Поколение" in Russian');
    console.log('2. ✅ Generation placeholder shows "Выберите поколение" in Russian');
    console.log('3. ✅ Generation "select first" message shows "Сначала выберите поколение" in Russian');
    console.log('4. ✅ English fallbacks work when Russian translations are missing');
    console.log('5. ✅ All translation keys are properly mapped in templates');
    
    console.log('\n🔧 Troubleshooting:');
    console.log('- If translations not showing: Check if Russian locale is active');
    console.log('- If fallbacks not working: Check translation function implementation');
    console.log('- If DOM elements not found: Check if generation flow is active');
    console.log('- If keys missing: Check translation file loading');
}, 100);

console.log('\n=== Russian generation translations test initiated ===');
