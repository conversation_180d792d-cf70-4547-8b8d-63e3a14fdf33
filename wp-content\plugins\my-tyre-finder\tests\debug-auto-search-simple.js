/**
 * Simple debug script for auto-search functionality
 * Run in browser console on admin appearance page
 */

console.log('🔍 Auto-Search Debug');

// Check current state
const checkbox = document.getElementById('auto_search_on_last_input');
console.log('1. Checkbox found:', !!checkbox);
console.log('2. Checkbox checked:', checkbox ? checkbox.checked : 'N/A');
console.log('3. WheelFitData available:', !!window.WheelFitData);
console.log('4. WheelFitData.autoSearch:', window.WheelFitData ? window.WheelFitData.autoSearch : 'N/A');

// Check preview widget
const previewWidget = document.querySelector('#widget-preview .wheel-fit-widget');
if (previewWidget) {
    const submitButtons = previewWidget.querySelectorAll('button[type="submit"]');
    console.log('5. Submit buttons in preview:', submitButtons.length);
    
    if (window.WheelFitData && window.WheelFitData.autoSearch) {
        const hiddenButtons = Array.from(submitButtons).filter(btn => btn.classList.contains('hidden'));
        console.log('6. Hidden buttons:', hiddenButtons.length, '(should equal total)');
    } else {
        const visibleButtons = Array.from(submitButtons).filter(btn => !btn.classList.contains('hidden'));
        console.log('6. Visible buttons:', visibleButtons.length, '(should equal total)');
    }
} else {
    console.log('5. Preview widget not found');
}

// Test toggle
if (checkbox) {
    console.log('\n🔄 Testing toggle...');
    const originalState = checkbox.checked;
    
    // Toggle
    checkbox.checked = !originalState;
    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
    
    setTimeout(() => {
        console.log('After toggle - WheelFitData.autoSearch:', window.WheelFitData ? window.WheelFitData.autoSearch : 'N/A');
        
        // Restore
        checkbox.checked = originalState;
        checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        console.log('Restored to original state');
    }, 1000);
}
