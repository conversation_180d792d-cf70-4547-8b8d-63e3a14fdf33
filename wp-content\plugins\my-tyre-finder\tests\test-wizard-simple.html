<!DOCTYPE html>
<html>
<head>
    <title>Wizard Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-8">
    <h1>Wizard Test Page</h1>
    
    <!-- Simple wizard container -->
    <div id="wheel-fit-wizard" class="max-w-4xl mx-auto p-6 bg-gray-50 rounded-lg">
        <h2>Wheel & Tyre Finder</h2>
        
        <!-- Navigation -->
        <div id="wizard-nav" class="flex justify-between mt-8">
            <button id="wizard-back-btn" class="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hidden">Back</button>
            <button id="wizard-next-btn" class="px-6 py-2 bg-blue-600 text-white rounded-lg ml-auto hidden">Next</button>
        </div>
        
        <!-- Progress -->
        <div id="wizard-header" class="mb-6">
            <div id="wizard-progress-bar" class="bg-blue-600 h-2 rounded" style="width: 20%"></div>
        </div>
        
        <!-- Selection Summary -->
        <div id="wizard-selection-summary" class="hidden">
            <span id="wizard-selected-make"></span>
            <span id="wizard-selected-model"></span>
            <span id="wizard-selected-year"></span>
        </div>
        
        <!-- Steps -->
        <div id="wizard-step-1" class="wizard-step">
            <h3>Choose a make</h3>
            <div id="wizard-makes-grid" class="grid grid-cols-4 gap-4">
                <button class="p-4 border rounded hover:bg-blue-100">BMW</button>
                <button class="p-4 border rounded hover:bg-blue-100">Audi</button>
                <button class="p-4 border rounded hover:bg-blue-100">Mercedes</button>
                <button class="p-4 border rounded hover:bg-blue-100">Toyota</button>
            </div>
        </div>
        
        <div id="wizard-step-2" class="wizard-step hidden">
            <h3>Choose a model</h3>
            <div id="wizard-models-list" class="grid grid-cols-3 gap-4">
                <!-- Models will be loaded here -->
            </div>
            <div id="wizard-models-count" class="text-sm text-gray-500"></div>
        </div>
        
        <div id="wizard-step-3" class="wizard-step hidden">
            <h3>Choose a year</h3>
            <div id="wizard-years-list" class="grid grid-cols-4 gap-4">
                <!-- Years will be loaded here -->
            </div>
            <div id="wizard-years-count" class="text-sm text-gray-500"></div>
        </div>
        
        <div id="wizard-step-4" class="wizard-step hidden">
            <h3>Choose a modification</h3>
            <div id="wizard-modifications-list" class="grid grid-cols-2 gap-4">
                <!-- Modifications will be loaded here -->
            </div>
            <div id="wizard-modifications-count" class="text-sm text-gray-500"></div>
        </div>
        
        <div id="wizard-results" class="wizard-step hidden">
            <h3>Results</h3>
            <div id="search-results">
                <!-- Results will be loaded here -->
            </div>
        </div>
    </div>
    
    <!-- Mock AJAX data -->
    <script>
        window.WheelFitData = {
            ajaxurl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce'
        };
        
        // Mock translation function
        window.t = function(key, fallback) {
            return fallback || key;
        };
        
        window.applyStaticTranslations = function() {
            console.log('Mock translation applied');
        };
    </script>
    
    <!-- Load wizard script -->
    <script src="assets/js/wizard.js"></script>
    
    <script>
        // Test make selection
        setTimeout(() => {
            console.log('Testing make selection...');
            const makeButtons = document.querySelectorAll('#wizard-makes-grid button');
            if (makeButtons.length > 0) {
                makeButtons[0].addEventListener('click', () => {
                    console.log('Make button clicked!');
                    // Simulate next step
                    document.getElementById('wizard-step-1').classList.add('hidden');
                    document.getElementById('wizard-step-2').classList.remove('hidden');
                    document.getElementById('wizard-next-btn').classList.remove('hidden');
                    document.getElementById('wizard-back-btn').classList.remove('hidden');
                });
            }
        }, 1000);
    </script>
</body>
</html>
