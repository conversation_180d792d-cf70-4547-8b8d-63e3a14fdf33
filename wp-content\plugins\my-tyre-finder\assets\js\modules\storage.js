// LocalStorageHandler module (restored)
(function(global){
 class LocalStorageHandler{
  constructor(){
    this.searchKey='wheel_fit_searches';
    this.garageKey='wheel_fit_garage';
    this.maxSearchHistory=10;
    this.maxGarageItems=50;
  }

  saveSearch(data){
    try{
      const arr=this.getSearchHistory();
      const n={...data,timestamp:Date.now(),id:this.generateId()};
      // Handle both generation and year-based flows for duplicate detection
      const f=arr.filter(s=>{
        const sameBasic = s.make===n.make && s.model===n.model && s.modification===n.modification;
        if (n.generation) {
          // Generation flow
          return !(sameBasic && s.generation===n.generation);
        } else {
          // Year flow
          return !(sameBasic && s.year===n.year);
        }
      });
      f.unshift(n);
      if(f.length>this.maxSearchHistory)f.splice(this.maxSearchHistory);
      localStorage.setItem(this.searchKey,JSON.stringify(f));
    }catch(e){console.error('LS save',e);}
  }

  getSearchHistory(){
    try{return JSON.parse(localStorage.getItem(this.searchKey)||'[]');}catch(e){return[];}
  }

  addToGarage(size){
    try{
      const g=this.getGarage();
      const n={...size,id:Math.random().toString(36).substr(2,9)};

      // Improved duplicate detection: check both tire sizes AND vehicle info
      const isDuplicate = g.some(item => {
        const sameTires = item.tire_full === n.tire_full &&
                         (item.rear_tire_full || '') === (n.rear_tire_full || '');

        // Check vehicle match based on flow type
        let sameVehicle;
        if (n.generation) {
          // Generation flow
          sameVehicle = item.make === n.make &&
                       item.model === n.model &&
                       item.generation === n.generation &&
                       item.modification === n.modification;
        } else {
          // Year flow
          sameVehicle = item.make === n.make &&
                       item.model === n.model &&
                       item.year === n.year &&
                       item.modification === n.modification;
        }

        return sameTires && sameVehicle;
      });

      if(isDuplicate) return false;

      g.unshift(n);
      if(g.length>this.maxGarageItems)g.splice(this.maxGarageItems);
      localStorage.setItem(this.garageKey,JSON.stringify(g));
      return true;
    }catch(e){console.error('LS garage',e);return false;}
  }

  getGarage(){
    try{return JSON.parse(localStorage.getItem(this.garageKey)||'[]');}catch(e){return[];}
  }

  generateId(){
    return Math.random().toString(36).substr(2,9);
  }

  clearSearchHistory(){
    localStorage.removeItem(this.searchKey);
  }

  clearGarage(){
    localStorage.removeItem(this.garageKey);
  }

  removeFromGarage(itemId){
    try{
      const g=this.getGarage();
      const filtered = g.filter(item => item.id !== itemId);
      localStorage.setItem(this.garageKey,JSON.stringify(filtered));
      return true;
    }catch(e){console.error('LS remove garage',e);return false;}
  }

  // Migration function for legacy garage data
  migrateGarageData() {
    try {
      const garage = this.getGarage();
      let migrated = false;

      const migratedGarage = garage.map(item => {
        if (!item.garage_version) {
          console.log('[Garage Migration] Migrating legacy item:', item);
          migrated = true;
          return {
            ...item,
            garage_version: '2.0',
            migrated_timestamp: Date.now(),
            // Mark as legacy if missing vehicle data
            is_legacy: (!item.make || !item.model || (!item.year && !item.generation) || !item.modification)
          };
        }
        return item;
      });

      if (migrated) {
        localStorage.setItem(this.garageKey, JSON.stringify(migratedGarage));
        console.log('[Garage Migration] Migration completed');
      }

      return migratedGarage;
    } catch (error) {
      console.error('Error migrating garage data:', error);
      return this.getGarage();
    }
  }
 }
 global.LocalStorageHandler=global.LocalStorageHandler||LocalStorageHandler;
})(typeof window!=='undefined'?window:this);