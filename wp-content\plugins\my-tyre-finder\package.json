{"name": "my-tyre-finder", "version": "1.0.0", "description": "Tyre & wheel fitment finder plugin for WooCommerce.", "main": "tailwind.config.js", "directories": {"test": "tests"}, "scripts": {"build": "npm run build:widget && npm run build:admin", "build:widget": "npx tailwindcss -c tailwind.config.js -i assets/css/wheel-fit-shared.src.css -o assets/css/wheel-fit-shared.css --minify", "build:admin": "npx tailwindcss -c tailwind.admin.config.js -i assets/css/admin-theme-panel.src.css -o assets/css/admin-theme-panel.css --minify --verbose", "watch": "npx tailwindcss -c tailwind.config.js -i assets/css/wheel-fit-shared.src.css -o assets/css/wheel-fit-shared.css --watch", "watch:admin": "npx tailwindcss -c tailwind.admin.config.js -i assets/css/admin-theme-panel.src.css -o assets/css/admin-theme-panel.css --watch"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.15"}}