/**
 * Test script to verify theme activation and preview fixes
 * Run this in browser console on the WordPress admin Appearance page
 */

(function() {
    'use strict';

    console.log('🎨 === TESTING THEME ACTIVATION & PREVIEW FIXES ===');

    if (typeof wpApiSettings === 'undefined') {
        console.error('❌ wpApiSettings not found - run this on the admin Appearance page');
        return;
    }

    // Check if ThemePresetsPanel is available
    let themePanel = null;
    if (window.ThemePresetsPanel) {
        themePanel = window.ThemePresetsPanel;
    } else {
        console.warn('⚠️ ThemePresetsPanel not found in global scope');
    }

    // Test 1: Check iframe presence and accessibility
    function testIframePresence() {
        console.log('\n1️⃣ Testing iframe presence and accessibility...');
        
        const previewFrame = document.getElementById('widget-preview');
        if (!previewFrame) {
            console.warn('⚠️ Preview iframe not found - preview tests will be skipped');
            return false;
        }
        
        console.log('✅ Preview iframe found:', previewFrame);
        
        // Test iframe accessibility
        try {
            const doc = previewFrame.contentDocument || 
                       (previewFrame.contentWindow && previewFrame.contentWindow.document);
            
            if (doc) {
                console.log('✅ Iframe document accessible');
                console.log('Iframe document ready state:', doc.readyState);
                return true;
            } else {
                console.warn('⚠️ Iframe document not accessible (may be cross-origin or not loaded)');
                return false;
            }
        } catch (error) {
            console.warn('⚠️ Cannot access iframe document:', error.message);
            return false;
        }
    }

    // Test 2: Test theme activation without preview errors
    async function testThemeActivation() {
        console.log('\n2️⃣ Testing theme activation...');
        
        try {
            const apiBase = wpApiSettings.root + 'wheel-size/v1/themes';
            
            // Get available themes
            const themesResponse = await fetch(apiBase, {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (!themesResponse.ok) {
                console.error('❌ Failed to fetch themes');
                return false;
            }

            const themesData = await themesResponse.json();
            const availableThemes = Object.keys(themesData.themes);
            console.log('Available themes:', availableThemes);

            // Test activating each theme
            for (const themeSlug of availableThemes) {
                console.log(`🎯 Testing activation of ${themeSlug} theme...`);
                
                const activateResponse = await fetch(apiBase + '/active', {
                    method: 'PUT',
                    headers: {
                        'X-WP-Nonce': wpApiSettings.nonce,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ slug: themeSlug })
                });

                if (activateResponse.ok) {
                    const result = await activateResponse.json();
                    console.log(`✅ ${themeSlug} theme activated successfully:`, result);
                } else {
                    const error = await activateResponse.text();
                    console.error(`❌ ${themeSlug} theme activation failed:`, error);
                    return false;
                }
                
                // Small delay between activations
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            return true;
        } catch (error) {
            console.error('❌ Theme activation test failed:', error);
            return false;
        }
    }

    // Test 3: Test preview application methods
    function testPreviewMethods() {
        console.log('\n3️⃣ Testing preview methods...');
        
        if (!themePanel) {
            console.warn('⚠️ Cannot test preview methods - ThemePresetsPanel not available');
            return false;
        }

        // Test isPreviewIframeReady method
        if (typeof themePanel.isPreviewIframeReady === 'function') {
            const isReady = themePanel.isPreviewIframeReady();
            console.log('Preview iframe ready:', isReady ? '✅ YES' : '⚠️ NO');
        } else {
            console.warn('⚠️ isPreviewIframeReady method not found');
        }

        // Test applyThemeToPreview method with mock theme
        if (typeof themePanel.applyThemeToPreview === 'function') {
            console.log('Testing applyThemeToPreview with mock theme...');
            
            const mockTheme = {
                name: 'Test Theme',
                '--wsf-primary': '#ff0000',
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#000000'
            };
            
            try {
                themePanel.applyThemeToPreview(mockTheme);
                console.log('✅ applyThemeToPreview executed without errors');
            } catch (error) {
                console.error('❌ applyThemeToPreview failed:', error);
                return false;
            }
        } else {
            console.warn('⚠️ applyThemeToPreview method not found');
        }

        return true;
    }

    // Test 4: Test error handling
    function testErrorHandling() {
        console.log('\n4️⃣ Testing error handling...');
        
        if (!themePanel) {
            console.warn('⚠️ Cannot test error handling - ThemePresetsPanel not available');
            return false;
        }

        // Test with null theme
        try {
            themePanel.applyThemeToPreview(null);
            console.log('✅ Handled null theme gracefully');
        } catch (error) {
            console.error('❌ Failed to handle null theme:', error);
        }

        // Test with invalid theme
        try {
            themePanel.applyThemeToPreview({ invalid: 'theme' });
            console.log('✅ Handled invalid theme gracefully');
        } catch (error) {
            console.error('❌ Failed to handle invalid theme:', error);
        }

        return true;
    }

    // Test 5: Simulate theme card clicks
    function testThemeCardClicks() {
        console.log('\n5️⃣ Testing theme card clicks...');
        
        const themeCards = document.querySelectorAll('.wsf-theme-card[data-theme-slug]');
        if (themeCards.length === 0) {
            console.warn('⚠️ No theme cards found');
            return false;
        }

        console.log(`Found ${themeCards.length} theme card(s)`);
        
        themeCards.forEach((card, index) => {
            const themeSlug = card.dataset.themeSlug;
            console.log(`Theme card ${index + 1}: ${themeSlug}`);
            
            // Highlight the card briefly
            card.style.border = '2px solid blue';
            setTimeout(() => {
                card.style.border = '';
            }, 1000 + (index * 500));
        });

        console.log('💡 Theme cards highlighted - try clicking them to test activation');
        return true;
    }

    // Run all tests
    async function runAllTests() {
        console.log('🚀 Starting comprehensive theme system tests...\n');
        
        const iframeTest = testIframePresence();
        const activationTest = await testThemeActivation();
        const previewTest = testPreviewMethods();
        const errorTest = testErrorHandling();
        const cardTest = testThemeCardClicks();
        
        console.log('\n📊 === TEST RESULTS ===');
        console.log(`Iframe Presence: ${iframeTest ? '✅ PASS' : '⚠️ WARN'}`);
        console.log(`Theme Activation: ${activationTest ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`Preview Methods: ${previewTest ? '✅ PASS' : '⚠️ WARN'}`);
        console.log(`Error Handling: ${errorTest ? '✅ PASS' : '⚠️ WARN'}`);
        console.log(`Theme Cards: ${cardTest ? '✅ PASS' : '⚠️ WARN'}`);
        
        if (activationTest) {
            console.log('\n🎉 Theme activation is working! Preview errors should be handled gracefully.');
        } else {
            console.log('\n⚠️ Some issues detected. Check the details above.');
        }
        
        console.log('\n💡 Next steps:');
        console.log('1. Try clicking theme cards in the admin panel');
        console.log('2. Check browser console for any remaining errors');
        console.log('3. Verify that themes activate even if preview fails');
    }

    // Start the tests
    runAllTests();

})();
