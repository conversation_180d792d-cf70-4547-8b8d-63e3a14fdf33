<?php
declare(strict_types=1);

namespace MyTyreFinder\Admin;

/**
 * Экран «Wheel-Size → Analytics»
 */
final class AnalyticsPage
{
    public const SLUG = 'wheel-size-analytics';

    public function register(): void
    {
        // add_action('admin_menu', [$this, 'add_menu']); // temporarily disabled: hide Analytics page
        add_action('admin_init', [$this, 'register_settings']);
        // assets при необходимости
        // add_action('admin_enqueue_scripts', [$this, 'enqueue_assets']);
    }

    /* ---------- Settings API ---------- */
    public function register_settings(): void
    {
        register_setting('wheel_size_analytics_settings', 'wheel_size_gtm_id');
        register_setting('wheel_size_analytics_settings', 'wheel_size_ga_id');
        register_setting('wheel_size_analytics_settings', 'wheel_size_enable_tracking');
    }

    /* ---------- Меню ---------- */
    public function add_menu(): void
    {
        add_submenu_page(
            'wheel-size',
            'Wheel-Size - Analytics',
            'Analytics',
            'manage_options',
            self::SLUG,
            [$this, 'render_page']
        );
    }

    /* ---------- Страница ---------- */
    public function render_page(): void
    {
        // --- Range filter handling ---
        $rangeParam = sanitize_text_field($_GET['range'] ?? '7');
        $rangeDays  = $rangeParam === 'all' ? null : (int) $rangeParam;

        $stats = $this->collect_stats($rangeDays);

        /* ---------- Handle actions: export CSV / reset stats ---------- */
        if (isset($_GET['export']) && $_GET['export']==='csv') {
            $this->export_csv($rangeParam, $stats);
            return;
        }

        if (isset($_GET['reset']) && $_GET['reset']==='1') {
            global $wpdb;

            // 1) clear cached transients
            $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wsf_analytics_%' OR option_name LIKE '_transient_timeout_wsf_analytics_%'");

            // 2) try truncate table; if fails (no privilege) fall back to DELETE
            $table = $wpdb->prefix . 'wsf_search_stats';
            $ok = $wpdb->query("TRUNCATE TABLE {$table}");
            if ($ok === false) {
                $wpdb->query("DELETE FROM {$table}");
            }

            // 3) flush object cache just in case
            if (function_exists('wp_cache_flush')) {
                wp_cache_flush();
            }

            // 4) set notice and redirect
            set_transient('wsf_stats_reset', 1, 30);
            wp_safe_redirect(remove_query_arg('reset'));
            exit;
        }

        // === Success notice after reset ===
        if (get_transient('wsf_stats_reset')) {
            echo '<div class="notice notice-success is-dismissible"><p>'.esc_html__('Search statistics have been reset.','wheel-size').'</p></div>';
            delete_transient('wsf_stats_reset');
        }

        ?>
        <div class="wrap">
            <h1><?php esc_html_e('Wheel-Size – Analytics', 'wheel-size'); ?></h1>

            <form method="get" style="margin:0 0 12px 0;">
                <input type="hidden" name="page" value="<?php echo esc_attr(self::SLUG); ?>">
                <select name="range" onchange="this.form.submit()">
                    <?php foreach ( [ '7' => __('Last 7 days','wheel-size'), '30' => __('30 days','wheel-size'), 'all' => __('All','wheel-size') ] as $k => $label ) : ?>
                        <option value="<?php echo esc_attr($k); ?>" <?php selected($rangeParam, $k); ?>><?php echo esc_html($label); ?></option>
                    <?php endforeach; ?>
                </select>
            </form>
            <?php
                // Reporting period caption
                if ($rangeDays) {
                    $end = current_time('timestamp');
                    $start = $end - DAY_IN_SECONDS * $rangeDays;
                } else {
                    // All time – use first log file date if available
                    $files = glob(LogsPage::LOG_DIR.'/wheel-size-*.log');
                    natsort($files);
                    $first = $files ? basename(reset($files),' .log') : '';
                    $start = $first ? strtotime(substr($first, 11)) : null;
                    $end = current_time('timestamp');
                }
                if ($start) {
                    echo '<p class="description" style="margin-top:4px;">'.
                         esc_html__('Reporting period','wheel-size').': '.
                         esc_html(date_i18n('j M Y',$start).' – '.date_i18n('j M Y',$end)).
                         '</p>';
                }
            ?>

            <?php $this->render_summary_cards($stats); ?>

            <?php if (!$stats['total']) {
                $this->render_empty( __('No search data yet','wheel-size') );
            } ?>

            <?php $this->render_chart_daily(30); ?>

            <hr style="border:none;border-top:1px solid #e5e7eb;margin:24px 0;">

            <div class="wsf-layout" style="display:flex;gap:24px;align-items:flex-start;">
                <div style="flex:0 0 320px;">
                    <?php $this->render_top_table(__('Top Requested Makes','wheel-size'), $stats['makes'], 10, 'Make'); ?>
                    <?php $this->render_top_table(__('Top Requested Models','wheel-size'), $stats['models'], 10, 'Model'); ?>
                </div>
                <div style="flex:1 1 auto;min-width:0;">
                    <?php $this->render_top_table(__('Top Modifications','wheel-size'), $stats['mods'], 10, 'Modification'); ?>
                </div>
            </div>

            <?php $this->render_recent(20); ?>

            <div class="wsf-actions" style="margin-top:24px;display:flex;gap:12px;">
                <a href="<?php echo esc_url(add_query_arg('export','csv')); ?>" class="button">Export CSV</a>
                <a href="<?php echo esc_url(add_query_arg('reset','1')); ?>" class="button button-secondary" style="color:#dc2626;border-color:#dc2626;">Reset Stats</a>
            </div>
        </div>
        <?php
    }

    /**
     * Parse log files and collect basic statistics
     * @return array{requests:int,responses:int,cached:int,errors:int,makes:array<string,int>}
     */
    private function collect_stats(?int $days = null): array
    {
        global $wpdb;
        $table = $wpdb->prefix . 'wsf_search_stats';

        $where = '';
        if ($days) {
            $where = $wpdb->prepare('WHERE searched_at >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL %d DAY)', $days);
        }

        // helper closure
        $fetch = function(string $sql) use ($wpdb){
            return $wpdb->get_results($sql, ARRAY_A) ?: [];
        };

        $make_rows = $fetch("SELECT make AS label, COUNT(*) hits FROM {$table} {$where} GROUP BY make ORDER BY hits DESC LIMIT 20");
        $model_rows= $fetch("SELECT CONCAT(make,' ',model) AS label, COUNT(*) hits FROM {$table} {$where} GROUP BY make,model ORDER BY hits DESC LIMIT 20");
        $rimWhere   = $where ? "$where AND rim>0"   : 'WHERE rim>0';
        $widthWhere = $where ? "$where AND width>0" : 'WHERE width>0';
        $rim_rows  = $fetch("SELECT rim AS label, COUNT(*) hits FROM {$table} {$rimWhere} GROUP BY rim ORDER BY hits DESC LIMIT 20");
        $width_rows= $fetch("SELECT width AS label, COUNT(*) hits FROM {$table} {$widthWhere} GROUP BY width ORDER BY hits DESC LIMIT 20");
        $mods_rows = $fetch("SELECT CONCAT(make,' ',model,' ',modification) AS label, COUNT(*) hits FROM {$table} {$where} GROUP BY make,model,modification ORDER BY hits DESC LIMIT 20");

        // convert to assoc array label=>hits
        $toAssoc = static function(array $rows): array {
            $out=[]; foreach ($rows as $r){ $out[$r['label']] = (int)$r['hits']; } return $out;
        };

        $total = (int) $wpdb->get_var("SELECT COUNT(*) FROM {$table} {$where}");
        $unique_combo = (int) $wpdb->get_var("SELECT COUNT(DISTINCT CONCAT(make,'|',model,'|',rim,'|',width)) FROM {$table} {$where}");

        $avg7 = (int) $wpdb->get_var("SELECT COUNT(*)/7 FROM {$table} WHERE searched_at >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL 7 DAY)");

        return [
            'total'  => $total,
            'unique' => $unique_combo,
            'makes'  => $toAssoc($make_rows),
            'models' => $toAssoc($model_rows),
            'rims'   => $toAssoc($rim_rows),
            'widths' => $toAssoc($width_rows),
            'mods'   => $toAssoc($mods_rows),
            'avg7'   => $avg7,
        ];
    }

    /* ---------- UI helpers ---------- */
    private function render_summary_cards(array $stats): void
    {
        ?>
        <style>
            .wsf-cards{display:grid;grid-template-columns:repeat(auto-fit,minmax(160px,1fr));gap:16px;margin:24px 0;}
            .wsf-card{background:#fff;border:none;border-radius:8px;padding:18px 14px;text-align:center;box-shadow:0 1px 3px rgba(0,0,0,.06);}
            .wsf-card h3{margin:0;font-size:28px;line-height:1;font-weight:600;color:#1f2937;}
            .wsf-card p{margin:6px 0 0;font-size:12px;color:#6b7280;letter-spacing:.3px;text-transform:uppercase;}
            .wsf-gray h3{color:#6b7280}
            .wsf-blue h3{color:#2563eb}
            .wsf-green h3{color:#16a34a}
            .wsf-red h3{color:#dc2626}
        </style>

        <?php
        ?>
        <div class="wsf-cards">
            <div class="wsf-card wsf-blue">
                <h3><?php echo number_format_i18n($stats['total']); ?></h3>
                <p><?php esc_html_e('Total Searches','wheel-size'); ?></p>
            </div>
            <div class="wsf-card wsf-gray">
                <h3><?php echo number_format_i18n($stats['unique']); ?></h3>
                <p><?php esc_html_e('Unique Combinations','wheel-size'); ?></p>
            </div>
            <div class="wsf-card wsf-green">
                <h3><?php echo number_format_i18n($stats['avg7']); ?></h3>
                <p><?php esc_html_e('Avg / day (7d)','wheel-size'); ?></p>
            </div>
        </div>
        <?php
    }

    private function render_top_table(string $title, array $top, int $limit, string $label): void
    {
        if (empty($top)) {
            echo '<style>.wsf-empty{max-width:360px;border:1px solid #e5e7eb;border-radius:8px;padding:28px 12px;text-align:center;color:#9ca3af;font-style:italic;}</style>';
            echo '<div class="wsf-empty">'.esc_html__('Not enough data yet','wheel-size').'</div>';
            return;
        }
        ?>
        <h2 style="margin-top:32px;font-weight:500;"><?php echo esc_html($title); ?></h2>
        <style>.wsf-scroll-table tbody{display:block;max-height:260px;overflow:auto}.wsf-scroll-table thead, .wsf-scroll-table tbody tr{display:table;width:100%;table-layout:fixed}</style>
        <div class="wsf-scroll-table">
        <table class="widefat striped" style="max-width:360px">
            <thead><tr><th><?php echo esc_html($label); ?></th><th style="width:70px;text-align:right;"><?php esc_html_e('Hits','wheel-size'); ?></th></tr></thead>
            <tbody>
                <?php foreach ($top as $name=>$cnt): ?>
                <tr>
                    <td><code><?php echo esc_html($name); ?></code></td>
                    <td style="text-align:right"><?php echo number_format_i18n($cnt); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table></div>
        <?php
    }

    private function render_bar_chart(string $title, array $data, int $limit, string $unit): void
    {
        $data = array_slice($data, 0, $limit, true);
        ?>
        <h2 style="margin-top:32px;font-weight:500;"><?php echo esc_html($title); ?></h2>
        <style>
            .wsf-chart      {max-width:360px;min-height:220px;border-radius:8px;padding:12px;background:#fff;box-shadow:0 1px 3px rgba(0,0,0,.06);}
            .wsf-row        {display:flex;gap:8px;align-items:center;margin:6px 0;}
            .wsf-bar        {flex:1 1 auto;height:14px;background:#e5e7eb;border-radius:4px;position:relative;}
            .wsf-bar span   {display:block;height:100%;background:#2563eb;border-radius:4px;}
            .wsf-empty      {text-align:center;padding:28px 12px;color:#9ca3af;font-style:italic;}
        </style>

        <div class="wsf-chart">
        <?php
        if (empty($data)) {
            echo '<div class="wsf-empty">'.esc_html__('Not enough data yet','wheel-size').'</div></div>';
            return;
        }

        $max = max($data);
        foreach ($data as $label=>$cnt):
            $width = $max ? round($cnt/$max*100) : 0;
        ?>
            <div class="wsf-row">
                <code style="width:60px"><?php echo esc_html($label.$unit); ?></code>
                <div class="wsf-bar"><span style="width:<?php echo $width; ?>%"></span></div>
                <span style="width:38px;text-align:right;"><?php echo number_format_i18n($cnt); ?></span>
            </div>
        <?php endforeach; ?>
        </div>
        <?php
    }

    private function render_error_breakdown(array $codes): void
    {
        if (empty($codes)) return;
        $total = array_sum($codes);
        ?>
        <h2 style="margin-top:32px;"><?php esc_html_e('Error breakdown','wheel-size'); ?></h2>
        <table class="widefat striped" style="max-width:260px">
            <thead><tr><th><?php esc_html_e('Status','wheel-size'); ?></th><th style="width:70px;text-align:right;"><?php esc_html_e('Count','wheel-size'); ?></th></tr></thead>
            <tbody>
                <?php foreach ($codes as $code=>$cnt): ?>
                <tr>
                    <td><code><?php echo esc_html($code); ?></code></td>
                    <td style="text-align:right"><?php echo number_format_i18n($cnt); ?></td>
                </tr>
                <?php endforeach; ?>
                <tr>
                    <th><?php esc_html_e('Total','wheel-size'); ?></th>
                    <th style="text-align:right"><?php echo number_format_i18n($total); ?></th>
                </tr>
            </tbody>
        </table>
        <?php
    }

    /**
     * Simple illustrated empty-state
     */
    private function render_empty(string $message): void
    {
        ?>
        <style>
            .wsf-empty-wrap{
                display:flex;flex-direction:column;align-items:center;
                justify-content:center;gap:16px;
                border:1px dashed #d1d5db;border-radius:12px;
                background:#f3f4f6;min-height:220px;margin-top:24px;
            }
            .wsf-empty-wrap svg{width:56px;height:56px;opacity:.3}
            .wsf-empty-wrap p{margin:0;color:#6b7280;font-size:14px}
            .wsf-empty-cta{margin-top:8px;font-size:13px}
        </style>
        <div class="wsf-empty-wrap">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.6">
              <circle cx="12" cy="12" r="9" stroke-dasharray="2 2"/>
              <circle cx="12" cy="12" r="4"/>
            </svg>

            <p><?php echo esc_html( $message ); ?></p>

            <p class="wsf-empty-cta">
                <a href="<?php echo esc_url( home_url('/') ); ?>" target="_blank" class="button button-primary">
                    <?php esc_html_e('Try a search on the front-end','wheel-size'); ?>
                </a>
            </p>
        </div>
        <?php
    }

    /* ---------- CSV Export ---------- */
    private function export_csv(string $range, array $stats): void
    {
        nocache_headers();
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=wheel-size-analytics-'.$range.'.csv');
        $out = fopen('php://output','w');
        fputcsv($out, ['Metric','Value']);
        fputcsv($out, ['Total Searches', $stats['total']]);
        fputcsv($out, ['Unique Combinations', $stats['unique']]);
        foreach ($stats['makes'] as $k=>$v) fputcsv($out, ['Top Make: '.$k, $v]);
        fclose($out);
        exit;
    }

    /* ---------- Daily chart ---------- */
    private function render_chart_daily(int $days = 30): void
    {
        $data = $this->get_daily_counts($days);
        echo '<h2 style="margin-top:32px;font-weight:500;">'.esc_html__('Daily Searches','wheel-size').'</h2>';
        if (empty($data)) { $this->render_empty(__('Not enough data yet','wheel-size')); return; }

        $max = max(array_column($data,'c'));
        echo '<style>.wsf-chart-daily{display:flex;gap:2px;align-items:flex-end;height:120px;max-width:100%;}.wsf-bar-day{flex:1 1 auto;background:#2563eb;border-radius:2px;}</style>';
        echo '<div class="wsf-chart-daily">';
        foreach ($data as $row){
            $h = $max ? round($row['c']/$max*100) : 0;
            echo '<span class="wsf-bar-day" style="height:'.$h.'%" title="'.esc_attr($row['d'].' – '.$row['c']).'"></span>';
        }
        echo '</div>';
    }

    private function get_daily_counts(int $days): array
    {
        global $wpdb;
        $sql = $wpdb->prepare("SELECT DATE(searched_at) d, COUNT(*) c FROM {$wpdb->prefix}wsf_search_stats WHERE searched_at >= DATE_SUB(UTC_TIMESTAMP(), INTERVAL %d DAY) GROUP BY d ORDER BY d", $days);
        return $wpdb->get_results($sql, ARRAY_A) ?: [];
    }

    /* ---------- Recent searches ---------- */
    private function render_recent(int $limit = 20): void
    {
        global $wpdb;
        $rows = $wpdb->get_results($wpdb->prepare("SELECT searched_at, make, model FROM {$wpdb->prefix}wsf_search_stats ORDER BY searched_at DESC LIMIT %d", $limit), ARRAY_A);
        echo '<h2 style="margin-top:32px;font-weight:500;">'.esc_html__('Recent Searches','wheel-size').'</h2>';
        if (empty($rows)) { $this->render_empty(__('No data yet','wheel-size')); return; }
        echo '<style>.wsf-table{max-width:480px;border:1px solid #e5e7eb;border-radius:8px;background:#fff;box-shadow:0 1px 3px rgba(0,0,0,.06);overflow:auto}</style>';
        echo '<table class="widefat striped wsf-table"><thead><tr><th>'.esc_html__('Time','wheel-size').'</th><th>'.esc_html__('Make','wheel-size').'</th><th>'.esc_html__('Model','wheel-size').'</th></tr></thead><tbody>';
        foreach ($rows as $r){
            echo '<tr><td>'.esc_html(date_i18n('Y-m-d H:i',strtotime($r['searched_at']))).'</td><td><code>'.esc_html($r['make']).'</code></td><td><code>'.esc_html($r['model']).'</code></td></tr>';
        }
        echo '</tbody></table>';
    }
} 