/* ========================================
   BUTTON SIZE & SPACING UNIFICATION
   Inline (1×4) and Step-by-Step layouts
   ======================================== */

/* Выравнивание высоты кнопки "Find Sizes" с полями формы в Inline layout */
.wheel-fit-widget .ws-submit .btn-primary,
.wsf-finder-widget .ws-submit .btn-primary {
  /* Устанавливаем точно такую же высоту, как у селекторов */
  height: var(--ws-control-height, 44px) !important;
  min-height: var(--ws-control-height, 44px) !important;
  max-height: var(--ws-control-height, 44px) !important;

  /* Убираем вертикальные отступы, чтобы контент поместился в заданную высоту */
  padding-top: 0 !important;
  padding-bottom: 0 !important;

  /* Сохраняем горизонтальные отступы для красоты */
  padding-left: 1.5rem !important; /* 24px */
  padding-right: 1.5rem !important; /* 24px */

  /* Центрируем содержимое по вертикали */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /* Убираем лишние отступы и границы */
  box-sizing: border-box !important;

  /* Сохраняем остальные стили кнопки */
  font-size: 0.875rem !important; /* 14px - как у селекторов */
  font-weight: 600 !important;
  line-height: 1.3 !important; /* Как у селекторов */

  /* Убираем трансформации, которые могут влиять на высоту */
  transform: none !important;
}

/* Состояние hover - убираем трансформацию, которая может изменить высоту */
.wheel-fit-widget .ws-submit .btn-primary:hover:not(:disabled),
.wsf-finder-widget .ws-submit .btn-primary:hover:not(:disabled) {
  transform: none !important; /* Убираем translateY(-1px) */
  /* Сохраняем изменение цвета при hover */
  background-color: var(--wsf-hover) !important;
}

/* Состояние focus - убираем изменения высоты */
.wheel-fit-widget .ws-submit .btn-primary:focus,
.wsf-finder-widget .ws-submit .btn-primary:focus {
  transform: none !important;
  /* Сохраняем focus ring */
  outline: none !important;
  box-shadow: 0 0 0 2px var(--wsf-primary), 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
}

/* Лоадер внутри кнопки - подгоняем под новую высоту */
.ws-submit .btn-primary #search-loader {
  height: 16px !important;
  width: 16px !important;
}

.ws-submit .btn-primary #search-loader > div {
  height: 16px !important;
  width: 16px !important;
}

/* Адаптивность для Inline layout */
@media (max-width: 639px) {
  /* На мобильных кнопка может быть чуть выше для удобства нажатия */
  .wheel-fit-widget .ws-submit .btn-primary,
  .wsf-finder-widget .ws-submit .btn-primary {
    height: 48px !important; /* Чуть выше на мобильных */
    min-height: 48px !important;
    max-height: 48px !important;
    font-size: 0.9rem !important; /* Чуть больше шрифт */
  }
}

/* ========================================
   STEP-BY-STEP LAYOUT - BUTTON SIZE & SPACING UNIFICATION
   ======================================== */

/* Выравнивание высоты кнопки "Search" с полями формы в Step-by-Step layout */
.wheel-fit-widget #wheel-fit-form .btn-primary,
.wsf-finder-widget #wheel-fit-form .btn-primary {
  /* Устанавливаем точно такую же высоту, как у селекторов */
  height: var(--ws-control-height, 44px) !important;
  min-height: var(--ws-control-height, 44px) !important;
  max-height: var(--ws-control-height, 44px) !important;

  /* Убираем вертикальные отступы, чтобы контент поместился в заданную высоту */
  padding-top: 0 !important;
  padding-bottom: 0 !important;

  /* Сохраняем горизонтальные отступы как в Live Preview */
  padding-left: 2.5rem !important; /* 40px - как px-10 */
  padding-right: 2.5rem !important; /* 40px - как px-10 */

  /* Обеспечиваем полную ширину как в Live Preview */
  width: 100% !important;
  max-width: 100% !important;

  /* Центрируем содержимое по вертикали */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /* Убираем лишние отступы и границы */
  box-sizing: border-box !important;

  /* Сохраняем остальные стили кнопки */
  font-size: 0.875rem !important; /* 14px - как у селекторов */
  font-weight: 600 !important;
  line-height: 1.3 !important; /* Как у селекторов */

  /* Убираем трансформации, которые могут влиять на высоту */
  transform: none !important;
}

/* Состояние hover - убираем трансформацию, которая может изменить высоту */
.wheel-fit-widget #wheel-fit-form .btn-primary:hover:not(:disabled),
.wsf-finder-widget #wheel-fit-form .btn-primary:hover:not(:disabled) {
  transform: none !important; /* Убираем translateY(-1px) */
  /* Сохраняем изменение цвета при hover */
  background-color: var(--wsf-hover) !important;
}

/* Состояние focus - убираем изменения высоты */
.wheel-fit-widget #wheel-fit-form .btn-primary:focus,
.wsf-finder-widget #wheel-fit-form .btn-primary:focus {
  transform: none !important;
  /* Сохраняем focus ring */
  outline: none !important;
  box-shadow: 0 0 0 2px var(--wsf-primary), 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
}

/* Контейнер кнопки поиска в Step-by-Step - уменьшаем отступ сверху */
.wheel-fit-widget #wheel-fit-form .mt-6,
.wsf-finder-widget #wheel-fit-form .mt-6 {
  margin-top: 1rem !important; /* Уменьшаем с 24px (mt-6) до 16px (mt-4) */
}

/* Лоадер внутри кнопки Step-by-Step - подгоняем под новую высоту */
#wheel-fit-form .btn-primary #search-loader {
  height: 16px !important;
  width: 16px !important;
}

#wheel-fit-form .btn-primary #search-loader > div {
  height: 16px !important;
  width: 16px !important;
}

/* Адаптивность для Step-by-Step layout */
@media (max-width: 639px) {
  /* На мобильных кнопка может быть чуть выше для удобства нажатия */
  .wheel-fit-widget #wheel-fit-form .btn-primary,
  .wsf-finder-widget #wheel-fit-form .btn-primary {
    height: 48px !important; /* Чуть выше на мобильных */
    min-height: 48px !important;
    max-height: 48px !important;
    font-size: 0.9rem !important; /* Чуть больше шрифт */
    padding-left: 2rem !important; /* Меньше отступы на мобильных */
    padding-right: 2rem !important;

    /* Обеспечиваем полную ширину и на мобильных */
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Еще больше уменьшаем отступ на мобильных */
  .wheel-fit-widget #wheel-fit-form .mt-6,
  .wsf-finder-widget #wheel-fit-form .mt-6 {
    margin-top: 0.75rem !important; /* 12px на мобильных */
  }
}
