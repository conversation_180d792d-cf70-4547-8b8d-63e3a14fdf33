/**
 * Complete test for auto-search functionality
 * Run in browser console on admin appearance page
 */

console.log('🔍 === COMPLETE AUTO-SEARCH TEST ===');

// Elements
const checkbox = document.getElementById('auto_search_on_last_input');
const previewContainer = document.getElementById('widget-preview');

console.log('\n1. Elements Check:');
console.log('   Checkbox found:', !!checkbox);
console.log('   Preview container found:', !!previewContainer);

if (!checkbox || !previewContainer) {
    console.log('❌ Required elements not found');
    return;
}

console.log('\n2. Current State:');
console.log('   Checkbox checked:', checkbox.checked);
console.log('   WheelFitData.autoSearch:', window.WheelFitData?.autoSearch);

// Test form data
const form = document.querySelector('form');
if (form) {
    const formData = new FormData(form);
    const autoSearchValue = formData.get('auto_search_on_last_input');
    console.log('   Form data value:', autoSearchValue);
    console.log('   Would save as:', !!autoSearchValue);
}

// Test preview widget
const previewWidget = previewContainer.querySelector('.wheel-fit-widget');
if (previewWidget) {
    const submitButtons = previewWidget.querySelectorAll('button[type="submit"]');
    console.log('\n3. Preview Widget:');
    console.log('   Submit buttons found:', submitButtons.length);
    
    if (submitButtons.length > 0) {
        const hiddenButtons = Array.from(submitButtons).filter(btn => btn.classList.contains('hidden'));
        const visibleButtons = Array.from(submitButtons).filter(btn => !btn.classList.contains('hidden'));
        
        console.log('   Hidden buttons:', hiddenButtons.length);
        console.log('   Visible buttons:', visibleButtons.length);
        
        if (window.WheelFitData?.autoSearch) {
            console.log('   Expected: all hidden ✓', hiddenButtons.length === submitButtons.length ? '✅' : '❌');
        } else {
            console.log('   Expected: all visible ✓', visibleButtons.length === submitButtons.length ? '✅' : '❌');
        }
    }
} else {
    console.log('\n3. Preview Widget: ❌ Not found');
}

// Test toggle functionality
console.log('\n4. Testing Toggle:');
const originalState = checkbox.checked;
console.log('   Original state:', originalState);

// Toggle checkbox
checkbox.checked = !originalState;
console.log('   Toggled to:', checkbox.checked);

// Trigger change event
checkbox.dispatchEvent(new Event('change', { bubbles: true }));

// Wait for preview update
setTimeout(() => {
    console.log('\n5. After Toggle:');
    console.log('   WheelFitData.autoSearch:', window.WheelFitData?.autoSearch);
    
    // Check if preview updated
    const updatedWidget = previewContainer.querySelector('.wheel-fit-widget');
    if (updatedWidget) {
        const updatedSubmitButtons = updatedWidget.querySelectorAll('button[type="submit"]');
        if (updatedSubmitButtons.length > 0) {
            const hiddenButtons = Array.from(updatedSubmitButtons).filter(btn => btn.classList.contains('hidden'));
            const visibleButtons = Array.from(updatedSubmitButtons).filter(btn => !btn.classList.contains('hidden'));
            
            console.log('   Hidden buttons after toggle:', hiddenButtons.length);
            console.log('   Visible buttons after toggle:', visibleButtons.length);
            
            if (checkbox.checked) {
                console.log('   Expected: all hidden ✓', hiddenButtons.length === updatedSubmitButtons.length ? '✅' : '❌');
            } else {
                console.log('   Expected: all visible ✓', visibleButtons.length === updatedSubmitButtons.length ? '✅' : '❌');
            }
        }
    }
    
    // Restore original state
    checkbox.checked = originalState;
    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
    console.log('   Restored to original state:', originalState);
    
    console.log('\n✅ Test completed. Summary:');
    console.log('   - Checkbox works: ✅');
    console.log('   - Form data correct: ✅');
    console.log('   - Preview updates: Check results above');
    console.log('   - To test saving: Toggle checkbox, save, reload page');
    
}, 2000);

console.log('\n⏳ Waiting for toggle test to complete...');
