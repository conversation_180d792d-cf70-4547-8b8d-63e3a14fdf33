const t = (k, fallback = null) => {
    if (window.WheelFitI18n && typeof window.WheelFitI18n === 'object' && k in window.WheelFitI18n) {
        return window.WheelFitI18n[k];
    }
    if (window.WheelFitData && window.WheelFitData.debug) {
        if (!window.__missingKeys) window.__missingKeys = new Set();
        if (!__missingKeys.has(k)) {
            console.warn('[Wheel-Size i18n] missing key:', k);
            __missingKeys.add(k);
        }
    }
    return fallback ?? k;
};

// Global helpers
window.capitalize = function(str) {
    if (!str || typeof str !== 'string') return '';
    return str.split(' ').map(word => {
        if (word.toUpperCase() === word) return word;
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    }).join(' ');
};

window.shortMod = function(str) {
    if (!str) return '';
    return str.split(/[ ,-—]+/).slice(0, 2).join(' ');
};

const GARAGE_ENABLED = !!(window.WheelFitData && window.WheelFitData.garageEnabled);
const AUTO_SEARCH   = !!(window.WheelFitData && window.WheelFitData.autoSearch);
const IS_INLINE     = !!(window.WheelFitData && window.WheelFitData.formLayout === 'inline');
const OE_ONLY_MODE  = !!(window.WheelFitData && window.WheelFitData.enableOeFilter);

// Debug garage state and auto-search
console.log('[Finder Init] GARAGE_ENABLED:', GARAGE_ENABLED);
console.log('[Finder Init] AUTO_SEARCH:', AUTO_SEARCH);
console.log('[Finder Init] OE_ONLY_MODE:', OE_ONLY_MODE);
console.log('[Finder Init] WheelFitData:', window.WheelFitData);

// Register critical icons if the helper is available
if (typeof lucide !== 'undefined' && typeof lucide.registerIcons === 'function') {
    const plusIcon  = lucide.PlusSquare || lucide.SquarePlus || lucide.Plus;
    const trashIcon = lucide.Trash2     || lucide.Trash;

    lucide.registerIcons({
        'plus-square': plusIcon,
        'trash-2':     trashIcon,
    });
}

function fuelIcon(fuel) {
    switch ((fuel || '').toLowerCase()) {
        case 'electric': return '⚡';
        case 'diesel':   return '⛽';
        case 'petrol':   return '⛽';
        case 'hybrid':   return '⚡';
        case 'natural gas': return '⛽';
        default:         return '🏎';
    }
}

/**
 * Wheel-Fit Widget - Progressive Form Handler
 * Vanilla JavaScript ES2020+
 */
class WheelFitWidget {
    constructor() {
        this.currentStep = 1;
        this.selectedData = {};
        this.cache = new Map();
        this.debounceTimer = null;
        
        // Initialize localStorage handler
        this.storage = new LocalStorageHandler();
        
        // Check if we have flow_order data to determine the flow type and max steps
        const flowOrderElement = document.querySelector('[data-flow-order]');
        if (flowOrderElement) {
            try {
                this.flowOrder = JSON.parse(flowOrderElement.dataset.flowOrder);
                this.maxSteps = this.flowOrder ? this.flowOrder.length : 4;
            } catch (e) {
                console.warn('Failed to parse flow order:', e);
                this.flowOrder = null;
                this.maxSteps = 4;
            }
        } else {
            this.flowOrder = null;
            this.maxSteps = 4;
        }
        // Explicit mode
        if (this.flowOrder && this.flowOrder[0] === 'year') {
            this.mode = 'byYear';
        } else if (this.flowOrder && this.flowOrder.includes('gen')) {
            this.mode = 'byGeneration';
        } else {
            this.mode = 'byVehicle';
        }
        // Central API endpoint map
        this.api = {
            byVehicle: {
                years: () => '/years/',
                makes: () => '/makes/',
                models: make => `/models/?make=${make}`,
                mods: (make, model, year) => `/modifications/?make=${make}&model=${model}&year=${year}`
            },
            byYear: {
                years: () => '/years/',
                makes: y => `/makes/?year=${y}`,
                models: (y, m) => `/models/?make=${m}&year=${y}`,
                mods: (y, m, md) => `/modifications/?make=${m}&model=${md}&year=${y}`
            },
            byGeneration: {
                makes: () => '/makes/',
                models: make => `/models/?make=${make}`,
                generations: (make, model) => `/generations/?make=${make}&model=${model}`,
                mods: (make, model, generation) => `/modifications/?make=${make}&model=${model}&generation=${generation}`
            }
        };
        this.init();
    }

    init() {
        this.bindEvents();
        // Hide submit buttons if automatic search is enabled
        if (AUTO_SEARCH) {
            document.querySelectorAll('.wheel-fit-widget button[type="submit"]').forEach(btn => {
                btn.classList.add('hidden');
            });
        }
        
        if (document.getElementById('wf-make')) {
            // Initialize form state - disable all selectors except the first one
            this.initializeFormState();

            // Determine which field to load first based on flow order
            if (this.mode === 'byYear') {
                this.loadAllYears();
                // 🛠️ Disable Make select until Year is chosen
                const makeSelect = document.getElementById('wf-make');
                if (makeSelect) makeSelect.disabled = true;
            } else {
                // Both byVehicle and byGeneration start with makes
                this.loadMakes();
            }
            this.loadSavedSearches();
        }
        if (document.getElementById('tab-by-size') && this.mode !== 'byYear') {
            this.loadTireSizeOptions();
        }
        this.updateStepIndicator(1, false);
    }

    initializeFormState() {
        // Disable all selectors except the first one based on flow mode
        const selectors = ['wf-make', 'wf-model', 'wf-year', 'wf-generation', 'wf-modification'];

        selectors.forEach(selectorId => {
            const select = document.getElementById(selectorId);
            if (!select) return;

            // Determine if this should be enabled initially
            let shouldEnable = false;
            if (this.mode === 'byYear' && selectorId === 'wf-year') {
                shouldEnable = true;
            } else if ((this.mode === 'byVehicle' || this.mode === 'byGeneration') && selectorId === 'wf-make') {
                shouldEnable = true;
            }

            if (shouldEnable) {
                select.disabled = false;
            } else {
                select.disabled = true;
                // Set appropriate placeholder text с правильными атрибутами (как у других элементов!)
                let placeholderKey = '';
                let fallbackText = '...';
                switch (selectorId) {
                    case 'wf-model':
                        placeholderKey = 'select_make_first_placeholder';
                        fallbackText = 'Select make first';
                        break;
                    case 'wf-year':
                        placeholderKey = 'select_model_first_placeholder';
                        fallbackText = 'Select model first';
                        break;
                    case 'wf-generation':
                        placeholderKey = 'select_model_first_placeholder';
                        fallbackText = 'Select model first';
                        break;
                    case 'wf-modification':
                        if (this.mode === 'byGeneration') {
                            placeholderKey = 'select_gen_first_placeholder';
                            fallbackText = 'Select generation first';
                        } else {
                            placeholderKey = 'select_year_first_placeholder';
                            fallbackText = 'Select year first';
                        }
                        break;
                }

                // ИСПОЛЬЗУЕМ ПЕРЕВОДЫ НАПРЯМУЮ (как при первой загрузке!)
                const placeholderText = window.WheelFitI18n && window.WheelFitI18n[placeholderKey]
                    ? window.WheelFitI18n[placeholderKey]
                    : fallbackText;

                // Создаем placeholder option с правильными атрибутами (как у других элементов!)
                const placeholderOption = document.createElement('option');
                placeholderOption.value = '';
                if (placeholderKey) {
                    placeholderOption.setAttribute('data-i18n', placeholderKey);
                }
                placeholderOption.textContent = placeholderText;

                // Очищаем и добавляем placeholder
                select.innerHTML = '';
                select.appendChild(placeholderOption);
            }
        });

        // Disable submit button initially
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) submitBtn.disabled = true;
    }

    bindEvents() {
        const carForm = document.getElementById('tab-by-car') || document.getElementById('wheel-fit-form');
        if (carForm) {
            carForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.searchSizes();
            });
        }

        // Bind events based on mode
        let eventFields;
        if (this.mode === 'byYear') {
            eventFields = ['year', 'make', 'model', 'modification'];
        } else if (this.mode === 'byGeneration') {
            eventFields = ['make', 'model', 'generation', 'modification'];
        } else {
            eventFields = ['make', 'model', 'year', 'modification'];
        }
            
        eventFields.forEach(key => {
            const element = document.getElementById(`wf-${key}`);
            if (element) {
                element.addEventListener('change', (e) => {
                    const handler = `on${capitalize(key)}Select`;
                    if (typeof this[handler] === 'function') {
                        this[handler](e.target.value);
                    }
                });
            }
        });

        document.addEventListener('click', (e) => {
            const tabBtn = e.target.closest('.select-tab');
            if (tabBtn) {
                e.preventDefault();
                const id = tabBtn.dataset.tab;
                const scope = document;
                scope.querySelectorAll('.select-tab').forEach(b => b.setAttribute('aria-selected', b === tabBtn ? 'true' : 'false'));
                scope.querySelectorAll('.tab-panel').forEach(p => {
                    const show = p.id === 'tab-' + id;
                    p.classList.toggle('hidden', !show);
                    p.setAttribute('aria-hidden', !show);
                });
                const sr = scope.getElementById('search-results');
                const tr = scope.getElementById('tire-search-results');
                if (typeof fadeOut === 'function') {
                    fadeOut(sr);
                    fadeOut(tr);
                } else {
                    if (sr) sr.classList.add('hidden');
                    if (tr) tr.classList.add('hidden');
                }
                return; // stop further processing on same click
            }

            if (e.target.closest('.history-load')) {
                e.preventDefault();
                this.loadFromHistory(e.target.closest('.history-load').dataset.search);
            }
            if (GARAGE_ENABLED) {
                const saveBtn = e.target.closest('.save-to-garage');
                if (saveBtn) {
                    console.log('[Event Handler] Save to garage button clicked', saveBtn);
                    e.preventDefault();
                    const saveResult = this.saveToGarage(saveBtn);
                    console.log('[Event Handler] Save result:', saveResult);
                    if (saveResult) {
                        const card = saveBtn.closest('.size-card');
                        if (card) {
                            saveBtn.remove();
                            card.insertAdjacentHTML('afterbegin',
                                `<span class="absolute -left-2 -top-2 bg-emerald-500 text-white text-[10px] px-1.5 py-[1px] rounded-sm shadow">✓</span>`);
                        }
                    }
                }
            }
        });

        const tireForm = document.getElementById('tab-by-size');
        if (tireForm) {
            tireForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const width = document.getElementById('tire-width').value;
                const profile = document.getElementById('tire-profile').value;
                const diameter = document.getElementById('tire-diameter').value;
                
                if (!width || !profile || !diameter) {
                    alert('Please select width, profile, and diameter.');
                    return;
                }

                console.log('Searching by tire:', { width, profile, diameter });

                this.makeAjaxRequest('wf_search_by_tire', {
                    section_width: width,
                    aspect_ratio: profile,
                    rim_diameter: diameter,
                }).then(response => {
                    if (response.success) {
                        this.displayTireSearchResults(response.data);
                    } else {
                        console.error('API Error:', response.data);
                        this.displayTireSearchResults([]); // Clear results on error
                        alert('Error: ' + response.data);
                    }
                }).catch(err => {
                    console.error('Fetch error:', err);
                    alert('An error occurred. Check the console.');
                });
            });
        }

        // Add change listeners for cascaded selects
        const widthSelect = document.getElementById('tire-width');
        const profileSelect = document.getElementById('tire-profile');
        const diameterSelect = document.getElementById('tire-diameter');

        if (widthSelect) {
            const ensureWidthOptions = () => {
                if (widthSelect.options.length <= 1) {
                    this.loadTireSizeOptions();
                }
            };
            widthSelect.addEventListener('focus', ensureWidthOptions);
            widthSelect.addEventListener('click', ensureWidthOptions);
        }

        if (widthSelect) {
            widthSelect.addEventListener('change', (e) => this.onTireWidthSelect(e.target.value));
        }
        if (profileSelect) {
            profileSelect.addEventListener('change', (e) => this.onTireProfileSelect(e.target.value));
        }
        if (diameterSelect) {
            diameterSelect.addEventListener('change', (e) => {
                console.log('[Tire Diameter Change] AUTO_SEARCH enabled:', AUTO_SEARCH);
                if (!AUTO_SEARCH) return;

                const width  = document.getElementById('tire-width').value;
                const profile = document.getElementById('tire-profile').value;
                const diameter = e.target.value;

                console.log('[Tire Diameter Change] Values:', { width, profile, diameter });

                if (width && profile && diameter) {
                    console.log('[Tire Diameter Change] All fields filled, triggering auto-search...');
                    // Programmatically submit the form to trigger search
                    if (tireForm && typeof tireForm.requestSubmit === 'function') {
                        tireForm.requestSubmit();
                    } else if (tireForm) {
                        tireForm.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                    }
                } else {
                    console.log('[Tire Diameter Change] Not all fields filled, skipping auto-search');
                }
            });
        }

        // Listener for all toggle buttons in the results area
        const resultsContainer = document.getElementById('tire-search-results');
        if(resultsContainer) {
            resultsContainer.addEventListener('click', (e) => {
                const modelsButton = e.target.closest('[data-toggle-models-make]');
                const brandsButton = e.target.closest('[data-toggle-all-brands]');

                if (modelsButton) {
                    e.preventDefault();
                    const makeName = modelsButton.dataset.toggleModelsMake;
                    this.expandedMakes[makeName] = !this.expandedMakes[makeName];
                    this.redrawTireSearchResults();
                    
                    const header = document.querySelector(`[data-make-header="${makeName}"]`);
                    if (header) {
                        header.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }

                if (brandsButton) {
                    e.preventDefault();
                    this.isShowingAllBrands = !this.isShowingAllBrands;
                    this.redrawTireSearchResults();
                }

                const regionToggleButton = e.target.closest('[data-region-toggle]');
                if(regionToggleButton) {
                    e.preventDefault();
                    const container = regionToggleButton.closest('[data-regions-container]');
                    if(container) {
                        const shortList = container.querySelector('[data-regions-short]');
                        const fullList = container.querySelector('[data-regions-full]');
                        shortList.classList.toggle('hidden');
                        fullList.classList.toggle('hidden');
                    }
                }
            });
        }
    }

    async onTireWidthSelect(width) {
        const profileSelect = document.getElementById('tire-profile');
        const diameterSelect = document.getElementById('tire-diameter');
        
        // Reset and disable downstream selects
        this.populateSelect('tire-profile', [], t('select_profile_first_placeholder', 'Select profile'));
        if (profileSelect) profileSelect.disabled = true;
        
        this.populateSelect('tire-diameter', [], t('select_diameter_first_placeholder', 'Select diameter'));
        if (diameterSelect) diameterSelect.disabled = true;

        if (!width) return;

        try {
            const response = await this.makeAjaxRequest('wf_get_tire_profiles', { section_width: width });
            if (response.success) {
                this.populateSelect('tire-profile', response.data, t('select_profile_first_placeholder', 'Select profile'));
                if (profileSelect) profileSelect.disabled = false;
            }
        } catch (e) {
            console.error('Error loading tire profiles:', e);
        }
    }

    async onTireProfileSelect(profile) {
        const width = document.getElementById('tire-width').value;
        const diameterSelect = document.getElementById('tire-diameter');

        // Reset and disable downstream select
        this.populateSelect('tire-diameter', [], t('select_diameter_first_placeholder', 'Select diameter'));
        if (diameterSelect) diameterSelect.disabled = true;

        if (!profile || !width) return;

        try {
            const response = await this.makeAjaxRequest('wf_get_tire_diameters', {
                section_width: width,
                aspect_ratio: profile
            });
            if (response.success) {
                this.populateSelect('tire-diameter', response.data, t('select_diameter_first_placeholder', 'Select diameter'));
                if (diameterSelect) diameterSelect.disabled = false;
            }
        } catch (e) {
            console.error('Error loading tire diameters:', e);
        }
    }

    async displayTireSearchResults(vehicles) {
        const container = document.getElementById('tire-search-results');
        if (!container) return;

        const updateContent = () => {
            let listData = vehicles?.data || vehicles || [];
            container.innerHTML = `<div class="p-4 text-center wsf-text-muted bg-gray-50 rounded-lg">Loading...</div>`;

            if (!Array.isArray(listData) || listData.length === 0) {
                container.innerHTML = `<div class="p-4 text-center wsf-text-muted bg-gray-50 rounded-lg">${t('no_vehicles_found_for_size', 'No vehicles found for this tire size.')}</div>`;
                fadeIn(container);
                return;
            }

            this.groupedTireSearchResults = listData.reduce((acc, vehicle) => {
                const makeName = vehicle.make?.name;
                const makeSlug = vehicle.make?.slug;
                if (makeName && makeSlug) {
                    if (!acc[makeName]) acc[makeName] = { slug: makeSlug, models: [] };
                    acc[makeName].models.push(vehicle);
                }
                return acc;
            }, {});

            // Fetch logos
            const makeSlugs = Object.values(this.groupedTireSearchResults).map(m => m.slug);
            this.makeLogos = {};
            this.makeAjaxRequest('wf_get_makes_details', { slugs: JSON.stringify(makeSlugs) }).then(logosResponse => {
                this.makeLogos = logosResponse.success ? logosResponse.data : {};

                // Sort models within each make alphabetically
                for (const make in this.groupedTireSearchResults) {
                    this.groupedTireSearchResults[make].models.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
                }

                this.expandedMakes = {};
                this.isShowingAllBrands = false;
                this.redrawTireSearchResults();
                fadeIn(container);
            });
        };

        if (!container.classList.contains('hidden')) {
            fadeOut(container, updateContent);
        } else {
            updateContent();
        }
    }
    
    redrawTireSearchResults() {
        const container = document.getElementById('tire-search-results');
        container.innerHTML = '';
        const sortedMakes = Object.keys(this.groupedTireSearchResults).sort();
        
        const grid = document.createElement('div');
        grid.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6';
        
        const makesToShow = this.isShowingAllBrands ? sortedMakes : sortedMakes.slice(0, 10);

        makesToShow.forEach(makeName => {
            grid.appendChild(this.createTireSearchMakeCard(makeName));
        });
        
        container.appendChild(grid);

        if (sortedMakes.length > 10) {
            const button = document.createElement('button');
            button.className = 'text-sm text-blue-600 hover:opacity-80 transition underline block mx-auto mt-6';
            button.textContent = this.isShowingAllBrands ? 'Show less brands' : `Show all ${sortedMakes.length} brands`;
            button.dataset.toggleAllBrands = true;
            container.appendChild(button);
        }
    }

    createTireSearchMakeCard(makeName) {
        const makeData = this.groupedTireSearchResults[makeName];
        const models = makeData.models;
        const logoUrl = this.makeLogos[makeData.slug]?.logo;
        const isExpanded = this.expandedMakes[makeName] || false;
        
        const card = document.createElement('div');
        card.className = 'wsf-card p-4 flex flex-col';
        
        const logoElement = logoUrl
            ? `<img src="${logoUrl}" alt="${makeName}" class="w-6 h-6 object-contain">`
            : `<span class="w-6 h-6 flex items-center justify-center wsf-text-muted">📦</span>`;

        const header = document.createElement('h2');
        header.className = 'flex items-center gap-2 text-base font-bold text-gray-900 mb-3';
        header.innerHTML = `${logoElement}<span>${makeName}</span>`;
        card.appendChild(header);

        const modelsToShow = isExpanded ? models : models.slice(0, 4);
        const modelsList = document.createElement('div');

        modelsList.className = 'flex flex-col gap-2';

        modelsToShow.forEach(model => {
            modelsList.appendChild(this.createTireSearchModelItem(model));
        });
        card.appendChild(modelsList);

        if (models.length > 4) {
            const button = document.createElement('button');
            button.className = 'text-xs text-blue-600 hover:underline mt-3 text-left';
            button.textContent = isExpanded ? 'Show less' : `+ ${models.length - 4} more`;
            button.dataset.toggleModelsMake = makeName;
            card.appendChild(button);
        }
        
        return card;
    }

    createTireSearchModelItem(model) {
        const item = document.createElement('div');
        item.className = 'bg-gray-50 border border-gray-200 rounded-lg p-2 text-sm w-full';
        
        const nameHtml = `<div class="font-semibold text-gray-900">${model.name || '—'}</div>`;
        const yearsHtml = `<div class="text-xs text-blue-600 font-medium">${model.year_ranges?.join(', ') || ''}</div>`;
        
        let regionsHtml = '';
        if (model.regions?.length) {
            const regionClass = 'inline-block bg-orange-100 text-orange-700 text-[10px] font-medium px-1.5 py-0.5 rounded';
            const allBadgesHtml = model.regions.map(r => `<span class="${regionClass}">${r.toUpperCase()}</span>`).join('');

            if (model.regions.length > 3) {
                 const visibleBadgesHtml = model.regions.slice(0, 3).map(r => `<span class="${regionClass}">${r.toUpperCase()}</span>`).join('');
                 const moreBadgeHtml = `<span class="inline-block bg-gray-100 text-gray-600 text-[10px] font-medium px-1.5 py-0.5 rounded">+${model.regions.length - 3}</span>`;

                regionsHtml = `
                    <div class="relative group mt-2">
                        <div class="flex flex-wrap items-center gap-1 h-5 overflow-hidden">
                            ${visibleBadgesHtml}
                            ${moreBadgeHtml}
                        </div>
                        <div class="absolute z-10 hidden group-hover:block wsf-card text-gray-900 text-xs p-2 rounded w-48 top-full mt-1 left-0">
                            <div class="flex flex-wrap gap-1">
                                ${allBadgesHtml}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                 regionsHtml = `<div class="mt-2 flex flex-wrap gap-1">${allBadgesHtml}</div>`;
            }
        }

        item.innerHTML = nameHtml + yearsHtml + regionsHtml;
        return item;
    }

    async loadMakes() {
        this.showLoader('make');
        try {
            const response = await this.makeAjaxRequest('wf_get_makes');
            if (response.success) {
                // Handle new response format with filter info
                const makes = response.data?.data || response.data || [];
                const filterInfo = response.data?.filter_info || null;

                this.populateMakes(makes);
                this.cache.set('makes', makes);

                // Show filter warning if needed
                this.handleFilterWarning(filterInfo, 'make');
            }
        } catch(e) { console.error(e) }
        finally { this.hideLoader('make'); }
    }

    async loadAllYears() {
        this.showLoader('year');
        try {
            const response = await this.makeAjaxRequest('wf_get_all_years');
            if (response.success) {
                this.populateYears(response.data);
                this.cache.set('all_years', response.data);
            }
        } catch(e) { console.error(e) }
        finally { this.hideLoader('year'); }
    }

    populateMakes(makes) {
        const select = document.getElementById('wf-make');

        // Создаем placeholder option с правильными атрибутами (как у других элементов!)
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';
        placeholderOption.setAttribute('data-i18n', 'select_make_placeholder');
        // ИСПОЛЬЗУЕМ ПЕРЕВОДЫ НАПРЯМУЮ (как при первой загрузке!)
        placeholderOption.textContent = window.WheelFitI18n && window.WheelFitI18n['select_make_placeholder']
            ? window.WheelFitI18n['select_make_placeholder']
            : 'Choose a make';

        // Очищаем и добавляем placeholder
        select.innerHTML = '';
        select.appendChild(placeholderOption);

        // Добавляем опции
        makes.forEach(make => select.add(new Option(make.name, make.slug || make.name)));

        // 🛠️ Enable Make select only if year is selected (byYear mode)
        if (this.mode === 'byYear' && !this.selectedData.year) {
            select.disabled = true;
        } else {
            select.disabled = false;
        }
    }

    async onMakeSelect(makeSlug) {
        if (!makeSlug) return;
        this.selectedData.make = makeSlug;
        
        // Check if this is by_year flow
        if (this.mode === 'byYear') {
            // In by_year flow, after selecting make, load models for the selected year and make
            this.resetStepsFrom(3);
            this.showLoader('model');
            this.showStep(3);
            try {
                const response = await this.makeAjaxRequest('wf_get_models_by_year', { 
                    year: this.selectedData.year,
                    make: makeSlug 
                });
                if (response.success) {
                    this.populateModels(response.data);
                    this.cache.set(`models_${this.selectedData.year}_${makeSlug}`, response.data);
                }
            } catch(e) { console.error(e) }
            finally { this.hideLoader('model'); }
        } else {
            // In by_model flow, after selecting make, load models
        this.resetStepsFrom(2);
        this.showLoader('model');
        this.showStep(2);
        try {
            const response = await this.makeAjaxRequest('wf_get_models', { make: makeSlug });
            if (response.success) {
                this.populateModels(response.data);
                this.cache.set(`models_${makeSlug}`, response.data);
            }
        } catch(e) { console.error(e) } 
        finally { this.hideLoader('model'); }
        }
    }
    
    populateModels(models) {
        const select = document.getElementById('wf-model');

        // Создаем placeholder option с правильными атрибутами (как у других элементов!)
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';
        placeholderOption.setAttribute('data-i18n', 'select_model_placeholder');
        // ИСПОЛЬЗУЕМ ПЕРЕВОДЫ НАПРЯМУЮ (как при первой загрузке!)
        placeholderOption.textContent = window.WheelFitI18n && window.WheelFitI18n['select_model_placeholder']
            ? window.WheelFitI18n['select_model_placeholder']
            : 'Choose a model';

        // Очищаем и добавляем placeholder
        select.innerHTML = '';
        select.appendChild(placeholderOption);

        models.forEach(m => {
            // берём first non-empty из нескольких возможных ключей
            const label = m.name || m.title || m.model || m.slug || 'Unknown Model';
            const value = m.slug || m.name || m.title || label;
            select.add(new Option(label, value));
        });

        select.disabled = false;

        // Если единственная модель, автоматически выбираем её
        if (models.length === 1) {
            select.value = select.options[1].value; // первая после placeholder
            this.onModelSelect(select.value); // триггерим дальнейшую цепочку
        }
    }

    async onModelSelect(modelSlug) {
        if (!modelSlug) return;
        this.selectedData.model = modelSlug;
        
        // Check if this is by_year flow
        if (this.mode === 'byYear') {
            // In by_year flow, after selecting model, load modifications
            this.resetStepsFrom(4);
            this.showLoader('modification');
            this.showStep(4);
            try {
                const response = await this.makeAjaxRequest('wf_get_modifications_by_year', {
                    year: this.selectedData.year,
                    make: this.selectedData.make,
                    model: modelSlug
                });
                if (response.success) {
                    console.log('[onModelSelect] Year-first flow - loaded modifications:', {
                        count: response.data.length,
                        sampleMod: response.data[0],
                        cacheKey: `mods_${this.selectedData.make}_${modelSlug}_${this.selectedData.year}`
                    });
                    this.populateModifications(response.data);
                    // Use consistent cache key format: make_model_year
                    this.cache.set(`mods_${this.selectedData.make}_${modelSlug}_${this.selectedData.year}`, response.data);
                }
            } catch(e) { console.error(e) }
            finally { this.hideLoader('modification'); }
        } else if (this.mode === 'byGeneration') {
            // In by_generation flow, after selecting model, load generations
            this.resetStepsFrom(3);
            this.showLoader('generation');
            this.showStep(3);
            try {
                const response = await this.makeAjaxRequest('wf_get_generations', {
                    make: this.selectedData.make,
                    model: modelSlug
                });
                if (response.success) {
                    this.populateGenerations(response.data);
                    this.cache.set(`generations_${this.selectedData.make}_${modelSlug}`, response.data);
                }
            } catch(e) { console.error(e) }
            finally { this.hideLoader('generation'); }
        } else {
            // In by_vehicle flow, after selecting model, load years
            this.resetStepsFrom(3);
            this.showLoader('year');
            this.showStep(3);
            try {
                const response = await this.makeAjaxRequest('wf_get_years', {
                    make: this.selectedData.make,
                    model: modelSlug
                });
                if (response.success) {
                    this.populateYears(response.data);
                    this.cache.set(`years_${this.selectedData.make}_${modelSlug}`, response.data);
                }
            } catch(e) { console.error(e) }
            finally { this.hideLoader('year'); }
        }
    }

    populateYears(years) {
        const select = document.getElementById('wf-year');

        // Создаем placeholder option с правильными атрибутами (как у других элементов!)
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';
        placeholderOption.setAttribute('data-i18n', 'select_year_placeholder');
        // ИСПОЛЬЗУЕМ ПЕРЕВОДЫ НАПРЯМУЮ (как при первой загрузке!)
        placeholderOption.textContent = window.WheelFitI18n && window.WheelFitI18n['select_year_placeholder']
            ? window.WheelFitI18n['select_year_placeholder']
            : 'Choose a year';

        // Очищаем и добавляем placeholder
        select.innerHTML = '';
        select.appendChild(placeholderOption);

        years.forEach(y => {
            // берём first non-empty из нескольких возможных ключей
            const label = y.name || y.year || y.title || y.slug || 'Unknown Year';
            const value = y.year || y.slug || y.name || label;
            select.add(new Option(label, value));
        });

        select.disabled = false;

        // Если единственный год, автоматически выбираем его
        if (years.length === 1) {
            select.value = select.options[1].value; // первая после placeholder
            this.onYearSelect(select.value); // триггерим дальнейшую цепочку
        }
    }

    populateGenerations(generations) {
        const select = document.getElementById('wf-generation');
        if (!select) return;

        console.log('[populateGenerations] Populating generations:', {
            count: generations.length,
            generations: generations
        });

        // Создаем placeholder option с правильными атрибутами (как у других элементов!)
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';
        placeholderOption.setAttribute('data-i18n', 'select_gen_placeholder');
        // ИСПОЛЬЗУЕМ ПЕРЕВОДЫ НАПРЯМУЮ (как при первой загрузке!)
        placeholderOption.textContent = window.WheelFitI18n && window.WheelFitI18n['select_gen_placeholder']
            ? window.WheelFitI18n['select_gen_placeholder']
            : 'Choose a generation';

        // Очищаем и добавляем placeholder
        select.innerHTML = '';
        select.appendChild(placeholderOption);

        // Helper function to detect internal IDs (long alphanumeric strings)
        const isInternalId = (str) => {
            if (!str || typeof str !== 'string') return false;
            // Internal IDs are typically long alphanumeric strings (8+ chars, mix of letters and numbers)
            return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
        };

        generations.forEach(g => {
            // Улучшенная обработка данных поколений
            console.log('[populateGenerations] Processing generation:', g);

            // Сначала найдем лучшее отображаемое название (избегая внутренние ID)
            let displayLabel = null;

            // Проверяем поля в порядке приоритета, исключая внутренние ID
            const candidateFields = ['name', 'title', 'range', 'year_range', 'gen'];
            for (const field of candidateFields) {
                if (g[field] && !isInternalId(g[field]) && String(g[field]).trim().toLowerCase() !== 'generation') {
                    displayLabel = g[field];
                    break;
                }
            }

            // Если не нашли хорошее название, проверим slug
            if (!displayLabel && g.slug && !isInternalId(g.slug)) {
                displayLabel = g.slug;
            }

            // Если все еще нет названия, попробуем id (но только если он не выглядит как внутренний ID)
            if (!displayLabel && g.id && !isInternalId(g.id)) {
                displayLabel = g.id;
            }

            // Последний fallback - создаем читаемое название
            if (!displayLabel) {
                // Если есть хоть какое-то поле с данными, используем его
                const anyField = g.name || g.title || g.range || g.year_range || g.gen || g.slug || g.id;
                if (anyField && String(anyField).trim().toLowerCase() !== 'generation') {
                    // Предпочитаем показать реальные данные, даже если они выглядят как внутренний ID
                    // чем показывать общий placeholder "Generation"
                    displayLabel = anyField;
                    if (isInternalId(anyField)) {
                        console.warn('[populateGenerations] Using internal ID as display label (no better alternative):', g);
                    }
                } else {
                    displayLabel = 'Unknown Generation';
                }
            }

            // Приоритет для значения (для отправки формы): slug > id > name > title > range
            const value = g.slug || g.id || g.name || g.title || g.range || displayLabel;

            // 🚫  Не позволяем отображать пустой или бесполезный placeholder "Generation"
            if (String(displayLabel).trim().toLowerCase() === 'generation') {
                displayLabel = g.slug || g.id || 'Unknown Generation';
            }

            select.add(new Option(displayLabel, value));

            console.log('[populateGenerations] Added generation option:', {
                displayLabel: displayLabel,
                value: value,
                originalData: g,
                usedFallback: displayLabel === 'Generation'
            });
        });

        select.disabled = false;

        // Если единственное поколение, автоматически выбираем его
        if (generations.length === 1) {
            const selectedValue = select.options[1].value;
            const selectedLabel = select.options[1].text;

            console.log('[populateGenerations] Auto-selecting single generation:', {
                value: selectedValue,
                label: selectedLabel
            });

            select.value = selectedValue; // первая после placeholder

            // Добавляем небольшую задержку чтобы убедиться что селектор обновился
            setTimeout(() => {
                console.log('[populateGenerations] Generation selector state after auto-select:', {
                    value: select.value,
                    selectedText: select.options[select.selectedIndex]?.text,
                    selectedIndex: select.selectedIndex
                });

                this.onGenerationSelect(selectedValue); // триггерим дальнейшую цепочку
            }, 10);
        }
    }

    async onYearSelect(year) {
        if (!year) return;
        this.selectedData.year = year;

        // Check if this is by_year flow
        if (this.mode === 'byYear') {
            // In by_year flow, after selecting year, load makes for that year
            this.resetStepsFrom(2);
            this.showLoader('make');
            this.showStep(2);
            try {
                const response = await this.makeAjaxRequest('wf_get_makes_by_year', { year });
                if (response.success) {
                    // Handle new response format with filter info
                    const makes = response.data?.data || response.data || [];
                    const filterInfo = response.data?.filter_info || null;

                    this.populateMakes(makes);
                    this.cache.set(`makes_${year}`, makes);

                    // Show filter warning if needed
                    this.handleFilterWarning(filterInfo, 'make');
                }
            } catch(e) { console.error(e) }
            finally { this.hideLoader('make'); }
        } else {
            // In by_vehicle flow, after selecting year, load modifications
            this.resetStepsFrom(4);
            this.showLoader('modification');
            this.showStep(4);
            try {
                const response = await this.makeAjaxRequest('wf_get_modifications', {
                    make: this.selectedData.make,
                    model: this.selectedData.model,
                    year: year
                });
                if (response.success) {
                    console.log('[onYearSelect] Make-first flow - loaded modifications:', {
                        count: response.data.length,
                        sampleMod: response.data[0],
                        cacheKey: `mods_${this.selectedData.make}_${this.selectedData.model}_${year}`
                    });
                    this.populateModifications(response.data);
                    this.cache.set(`mods_${this.selectedData.make}_${this.selectedData.model}_${year}`, response.data);
                }
            } catch(e) { console.error(e) }
            finally { this.hideLoader('modification'); }
        }
    }

    async onGenerationSelect(generationSlug) {
        if (!generationSlug) return;
        this.selectedData.generation = generationSlug;

        // In by_generation flow, after selecting generation, load modifications
        this.resetStepsFrom(4);
        this.showLoader('modification');
        this.showStep(4);
        try {
            const response = await this.makeAjaxRequest('wf_get_modifications_by_generation', {
                make: this.selectedData.make,
                model: this.selectedData.model,
                generation: generationSlug
            });
            if (response.success) {
                console.log('[onGenerationSelect] Generation-based flow - loaded modifications:', {
                    count: response.data.length,
                    sampleMod: response.data[0],
                    cacheKey: `mods_${this.selectedData.make}_${this.selectedData.model}_${generationSlug}`
                });
                this.populateModifications(response.data);
                this.cache.set(`mods_${this.selectedData.make}_${this.selectedData.model}_${generationSlug}`, response.data);

                // Note: Auto-search is handled in onModificationSelect, not here
                // This ensures search only triggers on the last input (modification)
            }
        } catch(e) {
            console.error('[onGenerationSelect] Error loading modifications:', e);
        }
        finally {
            this.hideLoader('modification');
        }
    }

    populateModifications(modifications) {
        const select = document.getElementById('wf-modification');
        if (!select) return;

        console.log('[populateModifications] Populating modifications:', {
            count: modifications.length,
            modifications: modifications
        });

        // Создаем placeholder option с правильными атрибутами (как у других элементов!)
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';
        placeholderOption.setAttribute('data-i18n', 'select_mods_placeholder');
        // ИСПОЛЬЗУЕМ ПЕРЕВОДЫ НАПРЯМУЮ (как при первой загрузке!)
        placeholderOption.textContent = window.WheelFitI18n && window.WheelFitI18n['select_mods_placeholder']
            ? window.WheelFitI18n['select_mods_placeholder']
            : 'Choose a modification';

        // Очищаем и добавляем placeholder
        select.innerHTML = '';
        select.appendChild(placeholderOption);

        modifications.forEach(mod => {
            // берём first non-empty из нескольких возможных ключей для базового названия
            let label = mod.name || mod.title || mod.modification || mod.slug || 'Unknown Modification';

            // добавляем дополнительную информацию если есть
            if (Array.isArray(mod.trim_levels) && mod.trim_levels.length) label += ` – ${mod.trim_levels.join(', ')}`;
            if (mod.engine?.power?.hp) label += ` (${mod.engine.power.hp} HP${mod.engine.type ? ', ' + mod.engine.type : ''})`;

            const value = mod.slug || mod.name || mod.title || label;
            select.add(new Option(label, value));
        });

        select.disabled = false;

        // Если единственная модификация, автоматически выбираем её
        if (modifications.length === 1) {
            const selectedValue = select.options[1].value;
            const selectedLabel = select.options[1].text;

            console.log('[populateModifications] Auto-selecting single modification:', {
                value: selectedValue,
                label: selectedLabel
            });

            select.value = selectedValue; // первая после placeholder

            // Проверяем, что селектор поколения не был затронут
            const generationSelect = document.getElementById('wf-generation');
            if (generationSelect && this.selectedData.generation) {
                const currentGenValue = generationSelect.value;
                const currentGenText = generationSelect.options[generationSelect.selectedIndex]?.text;

                console.log('[populateModifications] Generation selector state check:', {
                    expectedValue: this.selectedData.generation,
                    currentValue: currentGenValue,
                    currentText: currentGenText,
                    isCorrect: currentGenValue === this.selectedData.generation
                });

                // Если селектор поколения был сброшен, восстанавливаем его
                if (currentGenValue !== this.selectedData.generation) {
                    console.warn('[populateModifications] Generation selector was reset, restoring...');
                    generationSelect.value = this.selectedData.generation;
                }
            }

            this.onModificationSelect(selectedValue); // триггерим дальнейшую цепочку
        }
    }

    onModificationSelect(modificationSlug) {
        console.log('[onModificationSelect] Called with:', modificationSlug);
        console.log('[onModificationSelect] AUTO_SEARCH enabled:', AUTO_SEARCH);

        this.selectedData.modification = modificationSlug;
        const submitBtn = document.querySelector('.wheel-fit-widget button[type="submit"]');
        if (submitBtn) submitBtn.disabled = !modificationSlug;

        // Auto-search on last input if enabled
        if (AUTO_SEARCH && modificationSlug) {
            console.log('[onModificationSelect] Triggering auto-search...');
            this.searchSizes();
        } else if (AUTO_SEARCH && !modificationSlug) {
            console.log('[onModificationSelect] Auto-search enabled but no modification selected');
        } else if (!AUTO_SEARCH) {
            console.log('[onModificationSelect] Auto-search disabled');
        }
    }

    async searchSizes() {
        console.log('[searchSizes] Starting search with data:', this.selectedData);

        // Ensure we don't send both year and generation to avoid API confusion
        const searchData = { ...this.selectedData };
        if (searchData.year && searchData.generation) {
            console.log('[searchSizes] Both year and generation present, removing generation to prefer year');
            delete searchData.generation;
        }
        console.log('[searchSizes] Final search data:', searchData);

        this.showSearchLoader();
        try {
            const response = await this.makeAjaxRequest('wf_search_sizes', searchData);
            console.log('[searchSizes] Response received:', response);

            if (response.success) {
                console.log('[searchSizes] Success response, data:', response.data);
                const factoryCount = response.data?.factory_sizes?.length || 0;
                const optionalCount = response.data?.optional_sizes?.length || 0;
                console.log('[searchSizes] Size counts - Factory:', factoryCount, 'Optional:', optionalCount);

                this.displayResults(response.data);
                this.saveSearchToHistory(this.selectedData);
            } else {
                console.log('[searchSizes] Failed response:', response);
                this.showError('No sizes found for this vehicle.');
            }
        } catch (error) {
            console.error('[searchSizes] Search error:', error);
        } finally {
            this.hideSearchLoader();
        }
    }

    displayResults(data) {
        console.log('[displayResults] Called with data:', data);
        const container = document.getElementById('search-results');
        console.log('[displayResults] Results container found:', !!container);

        const updateContent = () => {
            container.classList.remove('hidden');
            document.getElementById('vehicle-label').textContent = this.getVehicleLabel();

            // Render mod info
            const modInfoContainer = document.getElementById('selected-modification-info');
            // Generate cache key based on flow mode
            let cacheKey;
            if (this.mode === 'byGeneration' && this.selectedData.generation) {
                cacheKey = `mods_${this.selectedData.make}_${this.selectedData.model}_${this.selectedData.generation}`;
            } else {
                cacheKey = `mods_${this.selectedData.make}_${this.selectedData.model}_${this.selectedData.year}`;
            }
            const allMods = this.cache.get(cacheKey) || [];
            const selectedMod = allMods.find(m => m.slug === this.selectedData.modification);

            console.log('[displayResults] Mod info debug:', {
                cacheKey,
                allModsCount: allMods.length,
                selectedModification: this.selectedData.modification,
                foundMod: !!selectedMod,
                modEngine: selectedMod?.engine
            });

            if (selectedMod) {
                modInfoContainer.innerHTML = this.createModInfoBar(selectedMod);
            }

            // Render grids - skip optional if OE only mode is enabled
            const typesToRender = OE_ONLY_MODE ? ['factory'] : ['factory', 'optional'];
            console.log('[displayResults] OE_ONLY_MODE:', OE_ONLY_MODE, 'Types to render:', typesToRender);

            typesToRender.forEach(type => {
                const grid = document.getElementById(`${type}-grid`);
                const section = document.getElementById(`${type}-section`);
                const sizes = data[`${type}_sizes`] || [];

                console.log(`[displayResults] Processing ${type} sizes:`, sizes.length, sizes);

                grid.innerHTML = '';
                if (sizes.length > 0) {
                    sizes.forEach(size => grid.appendChild(this.createSizeCard(size, type)));
                    if (typeof lucide !== 'undefined') {
                        try {
                            // Lucide v2 supports attrs option
                            lucide.createIcons({ attrs: ['data-lucide'] });
                        } catch (e) {
                            // Older versions ignore parameters
                            lucide.createIcons();
                        }
                    }
                    section.classList.remove('hidden');
                } else {
                    section.classList.add('hidden');
                }
            });

            // Hide optional section if OE only mode is enabled
            if (OE_ONLY_MODE) {
                const optionalSection = document.getElementById('optional-section');
                if (optionalSection) {
                    optionalSection.classList.add('hidden');
                    console.log('[displayResults] Optional section hidden due to OE only mode');
                }
            }
            
            // Calculate hasResults - in OE only mode, only factory sizes matter
            const factoryCount = data.factory_sizes?.length || 0;
            const optionalCount = data.optional_sizes?.length || 0;
            const hasResults = OE_ONLY_MODE ? factoryCount > 0 : (factoryCount + optionalCount) > 0;

            console.log('[displayResults] Has results calculation:', {
                factoryLength: factoryCount,
                optionalLength: optionalCount,
                oeOnlyMode: OE_ONLY_MODE,
                hasResults: hasResults
            });

            const noResultsElement = document.getElementById('no-results');
            if (noResultsElement) {
                noResultsElement.classList.toggle('hidden', hasResults);
                console.log('[displayResults] No-results element hidden:', hasResults);
            } else {
                console.warn('[WheelFit] No-results element not found');
            }
            
            // Smooth scroll so that the end of the results section (last size) aligns with the viewport bottom
            container.scrollIntoView({ behavior: 'smooth', block: 'end' });
        };

        if (!container.classList.contains('hidden')) {
            fadeOut(container, updateContent);
        } else {
            updateContent();
        }
    }

    createModInfoBar(mod) {
        const badges = [];
        if (mod.engine?.fuel) {
            const fuelKey = `fuel_${mod.engine.fuel.toLowerCase().replace(/\s+/g,'_')}`;
            badges.push(`<span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs font-semibold">${t(fuelKey, mod.engine.fuel.toUpperCase())}</span>`);
        }
        if (mod.engine?.power?.hp) badges.push(`<span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-semibold">${mod.engine.power.hp} HP</span>`);
        if (mod.engine?.type) badges.push(`<span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs font-semibold">${mod.engine.type}</span>`);
        return `<div class="flex flex-wrap gap-2 mt-1">${badges.join('')}</div>`;
    }

    createSizeCard(sizeData, type) {
        const card = document.createElement('div');
        card.className = 'size-card relative group bg-slate-50 border border-slate-200 rounded-xl p-4 pt-6 shadow-sm transition hover:ring hover:ring-blue-50 animate-fadeIn';

        const badgeTypeClass = type === 'factory' ? 'bg-orange-100 text-orange-700' : 'bg-blue-100 text-blue-700';
        const badgeFallback = type === 'factory' ? 'FACTORY' : 'OPTIONAL';
        const badgeText = t(`badge_${type}`, badgeFallback);
        const badgeHTML = `<span class="absolute top-1.5 right-1.5 z-10 pointer-events-none px-2 py-[2px] text-[10px] font-semibold rounded uppercase tracking-wider ${badgeTypeClass}">${badgeText}</span>`;
        
        const diams = this.getDiameters(sizeData);
        const diameterHTML = diams.length === 1
            ? `<span class="text-[clamp(1.75rem,5vw,2.5rem)] font-black text-black leading-none">${diams[0]}"</span>`
            : `<div class="flex items-baseline gap-1">
                 <span class="text-[clamp(1.75rem,5vw,2.5rem)] font-black text-black leading-none">${diams[0]}"</span>
                 <span class="text-xl font-semibold text-gray-500">/</span>
                 <span class="text-[clamp(1.75rem,5vw,2.5rem)] font-black text-black leading-none">${diams[1]}"</span>
               </div>`;
        
        const isDual = !sizeData.showing_fp_only && sizeData.rear_tire_full;
        const sizesHTML = isDual
            ? `<div class="mt-2 space-y-0.5 text-sm text-gray-700"><p><span class="font-semibold text-gray-500">${t('position_front','Front')}:</span> ${sizeData.tire_full}</p><p><span class="font-semibold text-gray-500">${t('position_rear','Rear')}:&nbsp;</span> ${sizeData.rear_tire_full}</p></div>`
            : `<div class="text-sm md:text-base text-gray-700 mt-1 font-medium tracking-tight">${sizeData.tire_full}</div>`;

        const garage = this.storage.getGarage();
        const alreadyInGarage = garage.some(g =>
            g.tire_full === sizeData.tire_full &&
            ((g.rear_tire_full || '') === (sizeData.rear_tire_full || ''))
        );

        let garageButtonHTML = '';
        if (GARAGE_ENABLED && !alreadyInGarage) {
            garageButtonHTML = `<button class=\"save-to-garage wsf-garage-hover absolute bottom-2 right-2 z-10 p-1 rounded-full transition\" title=\"${t('tooltip_add_to_garage','Add to Garage')}\" data-size-data='${encodeURIComponent(JSON.stringify(sizeData))}'>
                <i data-lucide="plus-square" class="w-6 h-6 text-blue-600"></i>
            </button>`;
        }

        const inGarageBadge = alreadyInGarage
          ? `<span class="absolute -left-2 -top-2 bg-emerald-500 text-white text-[10px] px-1.5 py-[1px] rounded-sm shadow">✓</span>`
          : '';

        card.innerHTML = `${inGarageBadge}${badgeHTML}${diameterHTML}${sizesHTML}${garageButtonHTML}`;
        return card;
    }

    loadSavedSearches() {
        const historyList = document.getElementById('history-list');
        const emptyMessage = document.getElementById('history-empty');

        // Защитная проверка - элементы могут отсутствовать в разметке
        if (!historyList || !emptyMessage) {
            console.warn('[WheelFit] History elements not found – skipping history block');
            return;
        }

        const searches = this.storage.getSearchHistory().slice(0, 5);

        historyList.innerHTML = '';
        if (searches.length > 0) {
            searches.forEach(search => historyList.appendChild(this.createHistoryItem(search)));
            emptyMessage.classList.add('hidden');
            historyList.classList.remove('hidden');
        } else {
            emptyMessage.classList.remove('hidden');
            historyList.classList.add('hidden');
        }
        if (typeof lucide !== 'undefined') lucide.createIcons();
    }

    createHistoryItem(search) {
        const item = document.createElement('li');
        item.className = 'flex items-center justify-between gap-4 px-4 py-3 wsf-card hover:wsf-bg-surface-hover transition';
        
        const textLabel = `${capitalize(search.make)} ${capitalize(search.model)} <span class="wsf-text-muted">(${search.year})</span>`;
        const detailsParts = [];
        if (search.fuel) detailsParts.push(search.fuel.toUpperCase());
        if (search.hp) detailsParts.push(`${search.hp} HP`);
        if (search.modLabel) detailsParts.push(shortMod(search.modLabel));
        const detailsText = detailsParts.join(' • ');

        item.innerHTML = `
            <i data-lucide="car" class="w-4 h-4 text-blue-600 shrink-0" aria-hidden="true"></i>
            <div class="flex-1 truncate">
                <p class="text-sm font-semibold text-gray-900 leading-tight">${textLabel}</p>
                ${detailsText ? `<p class="text-xs text-gray-600 leading-tight">${detailsText}</p>`: ''}
            </div>
            <button class="history-load text-xs font-semibold text-blue-600 hover:text-blue-800 active:scale-95 transition"
                    data-search='${encodeURIComponent(JSON.stringify(search))}' aria-label="Load search">
                Load
            </button>`;
        return item;
    }
    
    async loadFromHistory(encodedSearch) {
        const data = JSON.parse(decodeURIComponent(encodedSearch));
        this.selectedData = data;

        console.log('[loadFromHistory] Loading data:', data);
        console.log('[loadFromHistory] Current flow order:', this.flowOrder);
        console.log('[loadFromHistory] Data flow order:', data.flow_order);
        console.log('[loadFromHistory] Current mode:', this.mode);

        // Determine the data's original flow type and current widget flow type
        const currentIsGenerationFlow = this.mode === 'byGeneration';
        const currentIsYearFlow = this.flowOrder && this.flowOrder[0] === 'year';
        const dataIsGenerationFlow = data.generation && !data.year;
        const dataIsYearFlow = data.flow_order && data.flow_order[0] === 'year';

        console.log('[loadFromHistory] Flow analysis:', {
            currentIsGenerationFlow,
            currentIsYearFlow,
            dataIsGenerationFlow,
            dataIsYearFlow,
            hasGeneration: !!data.generation,
            hasYear: !!data.year
        });

        // Flexible validation - check for essential data regardless of flow type
        const hasBasicData = data.make && data.model && data.modification;
        const hasTimeIdentifier = data.year || data.generation;

        if (!hasBasicData || !hasTimeIdentifier) {
            console.error('[loadFromHistory] Missing essential vehicle data:', {
                data,
                hasBasicData,
                hasTimeIdentifier,
                make: !!data.make,
                model: !!data.model,
                modification: !!data.modification,
                year: !!data.year,
                generation: !!data.generation
            });
            alert('Cannot load this item: missing essential vehicle information.');
            return;
        }

        // Use semantic field mapping instead of hardcoded IDs
        const fieldMap = {
            'make': 'wf-make',
            'model': 'wf-model',
            'year': 'wf-year',
            'generation': 'wf-generation',
            'mod': 'wf-modification',
            'modification': 'wf-modification'
        };

        console.log('[loadFromHistory] Loading strategy selection...');

        try {
            // Strategy: Prioritize data format from garage over current widget flow
            if (data.generation && !data.year) {
                console.log('[loadFromHistory] Data has generation, loading with generation flow');
                await this.loadWithGenerationFlow(data, fieldMap);
            } else if (data.year && !data.generation) {
                console.log('[loadFromHistory] Data has year, loading with year-based flow');
                if (currentIsYearFlow) {
                    console.log('[loadFromHistory] Using year-first flow');
                    await this.loadWithYearFirstFlow(data, fieldMap);
                } else {
                    console.log('[loadFromHistory] Using make-first flow (year-based)');
                    await this.loadWithMakeFirstFlow(data, fieldMap);
                }
            } else if (data.year && data.generation) {
                // Both year and generation present - prefer year for consistency
                console.log('[loadFromHistory] Data has both year and generation, preferring year');
                if (currentIsYearFlow) {
                    await this.loadWithYearFirstFlow(data, fieldMap);
                } else {
                    await this.loadWithMakeFirstFlow(data, fieldMap);
                }
            } else {
                console.error('[loadFromHistory] Data missing both year and generation');
                alert('Cannot load this item: missing year or generation information.');
                return;
            }

            // Execute search after loading all fields
            console.log('[loadFromHistory] Executing search...');
            await this.searchSizes();
            console.log('[loadFromHistory] Load completed successfully');

        } catch (error) {
            console.error('[loadFromHistory] Error during loading:', error);
            alert('Failed to load garage item. Please try again.');
            throw error; // Re-throw for garage.js to handle
        }
    }

    async loadWithYearFirstFlow(data, fieldMap) {
        // Year-first flow: year → make → model → modification
        const yearSelect = document.getElementById(fieldMap.year);
        if (yearSelect) {
            if (yearSelect.options.length <= 1) {
                await this.loadAllYears();
            }
            yearSelect.value = data.year;
            await this.onYearSelect(data.year);
        }

        const makeSelect = document.getElementById(fieldMap.make);
        if (makeSelect) {
            makeSelect.value = data.make;
            await this.onMakeSelect(data.make);
        }

        const modelSelect = document.getElementById(fieldMap.model);
        if (modelSelect) {
            modelSelect.value = data.model;
            await this.onModelSelect(data.model);
        }

        const modSelect = document.getElementById(fieldMap.modification);
        if (modSelect) {
            modSelect.value = data.modification;
            this.onModificationSelect(data.modification);
        }
    }

    async loadWithGenerationFlow(data, fieldMap) {
        // Generation flow: make → model → generation → modification
        const makeSelect = document.getElementById(fieldMap.make);
        if (makeSelect && ![...makeSelect.options].some(o => o.value === data.make)) {
            await this.loadMakes();
        }
        if (makeSelect) makeSelect.value = data.make;

        await this.onMakeSelect(data.make);

        const modelSelect = document.getElementById(fieldMap.model);
        if (modelSelect) {
            modelSelect.value = data.model;
            await this.onModelSelect(data.model);
        }

        const generationSelect = document.getElementById(fieldMap.generation);
        if (generationSelect) {
            generationSelect.value = data.generation;
            await this.onGenerationSelect(data.generation);
        }

        const modSelect = document.getElementById(fieldMap.modification);
        if (modSelect) {
            modSelect.value = data.modification;
            this.onModificationSelect(data.modification);
        }
    }

    async loadWithMakeFirstFlow(data, fieldMap) {
        // Make-first flow: make → model → year → modification
        const makeSelect = document.getElementById(fieldMap.make);
        if (makeSelect && ![...makeSelect.options].some(o => o.value === data.make)) {
            await this.loadMakes();
        }
        if (makeSelect) makeSelect.value = data.make;

        await this.onMakeSelect(data.make);
        if (makeSelect) makeSelect.value = data.make; // Ensure value persists

        const modelSelect = document.getElementById(fieldMap.model);
        if (modelSelect) {
            modelSelect.value = data.model;
            await this.onModelSelect(data.model);
        }

        const yearSelect = document.getElementById(fieldMap.year);
        if (yearSelect) {
            yearSelect.value = data.year;
            await this.onYearSelect(data.year);
        }

        const modSelect = document.getElementById(fieldMap.modification);
        if (modSelect) {
            modSelect.value = data.modification;
            this.onModificationSelect(data.modification);
        }
    }

    // --- UI & Utility Methods ---

    updateStepIndicator(currentStep, isComplete = false) {
        // stepper indicators only exist in the 'stepper' layout
        if (!document.getElementById('step-indicator-1')) return;

        for (let i = 1; i <= this.maxSteps; i++) {
            const indicator = document.getElementById(`step-indicator-${i}`);
            const text = document.getElementById(`step-text-${i}`);
            const progress = document.getElementById(`progress-${i}`);

            if (!indicator || !text) continue;

            // Reset classes
            indicator.className = 'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold transition-colors';
            text.className = 'text-xs md:text-sm font-semibold transition-colors';

            if (i < currentStep) { // Completed steps
                indicator.classList.add('wsf-step-completed');
                indicator.innerHTML = '<i data-lucide="check" class="w-3 h-3"></i>';
                text.classList.add('text-blue-600');
                if(progress) progress.classList.add('wsf-progress-completed');
            } else if (i === currentStep) { // Active step
                indicator.classList.add('wsf-step-active');
                indicator.textContent = i;
                text.classList.add('text-blue-600');
                if(progress) progress.classList.add('wsf-progress-inactive');
            } else { // Future steps
                indicator.classList.add('wsf-step-inactive');
                indicator.textContent = i;
                text.classList.add('text-gray-500');
                if(progress) progress.classList.add('wsf-progress-inactive');
            }
        }
        if (typeof lucide !== 'undefined') lucide.createIcons();
    }

    showStep(stepNumber) {
        // Get the field at the specified step number
        const fieldName = this.flowOrder ? this.flowOrder[stepNumber - 1] : null;
        if (!fieldName) return;
        
        // Find the step element by the field name with fallback for older browsers
        let stepElement = null;
        try {
            // Chrome ≥ 105
            stepElement = document.querySelector(`[id^="step-"]:has(#wf-${fieldName})`);
        } catch(e) {}
        if (!stepElement) {
            // Fallback for older browsers
            stepElement = document.getElementById('step-' + stepNumber);
        }
        
        if (stepElement) {
            stepElement.classList.remove('hidden');
            stepElement.classList.add('animate-fadeIn');
            }
        
        // Also show all previous steps
        for (let i = 1; i < stepNumber; i++) {
            const prevFieldName = this.flowOrder ? this.flowOrder[i - 1] : null;
            if (prevFieldName) {
                let prevStepElement = null;
                try {
                    prevStepElement = document.querySelector(`[id^="step-"]:has(#wf-${prevFieldName})`);
                } catch(e) {}
                if (!prevStepElement) {
                    prevStepElement = document.getElementById('step-' + i);
                }
                if (prevStepElement) {
                    prevStepElement.classList.remove('hidden');
                }
            }
        }
        
        // Update the step indicator to reflect the current step
        this.updateStepIndicator(stepNumber);
    }

    resetStepsFrom(stepNumber) {
        // Reset all steps from the specified step number onwards
        for (let i = stepNumber; i <= this.maxSteps; i++) {
            const fieldName = this.flowOrder ? this.flowOrder[i - 1] : null;
            if (!fieldName) continue;
            
            let stepElement = null;
            try {
                stepElement = document.querySelector(`[id^="step-"]:has(#wf-${fieldName})`);
            } catch(e) {}
            if (!stepElement) {
                stepElement = document.getElementById('step-' + i);
            }
            if (!stepElement) continue;

            // In inline layout мы не скрываем шаги, только очищаем и дизейблим селекты
            if (!IS_INLINE) {
                stepElement.classList.add('hidden');
            }

            const select = stepElement.querySelector('select');
            if (select) {
                let phKey = '';
                switch (select.id) {
                    case 'wf-model': phKey = 'select_make_first_placeholder'; break;
                    case 'wf-year': phKey = 'select_model_first_placeholder'; break;
                    case 'wf-generation': phKey = 'select_model_first_placeholder'; break;
                    case 'wf-modification':
                        if (this.mode === 'byGeneration') {
                            phKey = 'select_gen_first_placeholder';
                        } else {
                            phKey = 'select_year_first_placeholder';
                        }
                        break;
                    default: break;
                }
                const phText = phKey ? t(phKey, '...') : '...';

                // Создаем placeholder option с правильными атрибутами (как у других элементов!)
                const placeholderOption = document.createElement('option');
                placeholderOption.value = '';
                if (phKey) {
                    placeholderOption.setAttribute('data-i18n', phKey);
                }
                placeholderOption.textContent = phText;

                // Очищаем и добавляем placeholder
                select.innerHTML = '';
                select.appendChild(placeholderOption);
                select.disabled = true;
            }
        }
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) submitBtn.disabled = true;
    }

    showLoader(type) {
        const loader = document.getElementById(`${type}-loader`);
        if (loader) {
            loader.classList.remove('hidden');
        } else {
            console.warn(`[WheelFit] Loader element '${type}-loader' not found`);
        }
    }

    hideLoader(type) {
        const loader = document.getElementById(`${type}-loader`);
        if (loader) {
            loader.classList.add('hidden');
        } else {
            console.warn(`[WheelFit] Loader element '${type}-loader' not found`);
        }
    }
    showSearchLoader() {
        const searchLoader = document.getElementById('search-loader');
        const searchText = document.getElementById('search-text');

        if (searchLoader) {
            searchLoader.classList.remove('hidden');
        } else {
            console.warn('[WheelFit] Search loader element not found');
        }

        if (searchText) {
            searchText.textContent = typeof t === 'function' ? t('searching', 'Searching...') : 'Searching...';
        }
    }

    hideSearchLoader() {
        const searchLoader = document.getElementById('search-loader');
        const searchText = document.getElementById('search-text');

        if (searchLoader) {
            searchLoader.classList.add('hidden');
        } else {
            console.warn('[WheelFit] Search loader element not found');
        }

        if (searchText) {
            searchText.textContent = typeof t === 'function' ? t('button_search', 'Find Sizes') : 'Find Sizes';
        }
    }
    showError(message) { alert(message); }
    
    async makeAjaxRequest(action, data = {}) {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('nonce', window.WheelFitData.nonce);
        Object.keys(data).forEach(key => formData.append(key, data[key]));

        const response = await fetch(window.WheelFitData.ajaxurl, { method: 'POST', body: formData });
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        return await response.json();
    }

    getDiameters(data) {
        // Extracts number after 'R' from a string
        const r = s => ((s || '').match(/R(\d+)/) || [, ''])[1];
        const front = r(data.tire_full);
        const rear = r(data.rear_tire_full);

        const result = (front && rear && front !== rear) ? [front, rear] : [front];
        // Return only non-empty diameters
        return result.filter(d => d);
    }

    saveSearchToHistory(searchData) {
        this.storage.saveSearch(searchData);
        this.loadSavedSearches();
    }

    saveToGarage(button) {
        try {
            const sizeData = JSON.parse(decodeURIComponent(button.dataset.sizeData));
            const augmentedData = { ...sizeData };

            console.log('[saveToGarage] Size data:', sizeData);
            console.log('[saveToGarage] Selected data:', this.selectedData);
            console.log('[saveToGarage] Current mode:', this.mode);

            const isGenerationFlow = this.mode === 'byGeneration';

            // Try to get vehicle data from selectedData first, then from form elements
            let vehicleData = {
                make: this.selectedData?.make,
                model: this.selectedData?.model,
                year: this.selectedData?.year,
                generation: this.selectedData?.generation,
                modification: this.selectedData?.modification
            };

            // If selectedData is incomplete, try to get from form elements
            const requiredFields = isGenerationFlow
                ? ['make', 'model', 'generation', 'modification']
                : ['make', 'model', 'year', 'modification'];

            const missingFields = requiredFields.filter(field => !vehicleData[field]);

            if (missingFields.length > 0) {
                console.log('[saveToGarage] selectedData incomplete, reading from form elements');

                const makeEl = document.getElementById('wf-make');
                const modelEl = document.getElementById('wf-model');
                const yearEl = document.getElementById('wf-year');
                const generationEl = document.getElementById('wf-generation');
                const modEl = document.getElementById('wf-modification');

                vehicleData = {
                    make: makeEl?.value || vehicleData.make,
                    model: modelEl?.value || vehicleData.model,
                    year: yearEl?.value || vehicleData.year,
                    generation: generationEl?.value || vehicleData.generation,
                    modification: modEl?.value || vehicleData.modification
                };
            }

            console.log('[saveToGarage] Final vehicle data:', vehicleData);

            // Validate required fields based on flow mode
            const stillMissingFields = requiredFields.filter(field => !vehicleData[field]);
            if (stillMissingFields.length > 0) {
                console.error('[saveToGarage] Missing required vehicle data:', { vehicleData, stillMissingFields, requiredFields });
                alert('Cannot save to garage: missing vehicle information. Please complete the vehicle selection first.');
                return false;
            }

            // Add vehicle data to augmented data
            augmentedData.make = vehicleData.make;
            augmentedData.model = vehicleData.model;
            augmentedData.modification = vehicleData.modification;

            if (isGenerationFlow) {
                augmentedData.generation = vehicleData.generation;
                // For generation flow, we might not have a year, so use generation as identifier
            } else {
                augmentedData.year = vehicleData.year;
            }

            // Get modification details for additional info
            let cacheKey, allMods;
            if (isGenerationFlow) {
                cacheKey = `mods_${vehicleData.make}_${vehicleData.model}_${vehicleData.generation}`;
            } else {
                cacheKey = `mods_${vehicleData.make}_${vehicleData.model}_${vehicleData.year}`;
            }

            allMods = this.cache.get(cacheKey) || [];
            const selectedMod = allMods.find(m => m.slug === vehicleData.modification);
            if(selectedMod) {
                augmentedData.fuel = selectedMod.engine?.fuel;
                augmentedData.hp = selectedMod.engine?.power?.hp;
                augmentedData.modLabel = selectedMod.name;
            }

            // Add metadata for data integrity
            augmentedData.garage_version = '2.0';
            augmentedData.flow_order = this.flowOrder;
            augmentedData.saved_timestamp = Date.now();

            console.log('[saveToGarage] Final augmented data:', augmentedData);

            return this.storage.addToGarage(augmentedData);
        } catch (error) {
            console.error('Error saving to garage:', error);
            return false;
        }
    }

    getVehicleLabel() {
        const { make, model, year, generation } = this.selectedData;

        // Generate cache key based on flow mode
        let cacheKey;
        if (this.mode === 'byGeneration' && generation) {
            cacheKey = `mods_${make}_${model}_${generation}`;
        } else {
            cacheKey = `mods_${make}_${model}_${year}`;
        }

        const allMods = this.cache.get(cacheKey) || [];
        const mod = allMods.find(m => m.slug === this.selectedData.modification);

        console.log('[getVehicleLabel] Debug info:', {
            selectedData: this.selectedData,
            mode: this.mode,
            cacheKey,
            allModsCount: allMods.length,
            foundMod: !!mod,
            modEngine: mod?.engine
        });

        // Build label based on flow mode
        let label;
        if (this.mode === 'byGeneration' && generation) {
            // For generation-based flow, show generation name instead of ID
            const generationName = this.getGenerationName(generation);
            label = `${capitalize(make)} ${capitalize(model)} ${capitalize(generationName)}`;
        } else {
            label = `${capitalize(make)} ${capitalize(model)} (${year})`;
        }

        if (mod) {
            // Add modification name if available
            if (mod.name) {
                label += ` — ${mod.name}`;
            }

            // Add engine details like in createModInfoBar
            const engineDetails = [];
            if (mod.engine?.type) {
                engineDetails.push(mod.engine.type);
            }
            if (mod.engine?.fuel) {
                engineDetails.push(mod.engine.fuel.toUpperCase());
            }
            if (mod.engine?.power?.hp) {
                engineDetails.push(`${mod.engine.power.hp} HP`);
            }

            if (engineDetails.length > 0) {
                label += ` ${engineDetails.join(' ')}`;
            }

            console.log('[getVehicleLabel] Engine details added:', engineDetails);
        } else {
            console.warn('[getVehicleLabel] No modification found for:', this.selectedData.modification);
            console.warn('[getVehicleLabel] Available modifications:', allMods.map(m => ({ slug: m.slug, name: m.name })));
        }

        console.log('[getVehicleLabel] Final label:', label);
        return label;
    }

    /**
     * Get generation name from cache by generation slug/ID
     */
    getGenerationName(generationSlug) {
        if (!generationSlug) return 'Unknown Generation';

        const { make, model } = this.selectedData;
        const generationsCacheKey = `generations_${make}_${model}`;
        const generations = this.cache.get(generationsCacheKey) || [];

        console.log('[getGenerationName] Looking for generation:', {
            generationSlug,
            cacheKey: generationsCacheKey,
            availableGenerations: generations
        });

        // Helper function to detect internal IDs
        const isInternalId = (str) => {
            if (!str || typeof str !== 'string') return false;
            return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
        };

        // Find generation by slug/value/id
        const generation = generations.find(g => {
            const value = g.slug || g.id || g.name || g.title || g.range || g.year_range || g.gen;
            return value === generationSlug;
        });

        if (generation) {
            // Return the display name, avoiding internal IDs
            let name = generation.name || generation.title || generation.range || generation.year_range || generation.gen || generation.slug || generation.id || 'Unknown Generation';

            // Если полученное имя является бесполезным placeholder-ом, заменяем на slug/ID
            if (name && String(name).trim().toLowerCase() === 'generation') {
                name = generation.slug || generation.id || name;
            }

            // If the name looks like an internal ID, try to find a better alternative
            if (isInternalId(name)) {
                const betterName = generation.name || generation.title || generation.range || generation.year_range || generation.gen;
                if (betterName && String(betterName).trim().toLowerCase() !== 'generation') {
                    name = betterName;
                }
            }

            return name;
        }

        // Если не нашли совпадение в кеше – возвращаем сам slug (даже если это внутренний ID),
        // но никогда не возвращаем общий placeholder "Generation".
        return generationSlug;
    }

    async loadTireSizeOptions() {
        // ONLY load widths initially
        const widthsRes = await this.makeAjaxRequest('wf_get_tire_widths');
        if (widthsRes.success) {
            this.populateSelect('tire-width', widthsRes.data, t('select_width_first_placeholder', 'Select width'));
        }

        // Ensure others are disabled and have placeholder
        const profileSelect = document.getElementById('tire-profile');
        if (profileSelect) {
            this.populateSelect('tire-profile', [], t('select_profile_first_placeholder', 'Select profile'));
            profileSelect.disabled = true;
        }
        const diameterSelect = document.getElementById('tire-diameter');
        if (diameterSelect) {
            this.populateSelect('tire-diameter', [], t('select_diameter_first_placeholder', 'Select diameter'));
            diameterSelect.disabled = true;
        }
    }

    populateSelect(elementId, options, placeholder) {
        const select = document.getElementById(elementId);
        if (!select) return;

        // Быстрое определение ключа перевода
        const translationKeyMap = {
            'wf-make': 'select_make_placeholder',
            'wf-model': 'select_model_placeholder',
            'wf-year': 'select_year_placeholder',
            'wf-modification': 'select_mods_placeholder',
            'wf-generation': 'select_gen_placeholder'
        };

        const translationKey = translationKeyMap[elementId];

        // Быстрое создание placeholder option
        const placeholderOption = document.createElement('option');
        placeholderOption.value = '';

        if (translationKey) {
            placeholderOption.setAttribute('data-i18n', translationKey);
            // ИСПОЛЬЗУЕМ ПЕРЕВОДЫ НАПРЯМУЮ (как при первой загрузке!)
            placeholderOption.textContent = window.WheelFitI18n && window.WheelFitI18n[translationKey]
                ? window.WheelFitI18n[translationKey]
                : placeholder;
        } else {
            placeholderOption.textContent = placeholder;
        }

        // Быстрое заполнение
        select.innerHTML = '';
        select.appendChild(placeholderOption);

        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.slug || option.name;
            optionElement.textContent = option.name;
            select.appendChild(optionElement);
        });
    }

    /**
     * Handle filter warning messages for empty results - show as toast
     */
    handleFilterWarning(filterInfo, stepType) {
        // Remove any existing warnings first
        this.clearFilterWarnings();

        if (!filterInfo || !filterInfo.empty_due_to_filters) {
            return;
        }

        // Show toast notification instead of inline warning
        this.showFilterToast(filterInfo);
    }

    /**
     * Show filter warning as toast notification
     */
    showFilterToast(filterInfo) {
        // Remove any existing filter toasts first to prevent duplicates
        const existingFilterToasts = document.querySelectorAll('.wsf-filter-toast');
        existingFilterToasts.forEach(toast => {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        });

        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('wsf-toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'wsf-toast-container';
            toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';

            // Calculate top position based on admin bar
            const adminBar = document.getElementById('wpadminbar');
            const topOffset = adminBar ? (adminBar.offsetHeight + 20) : 60;

            toastContainer.style.cssText = `
                position: fixed !important;
                top: ${topOffset}px !important;
                right: 20px !important;
                z-index: 9999 !important;
                pointer-events: none;
            `;
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'wsf-filter-toast bg-red-500 text-white rounded-lg shadow-lg p-4 max-w-sm transform transition-all duration-300 translate-x-full opacity-0';
        toast.setAttribute('data-filter-warning', 'true');
        toast.style.cssText = `
            background-color: #ef4444 !important;
            color: white !important;
            border-radius: 8px !important;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
            padding: 16px !important;
            max-width: 384px !important;
            pointer-events: auto !important;
            transform: translateX(100%) !important;
            opacity: 0 !important;
            transition: all 0.3s ease !important;
        `;

        const icon = `<svg style="width: 20px; height: 20px; flex-shrink: 0; margin-right: 8px;" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>`;

        toast.innerHTML = `
            <div style="display: flex; align-items: flex-start;">
                ${icon}
                <div style="flex: 1; min-width: 0;">
                    <h4 style="font-weight: 600; margin: 0 0 8px 0; font-size: 14px;">${window.t('filter_warning_title', 'No brands available')}</h4>
                    <p style="margin: 0 0 12px 0; font-size: 13px; opacity: 0.9;">${filterInfo.message}</p>
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <button type="button" class="clear-filters-btn" style="
                            background: rgba(255,255,255,0.2) !important;
                            color: white !important;
                            border: 1px solid rgba(255,255,255,0.3) !important;
                            padding: 4px 8px !important;
                            border-radius: 4px !important;
                            font-size: 12px !important;
                            cursor: pointer !important;
                            transition: background-color 0.2s !important;
                        " onmouseover="this.style.backgroundColor='rgba(255,255,255,0.3)'" onmouseout="this.style.backgroundColor='rgba(255,255,255,0.2)'">
                            ${window.t('clear_filters', 'Clear Filters')}
                        </button>
                        <button type="button" class="dismiss-toast-btn" style="
                            background: transparent !important;
                            color: rgba(255,255,255,0.8) !important;
                            border: none !important;
                            padding: 4px 8px !important;
                            border-radius: 4px !important;
                            font-size: 12px !important;
                            cursor: pointer !important;
                            transition: color 0.2s !important;
                        " onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.8)'">
                            ${window.t('dismiss', 'Dismiss')}
                        </button>
                    </div>
                </div>
                <button type="button" class="close-toast-btn" style="
                    background: none !important;
                    border: none !important;
                    color: rgba(255,255,255,0.8) !important;
                    cursor: pointer !important;
                    padding: 0 !important;
                    margin-left: 8px !important;
                    font-size: 18px !important;
                    line-height: 1 !important;
                    width: 20px !important;
                    height: 20px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                " onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.8)'">&times;</button>
            </div>
        `;

        // Add event listeners
        const clearBtn = toast.querySelector('.clear-filters-btn');
        const dismissBtn = toast.querySelector('.dismiss-toast-btn');
        const closeBtn = toast.querySelector('.close-toast-btn');

        const closeToast = () => {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        };

        clearBtn?.addEventListener('click', () => {
            this.clearAllFilters();
            closeToast();
        });

        dismissBtn?.addEventListener('click', closeToast);
        closeBtn?.addEventListener('click', closeToast);

        // Add to container and animate in
        toastContainer.appendChild(toast);

        // Animate in
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        });

        // Auto-dismiss after 8 seconds
        setTimeout(closeToast, 8000);
    }

    /**
     * Clear all filter warnings (toasts)
     */
    clearFilterWarnings() {
        const warnings = document.querySelectorAll('[data-filter-warning="true"], .wsf-filter-toast');
        warnings.forEach(warning => {
            warning.style.transform = 'translateX(100%)';
            warning.style.opacity = '0';
            setTimeout(() => warning.remove(), 300);
        });
    }

    /**
     * Clear all filters (redirect to admin or show helpful message)
     */
    clearAllFilters() {
        // Show a more helpful toast message
        this.showInfoToast(
            window.t('clear_filters_admin', 'To clear filters, please go to WordPress admin > Wheel-Size > Features and Brand Filters.'),
            'info'
        );
    }

    /**
     * Show info toast notification
     */
    showInfoToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('wsf-toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'wsf-toast-container';
            toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';

            // Calculate top position based on admin bar
            const adminBar = document.getElementById('wpadminbar');
            const topOffset = adminBar ? (adminBar.offsetHeight + 20) : 60;

            toastContainer.style.cssText = `
                position: fixed !important;
                top: ${topOffset}px !important;
                right: 20px !important;
                z-index: 9999 !important;
                pointer-events: none;
            `;
            document.body.appendChild(toastContainer);
        }

        const bgColor = type === 'info' ? '#3b82f6' : '#10b981';

        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'wsf-info-toast';
        toast.style.cssText = `
            background-color: ${bgColor} !important;
            color: white !important;
            border-radius: 8px !important;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
            padding: 16px !important;
            max-width: 384px !important;
            pointer-events: auto !important;
            transform: translateX(100%) !important;
            opacity: 0 !important;
            transition: all 0.3s ease !important;
        `;

        toast.innerHTML = `
            <div style="display: flex; align-items: flex-start;">
                <div style="flex: 1; min-width: 0;">
                    <p style="margin: 0; font-size: 14px;">${message}</p>
                </div>
                <button type="button" class="close-toast-btn" style="
                    background: none !important;
                    border: none !important;
                    color: rgba(255,255,255,0.8) !important;
                    cursor: pointer !important;
                    padding: 0 !important;
                    margin-left: 8px !important;
                    font-size: 18px !important;
                    line-height: 1 !important;
                    width: 20px !important;
                    height: 20px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                " onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.8)'">&times;</button>
            </div>
        `;

        // Add close functionality
        const closeBtn = toast.querySelector('.close-toast-btn');
        const closeToast = () => {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        };
        closeBtn?.addEventListener('click', closeToast);

        // Add to container and animate in
        toastContainer.appendChild(toast);

        // Animate in
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        });

        // Auto-dismiss after 5 seconds
        setTimeout(closeToast, 5000);
    }
}

/**
 * LocalStorage handler for saving searches and garage
 */
class LocalStorageHandler {
    constructor() {
        this.searchKey = 'wheel_fit_searches';
        this.garageKey = 'wheel_fit_garage';
        this.maxSearchHistory = 10;
        this.maxGarageItems = 50;
    }

    saveSearch(searchData) {
        try {
            const searches = this.getSearchHistory();
            const newSearch = {
                ...searchData,
                timestamp: Date.now(),
                id: this.generateId()
            };

            // Remove duplicate if exists
            const filtered = searches.filter(s => 
                !(s.make === newSearch.make && s.model === newSearch.model && s.year === newSearch.year && s.modification === newSearch.modification)
            );

            // Add new search at the beginning
            filtered.unshift(newSearch);

            // Limit array size
            if (filtered.length > this.maxSearchHistory) {
                filtered.splice(this.maxSearchHistory);
            }

            localStorage.setItem(this.searchKey, JSON.stringify(filtered));
        } catch (error) {
            console.error('Error saving search to localStorage:', error);
        }
    }

    getSearchHistory() {
        try {
            const data = localStorage.getItem(this.searchKey);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error reading search history:', error);
            return [];
        }
    }

    addToGarage(sizeData) {
        try {
            const garage = this.getGarage();
            const newItem = { ...sizeData, id: Math.random().toString(36).substring(2, 11) };

            // Improved duplicate detection: check both tire sizes AND vehicle info
            const isDuplicate = garage.some(item => {
                const sameTires = item.tire_full === newItem.tire_full &&
                                 (item.rear_tire_full || '') === (newItem.rear_tire_full || '');
                const sameVehicle = item.make === newItem.make &&
                                   item.model === newItem.model &&
                                   item.year === newItem.year &&
                                   item.modification === newItem.modification;
                return sameTires && sameVehicle;
            });

            if (isDuplicate) {
                if(window.showGarageToast) window.showGarageToast('garage_already_notification');
                return false;
            };

            garage.unshift(newItem);
            if (garage.length > this.maxGarageItems) garage.splice(this.maxGarageItems);
            localStorage.setItem(this.garageKey, JSON.stringify(garage));

            // Also save to recent searches to make loading more reliable
            this.saveSearch(newItem);

            if(window.showGarageToast) window.showGarageToast('garage_saved_notification');
            if(window.updateGarageCounter) window.updateGarageCounter();

            return true;
        } catch (error) {
            console.error('Error saving to garage:', error);
            return false;
        }
    }

    getGarage() {
        try {
            const data = localStorage.getItem(this.garageKey);
            const garage = data ? JSON.parse(data) : [];

            // Check if migration is needed
            const needsMigration = garage.some(item => !item.garage_version);
            if (needsMigration) {
                console.log('[Garage] Legacy data detected, migration may be needed');
            }

            return garage;
        } catch (error) {
            console.error('Error reading garage:', error);
            return [];
        }
    }

    // Migration function for legacy garage data
    migrateGarageData() {
        try {
            const garage = this.getGarage();
            let migrated = false;

            const migratedGarage = garage.map(item => {
                if (!item.garage_version) {
                    console.log('[Garage Migration] Migrating legacy item:', item);
                    migrated = true;
                    return {
                        ...item,
                        garage_version: '2.0',
                        migrated_timestamp: Date.now(),
                        // Mark as legacy if missing vehicle data
                        is_legacy: !item.make || !item.model || !item.year || !item.modification
                    };
                }
                return item;
            });

            if (migrated) {
                localStorage.setItem(this.garageKey, JSON.stringify(migratedGarage));
                console.log('[Garage Migration] Migration completed');
            }

            return migratedGarage;
        } catch (error) {
            console.error('Error migrating garage data:', error);
            return this.getGarage();
        }
    }

    generateId() {
        return Math.random().toString(36).substring(2, 11);
    }

    clearSearchHistory() {
        localStorage.removeItem(this.searchKey);
    }

    removeFromGarage(itemId) {
        try {
            let garage = this.getGarage();
            garage = garage.filter(item => item.id !== itemId);
            localStorage.setItem(this.garageKey, JSON.stringify(garage));
        } catch (error) {
            console.error('Error removing from garage:', error);
        }
    }

    clearGarage() {
        try {
            localStorage.removeItem(this.garageKey);
        } catch (error) {
            console.error('Error clearing garage:', error);
        }
    }
}

// Initialize widget when DOM is ready
let wheelFitWidget;

function applyStaticTranslations(container = document) {
    if (!window.WheelFitI18n) {
        console.warn('[Wheel-Size i18n] Translation object not found.');
        return;
    }

    console.log('[applyStaticTranslations] Available translations:', window.WheelFitI18n);
    console.log('[applyStaticTranslations] Container:', container);

    // Apply translations to text content elements
    const elements = container.querySelectorAll('[data-i18n]');
    console.log(`[applyStaticTranslations] Found ${elements.length} elements with data-i18n:`, elements);

    if (elements.length === 0) {
        console.warn('[applyStaticTranslations] No elements with data-i18n found in container');
        // Let's also check if there are any data-i18n elements in the entire document
        const allElements = document.querySelectorAll('[data-i18n]');
        console.log(`[applyStaticTranslations] Total data-i18n elements in document: ${allElements.length}`, allElements);
    }

    elements.forEach((el, index) => {
        const key = el.dataset.i18n;
        const originalText = el.textContent.trim();
        const translation = t(key, originalText);
        const changed = translation !== originalText;

        console.log(`[applyStaticTranslations] Element ${index + 1}:`, {
            element: el,
            key: key,
            originalText: originalText,
            translation: translation,
            changed: changed,
            tagName: el.tagName,
            className: el.className
        });

        el.textContent = translation;
    });

    // Apply translations to placeholder attributes
    const placeholderElements = container.querySelectorAll('[data-i18n-placeholder]');
    console.log(`[applyStaticTranslations] Found ${placeholderElements.length} elements with data-i18n-placeholder`);

    placeholderElements.forEach((el, index) => {
        const key = el.dataset.i18nPlaceholder;
        const originalPlaceholder = el.placeholder || '';
        const translation = t(key, originalPlaceholder);

        console.log(`[applyStaticTranslations] Placeholder ${index + 1}:`, {
            element: el,
            key: key,
            originalPlaceholder: originalPlaceholder,
            translation: translation
        });

        el.placeholder = translation;
    });

    // Apply translations to value attributes (for buttons and inputs)
    const valueElements = container.querySelectorAll('[data-i18n-value]');
    console.log(`[applyStaticTranslations] Found ${valueElements.length} elements with data-i18n-value`);

    valueElements.forEach((el, index) => {
        const key = el.dataset.i18nValue;
        const originalValue = el.value || '';
        const translation = t(key, originalValue);

        console.log(`[applyStaticTranslations] Value ${index + 1}:`, {
            element: el,
            key: key,
            originalValue: originalValue,
            translation: translation
        });

        el.value = translation;
    });

    // Apply translations to title attributes (for tooltips)
    const titleElements = container.querySelectorAll('[data-i18n-title]');
    console.log(`[applyStaticTranslations] Found ${titleElements.length} elements with data-i18n-title`);

    titleElements.forEach((el, index) => {
        const key = el.dataset.i18nTitle;
        const originalTitle = el.title || '';
        const translation = t(key, originalTitle);

        console.log(`[applyStaticTranslations] Title ${index + 1}:`, {
            element: el,
            key: key,
            originalTitle: originalTitle,
            translation: translation
        });

        el.title = translation;
    });

    // Apply translations to aria-label attributes (for accessibility)
    const ariaLabelElements = container.querySelectorAll('[data-i18n-aria-label]');
    console.log(`[applyStaticTranslations] Found ${ariaLabelElements.length} elements with data-i18n-aria-label`);

    ariaLabelElements.forEach((el, index) => {
        const key = el.dataset.i18nAriaLabel;
        const originalAriaLabel = el.getAttribute('aria-label') || '';
        const translation = t(key, originalAriaLabel);

        console.log(`[applyStaticTranslations] Aria-label ${index + 1}:`, {
            element: el,
            key: key,
            originalAriaLabel: originalAriaLabel,
            translation: translation
        });

        el.setAttribute('aria-label', translation);
    });
}

// Function to re-apply translations when widget is dynamically updated
function reapplyTranslations(container = document) {
    console.log('[reapplyTranslations] Re-applying translations to container:', container);

    // Ensure we have the latest translations
    if (!window.WheelFitI18n) {
        console.warn('[reapplyTranslations] No translations available');
        return;
    }

    // Apply static translations
    applyStaticTranslations(container);

    // Also re-translate any dynamically generated content
    const selectElements = container.querySelectorAll('select');
    selectElements.forEach(select => {
        // Re-translate placeholder options (используем data-i18n как у labels!)
        const placeholderOption = select.querySelector('option[value=""]');
        if (placeholderOption && placeholderOption.dataset.i18n) {
            const key = placeholderOption.dataset.i18n;
            const translation = window.WheelFitI18n && window.WheelFitI18n[key]
                ? window.WheelFitI18n[key]
                : placeholderOption.textContent;
            placeholderOption.textContent = translation;
            console.log(`[reapplyTranslations] Re-translated select option "${key}": "${translation}"`);
        }
    });

    console.log('[reapplyTranslations] Translation re-application completed');
}

// Make reapplyTranslations globally available
window.reapplyTranslations = reapplyTranslations;

(function initWheelFit() {
    function startWidget() {
        if (window.wheelFitWidget) {
            console.log('[Finder] Widget already exists, skipping initialization');
            return;
        }

        console.log('[Finder] Starting widget initialization...');

        try {
            // Apply translations first
            console.log('[Finder] Applying initial translations');
            applyStaticTranslations();

            // Create widget instance
            window.wheelFitWidget = new WheelFitWidget();
            window.wheelFitWidgetReady = true;

            console.log('[Finder] WheelFitWidget created successfully');

            // Dispatch events after a small delay to ensure everything is set up
            setTimeout(() => {
                console.log('[Finder] Dispatching widget ready events');

                // Re-apply translations after widget initialization in case new elements were created
                reapplyTranslations();

                document.dispatchEvent(new CustomEvent('wheelFitReady', { detail: window.wheelFitWidget }));
                document.dispatchEvent(new CustomEvent('wheelFitWidgetReady', { detail: window.wheelFitWidget }));

                console.log('[Finder] Widget initialization complete:', {
                    widget: !!window.wheelFitWidget,
                    loadMethod: !!(window.wheelFitWidget && typeof window.wheelFitWidget.loadFromHistory === 'function'),
                    mode: window.wheelFitWidget?.mode,
                    flowOrder: window.wheelFitWidget?.flowOrder,
                    documentState: document.readyState,
                    translations: !!window.WheelFitI18n
                });
            }, 50);

        } catch (error) {
            console.error('[Finder] Error initializing widget:', error);
        }
    }

    if (document.readyState === 'loading') {
        console.log('[Finder] Document still loading, waiting for DOMContentLoaded');
        document.addEventListener('DOMContentLoaded', startWidget);
    } else {
        console.log('[Finder] Document already ready, starting widget immediately');
        startWidget();
    }
})();

// Helper animation functions
function fadeOut(el, cb) {
    if (!el || el.classList.contains('hidden')) { cb && cb(); return; }
    // Ensure transition classes present
    el.classList.add('transition-all','duration-300','ease-in-out','transform');
    el.classList.add('opacity-0','scale-95');
    el.addEventListener('transitionend', () => {
        el.classList.add('hidden');
        el.classList.remove('opacity-0','scale-95');
        cb && cb();
    }, { once: true });
}

function fadeIn(el) {
    if (!el) return;
    el.classList.add('transition-all','duration-300','ease-in-out','transform');
    el.classList.add('opacity-0','translate-y-2');
    el.classList.remove('hidden');
    requestAnimationFrame(() => {
        el.classList.remove('opacity-0','translate-y-2');
    });
} 

// Universal i18n helper for all widgets
if (typeof window.t !== 'function') {
    window.t = function (key, fallback = '') {
        return (window.WheelFitI18n && window.WheelFitI18n[key]) || fallback;
    };
} 