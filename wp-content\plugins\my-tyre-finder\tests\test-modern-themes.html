<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Theme Color Schemes Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        /* Light Theme Variables */
        .theme-light {
            --wsf-bg: #FFFFFF;
            --wsf-surface: #FAFBFC;
            --wsf-surface-hover: #F1F3F5;
            --wsf-text: #1A1D23;
            --wsf-text-primary: #1A1D23;
            --wsf-text-secondary: #6B7280;
            --wsf-text-muted: #9CA3AF;
            --wsf-primary: #2563EB;
            --wsf-hover: #1D4ED8;
            --wsf-accent: #0EA5E9;
            --wsf-on-primary: #FFFFFF;
            --wsf-border: #E5E7EB;
            --wsf-secondary: #D1D5DB;
            --wsf-muted: #F3F4F6;
            --wsf-input-bg: #FFFFFF;
            --wsf-input-text: #1A1D23;
            --wsf-input-border: #D1D5DB;
            --wsf-input-placeholder: #9CA3AF;
            --wsf-input-focus: #2563EB;
            --wsf-success: #059669;
            --wsf-warning: #D97706;
            --wsf-error: #DC2626;
            --wsf-focus-ring: rgba(37, 99, 235, 0.2);
        }

        /* Dark Theme Variables */
        .theme-dark {
            --wsf-bg: #0B1120;
            --wsf-surface: #1A1F2E;
            --wsf-surface-hover: #252A3A;
            --wsf-text: #F8FAFC;
            --wsf-text-primary: #F8FAFC;
            --wsf-text-secondary: #CBD5E1;
            --wsf-text-muted: #94A3B8;
            --wsf-primary: #3B82F6;
            --wsf-hover: #60A5FA;
            --wsf-accent: #06B6D4;
            --wsf-on-primary: #FFFFFF;
            --wsf-border: #374151;
            --wsf-secondary: #4B5563;
            --wsf-muted: #1F2937;
            --wsf-input-bg: #1A1F2E;
            --wsf-input-text: #F8FAFC;
            --wsf-input-border: #374151;
            --wsf-input-placeholder: #9CA3AF;
            --wsf-input-focus: #3B82F6;
            --wsf-success: #10B981;
            --wsf-warning: #F59E0B;
            --wsf-error: #EF4444;
            --wsf-focus-ring: rgba(59, 130, 246, 0.4);
        }

        /* Apply theme styles */
        .theme-container {
            background-color: var(--wsf-bg);
            color: var(--wsf-text);
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            background-color: var(--wsf-surface);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid var(--wsf-border);
            margin-bottom: 30px;
            text-align: center;
        }

        .theme-switcher {
            margin: 20px 0;
            text-align: center;
        }

        .theme-btn {
            background-color: var(--wsf-primary);
            color: var(--wsf-on-primary);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .theme-btn:hover {
            background-color: var(--wsf-hover);
            transform: translateY(-1px);
        }

        .theme-btn.active {
            background-color: var(--wsf-accent);
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background-color: var(--wsf-surface);
            border: 1px solid var(--wsf-border);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s ease;
        }

        .card:hover {
            background-color: var(--wsf-surface-hover);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card h3 {
            color: var(--wsf-text-primary);
            margin-bottom: 15px;
            font-size: 18px;
        }

        .card p {
            color: var(--wsf-text-secondary);
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            color: var(--wsf-text);
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            background-color: var(--wsf-input-bg);
            color: var(--wsf-input-text);
            border: 1px solid var(--wsf-input-border);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--wsf-input-focus);
            box-shadow: 0 0 0 3px var(--wsf-focus-ring);
        }

        .form-input::placeholder {
            color: var(--wsf-input-placeholder);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--wsf-primary);
            color: var(--wsf-on-primary);
        }

        .btn-primary:hover {
            background-color: var(--wsf-hover);
        }

        .btn-success {
            background-color: var(--wsf-success);
            color: white;
        }

        .btn-warning {
            background-color: var(--wsf-warning);
            color: white;
        }

        .btn-error {
            background-color: var(--wsf-error);
            color: white;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .color-swatch {
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            border: 1px solid var(--wsf-border);
        }

        .muted-text {
            color: var(--wsf-text-muted);
            font-size: 14px;
        }

        .divider {
            height: 1px;
            background-color: var(--wsf-border);
            margin: 20px 0;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
            margin: 20px 0;
        }

        .stat {
            padding: 15px;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: var(--wsf-primary);
        }

        .stat-label {
            color: var(--wsf-text-secondary);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="theme-container" class="theme-container theme-light">
        <div class="header">
            <h1>🎨 Modern Theme Color Schemes</h1>
            <p class="muted-text">Testing the new minimalistic light and dark themes for the tire finder plugin</p>
            
            <div class="theme-switcher">
                <button class="theme-btn active" onclick="switchTheme('light')">☀️ Light Theme</button>
                <button class="theme-btn" onclick="switchTheme('dark')">🌙 Dark Theme</button>
            </div>
        </div>

        <div class="content-grid">
            <div class="card">
                <h3>🔍 Search Form</h3>
                <p>Example tire finder form with modern styling</p>
                
                <div class="form-group">
                    <label class="form-label">Vehicle Make</label>
                    <input type="text" class="form-input" placeholder="Enter vehicle make...">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Tire Size</label>
                    <input type="text" class="form-input" placeholder="e.g., 225/60R16">
                </div>
                
                <button class="btn btn-primary">🔍 Search Tires</button>
            </div>

            <div class="card">
                <h3>📊 Statistics</h3>
                <p>Dashboard-style statistics display</p>
                
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number">1,247</div>
                        <div class="stat-label">Tire Models</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">89</div>
                        <div class="stat-label">Brands</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">456</div>
                        <div class="stat-label">Sizes</div>
                    </div>
                </div>
                
                <div class="divider"></div>
                <p class="muted-text">Updated 2 minutes ago</p>
            </div>

            <div class="card">
                <h3>🎯 Action Buttons</h3>
                <p>Various button states and colors</p>
                
                <button class="btn btn-primary">Primary Action</button>
                <button class="btn btn-success">✅ Success</button>
                <button class="btn btn-warning">⚠️ Warning</button>
                <button class="btn btn-error">❌ Error</button>
            </div>

            <div class="card">
                <h3>🎨 Color Palette</h3>
                <p>All theme colors in action</p>
                
                <div class="color-palette">
                    <div class="color-swatch" style="background-color: var(--wsf-primary); color: var(--wsf-on-primary);">
                        Primary
                    </div>
                    <div class="color-swatch" style="background-color: var(--wsf-accent); color: white;">
                        Accent
                    </div>
                    <div class="color-swatch" style="background-color: var(--wsf-success); color: white;">
                        Success
                    </div>
                    <div class="color-swatch" style="background-color: var(--wsf-warning); color: white;">
                        Warning
                    </div>
                    <div class="color-swatch" style="background-color: var(--wsf-error); color: white;">
                        Error
                    </div>
                    <div class="color-swatch" style="background-color: var(--wsf-surface); color: var(--wsf-text); border: 1px solid var(--wsf-border);">
                        Surface
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📝 Theme Features</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: var(--wsf-primary); margin-bottom: 10px;">✨ Light Theme</h4>
                    <ul style="color: var(--wsf-text-secondary); line-height: 1.8;">
                        <li>Pure white background for maximum cleanliness</li>
                        <li>High contrast text (16.7:1 ratio)</li>
                        <li>Professional blue primary color</li>
                        <li>WCAG AA compliant accessibility</li>
                        <li>Subtle shadows and hover effects</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: var(--wsf-primary); margin-bottom: 10px;">🌙 Dark Theme</h4>
                    <ul style="color: var(--wsf-text-secondary); line-height: 1.8;">
                        <li>Deep blue-gray background (eye-friendly)</li>
                        <li>Optimized contrast for dark mode</li>
                        <li>Brighter accent colors for visibility</li>
                        <li>Reduced eye strain for night use</li>
                        <li>Modern automotive aesthetic</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTheme(theme) {
            const container = document.getElementById('theme-container');
            const buttons = document.querySelectorAll('.theme-btn');
            
            // Remove existing theme classes
            container.classList.remove('theme-light', 'theme-dark');
            
            // Add new theme class
            container.classList.add(`theme-${theme}`);
            
            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Log theme switch for testing
            console.log(`Switched to ${theme} theme`);
        }

        // Initialize
        console.log('Modern theme test loaded');
        console.log('Available CSS custom properties:', Object.keys(document.documentElement.style).filter(prop => prop.startsWith('--wsf')));
    </script>
</body>
</html>
