<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Consistency Test</title>
    <link rel="stylesheet" href="assets/css/wheel-fit-shared.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .color-controls {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .color-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .color-input-group label {
            min-width: 120px;
            font-weight: 500;
        }
        .color-input-group input[type="color"] {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Color Consistency Test</h1>
        <p>Тест для проверки единообразия цветов кнопок после исправления рассинхронизации.</p>

        <!-- Color Controls -->
        <div class="color-controls">
            <h3>🎨 Color Controls</h3>
            <div class="color-input-group">
                <label for="primary-color">Primary Color:</label>
                <input type="color" id="primary-color" value="#2563eb">
                <span id="primary-value">#2563eb</span>
            </div>
            <div class="color-input-group">
                <label for="hover-color">Hover Color:</label>
                <input type="color" id="hover-color" value="#1d4ed8">
                <span id="hover-value">#1d4ed8</span>
            </div>
        </div>

        <!-- Test Sections -->
        <div class="test-section">
            <h3>1. Кнопки с bg-[color:var(--wsf-primary)]</h3>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb); --wsf-hover: var(--test-hover, #1d4ed8);">
                <button type="submit" class="bg-[color:var(--wsf-primary)] hover:bg-[color:var(--wsf-hover)] text-white font-semibold py-3 px-6 rounded-lg transition mr-2">
                    Find Sizes (Direct CSS Var)
                </button>
                <button type="submit" class="bg-[color:var(--wsf-primary)] hover:bg-[color:var(--wsf-hover)] text-white font-semibold py-3 px-6 rounded-lg transition" disabled>
                    Disabled
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>2. Прогресс-бар и индикаторы</h3>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb);">
                <div class="bg-gray-200 rounded-full h-2 mb-4">
                    <div class="bg-[color:var(--wsf-primary)] h-2 rounded-full transition-all duration-500" style="width: 60%"></div>
                </div>
                <span class="inline-block ml-1 px-2 py-1 rounded-full text-xs font-bold text-white bg-[color:var(--wsf-primary)]">
                    Badge
                </span>
            </div>
        </div>

        <div class="test-section">
            <h3>3. Garage Toast и уведомления</h3>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb);">
                <div class="bg-[color:var(--wsf-primary)] text-white text-sm font-medium px-4 py-2 rounded-lg shadow-lg">
                    Saved to Garage!
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. Спиннеры и лоадеры</h3>
            <div class="wsf-finder-widget" style="--wsf-primary: var(--test-primary, #2563eb);">
                <div class="flex items-center gap-4">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-wsf-primary"></div>
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2" style="border-color: var(--wsf-primary)"></div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-results">
            <h3>🧪 Test Results</h3>
            <button onclick="runColorTest()" style="background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: 500;">
                Run Color Consistency Test
            </button>
            <div id="test-results" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script>
        function updateColors() {
            const primaryColor = document.getElementById('primary-color').value;
            const hoverColor = document.getElementById('hover-color').value;
            
            document.getElementById('primary-value').textContent = primaryColor;
            document.getElementById('hover-value').textContent = hoverColor;
            
            // Apply to all test widgets
            document.documentElement.style.setProperty('--test-primary', primaryColor);
            document.documentElement.style.setProperty('--test-hover', hoverColor);
            
            // Also apply to widgets directly
            document.querySelectorAll('.wsf-finder-widget').forEach(widget => {
                widget.style.setProperty('--wsf-primary', primaryColor);
                widget.style.setProperty('--wsf-hover', hoverColor);
            });
        }

        function runColorTest() {
            const results = [];
            const widgets = document.querySelectorAll('.wsf-finder-widget');
            
            // Test 1: Check if all buttons have the same computed background color
            const buttons = document.querySelectorAll('button[type="submit"]:not([disabled])');
            const buttonColors = Array.from(buttons).map(btn => {
                const style = window.getComputedStyle(btn);
                return style.backgroundColor;
            });
            
            const uniqueButtonColors = [...new Set(buttonColors)];
            const buttonsConsistent = uniqueButtonColors.length === 1;
            
            // Test 2: Check if all primary color elements use the same color
            const primaryElements = document.querySelectorAll('[class*="bg-[color:var(--wsf-primary)]"]');
            const primaryColors = Array.from(primaryElements).map(el => {
                const style = window.getComputedStyle(el);
                return style.backgroundColor;
            });
            
            const uniquePrimaryColors = [...new Set(primaryColors)];
            const primaryConsistent = uniquePrimaryColors.length <= 1;
            
            // Test 3: Check CSS variable values
            const rootStyle = window.getComputedStyle(document.documentElement);
            const testPrimary = rootStyle.getPropertyValue('--test-primary').trim();
            const expectedPrimary = document.getElementById('primary-color').value;
            
            // Display results
            const resultsDiv = document.getElementById('test-results');
            let html = '<h4>Test Results:</h4>';
            
            html += `<p><strong>Button Consistency:</strong> ${buttonsConsistent ? '✅ All buttons same color' : '❌ Buttons have different colors'}</p>`;
            if (!buttonsConsistent) {
                html += `<p>Found colors: ${uniqueButtonColors.join(', ')}</p>`;
            }
            
            html += `<p><strong>Primary Elements:</strong> ${primaryConsistent ? '✅ All primary elements consistent' : '❌ Primary elements inconsistent'}</p>`;
            if (!primaryConsistent) {
                html += `<p>Found colors: ${uniquePrimaryColors.join(', ')}</p>`;
            }
            
            html += `<p><strong>CSS Variables:</strong> ${testPrimary ? '✅ CSS variables working' : '❌ CSS variables not found'}</p>`;
            html += `<p>Expected: ${expectedPrimary}, Found: ${testPrimary}</p>`;
            
            const allPassed = buttonsConsistent && primaryConsistent && testPrimary;
            html += `<p><strong>Overall:</strong> ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}</p>`;
            
            resultsDiv.innerHTML = html;
        }

        // Initialize
        document.getElementById('primary-color').addEventListener('change', updateColors);
        document.getElementById('hover-color').addEventListener('change', updateColors);
        updateColors();
    </script>
</body>
</html>
