/**
 * Полный тест системы тем
 * Проверяет все компоненты: Live Preview, CSS переменные, применение тем
 */

(function() {
    'use strict';

    console.log('🎨 === ПОЛНЫЙ ТЕСТ СИСТЕМЫ ТЕМ ===');

    // Конфигурация тестов
    const config = {
        // CSS переменные для проверки
        requiredVariables: [
            '--wsf-bg',
            '--wsf-text', 
            '--wsf-primary',
            '--wsf-hover',
            '--wsf-border',
            '--wsf-muted'
        ],
        
        // Проблемные классы (должны быть заменены)
        problematicClasses: [
            'bg-white',
            'bg-slate-50',
            'text-slate-900',
            'text-slate-600',
            'bg-blue-600',
            'border-slate-300'
        ],
        
        // Правильные классы (должны использоваться)
        correctClasses: [
            'bg-wsf-bg',
            'text-wsf-text',
            'bg-wsf-primary',
            'border-wsf-border',
            'text-wsf-muted'
        ],
        
        // Тестовые темы
        testThemes: {
            light: {
                '--wsf-bg': '#ffffff',
                '--wsf-text': '#1f2937',
                '--wsf-primary': '#2563eb',
                '--wsf-hover': '#1d4ed8',
                '--wsf-border': '#e5e7eb',
                '--wsf-muted': '#6b7280'
            },
            dark: {
                '--wsf-bg': '#1e1e1e',
                '--wsf-text': '#f3f4f6',
                '--wsf-primary': '#7dd3fc',
                '--wsf-hover': '#0ea5e9',
                '--wsf-border': '#374151',
                '--wsf-muted': '#9ca3af'
            },
            custom: {
                '--wsf-bg': '#f0f9ff',
                '--wsf-text': '#0c4a6e',
                '--wsf-primary': '#0284c7',
                '--wsf-hover': '#0369a1',
                '--wsf-border': '#7dd3fc',
                '--wsf-muted': '#075985'
            }
        }
    };

    // Найти контейнеры
    const containers = {
        preview: document.getElementById('widget-preview'),
        widgets: document.querySelectorAll('.wheel-fit-widget, .wsf-finder-widget'),
        themePanel: document.querySelector('.wsf-theme-panel')
    };

    console.log('📍 Найденные контейнеры:');
    console.log(`  Live Preview: ${containers.preview ? '✅' : '❌'}`);
    console.log(`  Виджеты: ${containers.widgets.length}`);
    console.log(`  Панель тем: ${containers.themePanel ? '✅' : '❌'}`);

    // Тест 1: Проверка CSS переменных
    function testCSSVariables() {
        console.log('\n1️⃣ Тест CSS переменных...');
        
        const results = {};
        const testContainer = containers.preview || containers.widgets[0];
        
        if (!testContainer) {
            console.log('❌ Контейнер для тестирования не найден');
            return false;
        }
        
        config.requiredVariables.forEach(varName => {
            const value = testContainer.style.getPropertyValue(varName);
            results[varName] = !!value;
            console.log(`  ${varName}: ${value || 'не установлена'} ${value ? '✅' : '⚠️'}`);
        });
        
        const passed = Object.values(results).filter(Boolean).length;
        const total = Object.keys(results).length;
        console.log(`Результат: ${passed}/${total} переменных установлено`);
        
        return passed >= total * 0.5; // 50% минимум
    }

    // Тест 2: Проверка жестко заданных классов
    function testHardcodedClasses() {
        console.log('\n2️⃣ Тест жестко заданных классов...');
        
        let foundProblems = 0;
        const allContainers = [containers.preview, ...containers.widgets].filter(Boolean);
        
        if (allContainers.length === 0) {
            console.log('❌ Контейнеры не найдены');
            return false;
        }
        
        config.problematicClasses.forEach(className => {
            let count = 0;
            allContainers.forEach(container => {
                count += container.querySelectorAll(`.${className}`).length;
            });
            
            if (count > 0) {
                console.log(`  ❌ .${className}: ${count} элементов`);
                foundProblems += count;
            } else {
                console.log(`  ✅ .${className}: не найдено`);
            }
        });
        
        console.log(`Результат: ${foundProblems === 0 ? 'Отлично!' : `${foundProblems} проблем найдено`}`);
        return foundProblems === 0;
    }

    // Тест 3: Проверка правильных классов
    function testCorrectClasses() {
        console.log('\n3️⃣ Тест правильных классов...');
        
        let foundGood = 0;
        const allContainers = [containers.preview, ...containers.widgets].filter(Boolean);
        
        config.correctClasses.forEach(className => {
            let count = 0;
            allContainers.forEach(container => {
                count += container.querySelectorAll(`.${className}`).length;
            });
            
            if (count > 0) {
                console.log(`  ✅ .${className}: ${count} элементов`);
                foundGood += count;
            } else {
                console.log(`  ⚠️ .${className}: не найдено`);
            }
        });
        
        console.log(`Результат: ${foundGood} элементов с правильными классами`);
        return foundGood > 0;
    }

    // Тест 4: Применение тем
    function testThemeApplication() {
        console.log('\n4️⃣ Тест применения тем...');
        
        const testContainer = containers.preview || containers.widgets[0];
        if (!testContainer) {
            console.log('❌ Контейнер для тестирования не найден');
            return false;
        }
        
        let currentTheme = 0;
        const themeNames = Object.keys(config.testThemes);
        
        function applyNextTheme() {
            const themeName = themeNames[currentTheme];
            const theme = config.testThemes[themeName];
            
            console.log(`  Применение темы: ${themeName}`);
            Object.entries(theme).forEach(([prop, value]) => {
                testContainer.style.setProperty(prop, value);
            });
            
            // Проверяем применение
            const bgColor = getComputedStyle(testContainer).backgroundColor;
            console.log(`    Фон: ${bgColor}`);
            
            currentTheme = (currentTheme + 1) % themeNames.length;
            
            if (currentTheme === 0) {
                console.log('  ✅ Цикл тем завершен');
                return;
            }
            
            setTimeout(applyNextTheme, 1500);
        }
        
        applyNextTheme();
        return true;
    }

    // Тест 5: Проверка Theme Panel
    function testThemePanel() {
        console.log('\n5️⃣ Тест панели тем...');
        
        if (!containers.themePanel) {
            console.log('❌ Панель тем не найдена');
            return false;
        }
        
        // Проверяем наличие тем
        const themeCards = containers.themePanel.querySelectorAll('.wsf-theme-card');
        console.log(`  Найдено карточек тем: ${themeCards.length}`);
        
        // Проверяем активную тему
        const activeCard = containers.themePanel.querySelector('.wsf-theme-card--active');
        console.log(`  Активная тема: ${activeCard ? '✅' : '❌'}`);
        
        // Проверяем функцию активации
        const hasActivateFunction = typeof window.wsf_theme_panel?.activateTheme === 'function';
        console.log(`  Функция активации: ${hasActivateFunction ? '✅' : '❌'}`);
        
        return themeCards.length > 0 && activeCard && hasActivateFunction;
    }

    // Создание отчета
    function createReport(results) {
        const score = Object.values(results).filter(Boolean).length;
        const total = Object.keys(results).length;
        const percentage = Math.round((score / total) * 100);
        
        const report = document.createElement('div');
        report.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 3px solid ${percentage >= 80 ? '#28a745' : percentage >= 60 ? '#ffc107' : '#dc3545'};
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            z-index: 10000;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        report.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h3 style="margin: 0; font-size: 16px; color: #333;">
                    🎨 Система тем
                </h3>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="border: none; background: #dc3545; color: white; 
                               border-radius: 6px; padding: 6px 10px; cursor: pointer;">✕</button>
            </div>
            
            <div style="text-align: center; margin-bottom: 16px;">
                <div style="font-size: 32px; font-weight: bold; color: ${percentage >= 80 ? '#28a745' : percentage >= 60 ? '#ffc107' : '#dc3545'};">
                    ${percentage}%
                </div>
                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                    ${percentage >= 80 ? 'Система готова!' : 
                      percentage >= 60 ? 'Частично готова' : 
                      'Требуется доработка'}
                </div>
            </div>
            
            <div style="font-size: 13px; line-height: 1.4;">
                ${Object.entries(results).map(([test, passed]) => `
                    <div style="display: flex; align-items: center; margin-bottom: 6px;">
                        <span style="margin-right: 10px; font-size: 16px;">${passed ? '✅' : '❌'}</span>
                        <span>${test}</span>
                    </div>
                `).join('')}
            </div>
            
            <div style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #eee; font-size: 11px; color: #666;">
                ${percentage < 80 ? 'Рекомендации: проверьте CSS переменные и пересоберите стили' : 'Все тесты пройдены успешно!'}
            </div>
        `;
        
        document.body.appendChild(report);
        
        setTimeout(() => {
            if (document.body.contains(report)) {
                report.remove();
            }
        }, 20000);
    }

    // Основная функция
    function runCompleteTest() {
        const results = {
            'CSS переменные': testCSSVariables(),
            'Нет жестких классов': testHardcodedClasses(),
            'Правильные классы': testCorrectClasses(),
            'Панель тем': testThemePanel()
        };
        
        console.log('\n📊 === ИТОГОВЫЕ РЕЗУЛЬТАТЫ ===');
        Object.entries(results).forEach(([test, passed]) => {
            console.log(`${passed ? '✅' : '❌'} ${test}`);
        });
        
        createReport(results);
        
        // Запускаем тест применения тем в конце
        setTimeout(() => {
            testThemeApplication();
        }, 1000);
        
        // Добавляем функции для ручного тестирования
        Object.entries(config.testThemes).forEach(([name, theme]) => {
            window[`apply${name.charAt(0).toUpperCase() + name.slice(1)}Theme`] = () => {
                const container = containers.preview || containers.widgets[0];
                if (container) {
                    Object.entries(theme).forEach(([prop, value]) => {
                        container.style.setProperty(prop, value);
                    });
                    console.log(`✅ Применена тема: ${name}`);
                }
            };
        });
        
        console.log('\n🎮 Ручное тестирование:');
        console.log('applyLightTheme() - светлая тема');
        console.log('applyDarkTheme() - темная тема');
        console.log('applyCustomTheme() - кастомная тема');
    }

    // Запуск
    runCompleteTest();

})();
