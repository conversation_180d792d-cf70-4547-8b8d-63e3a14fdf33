<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Two-Column Layout Test - Form Configuration</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9fafb;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-2xl font-bold text-gray-900 mb-8">Two-Column Layout Test - Form Configuration</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 class="text-lg font-medium text-gray-900 mb-6 pb-4 border-b border-gray-200">Form Configuration</h2>

            <!-- Primary Color field is now deprecated: visually hidden but kept in DOM for compatibility -->
            <div class="flex items-center" style="display:none;">
                <label for="primary_color" class="w-48 shrink-0 text-sm font-medium text-gray-700">Primary Color</label>
                <input type="color" id="primary_color" name="primary_color" value="#2563eb" class="p-1 h-10 w-14 block bg-white border border-gray-300 rounded-md cursor-pointer">
            </div>

            <!-- Two-column layout for form configuration -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column: Main Configuration -->
                <div class="flex flex-col gap-6">
                    <!-- Search Flow -->
                    <div class="flex items-start">
                        <label for="search_flow" class="w-48 shrink-0 text-sm font-medium text-gray-700 pt-2">Search Flow</label>
                        <div>
                            <select id="search_flow" name="ws_search_flow" class="max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="by_vehicle" selected>Make → Model → Year → Modification</option>
                                <option value="by_year">Year → Make → Model → Modification</option>
                                <option value="by_generation">Make → Model → Generation → Modification</option>
                            </select>
                            <p class="description text-xs text-gray-500 mt-2">Choose how a user will refine the vehicle before the search. The full step sequence is shown below the selector.</p>
                        </div>
                    </div>

                    <!-- Form Layout -->
                    <div class="flex items-start">
                        <label for="form_layout" class="w-48 shrink-0 text-sm font-medium text-gray-700 pt-2">Form Layout</label>
                        <div>
                            <select id="form_layout" name="form_layout" class="max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="popup-horizontal" selected>Inline (1x4)</option>
                                <option value="inline">Grid (2x2)</option>
                                <option value="stepper">Step-by-Step</option>
                                <!-- Wizard option hidden but not removed -->
                            </select>
                            <p class="description text-xs text-gray-500 mt-2">Choose how the search form fields are arranged and displayed to users.</p>
                        </div>
                    </div>

                    <!-- Automatic Search -->
                    <div class="flex items-center">
                        <label class="w-48 shrink-0 text-sm font-medium text-gray-700">Automatic Search</label>
                        <label class="inline-flex items-center gap-2 text-sm font-normal text-gray-700">
                            <input type="checkbox" id="auto_search_on_last_input" name="auto_search_on_last_input" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                            Automatically search on last input
                        </label>
                    </div>
                </div>

                <!-- Right Column: Styling Configuration -->
                <div class="flex flex-col gap-6">
                    <!-- Font Family -->
                    <div class="flex items-start">
                        <label for="font_family" class="w-48 shrink-0 text-sm font-medium text-gray-700 pt-2">Font Family</label>
                        <div>
                            <select id="font_family" name="wheel_size_font_family" class="max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="system" selected>System Default</option>
                                <option value="inter">Inter</option>
                                <option value="roboto">Roboto</option>
                                <option value="open-sans">Open Sans</option>
                                <option value="lato">Lato</option>
                                <option value="poppins">Poppins</option>
                                <option value="montserrat">Montserrat</option>
                            </select>
                            <p class="description text-xs text-gray-500 mt-2">Choose the font family for the search form and all layouts</p>
                        </div>
                    </div>

                    <!-- Placeholder for future styling options -->
                    <div class="text-xs text-gray-400 italic">
                        Additional styling options can be added here in the future
                    </div>
                </div>
            </div>

            <div class="mt-8 pt-6 border-t border-gray-200">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Save Changes
                </button>
            </div>
        </div>

        <!-- Test Information -->
        <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">Layout Test Information</h3>
            <div class="text-sm text-blue-800 space-y-2">
                <p><strong>Desktop (lg+):</strong> Two columns - Left: Search Flow, Form Layout, Automatic Search | Right: Font Family</p>
                <p><strong>Mobile/Tablet:</strong> Single column - All fields stack vertically</p>
                <p><strong>Responsive Breakpoint:</strong> 1024px (lg in Tailwind)</p>
            </div>
        </div>

        <!-- Responsive Test Controls -->
        <div class="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Responsive Test</h3>
            <div class="flex gap-2 flex-wrap">
                <button onclick="setViewport(320)" class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300">Mobile (320px)</button>
                <button onclick="setViewport(768)" class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300">Tablet (768px)</button>
                <button onclick="setViewport(1024)" class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300">Desktop (1024px)</button>
                <button onclick="setViewport('100%')" class="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300">Full Width</button>
            </div>
            <p class="text-xs text-gray-500 mt-2">Click buttons to test different viewport sizes</p>
        </div>
    </div>

    <script>
        function setViewport(width) {
            const container = document.querySelector('.test-container');
            if (width === '100%') {
                container.style.maxWidth = 'none';
                container.style.width = '100%';
            } else {
                container.style.maxWidth = width + 'px';
                container.style.width = width + 'px';
            }
        }

        // Add visual indicators for responsive behavior
        function updateResponsiveIndicator() {
            const container = document.querySelector('.test-container');
            const width = container.offsetWidth;
            const indicator = document.getElementById('responsive-indicator') || createIndicator();
            
            if (width >= 1024) {
                indicator.textContent = 'Desktop Layout (Two Columns)';
                indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded text-sm';
            } else {
                indicator.textContent = 'Mobile Layout (Single Column)';
                indicator.className = 'fixed top-4 right-4 bg-orange-500 text-white px-3 py-1 rounded text-sm';
            }
        }

        function createIndicator() {
            const indicator = document.createElement('div');
            indicator.id = 'responsive-indicator';
            document.body.appendChild(indicator);
            return indicator;
        }

        // Update indicator on load and resize
        window.addEventListener('load', updateResponsiveIndicator);
        window.addEventListener('resize', updateResponsiveIndicator);
    </script>
</body>
</html>
