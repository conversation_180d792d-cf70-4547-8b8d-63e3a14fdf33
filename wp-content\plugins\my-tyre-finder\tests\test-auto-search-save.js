/**
 * Test script to verify auto-search setting is saved correctly
 * Run in browser console on admin appearance page
 */

console.log('🧪 Testing Auto-Search Save Functionality');

const checkbox = document.getElementById('auto_search_on_last_input');
const form = document.querySelector('form');

if (!checkbox) {
    console.log('❌ Checkbox not found');
} else if (!form) {
    console.log('❌ Form not found');
} else {
    console.log('✅ Elements found, testing...');
    
    // Test 1: Check current state
    console.log('\n1. Current State:');
    console.log('   Checkbox checked:', checkbox.checked);
    console.log('   Checkbox value:', checkbox.value);
    console.log('   Checkbox name:', checkbox.name);
    
    // Test 2: Check form data
    console.log('\n2. Form Data Test:');
    const formData = new FormData(form);
    const autoSearchValue = formData.get('auto_search_on_last_input');
    console.log('   Form data value:', autoSearchValue);
    console.log('   Would save as:', !!(autoSearchValue));
    
    // Test 3: Toggle and check again
    console.log('\n3. Toggle Test:');
    const originalState = checkbox.checked;
    
    // Toggle checkbox
    checkbox.checked = !originalState;
    console.log('   Toggled to:', checkbox.checked);
    
    // Check form data after toggle
    const newFormData = new FormData(form);
    const newAutoSearchValue = newFormData.get('auto_search_on_last_input');
    console.log('   New form data value:', newAutoSearchValue);
    console.log('   Would save as:', !!(newAutoSearchValue));
    
    // Restore original state
    checkbox.checked = originalState;
    console.log('   Restored to:', originalState);
    
    // Test 4: Manual form submission test
    console.log('\n4. Manual Submission Test:');
    console.log('   To test saving, toggle the checkbox and click Save Settings');
    console.log('   Then reload the page and check if the setting persisted');
}

// Test 5: Check WheelFitData
console.log('\n5. WheelFitData Check:');
if (window.WheelFitData) {
    console.log('   WheelFitData.autoSearch:', window.WheelFitData.autoSearch);
    console.log('   Type:', typeof window.WheelFitData.autoSearch);
} else {
    console.log('   ❌ WheelFitData not available');
}

console.log('\n✅ Test completed. Check results above.');
