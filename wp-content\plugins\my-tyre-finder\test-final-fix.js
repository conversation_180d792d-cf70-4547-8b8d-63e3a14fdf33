// Финальный тест исправления фильтров
// Запустите в консоли: window.testFinalFix()

window.testFinalFix = async function() {
    console.log('🔧 [Final Fix Test] Тестируем финальное исправление фильтров...');
    
    // Функция для AJAX запроса
    async function makeRequest(action, data = {}) {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('nonce', window.WheelFitData?.nonce || 'test');
        
        Object.keys(data).forEach(key => {
            formData.append(key, data[key]);
        });

        const response = await fetch(window.WheelFitData?.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }
    
    console.log('--- Тест: Загрузка брендов ---');
    
    const result1 = await makeRequest('wf_get_makes');
    console.log('wf_get_makes результат:', result1);
    
    if (result1.success && result1.data.length > 0) {
        console.log(`✅ Получено ${result1.data.length} брендов через wf_get_makes`);
        console.log('Первые 5 брендов:', result1.data.slice(0, 5).map(m => m.name || m.slug));
    } else {
        console.error('❌ wf_get_makes вернул пустой результат или ошибку');
    }
    
    const result2 = await makeRequest('wf_get_makes_by_year', { year: 2020 });
    console.log('wf_get_makes_by_year результат:', result2);
    
    if (result2.success && result2.data.length > 0) {
        console.log(`✅ Получено ${result2.data.length} брендов через wf_get_makes_by_year`);
        console.log('Первые 5 брендов:', result2.data.slice(0, 5).map(m => m.name || m.slug));
    } else {
        console.error('❌ wf_get_makes_by_year вернул пустой результат или ошибку');
    }
    
    console.log('\n--- Анализ ---');
    
    const count1 = result1.success ? result1.data.length : 0;
    const count2 = result2.success ? result2.data.length : 0;
    
    if (count1 > 0 && count2 > 0) {
        console.log('🎉 УСПЕХ! Оба метода возвращают бренды');
        console.log('✅ Фильтры исправлены и работают корректно');
        
        if (count1 >= 10 && count2 >= 10) {
            console.log('✅ Количество брендов достаточное - показываются все доступные бренды');
        } else {
            console.warn('⚠️ Количество брендов небольшое - возможно фильтры все еще ограничивают результат');
        }
    } else if (count1 === 0 && count2 === 0) {
        console.error('❌ ПРОБЛЕМА: Оба метода возвращают 0 брендов');
        console.log('💡 Возможные причины:');
        console.log('   1. Комбинация региональных и брендовых фильтров дает пустой результат');
        console.log('   2. API ключ не настроен или неверный');
        console.log('   3. Проблема с сетью или API');
        console.log('');
        console.log('🔧 Рекомендации:');
        console.log('   1. Проверьте настройки в админке: Wheel-Size > Features');
        console.log('   2. Временно отключите все фильтры и проверьте снова');
        console.log('   3. Проверьте API ключ в Wheel-Size > API Settings');
    } else {
        console.warn('⚠️ Один из методов не работает - частичная проблема');
    }
    
    return { count1, count2, result1, result2 };
};

console.log('🔧 [Final Fix Test] Скрипт загружен. Запустите: window.testFinalFix()');
