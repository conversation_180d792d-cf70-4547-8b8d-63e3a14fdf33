/**
 * Quick Width Test for Live Preview
 * 
 * Run this in browser console on /wp-admin/admin.php?page=wheel-size-appearance
 * to verify that widget width matches search results width (896px)
 */

console.log('🎯 Quick Widget Width Test...');

function quickWidthTest() {
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.error('❌ Live Preview container not found');
        return;
    }
    
    console.log('✅ Live Preview container found');
    
    // Test widget width
    const widgets = previewContainer.querySelectorAll('.wheel-fit-widget, .wsf-finder-widget');
    if (widgets.length === 0) {
        console.warn('⚠️ No widgets found');
        return;
    }
    
    widgets.forEach((widget, index) => {
        const computedStyle = window.getComputedStyle(widget);
        const maxWidth = computedStyle.maxWidth;
        const maxWidthPx = parseFloat(maxWidth);
        
        console.log(`📊 Widget #${index + 1}:`);
        console.log(`  Max-width: ${maxWidth}`);
        
        // Check if it's 896px (56rem)
        if (Math.abs(maxWidthPx - 896) < 10) {
            console.log(`  ✅ CORRECT: Widget width matches search results (896px)`);
        } else {
            console.warn(`  ⚠️ INCORRECT: Expected 896px, got ${maxWidth}`);
        }
    });
    
    // Test search results width for comparison
    const searchResults = previewContainer.querySelectorAll('#search-results, .max-w-4xl');
    if (searchResults.length > 0) {
        console.log('📊 Search Results:');
        searchResults.forEach((result, index) => {
            const computedStyle = window.getComputedStyle(result);
            const maxWidth = computedStyle.maxWidth;
            console.log(`  Result #${index + 1}: ${maxWidth}`);
        });
    }
    
    // Test for any elements that might be too wide
    const wideElements = previewContainer.querySelectorAll('*');
    let foundWideElements = false;
    
    wideElements.forEach(el => {
        const computedStyle = window.getComputedStyle(el);
        const width = parseFloat(computedStyle.width);
        
        if (width > 900) { // Wider than expected
            if (!foundWideElements) {
                console.warn('⚠️ Found elements wider than expected:');
                foundWideElements = true;
            }
            console.warn(`  ${el.tagName}${el.id ? '#' + el.id : ''}${el.className ? '.' + Array.from(el.classList).slice(0,2).join('.') : ''}: ${width}px`);
        }
    });
    
    if (!foundWideElements) {
        console.log('✅ No elements found wider than expected');
    }
    
    console.log('🎉 Quick width test completed!');
}

// Run the test
quickWidthTest();

// Make function available globally for manual testing
window.quickWidthTest = quickWidthTest;
