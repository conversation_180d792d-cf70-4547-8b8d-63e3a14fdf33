/**
 * Test script to verify Step-by-Step button width and spacing
 * after making button full-width like in Live Preview
 */

console.log('🧪 Testing Step-by-Step button width...');

function testStepperButtonWidth() {
    console.log('\n📏 Testing Step-by-Step button width...');
    
    // Find the Step-by-Step form
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm || !stepperForm.classList.contains('space-y-6')) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    console.log('✅ Found Step-by-Step form');
    
    // Find search button
    const searchButton = stepperForm.querySelector('.btn-primary');
    if (!searchButton) {
        console.log('❌ Search button not found');
        return false;
    }
    
    console.log('✅ Found search button');
    
    // Find button container
    const buttonContainer = searchButton.parentElement;
    if (!buttonContainer) {
        console.log('❌ Button container not found');
        return false;
    }
    
    console.log('✅ Found button container');
    
    // Get computed widths
    const buttonStyle = window.getComputedStyle(searchButton);
    const containerStyle = window.getComputedStyle(buttonContainer);
    
    const buttonWidth = parseFloat(buttonStyle.width);
    const containerWidth = parseFloat(containerStyle.width);
    
    // Account for button padding
    const buttonPaddingLeft = parseFloat(buttonStyle.paddingLeft);
    const buttonPaddingRight = parseFloat(buttonStyle.paddingRight);
    const buttonContentWidth = buttonWidth - buttonPaddingLeft - buttonPaddingRight;
    const containerContentWidth = containerWidth;
    
    console.log(`   Button total width: ${buttonWidth}px`);
    console.log(`   Button content width: ${buttonContentWidth}px`);
    console.log(`   Container width: ${containerWidth}px`);
    console.log(`   Button padding: ${buttonPaddingLeft}px + ${buttonPaddingRight}px`);
    
    // Check if button takes full width (allow 10px tolerance for padding/borders)
    const isFullWidth = Math.abs(buttonWidth - containerWidth) <= 10;
    
    console.log(`${isFullWidth ? '✅' : '❌'} Button takes full width (±10px tolerance)`);
    
    // Check if button has w-full class
    const hasWFullClass = searchButton.classList.contains('w-full');
    console.log(`${hasWFullClass ? '✅' : '❌'} Button has w-full class`);
    
    // Check if button does NOT have md:w-auto class (should be removed)
    const hasMdWAutoClass = searchButton.classList.contains('md:w-auto');
    console.log(`${!hasMdWAutoClass ? '✅' : '❌'} Button does not have md:w-auto class`);
    
    return isFullWidth && hasWFullClass && !hasMdWAutoClass;
}

function testStepperButtonSpacing() {
    console.log('\n📐 Testing Step-by-Step button spacing...');
    
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    // Find the button container with mt-6 class
    const buttonContainer = stepperForm.querySelector('.mt-6');
    if (!buttonContainer) {
        console.log('❌ Button container with mt-6 not found');
        return false;
    }
    
    const style = window.getComputedStyle(buttonContainer);
    const marginTop = parseFloat(style.marginTop);
    
    console.log(`   Button container margin-top: ${marginTop}px`);
    
    // Should be reduced from 24px (mt-6) to 16px (mt-4) by our CSS
    const expectedMargin = 16;
    const spacingCorrect = Math.abs(marginTop - expectedMargin) <= 4; // Allow 4px tolerance
    
    console.log(`${spacingCorrect ? '✅' : '❌'} Top spacing is optimized (16px ±4px)`);
    
    return spacingCorrect;
}

function testStepperButtonAlignment() {
    console.log('\n🎯 Testing Step-by-Step button alignment...');
    
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    const searchButton = stepperForm.querySelector('.btn-primary');
    if (!searchButton) {
        console.log('❌ Search button not found');
        return false;
    }
    
    // Check if button container has text-center class
    const buttonContainer = searchButton.parentElement;
    const hasTextCenter = buttonContainer.classList.contains('text-center');
    console.log(`${hasTextCenter ? '✅' : '❌'} Button container has text-center class`);
    
    // Check if button content is centered
    const buttonStyle = window.getComputedStyle(searchButton);
    const justifyContent = buttonStyle.justifyContent;
    const alignItems = buttonStyle.alignItems;
    
    console.log(`   Button justify-content: ${justifyContent}`);
    console.log(`   Button align-items: ${alignItems}`);
    
    const isContentCentered = justifyContent === 'center' && alignItems === 'center';
    console.log(`${isContentCentered ? '✅' : '❌'} Button content is centered`);
    
    return hasTextCenter && isContentCentered;
}

function testStepperGarageButtonPosition() {
    console.log('\n🚗 Testing Garage button position after width changes...');
    
    const stepperForm = document.getElementById('wheel-fit-form');
    if (!stepperForm) {
        console.log('❌ Step-by-Step form not found');
        return false;
    }
    
    const garageButton = stepperForm.querySelector('[data-garage-trigger]');
    if (!garageButton) {
        console.log('ℹ️ Garage button not found (may be disabled)');
        return true; // Not an error if garage is disabled
    }
    
    console.log('✅ Found Garage button');
    
    // Check if garage button is still in same container as search button
    const searchContainer = stepperForm.querySelector('.mt-6');
    const garageInSameContainer = searchContainer && searchContainer.contains(garageButton);
    
    console.log(`${garageInSameContainer ? '✅' : '❌'} Garage button is in same container as search button`);
    
    // Check if garage button is right-aligned
    const garageContainer = garageButton.parentElement;
    const isRightAligned = garageContainer.classList.contains('justify-end');
    console.log(`${isRightAligned ? '✅' : '❌'} Garage button is right-aligned`);
    
    return garageInSameContainer && isRightAligned;
}

function runStepperButtonWidthTests() {
    console.log('🚀 Starting Step-by-Step button width tests...\n');
    
    const results = {
        buttonWidth: testStepperButtonWidth(),
        buttonSpacing: testStepperButtonSpacing(),
        buttonAlignment: testStepperButtonAlignment(),
        garagePosition: testStepperGarageButtonPosition()
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Step-by-Step button width is now correct.');
    } else {
        console.log('⚠️ Some tests failed. Check the details above.');
    }
    
    console.log('\n📋 Width Unification Summary:');
    console.log('• Button width: full width (w-full, no md:w-auto)');
    console.log('• Button spacing: optimized top margin (16px)');
    console.log('• Button alignment: centered content');
    console.log('• Garage position: maintained in same container');
    
    return results;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runStepperButtonWidthTests);
} else {
    runStepperButtonWidthTests();
}

// Export for manual testing
window.testStepperButtonWidth = runStepperButtonWidthTests;
