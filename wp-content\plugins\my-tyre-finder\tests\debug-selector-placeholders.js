/**
 * Отладка переводов селекторов
 * Проверяет, почему не переводятся "Choose a make", "Choose a model" и т.д.
 */

(function() {
    'use strict';

    console.log('[Selector Debug] Начинаем отладку переводов селекторов...');

    // Функция проверки доступных переводов
    function checkAvailableTranslations() {
        console.log('[Selector Debug] === Проверка доступных переводов ===');
        
        if (window.WheelFitI18n) {
            console.log('[Selector Debug] window.WheelFitI18n найден:', window.WheelFitI18n);
            console.log('[Selector Debug] Количество ключей:', Object.keys(window.WheelFitI18n).length);
            
            // Проверяем нужные ключи
            const neededKeys = [
                'select_make_placeholder',
                'select_model_placeholder',
                'select_year_placeholder',
                'select_mods_placeholder',
                'select_gen_placeholder'
            ];
            
            console.log('[Selector Debug] Проверяем нужные ключи:');
            neededKeys.forEach(key => {
                const value = window.WheelFitI18n[key];
                const status = value ? '✅' : '❌';
                console.log(`[Selector Debug] ${status} ${key}: "${value}"`);
            });
            
        } else {
            console.error('[Selector Debug] window.WheelFitI18n НЕ НАЙДЕН!');
        }
    }

    // Функция проверки функции t()
    function checkTranslationFunction() {
        console.log('[Selector Debug] === Проверка функции t() ===');
        
        if (typeof window.t === 'function') {
            console.log('[Selector Debug] Глобальная функция t() найдена');
        } else {
            console.log('[Selector Debug] Глобальная функция t() НЕ найдена');
        }
        
        // Проверяем локальную функцию t
        if (typeof t === 'function') {
            console.log('[Selector Debug] Локальная функция t() найдена');
            
            // Тестируем переводы
            const testKeys = [
                'select_make_placeholder',
                'select_model_placeholder',
                'select_year_placeholder'
            ];
            
            console.log('[Selector Debug] Тестируем функцию t():');
            testKeys.forEach(key => {
                const result = t(key, 'FALLBACK');
                console.log(`[Selector Debug] t('${key}') = "${result}"`);
            });
            
        } else {
            console.error('[Selector Debug] Локальная функция t() НЕ найдена!');
        }
    }

    // Функция проверки селекторов
    function checkSelectorsContent() {
        console.log('[Selector Debug] === Проверка содержимого селекторов ===');
        
        const selectors = [
            { id: 'wf-make', expectedKey: 'select_make_placeholder' },
            { id: 'wf-model', expectedKey: 'select_model_placeholder' },
            { id: 'wf-year', expectedKey: 'select_year_placeholder' },
            { id: 'wf-modification', expectedKey: 'select_mods_placeholder' },
            { id: 'wf-generation', expectedKey: 'select_gen_placeholder' }
        ];
        
        selectors.forEach(({ id, expectedKey }) => {
            const select = document.getElementById(id);
            if (select) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    const currentText = placeholderOption.textContent.trim();
                    const dataI18n = placeholderOption.getAttribute('data-i18n');
                    const expectedText = window.WheelFitI18n && window.WheelFitI18n[expectedKey] 
                        ? window.WheelFitI18n[expectedKey] 
                        : 'НЕТ ПЕРЕВОДА';
                    
                    console.log(`[Selector Debug] ${id}:`);
                    console.log(`  Текущий текст: "${currentText}"`);
                    console.log(`  data-i18n: "${dataI18n}"`);
                    console.log(`  Ожидаемый ключ: "${expectedKey}"`);
                    console.log(`  Ожидаемый текст: "${expectedText}"`);
                    console.log(`  Правильно: ${currentText === expectedText ? '✅' : '❌'}`);
                } else {
                    console.warn(`[Selector Debug] ${id}: placeholder option НЕ найден`);
                }
            } else {
                console.warn(`[Selector Debug] ${id}: селектор НЕ найден`);
            }
        });
    }

    // Функция принудительного исправления
    function forceFixSelectors() {
        console.log('[Selector Debug] === Принудительное исправление селекторов ===');
        
        if (!window.WheelFitI18n) {
            console.error('[Selector Debug] Нет переводов для исправления');
            return;
        }
        
        const fixes = [
            { id: 'wf-make', key: 'select_make_placeholder' },
            { id: 'wf-model', key: 'select_model_placeholder' },
            { id: 'wf-year', key: 'select_year_placeholder' },
            { id: 'wf-modification', key: 'select_mods_placeholder' },
            { id: 'wf-generation', key: 'select_gen_placeholder' }
        ];
        
        let fixed = 0;
        
        fixes.forEach(({ id, key }) => {
            const select = document.getElementById(id);
            if (select && window.WheelFitI18n[key]) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    const oldText = placeholderOption.textContent.trim();
                    const newText = window.WheelFitI18n[key];
                    
                    if (oldText !== newText) {
                        placeholderOption.textContent = newText;
                        placeholderOption.setAttribute('data-i18n', key);
                        console.log(`[Selector Debug] ✅ Исправлен ${id}: "${oldText}" → "${newText}"`);
                        fixed++;
                    } else {
                        console.log(`[Selector Debug] ✓ ${id}: уже правильно`);
                    }
                }
            }
        });
        
        console.log(`[Selector Debug] Исправлено селекторов: ${fixed}`);
    }

    // Функция проверки загрузки переводов
    function checkTranslationLoading() {
        console.log('[Selector Debug] === Проверка загрузки переводов ===');
        
        // Проверяем различные источники переводов
        const sources = [
            'window.WheelFitI18n',
            'window.wheelFitI18n', 
            'window.WheelFitData',
            'window.wheelFitData'
        ];
        
        sources.forEach(source => {
            try {
                const obj = eval(source);
                if (obj) {
                    console.log(`[Selector Debug] ${source} найден:`, obj);
                } else {
                    console.log(`[Selector Debug] ${source}: null/undefined`);
                }
            } catch (e) {
                console.log(`[Selector Debug] ${source}: не существует`);
            }
        });
    }

    // Основная функция диагностики
    function runDiagnostics() {
        console.log('[Selector Debug] 🔍 ЗАПУСК ПОЛНОЙ ДИАГНОСТИКИ');
        
        checkTranslationLoading();
        checkAvailableTranslations();
        checkTranslationFunction();
        checkSelectorsContent();
        
        console.log('[Selector Debug] 🔧 Попытка исправления...');
        forceFixSelectors();
        
        console.log('[Selector Debug] ✅ Диагностика завершена');
    }

    // Глобальные функции для ручного тестирования
    window.debugSelectorPlaceholders = {
        checkTranslations: checkAvailableTranslations,
        checkFunction: checkTranslationFunction,
        checkSelectors: checkSelectorsContent,
        fixSelectors: forceFixSelectors,
        runDiagnostics: runDiagnostics
    };

    // Автоматический запуск через 2 секунды
    setTimeout(() => {
        console.log('[Selector Debug] Автоматический запуск диагностики...');
        runDiagnostics();
    }, 2000);

    console.log('[Selector Debug] Отладчик загружен. Доступные функции:');
    console.log('- debugSelectorPlaceholders.runDiagnostics() - полная диагностика');
    console.log('- debugSelectorPlaceholders.checkTranslations() - проверка переводов');
    console.log('- debugSelectorPlaceholders.fixSelectors() - принудительное исправление');

})();
