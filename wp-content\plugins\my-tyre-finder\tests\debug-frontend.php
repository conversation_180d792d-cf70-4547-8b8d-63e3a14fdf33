<?php
/**
 * Debug page to test frontend styles
 * 
 * Create a WordPress page and use this as a custom template
 * Or add this content to any WordPress page/post
 */

// This should be added to a WordPress page content or used as a page template

?>
<div style="padding: 2rem; max-width: 800px; margin: 0 auto; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
    <h1>🔍 Frontend Debug Page</h1>
    
    <div style="background: #f0f0f1; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
        <h2>Widget Test</h2>
        <p>This should show the widget with properly styled buttons:</p>
        <?php echo do_shortcode('[wheel_fit]'); ?>
    </div>
    
    <div style="background: #fff3cd; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border: 1px solid #ffeaa7;">
        <h2>Manual Button Test</h2>
        <p>These buttons should have the same styling as in the widget:</p>
        
        <button class="btn-secondary" style="margin: 0.5rem;">
            <i data-lucide="trash-2"></i>
            <span>Clear all (class)</span>
        </button>
        
        <button id="garage-clear-all-test" style="margin: 0.5rem;">
            <i data-lucide="trash-2"></i>
            <span>Clear all (ID)</span>
        </button>
        
        <button style="margin: 0.5rem;">
            <i data-lucide="trash-2"></i>
            <span>Unstyled button</span>
        </button>
    </div>
    
    <div style="background: #e0f2fe; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
        <h2>Debug Information</h2>
        <p><strong>Context:</strong> <?php echo is_admin() ? 'Admin' : 'Frontend'; ?></p>
        <p><strong>Current URL:</strong> <?php echo esc_url($_SERVER['REQUEST_URI'] ?? ''); ?></p>
        <p><strong>wp_head fired:</strong> <span id="wp-head-check">Checking...</span></p>
        <p><strong>Critical styles loaded:</strong> <span id="critical-styles-check">Checking...</span></p>
        <p><strong>Widget styles loaded:</strong> <span id="widget-styles-check">Checking...</span></p>
    </div>
    
    <div style="background: #ffebee; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
        <h2>Expected vs Actual</h2>
        <p><strong>Expected:</strong> Red text (#ef4444), transparent background, light border</p>
        <p><strong>If buttons look like default browser buttons:</strong> Styles are not loading</p>
        <p><strong>Check browser DevTools:</strong></p>
        <ul>
            <li>Elements tab → Look for <code>&lt;style id="wheel-fit-critical-button-styles"&gt;</code></li>
            <li>Console tab → Check for JavaScript errors</li>
            <li>Network tab → Check if CSS files are loading</li>
        </ul>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if wp_head was called (we should be in the head section)
        document.getElementById('wp-head-check').textContent = 
            document.head ? '✅ Yes' : '❌ No';
        
        // Check if critical styles are loaded
        const criticalStylesElement = document.getElementById('wheel-fit-critical-button-styles');
        document.getElementById('critical-styles-check').textContent = 
            criticalStylesElement ? '✅ Found' : '❌ Missing';
        
        // Check if widget styles are loaded
        const widgetStylesElement = document.querySelector('link[href*="wheel-fit-shared"]');
        document.getElementById('widget-styles-check').textContent = 
            widgetStylesElement ? '✅ Found' : '❌ Missing';
        
        // Log computed styles for debugging
        setTimeout(() => {
            const styledButton = document.querySelector('.btn-secondary');
            const unstyledButton = document.querySelector('button:not([class]):not([id])');
            
            if (styledButton) {
                const computed = window.getComputedStyle(styledButton);
                console.log('Styled button computed styles:', {
                    backgroundColor: computed.backgroundColor,
                    color: computed.color,
                    border: computed.border,
                    fontSize: computed.fontSize,
                    padding: computed.padding,
                    display: computed.display
                });
            }
            
            if (unstyledButton) {
                const computed = window.getComputedStyle(unstyledButton);
                console.log('Unstyled button computed styles:', {
                    backgroundColor: computed.backgroundColor,
                    color: computed.color,
                    border: computed.border,
                    fontSize: computed.fontSize
                });
            }
            
            // Check if Lucide icons are working
            const icons = document.querySelectorAll('[data-lucide]');
            console.log('Lucide icons found:', icons.length);
            
        }, 500);
    });
</script>

<?php
// Add some debug info to the page source
echo '<!-- Debug: Frontend.php should add critical button styles to wp_head -->';
echo '<!-- Debug: Current time: ' . date('Y-m-d H:i:s') . ' -->';
echo '<!-- Debug: is_admin(): ' . (is_admin() ? 'true' : 'false') . ' -->';
echo '<!-- Debug: wp_doing_ajax(): ' . (wp_doing_ajax() ? 'true' : 'false') . ' -->';
?>
