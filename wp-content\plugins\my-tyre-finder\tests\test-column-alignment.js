/**
 * Тест вертикального выравнивания колонок админ-панели
 * Запустите в консоли браузера на странице настроек плагина
 */

(function() {
    'use strict';

    console.log('📐 === ТЕСТ ВЕРТИКАЛЬНОГО ВЫРАВНИВАНИЯ КОЛОНОК ===');

    // Проверка CSS переменной
    function checkCSSVariable() {
        console.log('\n1️⃣ Проверка CSS переменной...');
        
        const rootStyles = getComputedStyle(document.documentElement);
        const columnTop = rootStyles.getPropertyValue('--wsf-admin-column-top').trim();
        
        console.log('CSS переменная --wsf-admin-column-top:', columnTop || 'не установлена');
        
        if (columnTop) {
            console.log('✅ CSS переменная определена');
            return columnTop;
        } else {
            console.log('⚠️ CSS переменная не найдена');
            return null;
        }
    }

    // Проверка применения стилей
    function checkStyleApplication() {
        console.log('\n2️⃣ Проверка применения стилей...');
        
        const themePanel = document.querySelector('.wsf-theme-panel');
        const mainGrid = document.querySelector('.wsf-admin-grid__main');
        
        if (themePanel) {
            const styles = getComputedStyle(themePanel);
            console.log('Theme Panel margin-top:', styles.marginTop);
        } else {
            console.log('⚠️ .wsf-theme-panel не найден');
        }
        
        if (mainGrid) {
            const styles = getComputedStyle(mainGrid);
            console.log('Main Grid margin-top:', styles.marginTop);
        } else {
            console.log('⚠️ .wsf-admin-grid__main не найден');
        }
    }

    // Измерение точного выравнивания
    function measureAlignment() {
        console.log('\n3️⃣ Измерение точного выравнивания...');
        
        // Ищем заголовки или первые элементы колонок
        const leftElements = [
            document.querySelector('.wrap h1'),
            document.querySelector('.wrap h2'),
            document.querySelector('.wsf-admin-grid__main > div:first-child'),
            document.querySelector('.wsf-admin-grid__main')
        ].filter(Boolean);
        
        const rightElements = [
            document.querySelector('.wsf-theme-panel__title'),
            document.querySelector('.wsf-theme-panel'),
            document.querySelector('.wsf-admin-grid__sidebar')
        ].filter(Boolean);
        
        if (leftElements.length === 0 || rightElements.length === 0) {
            console.log('❌ Не удалось найти элементы для сравнения');
            return null;
        }
        
        const leftElement = leftElements[0];
        const rightElement = rightElements[0];
        
        const leftRect = leftElement.getBoundingClientRect();
        const rightRect = rightElement.getBoundingClientRect();
        
        console.log('Левый элемент:', {
            selector: leftElement.tagName + (leftElement.className ? '.' + leftElement.className.split(' ')[0] : ''),
            top: leftRect.top.toFixed(1),
            text: leftElement.textContent?.trim().substring(0, 30) + '...'
        });
        
        console.log('Правый элемент:', {
            selector: rightElement.tagName + (rightElement.className ? '.' + rightElement.className.split(' ')[0] : ''),
            top: rightRect.top.toFixed(1),
            text: rightElement.textContent?.trim().substring(0, 30) + '...'
        });
        
        const verticalDiff = Math.abs(leftRect.top - rightRect.top);
        console.log(`Вертикальная разница: ${verticalDiff.toFixed(1)}px`);
        
        return {
            difference: verticalDiff,
            leftTop: leftRect.top,
            rightTop: rightRect.top,
            leftElement,
            rightElement
        };
    }

    // Проверка адаптивности
    function checkResponsiveness() {
        console.log('\n4️⃣ Проверка адаптивности...');
        
        const width = window.innerWidth;
        console.log(`Ширина экрана: ${width}px`);
        
        const grid = document.querySelector('.wsf-admin-grid');
        if (grid) {
            const styles = getComputedStyle(grid);
            const columns = styles.gridTemplateColumns;
            
            console.log('Grid template columns:', columns);
            
            if (width >= 1200) {
                if (columns.includes('1fr') && columns.includes('320px')) {
                    console.log('✅ Desktop: двухколоночная сетка активна');
                } else {
                    console.log('⚠️ Desktop: сетка может быть неправильной');
                }
            } else {
                if (columns === '1fr') {
                    console.log('✅ Mobile: одноколоночный layout активен');
                } else {
                    console.log('⚠️ Mobile: layout может быть неправильным');
                }
            }
        }
    }

    // Создание визуального отчета
    function createVisualReport(alignment) {
        console.log('\n5️⃣ Создание визуального отчета...');
        
        // Удаляем существующий отчет
        const existingReport = document.getElementById('alignment-report');
        if (existingReport) {
            existingReport.remove();
        }
        
        const isAligned = alignment && alignment.difference <= 5;
        const reportColor = isAligned ? '#28a745' : '#dc3545';
        
        const report = document.createElement('div');
        report.id = 'alignment-report';
        report.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 3px solid ${reportColor};
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 300px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        report.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h3 style="margin: 0; font-size: 14px; color: #333;">
                    📐 Выравнивание колонок
                </h3>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="border: none; background: #dc3545; color: white; 
                               border-radius: 4px; padding: 4px 8px; cursor: pointer;">✕</button>
            </div>
            
            <div style="margin-bottom: 12px;">
                <div style="font-size: 18px; font-weight: bold; color: ${reportColor}; text-align: center;">
                    ${isAligned ? '✅ ВЫРОВНЕНО' : '❌ НЕ ВЫРОВНЕНО'}
                </div>
                ${alignment ? `
                    <div style="font-size: 12px; color: #666; text-align: center; margin-top: 4px;">
                        Разница: ${alignment.difference.toFixed(1)}px
                    </div>
                ` : ''}
            </div>
            
            <div style="font-size: 12px; line-height: 1.4;">
                ${isAligned ? `
                    <div style="color: #28a745;">
                        ✅ Колонки выровнены правильно<br>
                        ✅ CSS переменная работает<br>
                        ✅ Адаптивность настроена
                    </div>
                ` : `
                    <div style="color: #dc3545;">
                        ❌ Требуется настройка выравнивания<br>
                        💡 Выполните: <code>fixAlignment()</code>
                    </div>
                `}
            </div>
            
            <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #eee;">
                <button onclick="window.fixAlignment && window.fixAlignment()" 
                        style="width: 100%; padding: 8px; background: #007cba; color: white; 
                               border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    🔧 Автоисправление
                </button>
            </div>
        `;
        
        document.body.appendChild(report);
        console.log('✅ Визуальный отчет создан в правом верхнем углу');
        
        // Автоматически удаляем через 30 секунд
        setTimeout(() => {
            if (document.getElementById('alignment-report')) {
                report.remove();
                console.log('🧹 Визуальный отчет автоматически удален');
            }
        }, 30000);
    }

    // Функция автоисправления
    function autoFix() {
        console.log('\n🔧 === АВТОИСПРАВЛЕНИЕ ===');
        
        const alignment = measureAlignment();
        if (!alignment || alignment.difference <= 5) {
            console.log('✅ Выравнивание уже в норме');
            return;
        }
        
        let adjustment = 0;
        if (alignment.rightTop > alignment.leftTop) {
            adjustment = -Math.ceil(alignment.difference);
        } else {
            adjustment = Math.ceil(alignment.difference);
        }
        
        document.documentElement.style.setProperty('--wsf-admin-column-top', `${adjustment}px`);
        console.log(`✅ Применена коррекция: ${adjustment}px`);
        
        // Проверяем результат
        setTimeout(() => {
            const newAlignment = measureAlignment();
            if (newAlignment && newAlignment.difference <= 5) {
                console.log('🎉 Выравнивание исправлено!');
                createVisualReport(newAlignment);
            } else {
                console.log('⚠️ Требуется дополнительная настройка');
            }
        }, 100);
    }

    // Основная функция тестирования
    function runTest() {
        console.log('🚀 Запуск теста выравнивания колонок...\n');
        
        const cssVar = checkCSSVariable();
        checkStyleApplication();
        const alignment = measureAlignment();
        checkResponsiveness();
        createVisualReport(alignment);
        
        console.log('\n📊 === РЕЗУЛЬТАТЫ ТЕСТА ===');
        console.log(`CSS переменная: ${cssVar ? '✅ Определена' : '❌ Отсутствует'}`);
        console.log(`Выравнивание: ${alignment && alignment.difference <= 5 ? '✅ В норме' : '❌ Требует настройки'}`);
        
        if (alignment && alignment.difference > 5) {
            console.log('\n💡 Для исправления выполните: fixAlignment()');
        }
        
        // Добавляем глобальную функцию
        window.fixAlignment = autoFix;
        
        console.log('\n🏁 Тест завершен. Проверьте визуальный отчет в правом верхнем углу.');
    }

    // Запускаем тест
    runTest();

})();
