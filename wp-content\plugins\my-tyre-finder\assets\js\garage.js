/**
 * Premium Garage Feature v2
 * Relies on a single LocalStorageHandler instance from finder.js
 */
function initGarageFeature(forceReinit = false) {
    // Guard against double-initialisation, but allow forced reinit for AJAX updates
    if (window.__garageInitiated && !forceReinit) {
        console.log('[Garage] Already initialized, skipping...');
        return;
    }

    console.log('[Garage] Initializing garage feature...', forceReinit ? '(forced reinit)' : '');
    window.__garageInitiated = true;

    const garageTriggers = document.querySelectorAll('[data-garage-trigger]');
    const garageDrawer = document.getElementById('garage-drawer');
    const garageOverlay = document.getElementById('garage-overlay');
    const garageCloseBtn = document.getElementById('garage-close-btn');
    const garageItemsList = document.getElementById('garage-items-list');
    const garageCountEl = document.getElementById('garage-count');
    const garageClearAll = document.getElementById('garage-clear-all');
    const toast = document.getElementById('garage-toast');

    console.log('[Garage] DOM elements found:', {
        triggers: garageTriggers.length,
        drawer: !!garageDrawer,
        overlay: !!garageOverlay,
        closeBtn: !!garageCloseBtn,
        itemsList: !!garageItemsList,
        countEl: !!garageCountEl,
        clearAll: !!garageClearAll,
        toast: !!toast
    });

    // Move the overlay to the body to ensure it covers the whole viewport
    if (garageOverlay) {
        document.body.appendChild(garageOverlay);
    }

    // Get storage instance - create if needed
    let storage;
    if (window.wheelFitWidget && window.wheelFitWidget.storage) {
        storage = window.wheelFitWidget.storage;
        console.log('[Garage] Using existing storage from wheelFitWidget');
    } else {
        // Create standalone storage if wheelFitWidget not available
        if (typeof LocalStorageHandler !== 'undefined') {
            storage = new LocalStorageHandler();
            console.log('[Garage] Created standalone storage instance');
        } else {
            console.error('[Garage] LocalStorageHandler not available, garage functionality will be limited');
            // Create minimal storage interface
            storage = {
                getGarage: () => {
                    try {
                        const data = localStorage.getItem('wheel_fit_garage');
                        return data ? JSON.parse(data) : [];
                    } catch (e) {
                        console.error('[Garage] Error reading garage:', e);
                        return [];
                    }
                },
                addToGarage: () => false,
                removeFromGarage: () => false
            };
        }
    }

    // Defensive look-ups for forms (may be absent if admin disabled them)
    const vehicleForm = document.getElementById('tab-by-car');
    const sizeForm    = document.getElementById('tab-by-size');

    function onSearchSubmit(){ /* placeholder for potential future integration */ }

    if (vehicleForm) {
        vehicleForm.addEventListener('submit', onSearchSubmit);
    }
    if (sizeForm) {
        sizeForm.addEventListener('submit', onSearchSubmit);
    }

    function openDrawer() {
        console.log('[Garage] Opening drawer...');
        if (!garageDrawer || !garageOverlay) {
            console.error('[Garage] Cannot open drawer - missing DOM elements:', {
                drawer: !!garageDrawer,
                overlay: !!garageOverlay
            });
            return;
        }

        // Ensure overlay is properly reset before showing
        garageOverlay.style.display = '';
        garageOverlay.classList.remove('hidden');

        // Force a reflow to ensure the overlay is visible before starting drawer animation
        garageOverlay.offsetHeight;

        // Render garage items
        renderGarageItems();

        // Start drawer animation
        garageDrawer.classList.remove('translate-x-full');

        // Lock body scroll
        document.documentElement.classList.add('overflow-hidden');

        console.log('[Garage] Drawer opened successfully');
    }

    function closeDrawer() {
        console.log('[Garage] Closing drawer...');
        if (!garageDrawer || !garageOverlay) {
            console.error('[Garage] Cannot close drawer - missing DOM elements');
            return;
        }

        // Add transition classes first
        garageDrawer.classList.add('translate-x-full');

        // Use a small delay to ensure the transition starts before hiding the overlay
        setTimeout(() => {
            garageOverlay.classList.add('hidden');

            // Force a reflow to ensure the hidden class is applied
            garageOverlay.offsetHeight;

            // Additional cleanup to prevent backdrop persistence
            garageOverlay.style.display = 'none';
            setTimeout(() => {
                garageOverlay.style.display = '';
            }, 50);

        }, 10);

        // Remove body scroll lock
        document.documentElement.classList.remove('overflow-hidden');

        console.log('[Garage] Drawer closed successfully');
    }

    function updateGarageCount() {
        const count = storage.getGarage().length;
        document.querySelectorAll('#garage-count, .garage-count-dup').forEach(el => {
            // Update text content
            el.textContent = count;

            // Update data attribute for styling
            el.setAttribute('data-count', count);

            // Show/hide badge based on count
            if (count > 0) {
                el.classList.remove('hidden');
            } else {
                el.classList.add('hidden');
            }

            // Add/remove zero count styling
            if (count === 0) {
                el.classList.add('wsf-garage-count-zero');
            } else {
                el.classList.remove('wsf-garage-count-zero');
            }
        });

        console.log('[Garage] Updated garage count badges:', count);
    }
    
    function showToast(messageKey='garage_saved_notification') {
        let defaultMessage = 'Saved to Garage!';
        if (messageKey === 'garage_loaded_notification') {
            defaultMessage = 'Item loaded successfully!';
        } else if (messageKey === 'garage_already_notification') {
            defaultMessage = 'Item already in garage!';
        }

        const msg = typeof t==='function' ? t(messageKey, defaultMessage) : defaultMessage;
        toast.textContent = msg;
        toast.classList.remove('opacity-0', 'translate-y-4');
        setTimeout(() => {
            toast.classList.add('opacity-0', 'translate-y-4');
        }, 2500);
    }
    
    // Expose for external calls from finder.js
    window.showGarageToast = showToast;
    window.updateGarageCounter = updateGarageCount;

    function renderGarageItems() {
        // Use migrated data if available
        const items = storage.migrateGarageData ? storage.migrateGarageData() : storage.getGarage();
        garageItemsList.innerHTML = '';

        if (items.length === 0) {
            garageItemsList.innerHTML = `<p class="text-sm text-wsf-muted text-center py-8">${typeof t==='function' ? t('garage_empty','Your garage is empty.') : 'Your garage is empty.'}</p>`;
            return;
        }

        items.forEach(item => {
            const article = document.createElement('article');
            article.className = 'bg-wsf-surface border border-wsf-border rounded-lg p-4 flex items-start gap-3 shadow-sm hover:ring hover:ring-wsf-primary/20 transition group';
            article.dataset.itemId = item.id;

            // Handle both new and legacy data formats
            const make = item.make || 'Unknown';
            const model = item.model || 'Unknown';
            const year = item.year || 'Unknown';
            const generation = item.generation || 'Unknown';

            // Determine if this is a generation flow item
            const isGenerationFlow = item.generation && !item.year;

            // Check if this is legacy data (missing vehicle info)
            let isLegacyData;
            if (isGenerationFlow) {
                isLegacyData = !item.make || !item.model || !item.generation;
            } else {
                isLegacyData = !item.make || !item.model || !item.year;
            }

            let textLabel, subtitle;

            if (isLegacyData) {
                // For legacy data, show tire size as primary info
                textLabel = `${item.tire_full || 'Unknown Size'}`;
                if (item.rear_tire_full && item.rear_tire_full !== item.tire_full) {
                    textLabel += ` / ${item.rear_tire_full}`;
                }
                subtitle = 'Vehicle info unavailable';
            } else {
                // For new data, show vehicle info as primary
                if (isGenerationFlow) {
                    // Generation flow: show generation instead of year
                    textLabel = `${window.capitalize(make)} ${window.capitalize(model)} <span class="text-wsf-muted">(${window.capitalize(generation)})</span>`;
                } else {
                    // Standard flow: show year
                    textLabel = `${window.capitalize(make)} ${window.capitalize(model)} <span class="text-wsf-muted">(${year})</span>`;
                }

                const fuelKey = item.fuel ? `fuel_${item.fuel.toLowerCase().replace(/\s+/g,'_')}` : null;
                const fuelLabel = fuelKey ? (typeof t==='function' ? t(fuelKey, item.fuel.toUpperCase()) : item.fuel.toUpperCase()) : null;

                // Build subtitle with proper filtering to avoid empty bullet points
                const subtitleParts = [
                    fuelLabel,
                    item.hp && `${item.hp} HP`,
                    item.modLabel && window.shortMod(item.modLabel),
                    item.tire_full
                ].filter(part => part && part.trim() !== ''); // Filter out empty/whitespace parts

                subtitle = subtitleParts.join(' • ');
            }

            // Only show version badge in debug mode (check for debug flag)
            const showDebugInfo = window.WheelFitData?.debug || false;
            const versionBadge = showDebugInfo ?
                (item.garage_version ?
                    `<span class="text-xs bg-green-100 text-green-600 px-1 rounded">v${item.garage_version}</span>` :
                    `<span class="text-xs bg-yellow-100 text-yellow-600 px-1 rounded">legacy</span>`) :
                '';

            article.innerHTML = `
                <i data-lucide="car" class="w-6 h-6 text-indigo-600 shrink-0 mt-0.5"></i>
                <div class="flex-1">
                  <p class="text-sm font-semibold text-wsf-text">${textLabel}</p>
                  <p class="text-xs text-wsf-muted">${subtitle}</p>
                  ${versionBadge ? `<div class="mt-1">${versionBadge}</div>` : ''}
                </div>
                <div class="flex flex-col items-end gap-2">
                  <button class="garage-load px-3 py-1.5 rounded-full text-xs font-semibold bg-indigo-50 text-indigo-600 hover:bg-indigo-100 transition ${isLegacyData ? 'opacity-50 cursor-not-allowed' : ''}" data-item-id="${item.id}" ${isLegacyData ? 'disabled title="Cannot load legacy data"' : ''}>${typeof t==='function' ? t('garage_load_button','Load') : 'Load'}</button>
                  <button class="garage-delete text-wsf-muted hover:text-wsf-error" aria-label="Remove" data-item-id="${item.id}">
                    <i data-lucide="trash-2" class="w-5 h-5 pointer-events-none"></i>
                  </button>
                </div>`;
            garageItemsList.appendChild(article);
        });
        
        if(typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // Event Delegation
    console.log('[Garage] Setting up event handlers for', garageTriggers.length, 'trigger buttons');
    garageTriggers.forEach((btn, index) => {
        console.log(`[Garage] Adding click handler to trigger ${index + 1}:`, btn);
        btn.addEventListener('click', (e) => {
            console.log('[Garage] Direct trigger clicked:', btn);
            e.preventDefault();
            e.stopPropagation();
            openDrawer();
        });
    });

    // Add universal garage button handler for admin and other contexts
    document.addEventListener('click', (e) => {
        const garageElement = e.target.closest('[data-garage-trigger]') ||
                             e.target.closest('.garage-trigger') ||
                             e.target.closest('#garage-btn');

        if (garageElement) {
            console.log('[Garage] Universal trigger clicked:', e.target, 'closest element:', garageElement);
            e.preventDefault();
            e.stopPropagation();
            openDrawer();
            return;
        }

        // Improved text-based detection with stricter validation
        // Only trigger if the element is a button or has explicit garage-related attributes
        if (e.target.textContent &&
            e.target.textContent.trim().toLowerCase().includes('garage') &&
            (e.target.tagName === 'BUTTON' ||
             e.target.classList.contains('garage-btn') ||
             e.target.hasAttribute('data-garage') ||
             e.target.closest('button'))) {

            console.log('[Garage] Text-based trigger clicked (validated):', e.target);
            e.preventDefault();
            e.stopPropagation();
            openDrawer();
        }
    });
    if (garageCloseBtn) {
        garageCloseBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            closeDrawer();
        });
    }
    if (garageOverlay) {
        garageOverlay.addEventListener('click', (e) => {
            // Only close if clicking directly on the overlay, not on child elements
            if (e.target === garageOverlay) {
                console.log('[Garage] Overlay clicked, closing drawer');
                e.preventDefault();
                e.stopPropagation();
                closeDrawer();
            }
        });
    }

    garageItemsList.addEventListener('click', async (e) => {
        const loadBtn = e.target.closest('.garage-load');
        const deleteBtn = e.target.closest('.garage-delete');

        if (loadBtn) {
            const itemId = loadBtn.dataset.itemId;
            console.log('[Garage] Load button clicked, itemId:', itemId);

            const garage = storage.getGarage();
            console.log('[Garage] Current garage contents:', garage);

            const item = garage.find(i => i.id === itemId);
            console.log('[Garage] Found item:', item);
            console.log('[Garage] wheelFitWidget available:', !!window.wheelFitWidget);
            console.log('[Garage] wheelFitWidgetReady flag:', !!window.wheelFitWidgetReady);

            if (!item) {
                console.error('[Garage] Item not found in garage:', { itemId, garageItems: garage.length });
                alert('Garage item not found. The garage may have been modified. Please refresh the page.');
                return;
            }

            // Use the new promise-based widget waiting system
            if (!isWidgetReady()) {
                console.log('[Garage] Widget not ready, waiting...');

                // Show loading feedback
                loadBtn.disabled = true;
                loadBtn.textContent = 'Loading...';

                waitForWidget(8000)
                    .then(() => {
                        console.log('[Garage] Widget ready, proceeding with load');
                        // Reset button state
                        loadBtn.disabled = false;
                        loadBtn.textContent = typeof t==='function' ? t('garage_load_button','Load') : 'Load';

                        // Proceed with the load operation (itemToLoad will be determined inside performGarageLoad)
                        console.log('[Garage] performGarageLoad called with item:', item);
                        performGarageLoad(item);
                    })
                    .catch(error => {
                        console.error('[Garage] Widget wait failed:', error);
                        // Reset button state
                        loadBtn.disabled = false;
                        loadBtn.textContent = typeof t==='function' ? t('garage_load_button','Load') : 'Load';
                        alert('Widget failed to initialize. Please refresh the page and try again.');
                    });
                return;
            }

            // If widget is ready, proceed immediately
            if (isWidgetReady()) {
                console.log('[Garage] Widget ready, proceeding with load');
                console.log('[Garage] performGarageLoad called with item:', item);
                performGarageLoad(item);
            }
        }

        if (deleteBtn) {
            const itemId = deleteBtn.dataset.itemId;
            const article = deleteBtn.closest('article');
            
            article.classList.add('opacity-0', 'scale-95');
            
            setTimeout(() => {
                storage.removeFromGarage(itemId);
                renderGarageItems();
                updateGarageCount();
            }, 300);
        }
    });
    
    garageClearAll.addEventListener('click', () => {
        const msg = typeof t==='function' ? t('garage_confirm_clear','Are you sure you want to clear your entire garage?') : 'Are you sure you want to clear your entire garage?';
        if (confirm(msg)) {
            storage.clearGarage();
            renderGarageItems();
            updateGarageCount();
        }
    });

    // Extracted garage load logic - moved inside initGarageFeature scope to access closeDrawer
    async function performGarageLoad(item, itemToLoad = null) {
    console.log('[Garage] Performing garage load for item:', item);
    console.log('[Garage] itemToLoad parameter:', itemToLoad);

    // Validate that widget is still available
    if (!window.wheelFitWidget) {
        console.error('[Garage] Widget not available in performGarageLoad');
        alert('Widget is not available. Please refresh the page and try again.');
        return;
    }

    // Get storage instance - use widget's storage or create new one
    const storage = window.wheelFitWidget?.storage || new LocalStorageHandler();
    console.log('[Garage] Storage instance:', !!storage);

    console.log('[Garage] Current widget mode:', window.wheelFitWidget.mode);
    console.log('[Garage] Current widget flow order:', window.wheelFitWidget.flowOrder);

    // Determine the item's original flow type from stored data
    const itemFlowOrder = item.flow_order;
    const itemIsGenerationFlow = item.generation && !item.year;
    const itemIsYearFlow = itemFlowOrder && itemFlowOrder[0] === 'year';

    console.log('[Garage] Item flow analysis:', {
        itemFlowOrder,
        itemIsGenerationFlow,
        itemIsYearFlow,
        hasGeneration: !!item.generation,
        hasYear: !!item.year
    });

    // Basic validation - ensure we have minimum required data
    const hasBasicData = item.make && item.model && item.modification;
    const hasTimeIdentifier = item.year || item.generation;

    if (!hasBasicData || !hasTimeIdentifier) {
        console.error('[Garage] Missing essential vehicle data:', {
            item,
            hasBasicData,
            hasTimeIdentifier,
            make: !!item.make,
            model: !!item.model,
            modification: !!item.modification,
            year: !!item.year,
            generation: !!item.generation
        });
        alert('This garage item is missing essential vehicle information and cannot be loaded.');
        return;
    }

    // Try to find the original search item for better compatibility if not provided
    if (!itemToLoad) {
        const history = storage.getSearchHistory();
        let originalSearch = null;

        // Try multiple search strategies
        if (item.generation) {
            originalSearch = history.find(h =>
                h.make === item.make &&
                h.model === item.model &&
                h.generation === item.generation &&
                h.modification === item.modification
            );
        }

        if (!originalSearch && item.year) {
            originalSearch = history.find(h =>
                h.make === item.make &&
                h.model === item.model &&
                h.year === item.year &&
                h.modification === item.modification
            );
        }

        itemToLoad = originalSearch || item;
    }

    // Ensure the item has the necessary structure for loading
    if (!itemToLoad.flow_order && window.wheelFitWidget.flowOrder) {
        itemToLoad.flow_order = window.wheelFitWidget.flowOrder;
    }

    console.log('[Garage] Final item to load:', {
        itemToLoad,
        originalFromHistory: itemToLoad !== item
    });

    try {
        // Call the load method and handle any errors
        await window.wheelFitWidget.loadFromHistory(encodeURIComponent(JSON.stringify(itemToLoad)));
        closeDrawer();

        // Show success feedback
        if (window.showGarageToast) {
            window.showGarageToast('garage_loaded_notification');
        }

        console.log('[Garage] Load completed successfully');
    } catch (error) {
        console.error('[Garage] Error loading item:', error);
        alert('Failed to load garage item. Please try again or contact support if the problem persists.');
    }
    }

    // Initial state
    updateGarageCount();
}

// Multiple initialization strategies for different contexts
function tryInitGarage() {
    console.log('[Garage] Attempting initialization...');
    console.log('[Garage] Widget availability check:', {
        wheelFitWidget: !!window.wheelFitWidget,
        wheelFitWidgetReady: !!window.wheelFitWidgetReady,
        WheelFitWidget: typeof WheelFitWidget,
        documentReady: document.readyState
    });

    // Check if garage elements exist in DOM
    const hasGarageElements = document.getElementById('garage-drawer') ||
                             document.getElementById('garage-overlay') ||
                             document.querySelectorAll('[data-garage-trigger]').length > 0;

    if (hasGarageElements) {
        console.log('[Garage] Garage elements found, initializing...');
        initGarageFeature();
        return true;
    } else {
        console.log('[Garage] No garage elements found yet');
        return false;
    }
}

// Enhanced widget availability check
function isWidgetReady() {
    const hasWidget = !!window.wheelFitWidget;
    const hasLoadMethod = !!(window.wheelFitWidget && typeof window.wheelFitWidget.loadFromHistory === 'function');
    const hasReadyFlag = !!window.wheelFitWidgetReady;
    const hasGarageFlag = !!window.garageWidgetReadyFlag;
    const widgetClassExists = typeof WheelFitWidget !== 'undefined';

    const ready = hasWidget && hasLoadMethod;

    console.log('[Garage] Widget ready check:', {
        hasWidget,
        hasLoadMethod,
        hasReadyFlag,
        hasGarageFlag,
        widgetClassExists,
        documentReady: document.readyState,
        ready
    });

    return ready;
}

// Wait for widget to be ready with promise-based approach
function waitForWidget(timeout = 5000) {
    return new Promise((resolve, reject) => {
        if (isWidgetReady()) {
            console.log('[Garage] Widget already ready');
            resolve(window.wheelFitWidget);
            return;
        }

        console.log('[Garage] Waiting for widget to be ready...');
        let attempts = 0;
        const maxAttempts = timeout / 100; // Check every 100ms

        const checkInterval = setInterval(() => {
            attempts++;
            console.log(`[Garage] Widget ready check attempt ${attempts}/${maxAttempts}`);

            if (isWidgetReady()) {
                console.log('[Garage] Widget is now ready!');
                clearInterval(checkInterval);
                resolve(window.wheelFitWidget);
                return;
            }

            if (attempts >= maxAttempts) {
                console.error('[Garage] Widget failed to become ready within timeout');
                clearInterval(checkInterval);
                reject(new Error('Widget initialization timeout'));
            }
        }, 100);
    });
}

// Global widget ready flag for synchronous checks
window.garageWidgetReadyFlag = false;

// Listen for widget ready events to set the flag
document.addEventListener('wheelFitReady', () => {
    console.log('[Garage] wheelFitReady event received, setting ready flag');
    window.garageWidgetReadyFlag = true;
});

document.addEventListener('wheelFitWidgetReady', () => {
    console.log('[Garage] wheelFitWidgetReady event received, setting ready flag');
    window.garageWidgetReadyFlag = true;
});

// Strategy 1: DOM ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('[Garage] DOM ready, trying to initialize...');
    if (!tryInitGarage()) {
        // Strategy 2: Wait for wheelFitReady event
        document.addEventListener('wheelFitReady', () => {
            console.log('[Garage] wheelFitReady event received during init');
            setTimeout(tryInitGarage, 100); // Small delay to ensure DOM is updated
        }, { once: true });

        // Strategy 2.5: Also listen for custom widget ready events
        document.addEventListener('wheelFitWidgetReady', () => {
            console.log('[Garage] wheelFitWidgetReady event received during init');
            setTimeout(tryInitGarage, 100);
        }, { once: true });

        // Strategy 3: Polling fallback with widget readiness check
        let attempts = 0;
        const maxAttempts = 50; // 5 seconds
        const interval = setInterval(() => {
            attempts++;
            console.log(`[Garage] Polling attempt ${attempts}/${maxAttempts}, widget ready: ${isWidgetReady()}`);

            // Only try to initialize if both DOM elements and widget are ready
            const domReady = tryInitGarage();
            const widgetReady = isWidgetReady();

            if ((domReady && widgetReady) || attempts >= maxAttempts) {
                clearInterval(interval);
                if (attempts >= maxAttempts) {
                    console.warn('[Garage] Failed to initialize after maximum attempts', {
                        domReady,
                        widgetReady,
                        finalAttempt: attempts
                    });
                } else {
                    console.log('[Garage] Successfully initialized on polling attempt', attempts);
                }
            }
        }, 100);
    }
});

// Strategy 4: Immediate attempt (for cases where script loads after DOM)
if (document.readyState === 'loading') {
    console.log('[Garage] Document still loading, waiting for DOMContentLoaded');
} else {
    console.log('[Garage] Document already loaded, trying immediate initialization');
    setTimeout(tryInitGarage, 50);
}

// Debug function to check widget state
window.debugGarageWidget = function() {
    console.log('=== Garage Widget Debug Info ===');
    console.log('Document ready state:', document.readyState);
    console.log('Widget availability:', {
        wheelFitWidget: !!window.wheelFitWidget,
        wheelFitWidgetReady: !!window.wheelFitWidgetReady,
        garageWidgetReadyFlag: !!window.garageWidgetReadyFlag,
        WheelFitWidget: typeof WheelFitWidget,
        isWidgetReady: typeof isWidgetReady === 'function' ? isWidgetReady() : 'function not available'
    });

    if (window.wheelFitWidget) {
        console.log('Widget details:', {
            mode: window.wheelFitWidget.mode,
            flowOrder: window.wheelFitWidget.flowOrder,
            loadFromHistory: typeof window.wheelFitWidget.loadFromHistory,
            selectedData: window.wheelFitWidget.selectedData
        });
    }

    console.log('Garage DOM elements:', {
        drawer: !!document.getElementById('garage-drawer'),
        overlay: !!document.getElementById('garage-overlay'),
        itemsList: !!document.getElementById('garage-items-list'),
        triggers: document.querySelectorAll('[data-garage-trigger]').length,
        loadButtons: document.querySelectorAll('.garage-load').length
    });

    if (typeof LocalStorageHandler !== 'undefined') {
        const storage = new LocalStorageHandler();
        const garage = storage.getGarage();
        console.log('Garage data:', {
            itemCount: garage.length,
            items: garage
        });
    }

    console.log('=== End Debug Info ===');
};

console.log('[Garage] Debug function available: window.debugGarageWidget()');

// Function to reset garage state for reinitialisation
window.resetGarageState = function() {
    console.log('[Garage] Resetting garage state...');
    window.__garageInitiated = false;

    // Remove existing event listeners by cloning and replacing elements
    const garageTriggers = document.querySelectorAll('[data-garage-trigger]');
    garageTriggers.forEach(trigger => {
        const newTrigger = trigger.cloneNode(true);
        trigger.parentNode.replaceChild(newTrigger, trigger);
    });

    const garageCloseBtn = document.getElementById('garage-close-btn');
    if (garageCloseBtn) {
        const newCloseBtn = garageCloseBtn.cloneNode(true);
        garageCloseBtn.parentNode.replaceChild(newCloseBtn, garageCloseBtn);
    }

    const garageClearAll = document.getElementById('garage-clear-all');
    if (garageClearAll) {
        const newClearAll = garageClearAll.cloneNode(true);
        garageClearAll.parentNode.replaceChild(newClearAll, garageClearAll);
    }

    console.log('[Garage] State reset complete');
};

// Function to reinitialize garage after AJAX updates
window.reinitGarage = function() {
    console.log('[Garage] Reinitializing garage...');
    window.resetGarageState();

    // Wait a bit for DOM to settle, then reinitialize
    setTimeout(() => {
        initGarageFeature(true);
    }, 100);
};