# Исправление переводов селекторов

## Проблема
При переключении Search Flow и Form Layout селекторы (dropdown placeholders) переводились обратно на английский, хотя labels переводились великолепно.

## Корень проблемы
**Labels** и **селекторы** использовали разные подходы:
- ✅ **Labels**: используют `data-i18n` атрибуты → переводятся автоматически
- ❌ **Селекторы**: используют функцию `populateSelect()` с прямой передачей текста → сбрасываются

## Решение
Сделать селекторы такими же, как labels - использовать `data-i18n` атрибуты!

## Исправления

### 1. Обновлена функция `populateSelect()` ✅
**Файл**: `assets/js/finder.js`

**Что изменилось**:
```javascript
// Было:
placeholderOption.textContent = placeholder;

// Стало:
if (translationKey) {
    placeholderOption.setAttribute('data-i18n', translationKey); // Как у labels!
    placeholderOption.textContent = window.WheelFitI18n[translationKey] || placeholder;
}
```

**Добавлено**:
- Автоматическое определение ключей переводов по ID селектов
- Установка `data-i18n` атрибутов (как у labels!)
- Вызов `reapplyTranslations()` после заполнения
- Подробное логирование для отладки

### 2. Исправлена функция `reapplyTranslations()` ✅
**Файл**: `assets/js/finder.js`

**Что изменилось**:
```javascript
// Было:
if (placeholderOption && placeholderOption.dataset.i18nKey) {

// Стало:
if (placeholderOption && placeholderOption.dataset.i18n) { // Как у labels!
```

**Результат**: Теперь функция правильно обрабатывает `data-i18n` атрибуты селекторов.

### 3. Добавлены новые ключи переводов ✅
**Поддерживаемые селекторы**:
- `wf-make` → `select_make_placeholder`
- `wf-model` → `select_model_placeholder`
- `wf-year` → `select_year_placeholder`
- `wf-modification` → `select_mods_placeholder`
- `wf-generation` → `select_gen_placeholder`
- `tire-width` → `select_width_first_placeholder`
- `tire-profile` → `select_profile_first_placeholder`
- `tire-diameter` → `select_diameter_first_placeholder`

## Как тестировать

### 1. В WordPress админ-панели:
1. Перейдите в **Tire Finder → Appearance**
2. Выберите язык (например, Русский)
3. Измените **Search Flow** (By Vehicle → By Year → By Generation)
4. Измените **Form Layout** (Popup → Inline → Stepper)
5. **Проверьте**: все селекторы должны оставаться переведенными!

### 2. Консоль браузера:
```javascript
// Загрузить тестовый скрипт
// (скопируйте содержимое test-selector-translations.js в консоль)

// Тестировать переводы
testSelectorTranslations('ru');

// Тестировать заполнение селекторов
testPopulateSelect();

// Принудительно применить переводы
forceApplyTranslations();
```

### 3. Проверка в коде:
```javascript
// Проверить, что селекторы имеют data-i18n атрибуты
document.querySelectorAll('select option[value=""]').forEach(opt => {
    console.log(opt.id || opt.parentElement.id, '→', opt.dataset.i18n, '→', opt.textContent);
});
```

## Ожидаемый результат

### ✅ Что теперь работает:
1. **Селекторы ведут себя как labels** - используют `data-i18n` атрибуты
2. **Переводы сохраняются** при изменении Search Flow/Layout
3. **Автоматическое определение** ключей переводов по ID селектов
4. **Единообразный подход** для всех элементов интерфейса
5. **Надежная работа** с множественными fallback механизмами

### 🔍 Проверочный список:
- [ ] "Choose a make" → "Выберите марку" (и остается при переключениях)
- [ ] "Choose a model" → "Выберите модель" (и остается при переключениях)
- [ ] "Choose a year" → "Выберите год" (и остается при переключениях)
- [ ] "Choose a modification" → "Выберите модификацию" (и остается при переключениях)
- [ ] "Select make first" → "Сначала выберите марку" (и остается при переключениях)
- [ ] Кнопка "Find Tire & Wheel Sizes" → "Подобрать размеры" (и остается при переключениях)

## Техническая реализация

### Принцип работы:
1. **При заполнении селектора** (`populateSelect()`):
   - Определяется ключ перевода по ID селектора
   - Устанавливается `data-i18n` атрибут (как у labels!)
   - Применяется переведенный текст
   - Вызывается `reapplyTranslations()`

2. **При изменении настроек**:
   - Translation Manager автоматически находит все элементы с `data-i18n`
   - Применяет переводы ко всем элементам (включая селекторы!)
   - Селекторы ведут себя точно как labels

3. **Fallback механизмы**:
   - `reapplyTranslations()` → `applyStaticTranslations()` → прямое применение
   - Автоматическое определение ключей по ID
   - Логирование для отладки

## Результат
Теперь селекторы работают точно так же, как labels - используют `data-i18n` атрибуты и автоматически переводятся при любых изменениях настроек! 🎉

**Единообразный подход**: все элементы интерфейса (labels, кнопки, селекторы) используют одну и ту же систему переводов через `data-i18n` атрибуты.
