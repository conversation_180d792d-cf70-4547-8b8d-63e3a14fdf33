/* file: assets/css/admin-brands.css */
.brand-filters__container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 16px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    background-color: #fff;
    max-height: 400px;
    overflow-y: auto;
}

.brand-filters__legend {
    display: flex;
    gap: 20px;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
    color: #50575e;
}

.brand-filters__legend > span::before {
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
    vertical-align: middle;
}

.brand-filters__legend span:nth-child(1)::before { background-color: #22c55e; } /* green-500 */
.brand-filters__legend span:nth-child(2)::before { background-color: #ef4444; } /* red-500 */
.brand-filters__legend span:nth-child(3)::before { background-color: #94a3b8; } /* slate-400 */

/* Priority note styling */
.brand-filters__priority-note {
    background-color: #e0f2fe; /* light blue background */
    border: 1px solid #0284c7; /* blue border */
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #0c4a6e; /* dark blue text */
}

.brand-filters__priority-note strong {
    color: #0369a1; /* slightly darker blue for emphasis */
}

/* Combined logic note styling */
.brand-filters__combined-note {
    background-color: #fef3c7; /* amber-100 background */
    border: 1px solid #f59e0b; /* amber-500 border */
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 12px;
    font-size: 13px;
    color: #92400e; /* amber-800 text */
}

.brand-filters__combined-note strong {
    color: #b45309; /* amber-700 for emphasis */
}


.brand-tag {
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 4px 10px;
    font-size: 13px;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.brand-tag:focus {
    outline: none;
    box-shadow: 0 0 0 1px #fff, 0 0 0 3px #007cba;
}

.brand-tag.is-neutral {
    background-color: #f1f1f1;
    border-color: #ccd0d4;
    color: #3c434a;
}
.brand-tag.is-neutral:hover {
    border-color: #007cba;
    color: #007cba;
}

.brand-tag.is-included {
    background-color: #dcfce7; /* green-100 */
    border-color: #4ade80; /* green-400 */
    color: #166534; /* green-800 */
}

.brand-tag.is-excluded {
    background-color: #fee2e2; /* red-100 */
    border-color: #f87171; /* red-400 */
    color: #991b1b; /* red-800 */
}

/* Visual priority indication: when includes exist, make excluded items semi-transparent */
.brand-filters__container.has-includes .brand-tag.is-excluded {
    opacity: 0.4;
    position: relative;
}

.brand-filters__container.has-includes .brand-tag.is-excluded::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 8px;
    right: 8px;
    height: 1px;
    background-color: #991b1b;
    transform: translateY(-50%);
    pointer-events: none;
}

/* Wrapper for region tags to control layout */
.region-filter__wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    max-width: 600px;
}

/* Custom Tooltip for Region Tags */
.region-tag {
    position: relative;
}

.region-tag::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) scale(0.9);
    
    background-color: #fff;
    color: #1e1e1e;
    font-size: 13px;
    font-weight: 400;
    white-space: nowrap;
    
    padding: 6px 10px;
    margin-bottom: 8px; /* Spacing from tag */
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: opacity 150ms ease-in-out, transform 150ms ease-in-out;
    z-index: 10;
}

.region-tag:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) scale(1);
}