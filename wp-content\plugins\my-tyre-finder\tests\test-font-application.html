<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Application Test - Wheel Size Finder</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/wheel-fit-shared.src.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-section h3 {
            margin: 0 0 20px 0;
            color: #1f2937;
            font-weight: 600;
        }
        .font-info {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔤 Font Application Test - Wheel Size Finder Widget</h1>
        <p>This test verifies that Inter font is correctly applied to all widget elements including selectors and labels.</p>
        
        <div class="font-info">
            <strong>Expected Font:</strong> Inter (fallback: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif)
        </div>

        <!-- Test 1: Standard Form Layout -->
        <div class="test-section">
            <h3>🧪 Test 1: Standard Form Layout (Inline)</h3>
            <div class="wsf-finder-widget" data-wsf-theme="light">
                <div class="wsf-widget__header">
                    <h2 class="wsf-widget__title">Find Your Wheel Size</h2>
                </div>
                <div class="wsf-widget__content">
                    <form id="tab-by-car" class="grid gap-4 grid-cols-1 md:grid-cols-4">
                        <!-- Make Field -->
                        <div id="step-1" class="flex flex-col w-full min-w-0">
                            <label for="wf-make" class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1">Make</label>
                            <div class="relative min-w-0">
                                <select id="wf-make" name="make" class="wsf-input block w-full" required>
                                    <option value="">Select make...</option>
                                    <option value="audi">Audi</option>
                                    <option value="bmw">BMW</option>
                                    <option value="mercedes">Mercedes-Benz</option>
                                    <option value="volkswagen">Volkswagen</option>
                                </select>
                            </div>
                        </div>

                        <!-- Model Field -->
                        <div id="step-2" class="flex flex-col w-full min-w-0">
                            <label for="wf-model" class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1">Model</label>
                            <div class="relative min-w-0">
                                <select id="wf-model" name="model" class="wsf-input block w-full" required disabled>
                                    <option value="">Select make first</option>
                                </select>
                            </div>
                        </div>

                        <!-- Year Field -->
                        <div id="step-3" class="flex flex-col w-full min-w-0">
                            <label for="wf-year" class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1">Year</label>
                            <div class="relative min-w-0">
                                <select id="wf-year" name="year" class="wsf-input block w-full" required disabled>
                                    <option value="">Select model first</option>
                                </select>
                            </div>
                        </div>

                        <!-- Modification Field -->
                        <div id="step-4" class="flex flex-col w-full min-w-0">
                            <label for="wf-modification" class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1">Modification</label>
                            <div class="relative min-w-0">
                                <select id="wf-modification" name="modification" class="wsf-input block w-full" required disabled>
                                    <option value="">Select year first</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Test 2: Step-by-Step Layout -->
        <div class="test-section">
            <h3>🧪 Test 2: Step-by-Step Layout (Stepper)</h3>
            <div class="wheel-fit-widget" data-wsf-theme="light">
                <div class="wsf-widget__header">
                    <h2 class="wsf-widget__title">Find Your Wheel Size - Step by Step</h2>
                </div>
                <div class="wsf-widget__content">
                    <!-- Step 1 -->
                    <div id="step-1" class="step-container">
                        <label for="wf-make-stepper" class="block text-sm font-semibold text-wsf-text uppercase tracking-wide mb-2">Make</label>
                        <div class="relative w-full">
                            <select id="wf-make-stepper" name="make" class="wsf-input block w-full" required>
                                <option value="">Select make...</option>
                                <option value="audi">Audi</option>
                                <option value="bmw">BMW</option>
                                <option value="mercedes">Mercedes-Benz</option>
                                <option value="volkswagen">Volkswagen</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test 3: Wizard Flow Layout -->
        <div class="test-section">
            <h3>🧪 Test 3: Wizard Flow Layout</h3>
            <div class="wsf-finder-widget" data-wsf-theme="light">
                <div class="wsf-widget__header">
                    <h2 class="wsf-widget__title">Wizard Flow</h2>
                </div>
                <div class="wsf-widget__content">
                    <div id="wizard-step-1" class="wizard-step">
                        <h2 class="text-2xl font-bold wsf-text-primary mb-4">Select Manufacturer</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 gap-4">
                            <button class="p-4 border border-wsf-border rounded-lg hover:bg-wsf-surface">Audi</button>
                            <button class="p-4 border border-wsf-border rounded-lg hover:bg-wsf-surface">BMW</button>
                            <button class="p-4 border border-wsf-border rounded-lg hover:bg-wsf-surface">Mercedes</button>
                            <button class="p-4 border border-wsf-border rounded-lg hover:bg-wsf-surface">VW</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test 4: Font Analysis -->
        <div class="test-section">
            <h3>🔍 Font Analysis</h3>
            <div id="font-analysis">
                <p>Click the button below to analyze the computed font families for all elements:</p>
                <button onclick="analyzeFonts()" style="background: #2563eb; color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer;">Analyze Fonts</button>
                <div id="analysis-results" style="margin-top: 20px;"></div>
            </div>
        </div>
    </div>

    <script>
        function analyzeFonts() {
            const elements = [
                { selector: '.wsf-widget__title', description: 'Widget Title' },
                { selector: '.wizard-step h2', description: 'Wizard Step Heading' },
                { selector: 'label[for^="wf-"]', description: 'Form Labels' },
                { selector: '.wsf-input', description: 'Form Selects' },
                { selector: '.step-container label', description: 'Step Container Labels' },
                { selector: 'h2.text-2xl', description: 'H2 with text-2xl class' },
                { selector: 'label.text-xs', description: 'Labels with text-xs class' },
                { selector: 'label.text-sm', description: 'Labels with text-sm class' }
            ];

            let results = '<h4>Font Analysis Results:</h4>';
            
            elements.forEach(({ selector, description }) => {
                const element = document.querySelector(selector);
                if (element) {
                    const computedStyle = window.getComputedStyle(element);
                    const fontFamily = computedStyle.fontFamily;
                    const fontWeight = computedStyle.fontWeight;
                    const color = computedStyle.color;
                    
                    const isInterFont = fontFamily.includes('Inter');
                    const status = isInterFont ? '✅' : '❌';
                    
                    results += `
                        <div style="margin: 10px 0; padding: 10px; background: ${isInterFont ? '#f0f9ff' : '#fef2f2'}; border-radius: 4px;">
                            <strong>${status} ${description}</strong><br>
                            Font: ${fontFamily}<br>
                            Weight: ${fontWeight}<br>
                            Color: ${color}
                        </div>
                    `;
                } else {
                    results += `
                        <div style="margin: 10px 0; padding: 10px; background: #fef2f2; border-radius: 4px;">
                            <strong>❌ ${description}</strong><br>
                            Element not found: ${selector}
                        </div>
                    `;
                }
            });

            document.getElementById('analysis-results').innerHTML = results;
        }

        // Auto-run analysis on page load
        window.addEventListener('load', () => {
            setTimeout(analyzeFonts, 500);
        });
    </script>
</body>
</html>
