/**
 * Тест границ карточек 2px с CSS переменными
 * Запустите в консоли браузера на странице админки с темами
 */

(function() {
    'use strict';

    console.log('🎨 === ТЕСТ ГРАНИЦ КАРТОЧЕК 2PX ===');

    // Проверка CSS переменных
    function checkCSSVariables() {
        console.log('\n1️⃣ Проверка CSS переменных...');
        
        const rootStyles = getComputedStyle(document.documentElement);
        const borderWidth = rootStyles.getPropertyValue('--wsf-card-border-width').trim();
        const borderColor = rootStyles.getPropertyValue('--wsf-card-border-color').trim();
        
        console.log('CSS переменные:');
        console.log('--wsf-card-border-width:', borderWidth || 'не установлена');
        console.log('--wsf-card-border-color:', borderColor || 'не установлена');
        
        const isWidthCorrect = borderWidth === '2px';
        const isColorCorrect = borderColor.includes('#cbd5e1') || borderColor.includes('203, 213, 225');
        
        console.log(`Ширина границы: ${isWidthCorrect ? '✅ 2px' : '❌ не 2px'}`);
        console.log(`Цвет границы: ${isColorCorrect ? '✅ slate-300' : '❌ неправильный'}`);
        
        return { isWidthCorrect, isColorCorrect };
    }

    // Проверка применения границ к карточкам
    function checkCardBorders() {
        console.log('\n2️⃣ Проверка границ карточек...');
        
        const cards = document.querySelectorAll('.wsf-theme-card');
        if (cards.length === 0) {
            console.warn('⚠️ Карточки тем не найдены');
            return { total: 0, correct: 0 };
        }

        let correctBorders = 0;
        let activeCards = 0;

        cards.forEach((card, index) => {
            const styles = window.getComputedStyle(card);
            const borderWidth = styles.borderWidth;
            const borderColor = styles.borderColor;
            const borderStyle = styles.borderStyle;
            const isActive = card.classList.contains('wsf-theme-card--active');
            
            // Проверяем параметры границы
            const isWidthCorrect = borderWidth === '2px';
            const isColorCorrect = borderColor.includes('203, 213, 225') || 
                                  borderColor.includes('#cbd5e1');
            const isStyleCorrect = borderStyle === 'solid';
            
            const isBorderCorrect = isWidthCorrect && isColorCorrect && isStyleCorrect;
            
            console.log(`Карточка ${index + 1} ${isActive ? '(АКТИВНАЯ)' : ''}:`, {
                width: borderWidth,
                color: borderColor,
                style: borderStyle,
                correct: isBorderCorrect ? '✅' : '❌'
            });
            
            if (isBorderCorrect) {
                correctBorders++;
            }
            
            if (isActive) {
                activeCards++;
                const boxShadow = styles.boxShadow;
                const hasRing = boxShadow && boxShadow.includes('4px');
                
                console.log(`  Активное кольцо: ${hasRing ? '✅ Есть' : '❌ Отсутствует'}`);
            }
        });

        console.log(`\n📊 Результат: ${correctBorders}/${cards.length} карточек с правильными границами`);
        console.log(`🎯 Активных карточек: ${activeCards}`);
        
        return { total: cards.length, correct: correctBorders, active: activeCards };
    }

    // Демонстрация различий
    function createComparisonDemo() {
        console.log('\n3️⃣ Создание демонстрации различий...');
        
        // Удаляем существующую демонстрацию
        const existingDemo = document.getElementById('border-comparison-demo');
        if (existingDemo) {
            existingDemo.remove();
        }
        
        const demoContainer = document.createElement('div');
        demoContainer.id = 'border-comparison-demo';
        demoContainer.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 450px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        demoContainer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h3 style="margin: 0; font-size: 16px; color: #333;">
                    🎨 Сравнение границ карточек
                </h3>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="border: none; background: #dc3545; color: white; 
                               border-radius: 4px; padding: 4px 8px; cursor: pointer;">✕</button>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                <div>
                    <h4 style="margin: 0 0 8px 0; font-size: 12px; color: #666;">Старая (1px)</h4>
                    <div style="
                        background: white;
                        border: 1px solid #e2e8f0;
                        border-radius: 8px;
                        padding: 12px;
                        text-align: center;
                        font-size: 11px;
                        color: #666;
                    ">
                        Слабо видна<br>на фоне панели
                    </div>
                </div>
                
                <div>
                    <h4 style="margin: 0 0 8px 0; font-size: 12px; color: #666;">Новая (2px)</h4>
                    <div style="
                        background: white;
                        border: 2px solid #cbd5e1;
                        border-radius: 8px;
                        padding: 12px;
                        text-align: center;
                        font-size: 11px;
                        color: #666;
                    ">
                        Четко видна<br>и контрастна
                    </div>
                </div>
            </div>
            
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 12px;">
                <h4 style="margin: 0 0 8px 0; font-size: 12px;">Активная карточка с кольцом</h4>
                <div style="
                    background: white;
                    border: 2px solid #cbd5e1;
                    border-radius: 8px;
                    padding: 12px;
                    box-shadow: 0 0 0 4px #334155;
                    text-align: center;
                    font-size: 11px;
                    color: #666;
                ">
                    Кольцо 4px перекрывает границу 2px
                </div>
            </div>
            
            <div style="background: #e3f2fd; padding: 12px; border-radius: 6px;">
                <h4 style="margin: 0 0 8px 0; font-size: 12px; color: #1976d2;">💡 Преимущества 2px</h4>
                <ul style="margin: 0; padding-left: 16px; font-size: 11px; color: #666;">
                    <li>Лучшая видимость на светлом фоне</li>
                    <li>Четкое разделение карточек</li>
                    <li>Настройка через CSS переменные</li>
                    <li>Совместимость с активным кольцом</li>
                </ul>
            </div>
        `;
        
        document.body.appendChild(demoContainer);
        console.log('✅ Демонстрация создана в левом верхнем углу');
        
        // Автоматически удаляем через 20 секунд
        setTimeout(() => {
            if (document.getElementById('border-comparison-demo')) {
                demoContainer.remove();
                console.log('🧹 Демонстрация автоматически удалена');
            }
        }, 20000);
    }

    // Проверка переменных в действии
    function testVariableControl() {
        console.log('\n4️⃣ Тест управления через переменные...');
        
        console.log('💡 Попробуйте изменить переменные в консоли:');
        console.log('document.documentElement.style.setProperty("--wsf-card-border-width", "3px")');
        console.log('document.documentElement.style.setProperty("--wsf-card-border-color", "#ff6b6b")');
        
        // Добавляем глобальные функции для тестирования
        window.setBorderWidth = (width) => {
            document.documentElement.style.setProperty('--wsf-card-border-width', width);
            console.log(`✅ Ширина границы изменена на: ${width}`);
        };
        
        window.setBorderColor = (color) => {
            document.documentElement.style.setProperty('--wsf-card-border-color', color);
            console.log(`✅ Цвет границы изменен на: ${color}`);
        };
        
        window.resetBorders = () => {
            document.documentElement.style.setProperty('--wsf-card-border-width', '2px');
            document.documentElement.style.setProperty('--wsf-card-border-color', '#cbd5e1');
            console.log('✅ Границы сброшены к значениям по умолчанию');
        };
        
        console.log('🎮 Доступные функции:');
        console.log('  setBorderWidth("3px") - изменить ширину');
        console.log('  setBorderColor("#ff6b6b") - изменить цвет');
        console.log('  resetBorders() - сбросить к умолчанию');
    }

    // Основная функция тестирования
    function runTest() {
        console.log('🚀 Запуск теста границ 2px...\n');
        
        const variables = checkCSSVariables();
        const borders = checkCardBorders();
        testVariableControl();
        createComparisonDemo();
        
        console.log('\n📋 === ИТОГОВЫЙ ОТЧЕТ ===');
        console.log(`CSS переменные: ${variables.isWidthCorrect && variables.isColorCorrect ? '✅ Настроены' : '❌ Требуют настройки'}`);
        console.log(`Границы карточек: ${borders.correct}/${borders.total} правильных`);
        console.log(`Активные карточки: ${borders.active}`);
        
        const success = variables.isWidthCorrect && variables.isColorCorrect && 
                       borders.correct === borders.total && borders.total > 0;
        
        if (success) {
            console.log('\n🎉 ВСЕ ГРАНИЦЫ НАСТРОЕНЫ ПРАВИЛЬНО!');
            console.log('💪 Карточки теперь имеют четкие границы 2px');
            console.log('🎯 Активное кольцо корректно перекрывает границы');
        } else {
            console.log('\n⚠️ Обнаружены проблемы с границами');
            console.log('💡 Проверьте CSS переменные и пересоберите стили');
        }
        
        console.log('\n🔧 Используйте функции setBorderWidth() и setBorderColor() для экспериментов');
    }

    // Запускаем тест
    runTest();

})();
