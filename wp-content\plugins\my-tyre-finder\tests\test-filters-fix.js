// Тест для проверки исправления фильтров по регионам и брендам
// Файл: tests/test-filters-fix.js
// Версия 2.0 - проверка новой логики (показ всех брендов региона)

console.log('🔧 [Filters Fix Test v2.0] Начинаем тестирование исправления фильтров...');

// Функция для проверки AJAX запросов
async function testAjaxRequest(action, data = {}) {
    try {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('nonce', window.WheelFitData?.nonce || 'test-nonce');
        
        Object.keys(data).forEach(key => {
            formData.append(key, data[key]);
        });

        const response = await fetch(window.WheelFitData?.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        return result;
    } catch (error) {
        console.error(`[Filters Fix Test] Ошибка в AJAX запросе ${action}:`, error);
        return { success: false, error: error.message };
    }
}

// Тест 1: Проверка загрузки брендов в режиме "by vehicle"
async function testGetMakes() {
    console.log('[Filters Fix Test] Тест 1: Загрузка брендов (by vehicle)...');
    
    const result = await testAjaxRequest('wf_get_makes');
    
    if (result.success && Array.isArray(result.data)) {
        console.log(`✅ [Filters Fix Test] Получено ${result.data.length} брендов в режиме by vehicle`);
        console.log('[Filters Fix Test] Первые 5 брендов:', result.data.slice(0, 5).map(m => m.name || m.slug));
        return true;
    } else {
        console.error('❌ [Filters Fix Test] Ошибка загрузки брендов by vehicle:', result);
        return false;
    }
}

// Тест 2: Проверка загрузки брендов в режиме "by year"
async function testGetMakesByYear() {
    console.log('[Filters Fix Test] Тест 2: Загрузка брендов (by year)...');
    
    const testYear = 2020;
    const result = await testAjaxRequest('wf_get_makes_by_year', { year: testYear });
    
    if (result.success && Array.isArray(result.data)) {
        console.log(`✅ [Filters Fix Test] Получено ${result.data.length} брендов для года ${testYear}`);
        console.log('[Filters Fix Test] Первые 5 брендов:', result.data.slice(0, 5).map(m => m.name || m.slug));
        return true;
    } else {
        console.error('❌ [Filters Fix Test] Ошибка загрузки брендов by year:', result);
        return false;
    }
}

// Тест 3: Проверка загрузки моделей
async function testGetModels() {
    console.log('[Filters Fix Test] Тест 3: Загрузка моделей...');
    
    const testMake = 'bmw';
    const result = await testAjaxRequest('wf_get_models', { make: testMake });
    
    if (result.success && Array.isArray(result.data)) {
        console.log(`✅ [Filters Fix Test] Получено ${result.data.length} моделей для ${testMake}`);
        console.log('[Filters Fix Test] Первые 5 моделей:', result.data.slice(0, 5).map(m => m.name || m.slug));
        return true;
    } else {
        console.error('❌ [Filters Fix Test] Ошибка загрузки моделей:', result);
        return false;
    }
}

// Тест 4: Проверка загрузки моделей по году
async function testGetModelsByYear() {
    console.log('[Filters Fix Test] Тест 4: Загрузка моделей по году...');
    
    const testYear = 2020;
    const testMake = 'bmw';
    const result = await testAjaxRequest('wf_get_models_by_year', { year: testYear, make: testMake });
    
    if (result.success && Array.isArray(result.data)) {
        console.log(`✅ [Filters Fix Test] Получено ${result.data.length} моделей для ${testMake} ${testYear}`);
        console.log('[Filters Fix Test] Первые 5 моделей:', result.data.slice(0, 5).map(m => m.name || m.slug));
        return true;
    } else {
        console.error('❌ [Filters Fix Test] Ошибка загрузки моделей по году:', result);
        return false;
    }
}

// Тест 5: Проверка логики фильтров (новая функциональность)
async function testFilterLogic() {
    console.log('[Filters Fix Test] Тест 5: Проверка логики фильтров...');

    // Получаем бренды без фильтров
    const allMakes = await testAjaxRequest('wf_get_makes');

    if (!allMakes.success || !Array.isArray(allMakes.data)) {
        console.error('❌ [Filters Fix Test] Не удалось получить список всех брендов');
        return false;
    }

    const totalBrands = allMakes.data.length;
    console.log(`[Filters Fix Test] Всего брендов без фильтров: ${totalBrands}`);

    // Проверяем что при включенных региональных фильтрах
    // мы все еще видим достаточное количество брендов
    // (должно быть больше 10, иначе что-то не так)
    if (totalBrands > 10) {
        console.log('✅ [Filters Fix Test] Логика фильтров работает корректно - показываются все доступные бренды');
        return true;
    } else {
        console.error(`❌ [Filters Fix Test] Подозрительно мало брендов: ${totalBrands}. Возможно, фильтры работают неправильно.`);
        return false;
    }
}

// Основная функция тестирования
async function runFiltersFixTest() {
    console.log('🚀 [Filters Fix Test v2.0] Запуск полного теста исправления фильтров...');

    const tests = [
        { name: 'Get Makes (by vehicle)', fn: testGetMakes },
        { name: 'Get Makes By Year', fn: testGetMakesByYear },
        { name: 'Get Models', fn: testGetModels },
        { name: 'Get Models By Year', fn: testGetModelsByYear },
        { name: 'Filter Logic Test', fn: testFilterLogic }
    ];
    
    let passed = 0;
    let total = tests.length;
    
    for (const test of tests) {
        try {
            console.log(`\n--- Выполняется: ${test.name} ---`);
            const result = await test.fn();
            if (result) {
                passed++;
            }
            // Небольшая пауза между тестами
            await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
            console.error(`❌ [Filters Fix Test] Ошибка в тесте ${test.name}:`, error);
        }
    }
    
    console.log(`\n📊 [Filters Fix Test] Результаты: ${passed}/${total} тестов прошли успешно`);
    
    if (passed === total) {
        console.log('🎉 [Filters Fix Test] Все тесты прошли! Исправление фильтров работает корректно.');
    } else {
        console.log('⚠️ [Filters Fix Test] Некоторые тесты не прошли. Проверьте настройки фильтров и API.');
    }
    
    return { passed, total };
}

// Проверка виджета и запуск тестов
function initFiltersFixTest() {
    if (typeof window.WheelFitData === 'undefined') {
        console.warn('[Filters Fix Test] WheelFitData не найден. Тест может работать некорректно.');
    }
    
    // Запускаем тесты через небольшую задержку
    setTimeout(() => {
        runFiltersFixTest().then(result => {
            console.log('[Filters Fix Test] Тестирование завершено:', result);
        });
    }, 1000);
}

// Автозапуск при загрузке страницы
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initFiltersFixTest);
} else {
    initFiltersFixTest();
}

// Экспорт для ручного запуска
window.testFiltersFixTest = runFiltersFixTest;

console.log('[Filters Fix Test] Тестовый скрипт загружен. Используйте window.testFiltersFixTest() для ручного запуска.');
