/* Tailwind source file for Wheel-Fit shared widget styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* ------------------------------------------------------------------ */
/* Existing manual CSS (design tokens, layers, etc.) */
/* ------------------------------------------------------------------ */

/*
  Shared CSS for all Wheel-Fit-Widget forms
  Enhanced with comprehensive theme token system
*/

/* CSS Cascade Layers for proper specificity control */
@layer base, components, utilities;

/* 1. — Font import and global family */
/* Font import removed – fonts.css уже подключается через wp_enqueue_style */

/* 2. — CSS Custom Properties (Design Tokens) */
@layer base {
  /* Default theme tokens (light theme) */
  .wsf-finder-widget,
  .wheel-fit-widget {
    /* Core color tokens */
    --wsf-bg: #ffffff;
    --wsf-text: #1f2937;
    --wsf-primary: #2563eb;
    --wsf-border: #e5e7eb;
    --wsf-hover: #1d4ed8;

    /* Semantic color tokens */
    --wsf-secondary: #6b7280;
    --wsf-accent: #3b82f6;
    --wsf-muted: #9ca3af;
    --wsf-success: #10b981;
    --wsf-warning: #f59e0b;
    --wsf-error: #ef4444;

    /* Surface and interaction tokens */
    --wsf-surface: #f9fafb;
    --wsf-surface-hover: #f3f4f6;
    --wsf-border-light: #f3f4f6;
    --wsf-border-focus: var(--wsf-primary);
    --wsf-shadow: rgba(0, 0, 0, 0.1);
    --wsf-shadow-hover: rgba(0, 0, 0, 0.15);

    /* Typography tokens */
    --wsf-font-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --wsf-font-variable: 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --wsf-text-primary: var(--wsf-text);
    --wsf-text-secondary: var(--wsf-secondary);
    --wsf-text-muted: var(--wsf-muted);
    --wsf-text-inverse: #ffffff;

    /* Input-specific tokens - independent from surface */
    --wsf-input-bg:            #ffffff;
    --wsf-input-text:          var(--wsf-text);
    --wsf-input-border:        var(--wsf-border);
    --wsf-input-placeholder:   var(--wsf-muted);
    --wsf-input-focus:         var(--wsf-primary);

    /* Spacing and sizing tokens */
    --wsf-radius: 0.375rem;
    --wsf-radius-lg: 0.5rem;
    --wsf-spacing-xs: 0.25rem;
    --wsf-spacing-sm: 0.5rem;
    --wsf-spacing-md: 1rem;
    --wsf-spacing-lg: 1.5rem;
    --wsf-spacing-xl: 2rem;
  }

  /* Light theme explicit class */
  .wsf-theme-light {
    --wsf-bg: #ffffff;
    --wsf-text: #1f2937;
    --wsf-primary: #2563eb;
    --wsf-border: #e5e7eb;
    --wsf-hover: #1d4ed8;
    --wsf-secondary: #6b7280;
    --wsf-accent: #3b82f6;
    --wsf-muted: #9ca3af;
    --wsf-success: #10b981;
    --wsf-warning: #f59e0b;
    --wsf-error: #ef4444;
    --wsf-surface: #f9fafb;
    --wsf-surface-hover: #f3f4f6;
    --wsf-border-light: #f3f4f6;
    --wsf-border-focus: var(--wsf-primary);
    --wsf-shadow: rgba(0, 0, 0, 0.1);
    --wsf-shadow-hover: rgba(0, 0, 0, 0.15);
    --wsf-text-primary: var(--wsf-text);
    --wsf-text-secondary: var(--wsf-secondary);
    --wsf-text-muted: var(--wsf-muted);
    --wsf-text-inverse: #ffffff;
    --wsf-input-bg: #ffffff;
    --wsf-input-text: var(--wsf-text);
    --wsf-input-border: var(--wsf-border);
    --wsf-input-placeholder: var(--wsf-muted);
    --wsf-input-focus: var(--wsf-primary);
  }

  /* Dark theme class */
  .wsf-theme-dark {
    /* Font family (same as light theme) */
    --wsf-font-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --wsf-font-variable: 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    --wsf-bg: #1e1e1e;
    --wsf-text: #f3f4f6;
    --wsf-primary: #7dd3fc;
    --wsf-border: #374151;
    --wsf-hover: #0ea5e9;
    --wsf-secondary: #9ca3af;
    --wsf-accent: #60a5fa;
    --wsf-muted: #6b7280;
    --wsf-success: #34d399;
    --wsf-warning: #fbbf24;
    --wsf-error: #f87171;
    --wsf-surface: #2d2d2d;
    --wsf-surface-hover: #3d3d3d;
    --wsf-border-light: #4b5563;
    --wsf-border-focus: var(--wsf-primary);
    --wsf-shadow: rgba(0, 0, 0, 0.3);
    --wsf-shadow-hover: rgba(0, 0, 0, 0.4);
    --wsf-text-primary: var(--wsf-text);
    --wsf-text-secondary: var(--wsf-secondary);
    --wsf-text-muted: var(--wsf-muted);
    --wsf-text-inverse: #1f2937;
    --wsf-input-bg: #2d2d2d;
    --wsf-input-text: var(--wsf-text);
    --wsf-input-border: var(--wsf-border);
    --wsf-input-placeholder: var(--wsf-muted);
    --wsf-input-focus: var(--wsf-primary);
  }
}

/* 3. — Font family definitions */
@layer wsf-base {
  /* Widget font now comes from Tailwind font-sans. Keep Inter for Garage overlay only */
  #garage-drawer,
  #garage-drawer *,
  #garage-overlay,
  #garage-toast {
    font-family: var(--wsf-font-base);
    font-feature-settings: 'liga' 1, 'calt' 1;
  }

  @supports (font-variation-settings: normal) {
    #garage-drawer,
    #garage-drawer *,
    #garage-overlay,
    #garage-toast {
      font-family: var(--wsf-font-variable);
    }
  }

  /* Override Tailwind's font-sans if it's on the root widget element */
  /* REMOVED: Let Tailwind font-sans work naturally
  .wheel-fit-widget.font-sans,
  .wsf-finder-widget.font-sans,
  #garage-drawer.font-sans,
  #garage-overlay.font-sans,
  #garage-toast.font-sans {
    font-family: var(--wsf-font-base);
  }
  .wheel-fit-widget.font-sans *,
  .wsf-finder-widget.font-sans *,
  #garage-drawer.font-sans *,
  #garage-overlay.font-sans *,
  #garage-toast.font-sans * {
    font-family:inherit;
  }
  */
}

/* 4. — Component styles using design tokens */
@layer components {
  /* Universal button component */
  .btn-primary {
    background-color: var(--wsf-primary);
    color: white;
    font-weight: 600;
    padding: 0.625rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease-in-out;
    border: none;
    cursor: pointer;
  }

  /* Wizard Step 2 Layout Components */
  #wizard-step-2 .flex {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 12px;
  }

  #wizard-step-2 h2 {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.3;
    margin: 0;
    flex: 1;
  }

  #wizard-step-2 #wizard-models-count {
    display: block;
    text-align: right;
    flex-shrink: 0;
    margin-left: 16px;
    font-size: 13px;
    color: var(--wsf-muted);
  }

  #wizard-step-2 #wizard-breadcrumbs-2 {
    display: block;
    text-align: left;
    margin-bottom: 16px;
    font-size: 14px;
    color: var(--wsf-secondary);
  }

  /* Wizard Search Input */
  #wizard-step-2 .wizard-search-wrapper,
  #wizard-step-2 .search-wrapper {
    display: block;
    width: 100%;
    max-width: 100%;
    margin-bottom: 20px;
    margin-top: 0;
    position: relative;
  }

  #wizard-step-2 .wizard-search-wrapper input,
  #wizard-step-2 .search-wrapper input,
  #wizard-step-2 input[type="text"] {
    width: 100%;
    max-width: 100%;
    height: 44px;
    padding: 12px 44px 12px 16px;
    font-size: 15px;
    border: 1px solid var(--wsf-border);
    border-radius: 6px;
    background: var(--wsf-bg);
  }

  #wizard-step-2 .wizard-search-wrapper .absolute,
  #wizard-step-2 .search-wrapper .absolute {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: flex;
    align-items: center;
    padding-right: 12px;
    pointer-events: none;
  }

  #wizard-step-2 .wizard-search-wrapper svg,
  #wizard-step-2 .search-wrapper svg {
    width: 18px;
    height: 18px;
    color: var(--wsf-muted);
  }

  /* Lightweight secondary button for actions like "Clear All" */
  .btn-secondary {
    background-color: transparent;
    color: var(--wsf-error, #ef4444);
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid var(--wsf-border-light, #f3f4f6);
    transition: all 0.15s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: var(--wsf-surface-hover, #f3f4f6);
    border-color: var(--wsf-error, #ef4444);
    color: var(--wsf-error, #dc2626);
  }

  .btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--wsf-error, #ef4444), 0 0 0 4px rgba(239, 68, 68, 0.1);
  }

  .btn-secondary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .btn-secondary i {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: var(--wsf-hover);
    transform: translateY(-1px);
  }

  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--wsf-primary), 0 0 0 4px rgba(59, 130, 246, 0.1);
  }

  .btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .btn-primary:disabled:hover {
    transform: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Control height variable and widget styles */
  .wheel-fit-widget,
  .wsf-finder-widget {
    --ws-control-height: 44px;
    /* background: var(--wsf-bg); - Removed: conflicts with Tailwind, using bg-wsf-bg utility class instead */
    color: var(--wsf-text-primary);
  }

  /* Form wrapper for proper background application */
  .wsf-form-wrapper {
    background: var(--wsf-bg);
    border-radius: var(--wsf-radius-lg);
    padding: 1.5rem;
    border: 1px solid var(--wsf-border-light);
    box-shadow: 0 1px 3px 0 var(--wsf-shadow);
    transition: all 0.15s ease;
  }

  .wsf-form-wrapper:hover {
    box-shadow: 0 4px 6px -1px var(--wsf-shadow-hover);
  }

  /* Form step containers for stepper layout */
  .wsf-form-wrapper .step-container {
    background: transparent;
    border: 1px solid var(--wsf-border);
    border-radius: var(--wsf-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.15s ease;
  }

  .wsf-form-wrapper .step-container:hover {
    border-color: var(--wsf-primary);
    box-shadow: 0 0 0 1px color-mix(in srgb, var(--wsf-primary) 20%, transparent);
  }

  /* Ensure inputs inside form wrapper use correct colors */
  .wsf-form-wrapper select,
  .wsf-form-wrapper input,
  .wsf-form-wrapper .wsf-input {
    background-color: var(--wsf-input-bg);
    color: var(--wsf-input-text);
    border-color: var(--wsf-input-border);
  }

  .wheel-fit-widget select,
  .wsf-finder-widget select {
    height: var(--ws-control-height);
    background-color: var(--wsf-input-bg);
    color: var(--wsf-input-text);
    border-color: var(--wsf-input-border);
  }

  .wheel-fit-widget select:focus,
  .wsf-finder-widget select:focus {
    border-color: var(--wsf-input-focus);
    outline: none;
    box-shadow: 0 0 0 2px color-mix(in srgb, var(--wsf-input-focus) 30%, transparent);
  }

  .wheel-fit-widget select option[value=""],
  .wsf-finder-widget select option[value=""] {
    font-style: normal;
    color: var(--wsf-input-placeholder);
  }

  .wheel-fit-widget select[disabled],
  .wsf-finder-widget select[disabled] {
    opacity: .5;
    pointer-events: none;
    cursor: not-allowed;
    transition: .25s;
    background-color: var(--wsf-surface-hover);
  }

  /* Unified input component class */
  .wsf-input {
    background: var(--wsf-input-bg);
    color: var(--wsf-input-text);
    border: 1px solid var(--wsf-input-border);
    border-radius: .5rem;
    transition: .15s;
    height: var(--ws-control-height);
    padding: 0 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.3;
  }

  .wsf-input:focus {
    border-color: var(--wsf-input-focus);
    box-shadow: 0 0 0 3px color-mix(in srgb, var(--wsf-input-focus) 35%, transparent);
    outline: none;
  }

  .wsf-input::placeholder {
    color: var(--wsf-input-placeholder);
  }

  .wsf-input:disabled {
    opacity: 0.35;
    pointer-events: none;
    background-color: var(--wsf-surface);
  }

  /* Ensure full width for inputs in admin preview and frontend */
  .wsf-input,
  .wheel-fit-widget select,
  .wsf-finder-widget select,
  .wheel-fit-widget input,
  .wsf-finder-widget input {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
  }

  /* ========================================
     STEP-BY-STEP LAYOUT - FRONTEND SELECTOR FIXES
     ======================================== */

  /* Enhanced selector styling for Step-by-Step layout on frontend */
  .wheel-fit-widget .step-container select,
  .wsf-finder-widget .step-container select,
  .wheel-fit-widget .wsf-input,
  .wsf-finder-widget .wsf-input,
  .wheel-fit-widget select.wsf-input,
  .wsf-finder-widget select.wsf-input {
    /* Font consistency - override theme fonts */
    font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
    font-size: 0.875rem !important; /* 14px */
    font-weight: 500 !important;
    line-height: 1.3 !important;

    /* Vertical alignment and spacing */
    height: var(--ws-control-height, 44px) !important;
    min-height: var(--ws-control-height, 44px) !important;
    padding: 0 0.75rem !important; /* 12px horizontal */

    /* Ensure proper vertical centering */
    display: flex !important;
    align-items: center !important;

    /* Visual styling */
    background: var(--wsf-input-bg, #ffffff) !important;
    color: var(--wsf-input-text, #1f2937) !important;
    border: 1px solid var(--wsf-input-border, #d1d5db) !important;
    border-radius: 0.5rem !important; /* 8px */

    /* Smooth transitions */
    transition: all 0.15s ease !important;

    /* Reset any theme overrides */
    box-shadow: none !important;
    outline: none !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
  }

  /* Focus states for selectors */
  .wheel-fit-widget .step-container select:focus,
  .wsf-finder-widget .step-container select:focus,
  .wheel-fit-widget .wsf-input:focus,
  .wsf-finder-widget .wsf-input:focus,
  .wheel-fit-widget select.wsf-input:focus,
  .wsf-finder-widget select.wsf-input:focus {
    border-color: var(--wsf-input-focus, #2563eb) !important;
    box-shadow: 0 0 0 3px color-mix(in srgb, var(--wsf-input-focus, #2563eb) 35%, transparent) !important;
    outline: none !important;
  }

  /* Placeholder and option styling */
  .wheel-fit-widget .step-container select option,
  .wsf-finder-widget .step-container select option,
  .wheel-fit-widget .wsf-input option,
  .wsf-finder-widget .wsf-input option {
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    color: var(--wsf-input-text, #1f2937) !important;
    background: var(--wsf-input-bg, #ffffff) !important;
  }

  /* Empty option (placeholder) styling */
  .wheel-fit-widget .step-container select option[value=""],
  .wsf-finder-widget .step-container select option[value=""],
  .wheel-fit-widget .wsf-input option[value=""],
  .wsf-finder-widget .wsf-input option[value=""] {
    color: var(--wsf-input-placeholder, #9ca3af) !important;
    font-style: normal !important;
  }

  /* Disabled state */
  .wheel-fit-widget .step-container select:disabled,
  .wsf-finder-widget .step-container select:disabled,
  .wheel-fit-widget .wsf-input:disabled,
  .wsf-finder-widget .wsf-input:disabled {
    opacity: 0.35 !important;
    pointer-events: none !important;
    background-color: var(--wsf-surface, #f9fafb) !important;
    cursor: not-allowed !important;
  }

  /* Custom dropdown arrow for consistency */
  .wheel-fit-widget .step-container select,
  .wsf-finder-widget .step-container select,
  .wheel-fit-widget select.wsf-input,
  .wsf-finder-widget select.wsf-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 0.75rem center !important;
    background-repeat: no-repeat !important;
    background-size: 1.25em 1.25em !important;
    padding-right: 2.5rem !important;
  }

  /* Override any theme-specific select styling */
  .wheel-fit-widget .step-container select,
  .wsf-finder-widget .step-container select {
    /* Reset WordPress theme overrides */
    margin: 0 !important;
    vertical-align: baseline !important;

    /* Ensure consistent text rendering */
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  /* Utility classes for consistent styling */
  .wsf-card {
    background: var(--wsf-bg);
    border: 1px solid var(--wsf-border);
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 var(--wsf-shadow);
    transition: all 0.15s ease;
  }

  .wsf-card:hover {
    box-shadow: 0 4px 6px -1px var(--wsf-shadow-hover);
  }

  .wsf-text-primary { color: var(--wsf-text-primary); }
  .wsf-text-secondary { color: var(--wsf-text-secondary); }
  .wsf-text-muted { color: var(--wsf-text-muted); }
  .wsf-text-inverse { color: var(--wsf-text-inverse); }
  .wsf-text-accent { color: var(--wsf-accent); }

  /* Garage button hover states */
  .wsf-garage-hover:hover { background-color: var(--wsf-accent); }
  .wsf-garage-hover:hover .wsf-text-accent { color: var(--wsf-bg); }

  /* Garage button with count badge alignment */
  [data-garage-trigger] {
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  [data-garage-trigger] .wsf-garage-count-badge {
    margin-left: 2px !important; /* Tighter spacing */
  }

  .wsf-bg-surface { background-color: var(--wsf-surface); }
  .wsf-bg-surface-hover { background-color: var(--wsf-surface-hover); }

  .wsf-border-light { border-color: var(--wsf-border-light); }

  /* Step indicator classes */
  .wsf-step-completed {
    background-color: color-mix(in srgb, var(--wsf-primary) 20%, transparent);
    color: var(--wsf-primary);
  }

  .wsf-step-active {
    background-color: var(--wsf-primary);
    color: var(--wsf-text-inverse);
  }

  .wsf-step-inactive {
    background-color: var(--wsf-surface);
    color: var(--wsf-muted);
  }

  .wsf-progress-completed {
    background-color: var(--wsf-primary);
  }

  .wsf-progress-inactive {
    background-color: var(--wsf-surface);
  }

  /* Submit button base styles - removed to avoid conflicts with .btn-primary */
  /* Use .btn-primary class instead for consistent styling */
}

/* Responsive rules */
@layer components {
  /* ≥768 px — grid layout */
  @media (min-width:768px){
    #tab-by-car:not(.grid-2x2){
      grid-template-columns:repeat(var(--wf-cols,5),minmax(0,1fr));
      grid-auto-flow:column;
    }
    #tab-by-car:not(.grid-2x2) .ws-submit{grid-column:auto;}

    /* Auto-search mode: stretch selectors to fill available space */
    #tab-by-car.auto-search-mode:not(.grid-2x2) {
      grid-template-columns: repeat(4, 1fr) !important;
    }
  }

  /* <768 px — horizontal scroll */
  @media (max-width:767px){
    #tab-by-car:not(.grid-2x2){
      display:flex;
      overflow-x:auto;
      gap:1rem;
    }
    #tab-by-car:not(.grid-2x2) > .flex-col,
    #tab-by-car:not(.grid-2x2) .ws-submit{ flex:0 0 12rem; }
  }
}

@layer utilities {
  /* Font utility class for consistent typography */
  /* REMOVED: Let Tailwind font-sans work naturally
  .wsf-font {
    font-family: var(--wsf-font-base) !important;
    font-feature-settings: 'liga' 1, 'calt' 1 !important;
  }

  @supports (font-variation-settings: normal) {
    .wsf-font {
      font-family: var(--wsf-font-variable) !important;
    }
  }

  .wsf-font.font-sans,
  .wsf-font .font-sans,
  .wsf-font.has-global-padding,
  .wsf-font .has-global-padding,
  .wsf-font.prose,
  .wsf-font .prose {
    font-family: var(--wsf-font-base) !important;
  }
  */

  /* Root font utility to ensure consistent font across all widget elements */
  .wsf-root-font {
    font-family: var(--wsf-font-base, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji") !important;
  }
  .wsf-root-font * {
    font-family: inherit !important;
  }

  /* ========================================
     FRONTEND FONT CONSISTENCY
     ======================================== */

  /* Ensure consistent Inter font on frontend for all widget elements */
  .wheel-fit-widget,
  .wsf-finder-widget {
    font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
    font-feature-settings: 'liga' 1, 'calt' 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
  }

  /* Ensure all child elements inherit the font */
  .wheel-fit-widget *,
  .wsf-finder-widget * {
    font-family: inherit !important;
  }

  /* Override WordPress theme fonts specifically for form elements */
  .wheel-fit-widget select,
  .wheel-fit-widget input,
  .wheel-fit-widget button,
  .wheel-fit-widget label,
  .wsf-finder-widget select,
  .wsf-finder-widget input,
  .wsf-finder-widget button,
  .wsf-finder-widget label {
    font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
  }

  /* Enhanced label styling with higher specificity to override Tailwind classes */
  .wheel-fit-widget .step-container label,
  .wheel-fit-widget label[for^="wf-"],
  .wsf-finder-widget .step-container label,
  .wsf-finder-widget label[for^="wf-"],
  .wheel-fit-widget label.text-sm,
  .wheel-fit-widget label.text-xs,
  .wsf-finder-widget label.text-sm,
  .wsf-finder-widget label.text-xs {
    font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
    font-weight: 600 !important; /* Ensure semibold weight */
    color: var(--wsf-text, #1f2937) !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  /* Enhanced wizard step heading styling with higher specificity */
  .wheel-fit-widget .wizard-step h2,
  .wsf-finder-widget .wizard-step h2,
  .wheel-fit-widget h2.text-2xl,
  .wheel-fit-widget h2.font-bold,
  .wsf-finder-widget h2.text-2xl,
  .wsf-finder-widget h2.font-bold,
  .wheel-fit-widget h2.wsf-text-primary,
  .wsf-finder-widget h2.wsf-text-primary {
    font-family: var(--wsf-font-base, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
    font-weight: 700 !important; /* Ensure bold weight */
    color: var(--wsf-primary, #2563eb) !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  /* Support for variable fonts when available */
  @supports (font-variation-settings: normal) {
    .wheel-fit-widget,
    .wsf-finder-widget {
      font-family: var(--wsf-font-variable, 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
    }

    .wheel-fit-widget select,
    .wheel-fit-widget input,
    .wheel-fit-widget button,
    .wheel-fit-widget label,
    .wsf-finder-widget select,
    .wsf-finder-widget input,
    .wsf-finder-widget button,
    .wsf-finder-widget label {
      font-family: var(--wsf-font-variable, 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
    }

    /* Enhanced label styling with variable fonts */
    .wheel-fit-widget .step-container label,
    .wheel-fit-widget label[for^="wf-"],
    .wsf-finder-widget .step-container label,
    .wsf-finder-widget label[for^="wf-"],
    .wheel-fit-widget label.text-sm,
    .wheel-fit-widget label.text-xs,
    .wsf-finder-widget label.text-sm,
    .wsf-finder-widget label.text-xs {
      font-family: var(--wsf-font-variable, 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
      font-weight: 600 !important;
      color: var(--wsf-text, #1f2937) !important;
    }

    /* Enhanced wizard step heading styling with variable fonts */
    .wheel-fit-widget .wizard-step h2,
    .wsf-finder-widget .wizard-step h2,
    .wheel-fit-widget h2.text-2xl,
    .wheel-fit-widget h2.font-bold,
    .wsf-finder-widget h2.text-2xl,
    .wsf-finder-widget h2.font-bold,
    .wheel-fit-widget h2.wsf-text-primary,
    .wsf-finder-widget h2.wsf-text-primary {
      font-family: var(--wsf-font-variable, 'InterVariable', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif) !important;
      font-weight: 700 !important;
      color: var(--wsf-primary, #2563eb) !important;
    }
  }

  /* Keep steps visible if JS hides them */
  #tab-by-car [id^="step-"][hidden]{
    display:flex !important;
    visibility:visible !important;
    opacity:.35;
    pointer-events:none;
  }

  #tab-by-car [id^="step-"].hidden{
    display:flex !important;
    visibility:visible !important;
    opacity:.35;
    pointer-events:none;
  }
}

@layer components {
  /* Widget Structure - Header and Content Layout */
  .wsf-widget__header {
    /* Header container with proper spacing */
  }

  .wsf-widget__title {
    margin: 0;
    color: var(--wsf-text, #1f2937);
    font-size: 1.5rem; /* 24px */
    font-weight: 700;
    line-height: 1.3;
    text-align: center;
  }

  /* Responsive title sizing */
  @media (min-width: 768px) {
    .wsf-widget__title {
      font-size: 1.875rem; /* 30px */
    }
  }

  .wsf-widget__content {
    /* Content container - padding handled by Tailwind classes */
  }

  /* Garage specific styling fixes */
  #garage-drawer,
  #garage-overlay,
  #garage-toast {
    font-family: var(--wsf-font-base) !important;
    line-height: 1.5 !important;
    letter-spacing: normal !important;
    text-transform: none !important;
    background-color: var(--wsf-surface);
    color: var(--wsf-text-primary);
    border-color: var(--wsf-border-light);
  }

  /* Z-index fixes */
  #garage-overlay { z-index: 999998 !important; }
  #garage-drawer  { z-index: 999999 !important; }
  #garage-toast   { z-index: 1000000 !important; }

  /* Typography in garage */
  #garage-drawer h2,
  #garage-drawer p,
  #garage-drawer button,
  #garage-drawer span {
    font-family: inherit !important;
    font-weight: inherit !important;
    color: inherit !important;
  }

  /* Garage Count Badge Styles - Compact Circle Design */
  .wsf-garage-count-badge {
    background-color: var(--wsf-accent);
    color: var(--wsf-bg);
    font-size: 0.6875rem; /* 11px - smaller font */
    font-weight: 600;
    width: 18px;
    height: 18px;
    border-radius: 50%; /* Perfect circle */
    line-height: 18px; /* Match height for perfect vertical centering */
    margin-left: 6px; /* Closer to text */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    transition: all 0.2s ease;
    font-family: var(--wsf-font-base) !important;
    flex-shrink: 0; /* Prevent shrinking */
    text-align: center; /* Additional centering */
    box-sizing: border-box; /* Include padding in size calculation */
    padding: 0; /* Remove any default padding */
    position: relative; /* For fine-tuning positioning */
  }

  /* Fine-tune text positioning for perfect centering */
  .wsf-garage-count-badge::before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
  }

  /* Badge states */
  .wsf-garage-count-badge.hidden {
    display: none;
  }

  .wsf-garage-count-badge:empty {
    display: none;
  }

  /* Badge with zero count - muted style */
  .wsf-garage-count-badge[data-count="0"],
  .wsf-garage-count-badge.wsf-garage-count-zero {
    background-color: var(--wsf-muted);
    color: var(--wsf-text-muted);
    opacity: 0.7;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .wsf-garage-count-badge {
      font-size: 0.625rem; /* 10px */
      width: 16px;
      height: 16px;
      margin-left: 4px;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .wsf-garage-count-badge {
      border: 1px solid var(--wsf-border);
      font-weight: 800;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .wsf-garage-count-badge {
      transition: none;
    }
  }
}

/* Override layer – left empty for further customisations */
@layer utilities {
  /* Primary button override to ensure proper theming */
  .wheel-fit-widget .btn-primary {
    background: var(--wsf-primary) !important;
    color: var(--wsf-text-inverse) !important;
  }

  .wheel-fit-widget .btn-primary:hover:not(:disabled) {
    background: var(--wsf-hover) !important;
  }

  /* Disabled button styles - use opacity instead of changing background color */
  .wheel-fit-widget button[disabled] {
    cursor: not-allowed !important;
    opacity: 0.5 !important;
    pointer-events: none !important;
  }

  .wheel-fit-widget button[disabled]:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}

/* 5. — Disabled button states */
@layer components {
  .wheel-fit-widget button:disabled,
  .wsf-finder-widget button:disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
  }

  .wheel-fit-widget button:disabled:hover,
  .wsf-finder-widget button:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}

/* Misc fixes */
#garage-drawer button{
  font-size: inherit !important;
  line-height: inherit !important;
  border-radius: inherit !important;
}

#garage-drawer [data-lucide],
#garage-toast [data-lucide]{
  display:inline-block !important;
  width:1.25rem !important;
  height:1.25rem !important;
  stroke-width:2 !important;
}

@media screen and (min-width: 783px) {
  body.admin-bar #garage-drawer{ top:32px !important; height:calc(100vh - 32px) !important; }
}

@media screen and (max-width: 782px) {
  body.admin-bar #garage-drawer{ top:46px !important; height:calc(100vh - 46px) !important; }
}

/* ========================================
   WIZARD LAYOUT - MANUFACTURER GRID
   ======================================== */

/* Wizard makes grid - proper grid layout for frontend */
#wizard-makes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  align-items: stretch;
}

#wizard-makes-grid > * {
  display: grid;
  grid-template-rows: 1fr auto;   /* логотип сверху, подпись снизу */
  place-items: center;
  height: 96px;                   /* фиксированная комфортная высота */
  padding: 8px;
  border-radius: 12px;
  box-sizing: border-box;
  overflow: hidden;               /* на всякий случай */
}

#wizard-makes-grid > * img {
  max-width: 64px;
  max-height: 40px;
  object-fit: contain;
}

#wizard-makes-grid > * span {
  margin-top: 6px;
  font-size: 12px;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 640px) {
  #wizard-makes-grid {
    grid-template-columns: repeat(auto-fill, minmax(112px, 1fr));
    height: 88px;
  }
}

/* ========================================
   GRID (2×2) LAYOUT - FRONTEND SPACING UNIFICATION
   ======================================== */

/* Унификация отступов Grid (2×2) layout с Live Preview */
.wheel-fit-widget #tab-by-car.grid-2x2,
.wsf-finder-widget #tab-by-car.grid-2x2 {
  /* Используем тот же gap, что и в Live Preview */
  gap: 0.75rem !important; /* 12px вместо 16px */
}

/* Убираем лишние отступы у контейнера Garage */
#tab-by-car.grid-2x2 .flex.flex-col.w-full.sm\:col-span-2.items-end {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* Кнопка Garage - убираем верхний отступ */
#tab-by-car.grid-2x2 [data-garage-trigger] {
  margin-top: 0 !important;
  margin-bottom: 0.25rem !important; /* Небольшой отступ снизу */
}

/* Контейнер кнопки "Find Sizes" в Grid layout */
#tab-by-car.grid-2x2 .ws-submit {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
}

/* Прозрачный label над кнопкой "Find Sizes" */
#tab-by-car.grid-2x2 .ws-submit label[style*="height: 0"] {
  margin-bottom: 0 !important;
  height: 0 !important;
  line-height: 0 !important;
  padding: 0 !important;
}

/* Кнопка "Find Sizes" в Grid layout */
#tab-by-car.grid-2x2 .ws-submit .btn-primary {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Адаптивные правила для мобильных устройств */
@media (max-width: 639px) {
  .wheel-fit-widget #tab-by-car.grid-2x2,
  .wsf-finder-widget #tab-by-car.grid-2x2 {
    gap: 0.5rem !important; /* Еще более компактно на мобильных */
  }

  /* На мобильных убираем все лишние отступы */
  #tab-by-car.grid-2x2 [data-garage-trigger] {
    margin-bottom: 0.125rem !important; /* Минимальный отступ */
  }
}

/* ========================================
   INLINE (1×4) LAYOUT - BUTTON SIZE UNIFICATION
   ======================================== */

/* Выравнивание высоты кнопки "Find Sizes" с полями формы в Inline layout */
.wheel-fit-widget .ws-submit .btn-primary,
.wsf-finder-widget .ws-submit .btn-primary {
  /* Устанавливаем точно такую же высоту, как у селекторов */
  height: var(--ws-control-height, 44px) !important;
  min-height: var(--ws-control-height, 44px) !important;
  max-height: var(--ws-control-height, 44px) !important;

  /* Убираем вертикальные отступы, чтобы контент поместился в заданную высоту */
  padding-top: 0 !important;
  padding-bottom: 0 !important;

  /* Сохраняем горизонтальные отступы для красоты */
  padding-left: 1.5rem !important; /* 24px */
  padding-right: 1.5rem !important; /* 24px */

  /* Центрируем содержимое по вертикали */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /* Убираем лишние отступы и границы */
  box-sizing: border-box !important;

  /* Сохраняем остальные стили кнопки */
  font-size: 0.875rem !important; /* 14px - как у селекторов */
  font-weight: 600 !important;
  line-height: 1.3 !important; /* Как у селекторов */

  /* Убираем трансформации, которые могут влиять на высоту */
  transform: none !important;
}

/* Состояние hover - убираем трансформацию, которая может изменить высоту */
.wheel-fit-widget .ws-submit .btn-primary:hover:not(:disabled),
.wsf-finder-widget .ws-submit .btn-primary:hover:not(:disabled) {
  transform: none !important; /* Убираем translateY(-1px) */
  /* Сохраняем изменение цвета при hover */
  background-color: var(--wsf-hover) !important;
}

/* Состояние focus - убираем изменения высоты */
.wheel-fit-widget .ws-submit .btn-primary:focus,
.wsf-finder-widget .ws-submit .btn-primary:focus {
  transform: none !important;
  /* Сохраняем focus ring */
  outline: none !important;
  box-shadow: 0 0 0 2px var(--wsf-primary), 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
}

/* Лоадер внутри кнопки - подгоняем под новую высоту */
.ws-submit .btn-primary #search-loader {
  height: 16px !important;
  width: 16px !important;
}

.ws-submit .btn-primary #search-loader > div {
  height: 16px !important;
  width: 16px !important;
}

/* Адаптивность для Inline layout */
@media (max-width: 639px) {
  /* На мобильных кнопка может быть чуть выше для удобства нажатия */
  .wheel-fit-widget .ws-submit .btn-primary,
  .wsf-finder-widget .ws-submit .btn-primary {
    height: 48px !important; /* Чуть выше на мобильных */
    min-height: 48px !important;
    max-height: 48px !important;
    font-size: 0.9rem !important; /* Чуть больше шрифт */
  }
}