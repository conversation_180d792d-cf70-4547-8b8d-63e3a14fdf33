/**
 * Test script for Steps Inside Form
 * 
 * This test verifies that:
 * 1. Progress indicators (Make, Model, Year, Modification) are inside the form wrapper
 * 2. Steps are positioned below the widget title
 * 3. Steps inherit theme styles and background
 * 4. No visual separation between steps and form content
 * 5. All themes apply correctly to the steps panel
 * 
 * Run this in browser console on /wp-admin/admin.php?page=wheel-size-appearance
 */

console.log('📋 Testing Steps Inside Form...');

function testStepsInsideForm() {
    console.log('📋 Starting steps placement tests...');
    
    const previewContainer = document.getElementById('widget-preview');
    if (!previewContainer) {
        console.error('❌ Live Preview container not found');
        return;
    }
    
    console.log('✅ Live Preview container found');
    
    // Test 1: Check if steps are inside form wrapper
    testStepsPlacement(previewContainer);
    
    // Test 2: Check visual integration
    testVisualIntegration(previewContainer);
    
    // Test 3: Check theme inheritance
    testThemeInheritance(previewContainer);
    
    // Test 4: Check step indicators styling
    testStepIndicators(previewContainer);
    
    // Test 5: Check hierarchy order
    testHierarchyOrder(previewContainer);
    
    // Test 6: Generate integration report
    generateIntegrationReport(previewContainer);
    
    console.log('🎉 Steps placement tests completed!');
}

function testStepsPlacement(container) {
    console.log('📍 Testing steps placement...');
    
    const formWrapper = container.querySelector('.wsf-form-wrapper');
    const widgetTitle = container.querySelector('.wsf-widget__title, h1');
    const stepsPanel = container.querySelector('.flex.items-center.gap-3');
    
    if (!formWrapper) {
        console.error('❌ Form wrapper not found');
        return;
    }
    
    if (!widgetTitle) {
        console.error('❌ Widget title not found');
        return;
    }
    
    if (!stepsPanel) {
        console.error('❌ Steps panel not found');
        return;
    }
    
    // Check if steps panel is inside form wrapper
    const stepsInsideForm = formWrapper.contains(stepsPanel);
    const titleInsideForm = formWrapper.contains(widgetTitle);
    
    console.log(`📊 Element placement:`);
    console.log(`  Title inside form: ${titleInsideForm ? '✅' : '❌'}`);
    console.log(`  Steps inside form: ${stepsInsideForm ? '✅' : '❌'}`);
    
    if (stepsInsideForm && titleInsideForm) {
        // Check order within form
        const formChildren = Array.from(formWrapper.children);
        const titleIndex = formChildren.findIndex(child => child.contains(widgetTitle));
        const stepsIndex = formChildren.findIndex(child => child === stepsPanel || child.contains(stepsPanel));
        
        console.log(`  Title position: ${titleIndex + 1} of ${formChildren.length}`);
        console.log(`  Steps position: ${stepsIndex + 1} of ${formChildren.length}`);
        
        const correctOrder = titleIndex < stepsIndex;
        
        if (correctOrder) {
            console.log('✅ Correct hierarchy: Title comes before Steps');
        } else {
            console.error('❌ Incorrect hierarchy: Steps should come after Title');
        }
        
        return { stepsInsideForm, titleInsideForm, correctOrder, titleIndex, stepsIndex };
    } else {
        console.error('❌ Elements not properly placed inside form wrapper');
        return { stepsInsideForm, titleInsideForm, correctOrder: false };
    }
}

function testVisualIntegration(container) {
    console.log('👁️ Testing visual integration...');
    
    const formWrapper = container.querySelector('.wsf-form-wrapper');
    const stepsPanel = container.querySelector('.flex.items-center.gap-3');
    const widgetContainer = container.querySelector('.wheel-fit-widget, .wsf-finder-widget');
    
    if (!formWrapper || !stepsPanel || !widgetContainer) {
        console.warn('⚠️ Required elements not found for visual test');
        return;
    }
    
    const formStyle = window.getComputedStyle(formWrapper);
    const stepsStyle = window.getComputedStyle(stepsPanel);
    const widgetStyle = window.getComputedStyle(widgetContainer);
    
    console.log(`📊 Visual integration:`);
    console.log(`  Widget background: ${widgetStyle.backgroundColor}`);
    console.log(`  Form background: ${formStyle.backgroundColor}`);
    console.log(`  Steps background: ${stepsStyle.backgroundColor}`);
    
    // Check background inheritance
    const stepsBg = stepsStyle.backgroundColor;
    const formBg = formStyle.backgroundColor;
    const widgetBg = widgetStyle.backgroundColor;
    
    const isTransparent = stepsBg === 'rgba(0, 0, 0, 0)' || stepsBg === 'transparent';
    const bgInherited = isTransparent || stepsBg === formBg || stepsBg === widgetBg;
    
    if (bgInherited) {
        console.log('✅ Steps background is properly inherited');
    } else {
        console.warn('⚠️ Steps background might not be inheriting correctly');
    }
    
    // Check if steps are visually continuous with form
    const stepsRect = stepsPanel.getBoundingClientRect();
    const formRect = formWrapper.getBoundingClientRect();
    
    const stepsInsideFormBounds = stepsRect.left >= formRect.left && 
                                 stepsRect.right <= formRect.right &&
                                 stepsRect.top >= formRect.top &&
                                 stepsRect.bottom <= formRect.bottom;
    
    if (stepsInsideFormBounds) {
        console.log('✅ Steps are visually contained within form bounds');
    } else {
        console.warn('⚠️ Steps might extend outside form visual bounds');
    }
    
    return { bgInherited, stepsInsideFormBounds, stepsBg, formBg };
}

function testThemeInheritance(container) {
    console.log('🌈 Testing theme inheritance...');
    
    const stepsPanel = container.querySelector('.flex.items-center.gap-3');
    
    if (!stepsPanel) {
        console.warn('⚠️ Steps panel not found for theme test');
        return;
    }
    
    // Check step indicators
    const stepIndicators = stepsPanel.querySelectorAll('[id^="step-indicator-"]');
    const stepTexts = stepsPanel.querySelectorAll('[id^="step-text-"]');
    const progressBars = stepsPanel.querySelectorAll('[id^="progress-"]');
    
    console.log(`📊 Theme inheritance:`);
    console.log(`  Found ${stepIndicators.length} step indicators`);
    console.log(`  Found ${stepTexts.length} step texts`);
    console.log(`  Found ${progressBars.length} progress bars`);
    
    // Check if indicators use theme colors
    let themeColorsUsed = 0;
    
    stepIndicators.forEach((indicator, index) => {
        const style = window.getComputedStyle(indicator);
        const bgColor = style.backgroundColor;
        const color = style.color;
        const borderColor = style.borderColor;
        
        console.log(`  Indicator ${index + 1}: bg: ${bgColor}, color: ${color}, border: ${borderColor}`);
        
        // Check if it uses CSS custom properties or theme-appropriate colors
        if (bgColor.includes('rgb') || color.includes('rgb') || borderColor.includes('rgb')) {
            themeColorsUsed++;
        }
    });
    
    // Check step texts
    stepTexts.forEach((text, index) => {
        const style = window.getComputedStyle(text);
        const color = style.color;
        
        console.log(`  Text ${index + 1}: color: ${color}`);
    });
    
    // Check progress bars
    progressBars.forEach((bar, index) => {
        const style = window.getComputedStyle(bar);
        const bgColor = style.backgroundColor;
        
        console.log(`  Progress ${index + 1}: bg: ${bgColor}`);
    });
    
    const themeIntegrated = themeColorsUsed > 0;
    
    if (themeIntegrated) {
        console.log('✅ Steps use theme colors');
    } else {
        console.warn('⚠️ Steps might not be using theme colors');
    }
    
    return { stepIndicators: stepIndicators.length, stepTexts: stepTexts.length, progressBars: progressBars.length, themeIntegrated };
}

function testStepIndicators(container) {
    console.log('🔵 Testing step indicators...');
    
    const stepIndicators = container.querySelectorAll('[id^="step-indicator-"]');
    
    if (stepIndicators.length === 0) {
        console.warn('⚠️ No step indicators found');
        return;
    }
    
    console.log(`📊 Step indicators analysis:`);
    
    let uniformSize = true;
    const expectedSize = 24; // 24px
    
    stepIndicators.forEach((indicator, index) => {
        const style = window.getComputedStyle(indicator);
        const rect = indicator.getBoundingClientRect();
        
        const cssWidth = parseFloat(style.width);
        const cssHeight = parseFloat(style.height);
        const actualWidth = rect.width;
        const actualHeight = rect.height;
        
        console.log(`  Indicator ${index + 1}: ${cssWidth}x${cssHeight}px (actual: ${actualWidth.toFixed(1)}x${actualHeight.toFixed(1)}px)`);
        
        const correctSize = Math.abs(cssWidth - expectedSize) <= 2 && Math.abs(cssHeight - expectedSize) <= 2;
        const isCircular = style.borderRadius === '50%' || style.borderRadius.includes('50%');
        
        if (!correctSize) {
            uniformSize = false;
        }
        
        console.log(`    Size: ${correctSize ? '✅' : '❌'}, Circular: ${isCircular ? '✅' : '❌'}`);
    });
    
    if (uniformSize) {
        console.log('✅ All step indicators have uniform size');
    } else {
        console.warn('⚠️ Step indicators have inconsistent sizes');
    }
    
    return { uniformSize, totalIndicators: stepIndicators.length };
}

function testHierarchyOrder(container) {
    console.log('🏗️ Testing hierarchy order...');
    
    const formWrapper = container.querySelector('.wsf-form-wrapper');
    
    if (!formWrapper) {
        console.warn('⚠️ Form wrapper not found for hierarchy test');
        return;
    }
    
    const formChildren = Array.from(formWrapper.children);
    
    console.log(`📊 Form hierarchy (${formChildren.length} children):`);
    
    formChildren.forEach((child, index) => {
        let description = 'Unknown element';
        
        if (child.classList.contains('wsf-widget__header')) {
            description = '📝 Widget Title Header';
        } else if (child.classList.contains('flex') && child.classList.contains('items-center') && child.classList.contains('gap-3')) {
            description = '📋 Steps Progress Panel';
        } else if (child.tagName === 'FORM') {
            description = '📄 Form Content';
        } else {
            const classes = Array.from(child.classList).slice(0, 2).join('.');
            description = `${child.tagName.toLowerCase()}.${classes}`;
        }
        
        console.log(`  ${index + 1}. ${description}`);
    });
    
    // Check expected order: Title → Steps → Form
    const titleIndex = formChildren.findIndex(child => child.classList.contains('wsf-widget__header'));
    const stepsIndex = formChildren.findIndex(child => 
        child.classList.contains('flex') && 
        child.classList.contains('items-center') && 
        child.classList.contains('gap-3')
    );
    const formIndex = formChildren.findIndex(child => child.tagName === 'FORM');
    
    const correctOrder = titleIndex < stepsIndex && stepsIndex < formIndex;
    
    if (correctOrder) {
        console.log('✅ Correct hierarchy order: Title → Steps → Form');
    } else {
        console.warn('⚠️ Hierarchy order might be incorrect');
    }
    
    return { correctOrder, titleIndex, stepsIndex, formIndex };
}

function generateIntegrationReport(container) {
    console.log('📋 Generating integration report...');
    
    const formWrapper = container.querySelector('.wsf-form-wrapper');
    const stepsPanel = container.querySelector('.flex.items-center.gap-3');
    const stepIndicators = container.querySelectorAll('[id^="step-indicator-"]');
    
    if (!formWrapper || !stepsPanel) {
        console.error('❌ Cannot generate report - required elements missing');
        return;
    }
    
    const stepsInsideForm = formWrapper.contains(stepsPanel);
    const stepsStyle = window.getComputedStyle(stepsPanel);
    const bgInherited = stepsStyle.backgroundColor === 'rgba(0, 0, 0, 0)' || 
                       stepsStyle.backgroundColor === 'transparent';
    
    const report = {
        placement: stepsInsideForm ? 'INSIDE_FORM' : 'OUTSIDE_FORM',
        backgroundInherited: bgInherited,
        stepIndicators: stepIndicators.length,
        themeIntegration: stepIndicators.length > 0,
        status: 'CHECKING',
        issues: []
    };
    
    // Determine overall status
    if (stepsInsideForm && bgInherited && stepIndicators.length > 0) {
        report.status = '✅ PERFECT INTEGRATION';
    } else if (stepsInsideForm && stepIndicators.length > 0) {
        report.status = '⚠️ GOOD INTEGRATION, MINOR ISSUES';
    } else {
        report.status = '❌ NEEDS FIXING';
    }
    
    // Generate issues list
    if (!stepsInsideForm) {
        report.issues.push('Steps panel is not inside form wrapper');
    }
    
    if (!bgInherited) {
        report.issues.push('Steps background might not be inheriting correctly');
    }
    
    if (stepIndicators.length === 0) {
        report.issues.push('No step indicators found');
    }
    
    console.log('\n📊 === INTEGRATION REPORT ===');
    console.log(`Status: ${report.status}`);
    console.log(`Placement: ${report.placement}`);
    console.log(`Background inherited: ${report.backgroundInherited ? '✅' : '❌'}`);
    console.log(`Step indicators: ${report.stepIndicators}`);
    console.log(`Theme integration: ${report.themeIntegration ? '✅' : '❌'}`);
    
    if (report.issues.length > 0) {
        console.log('\n💡 Issues to fix:');
        report.issues.forEach((issue, index) => {
            console.log(`${index + 1}. ${issue}`);
        });
    } else {
        console.log('\n🎉 No issues found - steps are perfectly integrated!');
    }
    
    // Store report for manual access
    window.integrationReport = report;
}

// Manual test helper functions
window.testStepsInsideForm = function() {
    console.log('🔧 Manual steps inside form test...');
    testStepsInsideForm();
};

window.highlightStepsIntegration = function() {
    console.log('🎨 Highlighting steps integration...');
    
    const title = document.querySelector('.wsf-widget__header');
    const steps = document.querySelector('.flex.items-center.gap-3');
    const form = document.querySelector('.wsf-form-wrapper');
    const widget = document.querySelector('.wheel-fit-widget, .wsf-finder-widget');
    
    const elements = [
        { element: widget, color: 'rgba(0, 0, 255, 0.2)', label: 'Widget Container' },
        { element: form, color: 'rgba(0, 255, 0, 0.2)', label: 'Form Wrapper' },
        { element: title, color: 'rgba(255, 255, 0, 0.3)', label: 'Widget Title' },
        { element: steps, color: 'rgba(255, 0, 0, 0.3)', label: 'Steps Panel' }
    ];
    
    elements.forEach(({ element, color, label }) => {
        if (element) {
            element.style.backgroundColor = color;
            element.style.border = `2px solid ${color.replace('0.2', '1').replace('0.3', '1')}`;
            console.log(`✅ Highlighted ${label}`);
        } else {
            console.warn(`⚠️ ${label} not found`);
        }
    });
    
    setTimeout(() => {
        elements.forEach(({ element }) => {
            if (element) {
                element.style.backgroundColor = '';
                element.style.border = '';
            }
        });
        console.log('🧹 Highlights removed');
    }, 5000);
};

window.showFormStructure = function() {
    console.log('🏗️ Showing form structure...');
    
    const form = document.querySelector('.wsf-form-wrapper');
    if (form) {
        const children = Array.from(form.children);
        console.log('📊 Form wrapper children (in order):');
        children.forEach((child, index) => {
            const tag = child.tagName.toLowerCase();
            const id = child.id || 'no-id';
            const classes = Array.from(child.classList).slice(0, 3).join('.');
            const text = child.textContent ? child.textContent.substring(0, 30) + '...' : 'no-text';
            console.log(`  ${index + 1}. <${tag}> #${id} .${classes} "${text}"`);
        });
    } else {
        console.warn('⚠️ Form wrapper not found');
    }
};

// Auto-run the test
testStepsInsideForm();

console.log('\n🔧 Manual test functions available:');
console.log('  - testStepsInsideForm() - Run integration test');
console.log('  - highlightStepsIntegration() - Highlight elements (5s)');
console.log('  - showFormStructure() - Show form children order');
console.log('  - integrationReport - Access detailed report');
