<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Configuration Change Fixes</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography&preflight=false"></script>
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-results {
            background: #f9fafb;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="test-container">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Configuration Change Fixes Test</h1>
        
        <!-- Test Status Overview -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Test Status Overview</h2>
            <div id="status-overview">
                <div class="flex items-center mb-2">
                    <span class="status-indicator status-warning"></span>
                    <span>Tests not yet run</span>
                </div>
            </div>
        </div>

        <!-- Translation Test Section -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Translation System Test</h2>
            <p class="text-gray-600 mb-4">
                Tests the translation system to ensure Russian translations persist after configuration changes.
            </p>
            
            <!-- Mock translation elements -->
            <div class="mb-4 p-4 border border-gray-200 rounded">
                <h3 class="font-medium mb-2">Sample Translatable Elements:</h3>
                <button data-i18n="button_search" class="test-button">Find Sizes</button>
                <span data-i18n="label_make" class="inline-block bg-gray-100 px-2 py-1 rounded">Make</span>
                <span data-i18n="label_model" class="inline-block bg-gray-100 px-2 py-1 rounded">Model</span>
                <input type="text" data-i18n-placeholder="select_make_placeholder" placeholder="Choose a make" class="border border-gray-300 rounded px-3 py-2 ml-2">
            </div>
            
            <button onclick="testTranslations()" class="test-button">Run Translation Tests</button>
            <button onclick="simulateConfigChange()" class="test-button">Simulate Config Change</button>
            <button onclick="applyTranslationsManually()" class="test-button">Apply Translations</button>
            
            <div id="translation-results" class="test-results" style="display: none;"></div>
        </div>

        <!-- Garage Test Section -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Garage System Test</h2>
            <p class="text-gray-600 mb-4">
                Tests the garage functionality to ensure it remains functional after configuration changes.
            </p>
            
            <!-- Mock garage elements -->
            <div class="mb-4 p-4 border border-gray-200 rounded">
                <h3 class="font-medium mb-2">Mock Garage Elements:</h3>
                <button data-garage-trigger class="test-button">
                    <span data-i18n="label_garage">Garage</span> (<span id="garage-count">0</span>)
                </button>
                
                <!-- Mock garage drawer (hidden) -->
                <div id="garage-drawer" class="hidden fixed right-0 top-0 h-full w-80 bg-white shadow-lg transform translate-x-full transition-transform">
                    <div class="p-4">
                        <h3 data-i18n="garage_title">My Garage</h3>
                        <button id="garage-close-btn" class="absolute top-4 right-4">×</button>
                        <div id="garage-items-list"></div>
                        <button id="garage-clear-all" data-i18n="garage_clear_all">Clear all</button>
                    </div>
                </div>
                
                <!-- Mock garage overlay -->
                <div id="garage-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50"></div>
            </div>
            
            <button onclick="testGarage()" class="test-button">Run Garage Tests</button>
            <button onclick="simulateGarageReinit()" class="test-button">Simulate Reinit</button>
            <button onclick="debugGarage()" class="test-button">Debug Garage</button>
            
            <div id="garage-results" class="test-results" style="display: none;"></div>
        </div>

        <!-- Comprehensive Test Section -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Comprehensive Tests</h2>
            <p class="text-gray-600 mb-4">
                Run all tests to verify the complete fix implementation.
            </p>
            
            <button onclick="runAllTests()" class="test-button bg-green-600 hover:bg-green-700">Run All Tests</button>
            <button onclick="clearResults()" class="test-button bg-gray-600 hover:bg-gray-700">Clear Results</button>
            
            <div id="comprehensive-results" class="test-results" style="display: none;"></div>
        </div>
    </div>

    <!-- Mock data and functions -->
    <script>
        // Mock WheelFitData
        window.WheelFitData = {
            garageEnabled: true,
            formLayout: 'popup-horizontal',
            debug: true,
            ajaxurl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce'
        };

        // Mock Russian translations
        window.WheelFitI18n = {
            "button_search": "Поиск",
            "label_make": "Бренд", 
            "label_model": "Модель",
            "label_garage": "Гараж",
            "garage_title": "Мой гараж",
            "garage_clear_all": "Очистить все",
            "select_make_placeholder": "Выберите бренд"
        };

        // Mock translation function
        window.t = function(key, fallback = '') {
            return (window.WheelFitI18n && window.WheelFitI18n[key]) || fallback || key;
        };

        // Mock garage state
        window.__garageInitiated = false;

        // Mock LocalStorageHandler
        window.LocalStorageHandler = function() {
            this.getGarage = () => [];
            this.addToGarage = () => true;
            this.removeFromGarage = () => true;
        };

        // Test functions
        function testTranslations() {
            const results = document.getElementById('translation-results');
            results.style.display = 'block';
            results.textContent = 'Running translation tests...\n';
            
            // Test if translations are applied
            const elements = document.querySelectorAll('[data-i18n]');
            let passed = 0;
            let total = elements.length;
            
            elements.forEach(el => {
                const key = el.dataset.i18n;
                const expected = window.WheelFitI18n[key];
                const actual = el.textContent.trim();
                
                if (actual === expected) {
                    passed++;
                    results.textContent += `✓ ${key}: "${actual}"\n`;
                } else {
                    results.textContent += `✗ ${key}: expected "${expected}", got "${actual}"\n`;
                }
            });
            
            // Test placeholder translations
            const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
            placeholderElements.forEach(el => {
                const key = el.dataset.i18nPlaceholder;
                const expected = window.WheelFitI18n[key];
                const actual = el.placeholder;
                total++;
                
                if (actual === expected) {
                    passed++;
                    results.textContent += `✓ ${key} (placeholder): "${actual}"\n`;
                } else {
                    results.textContent += `✗ ${key} (placeholder): expected "${expected}", got "${actual}"\n`;
                }
            });
            
            results.textContent += `\nTranslation Tests: ${passed}/${total} passed\n`;
            updateStatusOverview('translations', passed === total);
        }

        function testGarage() {
            const results = document.getElementById('garage-results');
            results.style.display = 'block';
            results.textContent = 'Running garage tests...\n';
            
            let passed = 0;
            let total = 0;
            
            // Test garage elements exist
            total++;
            const drawer = document.getElementById('garage-drawer');
            if (drawer) {
                passed++;
                results.textContent += '✓ Garage drawer element exists\n';
            } else {
                results.textContent += '✗ Garage drawer element missing\n';
            }
            
            // Test garage triggers exist
            total++;
            const triggers = document.querySelectorAll('[data-garage-trigger]');
            if (triggers.length > 0) {
                passed++;
                results.textContent += `✓ Found ${triggers.length} garage trigger(s)\n`;
            } else {
                results.textContent += '✗ No garage triggers found\n';
            }
            
            // Test garage functions exist
            total++;
            if (typeof window.initGarageFeature === 'function') {
                passed++;
                results.textContent += '✓ initGarageFeature function exists\n';
            } else {
                results.textContent += '✗ initGarageFeature function missing\n';
            }
            
            results.textContent += `\nGarage Tests: ${passed}/${total} passed\n`;
            updateStatusOverview('garage', passed === total);
        }

        function simulateConfigChange() {
            const results = document.getElementById('translation-results');
            results.style.display = 'block';
            results.textContent += '\nSimulating configuration change...\n';
            
            // Simulate clearing translations
            window.WheelFitI18n = {};
            results.textContent += 'Translations cleared (simulating cache issue)\n';
            
            // Restore translations (simulating fix)
            setTimeout(() => {
                window.WheelFitI18n = {
                    "button_search": "Поиск",
                    "label_make": "Бренд", 
                    "label_model": "Модель",
                    "label_garage": "Гараж",
                    "garage_title": "Мой гараж",
                    "garage_clear_all": "Очистить все",
                    "select_make_placeholder": "Выберите бренд"
                };
                results.textContent += 'Translations restored (fix applied)\n';
                applyTranslationsManually();
            }, 1000);
        }

        function applyTranslationsManually() {
            if (typeof window.applyStaticTranslations === 'function') {
                window.applyStaticTranslations();
            } else {
                // Manual application for testing
                document.querySelectorAll('[data-i18n]').forEach(el => {
                    const key = el.dataset.i18n;
                    const translation = window.t(key, el.textContent);
                    el.textContent = translation;
                });
                
                document.querySelectorAll('[data-i18n-placeholder]').forEach(el => {
                    const key = el.dataset.i18nPlaceholder;
                    const translation = window.t(key, el.placeholder);
                    el.placeholder = translation;
                });
            }
            
            const results = document.getElementById('translation-results');
            if (results.style.display !== 'none') {
                results.textContent += 'Translations applied manually\n';
            }
        }

        function simulateGarageReinit() {
            const results = document.getElementById('garage-results');
            results.style.display = 'block';
            results.textContent += '\nSimulating garage reinitialization...\n';
            
            // Reset garage state
            window.__garageInitiated = false;
            results.textContent += 'Garage state reset\n';
            
            // Simulate reinit
            if (typeof window.reinitGarage === 'function') {
                window.reinitGarage();
                results.textContent += 'Garage reinitialized using reinitGarage()\n';
            } else if (typeof window.initGarageFeature === 'function') {
                window.initGarageFeature(true);
                results.textContent += 'Garage reinitialized using initGarageFeature(true)\n';
            } else {
                results.textContent += 'No garage initialization functions available\n';
            }
        }

        function debugGarage() {
            if (typeof window.debugGarageWidget === 'function') {
                window.debugGarageWidget();
            } else {
                console.log('debugGarageWidget function not available');
            }
        }

        function runAllTests() {
            clearResults();
            testTranslations();
            testGarage();
            
            const results = document.getElementById('comprehensive-results');
            results.style.display = 'block';
            results.textContent = 'Comprehensive test completed. Check individual test results above.\n';
        }

        function clearResults() {
            document.querySelectorAll('.test-results').forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
            
            // Reset status overview
            document.getElementById('status-overview').innerHTML = `
                <div class="flex items-center mb-2">
                    <span class="status-indicator status-warning"></span>
                    <span>Tests not yet run</span>
                </div>
            `;
        }

        function updateStatusOverview(testType, passed) {
            const overview = document.getElementById('status-overview');
            const statusClass = passed ? 'status-success' : 'status-error';
            const statusText = passed ? 'Passed' : 'Failed';
            
            overview.innerHTML += `
                <div class="flex items-center mb-2">
                    <span class="status-indicator ${statusClass}"></span>
                    <span>${testType.charAt(0).toUpperCase() + testType.slice(1)} Test: ${statusText}</span>
                </div>
            `;
        }

        // Apply initial translations
        document.addEventListener('DOMContentLoaded', () => {
            applyTranslationsManually();
        });
    </script>
</body>
</html>
