{# Modification Field Template - Reusable across layouts #}
{% set is_stepper = (form_layout ?? 'stepper') == 'stepper' %}
{% set is_inline = (form_layout ?? 'inline') == 'inline' %}
{% set is_popup_horizontal = (form_layout ?? 'popup-horizontal') == 'popup-horizontal' %}

{% if is_stepper %}
    {# Step-by-Step layout #}
    <div id="step-4" class="step-container hidden relative">
        <label for="wf-modification" class="block text-sm font-semibold text-wsf-text uppercase tracking-wide mb-2" data-i18n="label_mods">Modification</label>
        <div class="relative w-full">
            <select id="wf-modification" name="modification" class="wsf-input block w-full" required disabled>
                <option value="">Select year first</option>
            </select>
            <div id="modification-loader" class="hidden absolute right-4 top-1/2 -translate-y-1/2"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-wsf-primary"></div></div>
        </div>
        {% if not auto_search %}
        <div class="mt-6 text-center">
            <button type="submit" class="btn-primary w-full md:w-auto py-3 px-10" disabled>
                <span id="search-text" data-i18n="button_search">Find Tire & Wheel Sizes</span>
                <div id="search-loader" class="hidden inline-block ml-2"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div></div>
            </button>
        </div>
        {% endif %}
        {% if garage_enabled %}
        <div class="mt-2 flex justify-end">
            <button type="button" data-garage-trigger class="inline-flex items-center gap-2 text-sm text-wsf-text px-3 py-1.5 rounded-md hover:bg-wsf-bg hover:text-wsf-text transition">
                <i data-lucide="car" class="w-5 h-5"></i>
                <span class="font-semibold" data-i18n="label_garage">Garage</span>
                <span id="garage-count" class="wsf-garage-count-badge hidden"></span>
            </button>
        </div>
        {% endif %}
    </div>
{% else %}
    {# Inline and Popup-Horizontal layouts #}
    <div id="step-4" class="flex flex-col w-full min-w-0">
        <label for="wf-modification" class="block text-xs font-semibold text-wsf-text uppercase tracking-wide mb-1" data-i18n="label_mods">Modification</label>
        <div class="relative min-w-0">
            <select id="wf-modification" name="modification" class="wsf-input block w-full" required disabled>
                <option value="" data-i18n="select_year_first_placeholder">Select year first</option>
            </select>
            <div id="modification-loader" class="loader hidden absolute right-3 top-1/2 -translate-y-1/2"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-wsf-primary"></div></div>
        </div>
    </div>
{% endif %} 