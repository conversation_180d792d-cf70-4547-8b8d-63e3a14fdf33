// Comprehensive diagnostic test for automatic search functionality
console.log('=== Automatic Search Functionality Diagnosis ===');

// Test 1: Check AUTO_SEARCH constant and configuration
console.log('1. AUTO_SEARCH Configuration Check:');
console.log('   window.WheelFitData:', window.WheelFitData);
console.log('   WheelFitData.autoSearch:', window.WheelFitData?.autoSearch);

// Check if AUTO_SEARCH constant is properly set
let AUTO_SEARCH_VALUE;
try {
    // Try to access the constant from the global scope or recreate it
    if (window.WheelFitData && window.WheelFitData.autoSearch !== undefined) {
        AUTO_SEARCH_VALUE = !!(window.WheelFitData.autoSearch);
        console.log('   ✅ AUTO_SEARCH value:', AUTO_SEARCH_VALUE);
    } else {
        console.log('   ❌ WheelFitData.autoSearch not found');
        AUTO_SEARCH_VALUE = false;
    }
} catch (error) {
    console.log('   ❌ Error accessing AUTO_SEARCH:', error);
    AUTO_SEARCH_VALUE = false;
}

// Test 2: Check WordPress option value
console.log('2. WordPress Option Check:');
// We can't directly access PHP options from JS, but we can check if it's passed correctly
if (window.WheelFitData) {
    console.log('   autoSearch in WheelFitData:', window.WheelFitData.autoSearch);
    console.log('   Type:', typeof window.WheelFitData.autoSearch);
    console.log('   Boolean conversion:', !!window.WheelFitData.autoSearch);
} else {
    console.log('   ❌ WheelFitData not available');
}

// Test 3: Check submit button visibility
console.log('3. Submit Button Visibility Check:');
const submitButtons = document.querySelectorAll('.wheel-fit-widget button[type="submit"]');
console.log('   Submit buttons found:', submitButtons.length);
submitButtons.forEach((btn, index) => {
    const isHidden = btn.classList.contains('hidden') || btn.style.display === 'none';
    console.log(`   Button ${index + 1}: ${isHidden ? 'Hidden ✅' : 'Visible ❌'} (should be hidden if auto-search enabled)`);
});

// Test 4: Check widget initialization and event binding
console.log('4. Widget and Event Binding Check:');
if (window.wheelFitWidget) {
    console.log('   ✅ Widget found');
    console.log('   Widget mode:', window.wheelFitWidget.mode);
    
    // Check if onModificationSelect method exists
    if (typeof window.wheelFitWidget.onModificationSelect === 'function') {
        console.log('   ✅ onModificationSelect method exists');
    } else {
        console.log('   ❌ onModificationSelect method missing');
    }
    
    // Check if searchSizes method exists
    if (typeof window.wheelFitWidget.searchSizes === 'function') {
        console.log('   ✅ searchSizes method exists');
    } else {
        console.log('   ❌ searchSizes method missing');
    }
} else {
    console.log('   ❌ Widget not found');
}

// Test 5: Check modification select element and its event listeners
console.log('5. Modification Select Element Check:');
const modificationSelects = [
    document.getElementById('wf-modification'),
    document.getElementById('wf-mod'),
    document.querySelector('select[name="modification"]'),
    document.querySelector('#modification')
];

let modSelect = null;
modificationSelects.forEach((select, index) => {
    if (select) {
        console.log(`   Modification select ${index + 1} found:`, select.id || select.name || 'unnamed');
        if (!modSelect) modSelect = select;
    }
});

if (modSelect) {
    console.log('   ✅ Modification select found:', modSelect.id);
    
    // Check if it has event listeners (we can't directly check, but we can test)
    console.log('   Testing change event...');
    
    // Create a test change event
    const testEvent = new Event('change', { bubbles: true });
    
    // Store original value
    const originalValue = modSelect.value;
    
    // Set a test value and trigger change
    if (modSelect.options.length > 1) {
        modSelect.value = modSelect.options[1].value;
        modSelect.dispatchEvent(testEvent);
        console.log('   Change event dispatched with value:', modSelect.value);
        
        // Restore original value
        modSelect.value = originalValue;
    } else {
        console.log('   ⚠️ No options available for testing');
    }
} else {
    console.log('   ❌ No modification select found');
}

// Test 6: Check tire search auto-search functionality
console.log('6. Tire Search Auto-Search Check:');
const tireForm = document.getElementById('tab-by-size') || document.querySelector('form[data-tab="by-size"]');
const diameterSelect = document.getElementById('tire-diameter');

if (tireForm && diameterSelect) {
    console.log('   ✅ Tire form and diameter select found');
    
    // Check if diameter select has the auto-search event listener
    console.log('   Testing tire diameter auto-search...');
    
    const widthSelect = document.getElementById('tire-width');
    const profileSelect = document.getElementById('tire-profile');
    
    if (widthSelect && profileSelect && diameterSelect) {
        // Set test values
        if (widthSelect.options.length > 1) widthSelect.value = widthSelect.options[1].value;
        if (profileSelect.options.length > 1) profileSelect.value = profileSelect.options[1].value;
        if (diameterSelect.options.length > 1) {
            diameterSelect.value = diameterSelect.options[1].value;
            
            // Trigger change event
            const changeEvent = new Event('change', { bubbles: true });
            diameterSelect.dispatchEvent(changeEvent);
            console.log('   Tire diameter change event dispatched');
        }
    }
} else {
    console.log('   ⚠️ Tire form or diameter select not found (normal if not on tire search tab)');
}

// Test 7: Manual auto-search simulation
console.log('7. Manual Auto-Search Simulation:');

function simulateAutoSearch() {
    if (!window.wheelFitWidget) {
        console.log('   ❌ Cannot simulate - widget not found');
        return;
    }
    
    console.log('   Simulating modification selection with auto-search...');
    
    // Check current selected data
    const selectedData = window.wheelFitWidget.selectedData;
    console.log('   Current selected data:', selectedData);
    
    // Check if we have enough data for a search
    const hasRequiredData = selectedData.make && selectedData.model && 
                           (selectedData.year || selectedData.generation) && 
                           selectedData.modification;
    
    console.log('   Has required data for search:', hasRequiredData);
    
    if (hasRequiredData) {
        console.log('   Calling onModificationSelect with current modification...');
        try {
            window.wheelFitWidget.onModificationSelect(selectedData.modification);
            console.log('   ✅ onModificationSelect called successfully');
        } catch (error) {
            console.log('   ❌ Error calling onModificationSelect:', error);
        }
    } else {
        console.log('   ⚠️ Insufficient data for auto-search test');
    }
}

// Test 8: Check for JavaScript errors that might prevent auto-search
console.log('8. JavaScript Error Detection:');
let errorCount = 0;
const originalError = console.error;
console.error = function(...args) {
    errorCount++;
    console.log(`   ❌ JS Error ${errorCount}:`, ...args);
    originalError.apply(console, args);
};

// Run simulation after a short delay
setTimeout(() => {
    simulateAutoSearch();
    
    // Restore error handler and report
    setTimeout(() => {
        console.error = originalError;
        console.log(`   Total JS errors during test: ${errorCount}`);
        
        // Final summary
        console.log('\n=== Diagnosis Summary ===');
        console.log('Key findings:');
        console.log(`1. AUTO_SEARCH enabled: ${AUTO_SEARCH_VALUE}`);
        console.log(`2. Submit buttons hidden: ${submitButtons.length > 0 && Array.from(submitButtons).some(btn => btn.classList.contains('hidden'))}`);
        console.log(`3. Widget available: ${!!window.wheelFitWidget}`);
        console.log(`4. Modification select found: ${!!modSelect}`);
        console.log(`5. JavaScript errors: ${errorCount}`);
        
        console.log('\nPossible issues:');
        if (!AUTO_SEARCH_VALUE) {
            console.log('- AUTO_SEARCH is disabled - check WordPress admin settings');
        }
        if (!window.wheelFitWidget) {
            console.log('- Widget not initialized properly');
        }
        if (!modSelect) {
            console.log('- Modification select element not found');
        }
        if (errorCount > 0) {
            console.log('- JavaScript errors preventing proper execution');
        }
        
        console.log('\nNext steps:');
        console.log('1. Enable auto-search in WordPress admin if disabled');
        console.log('2. Check widget initialization if widget missing');
        console.log('3. Verify DOM structure if elements missing');
        console.log('4. Fix JavaScript errors if any found');
    }, 1000);
}, 500);

console.log('\n=== Diagnosis test initiated ===');
