/**
 * Safe Step Indicators Uniformity Check
 * 
 * Проверяет ТОЛЬКО индикаторы шагов, не влияя на другие элементы
 * 
 * Copy and paste this entire script into browser console on:
 * /wp-admin/admin.php?page=wheel-size-appearance
 */

console.log('🛡️ Safe Step Indicators Uniformity Check...');

// Безопасный поиск ТОЛЬКО индикаторов шагов в Live Preview
const previewContainer = document.getElementById('widget-preview');

if (!previewContainer) {
    console.error('❌ Live Preview container not found');
} else {
    console.log('✅ Live Preview container found');
    
    // Ищем ТОЛЬКО индикаторы шагов, избегая других элементов
    const stepIndicators = previewContainer.querySelectorAll('.wsf-step-index, [id^="step-indicator-"]');
    
    if (stepIndicators.length === 0) {
        console.warn('⚠️ No step indicators found in Live Preview');
        console.log('💡 This might be normal if the wizard is not currently visible');
    } else {
        console.log(`✅ Found ${stepIndicators.length} step indicators in Live Preview`);
        
        console.log('\n📊 STEP INDICATORS ANALYSIS:');
        
        let allUniform = true;
        const expectedSize = 24;
        const tolerance = 2; // 2px tolerance for browser rendering
        
        stepIndicators.forEach((indicator, index) => {
            const computedStyle = window.getComputedStyle(indicator);
            const rect = indicator.getBoundingClientRect();
            
            // Получаем основные свойства
            const cssWidth = parseFloat(computedStyle.width);
            const cssHeight = parseFloat(computedStyle.height);
            const actualWidth = rect.width;
            const actualHeight = rect.height;
            const flexGrow = computedStyle.flexGrow;
            const borderRadius = computedStyle.borderRadius;
            
            // Проверяем критерии
            const correctCSSSize = Math.abs(cssWidth - expectedSize) <= tolerance && 
                                  Math.abs(cssHeight - expectedSize) <= tolerance;
            const correctActualSize = Math.abs(actualWidth - expectedSize) <= tolerance && 
                                     Math.abs(actualHeight - expectedSize) <= tolerance;
            const isSquare = Math.abs(actualWidth - actualHeight) <= tolerance;
            const noFlexGrow = flexGrow === '0';
            const isCircular = borderRadius === '50%' || borderRadius.includes('50%') || 
                              parseFloat(borderRadius) >= 12; // Half of 24px
            
            const isUniform = correctCSSSize && correctActualSize && isSquare && noFlexGrow && isCircular;
            
            console.log(`\n${index + 1}. Indicator ${isUniform ? '✅ UNIFORM' : '❌ ISSUES'}:`);
            console.log(`   Element: ${indicator.tagName}${indicator.id ? '#' + indicator.id : ''}${indicator.className ? '.' + Array.from(indicator.classList).slice(0,2).join('.') : ''}`);
            console.log(`   CSS Size: ${cssWidth}×${cssHeight}px ${correctCSSSize ? '✅' : '❌'}`);
            console.log(`   Actual Size: ${actualWidth.toFixed(1)}×${actualHeight.toFixed(1)}px ${correctActualSize ? '✅' : '❌'}`);
            console.log(`   Square: ${isSquare ? '✅' : '❌'}`);
            console.log(`   No Flex Grow: ${noFlexGrow ? '✅' : '❌'} (flex-grow: ${flexGrow})`);
            console.log(`   Circular: ${isCircular ? '✅' : '❌'} (border-radius: ${borderRadius})`);
            
            if (!isUniform) {
                allUniform = false;
            }
        });
        
        // Проверяем прогресс-бары (если есть)
        console.log('\n📊 PROGRESS BARS CHECK:');
        const progressBars = previewContainer.querySelectorAll('[id^="progress-"], .wizard-progress, .progress-bar');
        
        let progressOK = true;
        
        if (progressBars.length === 0) {
            console.log('ℹ️ No progress bars found (this is normal for some wizard layouts)');
        } else {
            progressBars.forEach((bar, index) => {
                const computedStyle = window.getComputedStyle(bar);
                const flexGrow = computedStyle.flexGrow;
                const height = parseFloat(computedStyle.height);
                
                const shouldStretch = flexGrow === '1';
                const reasonableHeight = height >= 1 && height <= 10; // 1-10px range
                
                const barOK = shouldStretch && reasonableHeight;
                
                console.log(`${index + 1}. Progress bar: ${barOK ? '✅' : '❌'}`);
                console.log(`   Should stretch: ${shouldStretch ? '✅' : '❌'} (flex-grow: ${flexGrow})`);
                console.log(`   Height: ${height}px ${reasonableHeight ? '✅' : '❌'}`);
                
                if (!barOK) {
                    progressOK = false;
                }
            });
        }
        
        // Финальный вердикт
        const overallSuccess = allUniform && progressOK;
        
        console.log(`\n🎯 FINAL RESULT: ${overallSuccess ? '✅ ALL STEP INDICATORS ARE UNIFORM' : '❌ ISSUES DETECTED'}`);
        
        if (overallSuccess) {
            console.log('🎉 SUCCESS! All step indicators are exactly 24×24px and uniform!');
            console.log('✅ Size consistency maintained across all states');
            console.log('✅ Progress bars properly configured to stretch');
        } else {
            console.log('\n💡 ISSUES TO ADDRESS:');
            if (!allUniform) {
                console.log('   - Some step indicators are not uniform (check CSS rules)');
            }
            if (!progressOK) {
                console.log('   - Progress bars need flex-grow: 1 and reasonable height');
            }
        }
        
        // Безопасная визуальная подсветка (только на 3 секунды)
        console.log('\n🎨 Safe visual highlight (3 seconds)...');
        
        stepIndicators.forEach((indicator, index) => {
            const originalOutline = indicator.style.outline;
            const originalOutlineOffset = indicator.style.outlineOffset;
            
            indicator.style.outline = '2px solid red';
            indicator.style.outlineOffset = '2px';
            
            // Добавляем размер как tooltip
            indicator.title = `${Math.round(indicator.getBoundingClientRect().width)}×${Math.round(indicator.getBoundingClientRect().height)}px`;
            
            // Восстанавливаем через 3 секунды
            setTimeout(() => {
                indicator.style.outline = originalOutline;
                indicator.style.outlineOffset = originalOutlineOffset;
                indicator.title = '';
            }, 3000);
        });
        
        // Сохраняем результаты для дальнейшего анализа
        window.safeIndicatorCheck = {
            totalIndicators: stepIndicators.length,
            allUniform: allUniform,
            progressOK: progressOK,
            overallSuccess: overallSuccess,
            indicators: stepIndicators
        };
        
        console.log('\n📋 Results saved to window.safeIndicatorCheck for further inspection');
    }
}

// Дополнительная проверка: убеждаемся, что мы не сломали селекторы
console.log('\n🔍 SAFETY CHECK - Verifying selectors are not broken...');

const allSelects = document.querySelectorAll('select');
const allInputs = document.querySelectorAll('input');

let selectorsOK = true;

// Проверяем, что селекторы выглядят нормально
allSelects.forEach((select, index) => {
    const style = window.getComputedStyle(select);
    const width = parseFloat(style.width);
    
    // Селекторы не должны быть 24px (это было бы признаком поломки)
    if (width === 24) {
        console.warn(`⚠️ Select ${index + 1} has suspicious width of 24px - might be affected by step indicator rules`);
        selectorsOK = false;
    }
});

// Проверяем инпуты
allInputs.forEach((input, index) => {
    const style = window.getComputedStyle(input);
    const width = parseFloat(style.width);
    
    // Инпуты не должны быть 24px
    if (width === 24) {
        console.warn(`⚠️ Input ${index + 1} has suspicious width of 24px - might be affected by step indicator rules`);
        selectorsOK = false;
    }
});

console.log(`🛡️ Selectors safety check: ${selectorsOK ? '✅ ALL SAFE' : '❌ POTENTIAL ISSUES'}`);

if (selectorsOK) {
    console.log('✅ Form elements (selects, inputs) are not affected by step indicator rules');
} else {
    console.log('⚠️ Some form elements might be affected - review CSS selectors for specificity');
}

console.log('\n📋 SUMMARY:');
console.log('- Step indicators should be 24×24px and circular');
console.log('- Form selectors should maintain their normal appearance');
console.log('- Progress bars should stretch between steps');
console.log('- No other UI elements should be affected');
