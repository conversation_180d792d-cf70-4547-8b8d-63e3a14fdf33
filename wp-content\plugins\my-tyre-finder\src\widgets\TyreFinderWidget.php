<?php

declare(strict_types=1);

namespace MyTyreFinder\Widgets;

use WP_Widget;

/**
 * Classic widget wrapper for the finder form.
 */
final class TyreFinderWidget extends WP_Widget
{
    public function __construct()
    {
        parent::__construct(
            'tyre_finder_widget',
            __('Tyre Finder', 'my-tyre-finder'),
            ['description' => __('Displays the tyre finder form', 'my-tyre-finder')]
        );
    }

    public function widget($args, $instance): void
    {
        // Output widget content.
        echo 'Tyre Finder Widget';
    }

    public function form($instance): void
    {
        // Widget admin form.
    }

    public function update($new_instance, $old_instance)
    {
        // Process widget options to be saved.
        return $new_instance;
    }
} 