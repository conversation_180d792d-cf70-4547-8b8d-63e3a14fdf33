/**
 * Тест отсутствия перезаписи селекторов
 * Проверяет, что селекторы не перезаписываются обратно на английский
 */

(function() {
    'use strict';

    console.log('[No Overwrite Test] Инициализация теста отсутствия перезаписи...');

    // Тестовые переводы
    const testTranslations = {
        ru: {
            'select_make_placeholder': 'Выберите бренд',
            'select_model_placeholder': 'Выберите модель',
            'select_year_placeholder': 'Выберите год',
            'select_mods_placeholder': 'Выберите модификацию',
            'select_gen_placeholder': 'Выберите поколение',
            'select_make_first_placeholder': 'Сначала выберите бренд',
            'select_model_first_placeholder': 'Сначала выберите модель',
            'select_year_first_placeholder': 'Сначала выберите год'
        }
    };

    // Функция мониторинга изменений селекторов
    function monitorSelectorChanges() {
        console.log('[No Overwrite Test] === Запуск мониторинга изменений ===');
        
        const selectors = ['wf-make', 'wf-model', 'wf-year', 'wf-modification', 'wf-generation'];
        const initialStates = {};
        
        // Устанавливаем русские переводы
        window.WheelFitI18n = testTranslations.ru;
        
        // Записываем начальное состояние
        selectors.forEach(id => {
            const select = document.getElementById(id);
            if (select) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    initialStates[id] = {
                        text: placeholderOption.textContent.trim(),
                        dataI18n: placeholderOption.getAttribute('data-i18n'),
                        timestamp: Date.now()
                    };
                    console.log(`[No Overwrite Test] Начальное состояние ${id}:`, initialStates[id]);
                }
            }
        });
        
        // Функция проверки изменений
        function checkForChanges() {
            let changesDetected = false;
            
            selectors.forEach(id => {
                const select = document.getElementById(id);
                if (select && initialStates[id]) {
                    const placeholderOption = select.querySelector('option[value=""]');
                    if (placeholderOption) {
                        const currentText = placeholderOption.textContent.trim();
                        const currentDataI18n = placeholderOption.getAttribute('data-i18n');
                        
                        const textChanged = currentText !== initialStates[id].text;
                        const dataI18nChanged = currentDataI18n !== initialStates[id].dataI18n;
                        
                        if (textChanged || dataI18nChanged) {
                            console.warn(`[No Overwrite Test] ⚠️ ИЗМЕНЕНИЕ ОБНАРУЖЕНО в ${id}:`);
                            console.warn(`  Текст: "${initialStates[id].text}" → "${currentText}"`);
                            console.warn(`  data-i18n: "${initialStates[id].dataI18n}" → "${currentDataI18n}"`);
                            
                            // Проверяем, не перезаписался ли на английский
                            const englishPhrases = ['Choose a make', 'Choose a model', 'Choose a year', 'Choose a modification', 'Choose a generation'];
                            if (englishPhrases.includes(currentText)) {
                                console.error(`[No Overwrite Test] ❌ КРИТИЧЕСКАЯ ОШИБКА: ${id} перезаписан на английский!`);
                            }
                            
                            changesDetected = true;
                            
                            // Обновляем состояние
                            initialStates[id] = {
                                text: currentText,
                                dataI18n: currentDataI18n,
                                timestamp: Date.now()
                            };
                        }
                    }
                }
            });
            
            if (!changesDetected) {
                console.log('[No Overwrite Test] ✅ Изменений не обнаружено - селекторы стабильны');
            }
        }
        
        // Запускаем мониторинг каждые 2 секунды
        const monitorInterval = setInterval(checkForChanges, 2000);
        
        // Останавливаем мониторинг через 30 секунд
        setTimeout(() => {
            clearInterval(monitorInterval);
            console.log('[No Overwrite Test] Мониторинг завершен');
        }, 30000);
        
        return { checkForChanges, stop: () => clearInterval(monitorInterval) };
    }

    // Функция симуляции действий пользователя
    function simulateUserActions() {
        console.log('[No Overwrite Test] === Симуляция действий пользователя ===');
        
        // Симулируем изменение Search Flow
        const searchFlowSelect = document.getElementById('search_flow');
        if (searchFlowSelect) {
            console.log('[No Overwrite Test] Симулируем изменение Search Flow...');
            const originalValue = searchFlowSelect.value;
            const options = Array.from(searchFlowSelect.options).map(opt => opt.value).filter(val => val !== originalValue);
            
            if (options.length > 0) {
                searchFlowSelect.value = options[0];
                searchFlowSelect.dispatchEvent(new Event('change', { bubbles: true }));
                
                setTimeout(() => {
                    searchFlowSelect.value = originalValue;
                    searchFlowSelect.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log('[No Overwrite Test] Search Flow возвращен к исходному значению');
                }, 3000);
            }
        }
        
        // Симулируем изменение Form Layout
        setTimeout(() => {
            const layoutSelect = document.getElementById('form_layout');
            if (layoutSelect) {
                console.log('[No Overwrite Test] Симулируем изменение Form Layout...');
                const originalValue = layoutSelect.value;
                const options = Array.from(layoutSelect.options).map(opt => opt.value).filter(val => val !== originalValue);
                
                if (options.length > 0) {
                    layoutSelect.value = options[0];
                    layoutSelect.dispatchEvent(new Event('change', { bubbles: true }));
                    
                    setTimeout(() => {
                        layoutSelect.value = originalValue;
                        layoutSelect.dispatchEvent(new Event('change', { bubbles: true }));
                        console.log('[No Overwrite Test] Form Layout возвращен к исходному значению');
                    }, 3000);
                }
            }
        }, 5000);
    }

    // Функция проверки data-i18n атрибутов
    function checkDataI18nAttributes() {
        console.log('[No Overwrite Test] === Проверка data-i18n атрибутов ===');
        
        const selectors = [
            { id: 'wf-make', expectedKeys: ['select_make_placeholder'] },
            { id: 'wf-model', expectedKeys: ['select_model_placeholder', 'select_make_first_placeholder'] },
            { id: 'wf-year', expectedKeys: ['select_year_placeholder', 'select_model_first_placeholder'] },
            { id: 'wf-modification', expectedKeys: ['select_mods_placeholder', 'select_year_first_placeholder'] },
            { id: 'wf-generation', expectedKeys: ['select_gen_placeholder', 'select_model_first_placeholder'] }
        ];
        
        let allCorrect = true;
        
        selectors.forEach(({ id, expectedKeys }) => {
            const select = document.getElementById(id);
            if (select) {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    const dataI18n = placeholderOption.getAttribute('data-i18n');
                    const hasCorrectAttribute = expectedKeys.includes(dataI18n);
                    
                    console.log(`[No Overwrite Test] ${id}:`);
                    console.log(`  data-i18n: "${dataI18n}"`);
                    console.log(`  ожидаемые: [${expectedKeys.join(', ')}]`);
                    console.log(`  корректно: ${hasCorrectAttribute ? '✅' : '❌'}`);
                    
                    if (!hasCorrectAttribute) {
                        allCorrect = false;
                    }
                } else {
                    console.warn(`[No Overwrite Test] ❌ ${id}: placeholder option не найден`);
                    allCorrect = false;
                }
            } else {
                console.warn(`[No Overwrite Test] ❌ ${id}: селектор не найден`);
                allCorrect = false;
            }
        });
        
        if (allCorrect) {
            console.log('[No Overwrite Test] ✅ Все data-i18n атрибуты корректны');
        } else {
            console.log('[No Overwrite Test] ❌ Некоторые data-i18n атрибуты требуют исправления');
        }
        
        return allCorrect;
    }

    // Основная функция тестирования
    function runOverwriteTest() {
        console.log('[No Overwrite Test] 🚀 ЗАПУСК ТЕСТА ОТСУТСТВИЯ ПЕРЕЗАПИСИ');
        
        // 1. Проверяем data-i18n атрибуты
        console.log('[No Overwrite Test] 1. Проверка data-i18n атрибутов...');
        checkDataI18nAttributes();
        
        // 2. Запускаем мониторинг
        console.log('[No Overwrite Test] 2. Запуск мониторинга изменений...');
        const monitor = monitorSelectorChanges();
        
        // 3. Симулируем действия пользователя
        setTimeout(() => {
            console.log('[No Overwrite Test] 3. Симуляция действий пользователя...');
            simulateUserActions();
        }, 2000);
        
        // 4. Финальная проверка
        setTimeout(() => {
            console.log('[No Overwrite Test] 4. Финальная проверка...');
            checkDataI18nAttributes();
            console.log('[No Overwrite Test] ✅ ТЕСТ ЗАВЕРШЕН');
        }, 25000);
        
        return monitor;
    }

    // Глобальные функции для ручного тестирования
    window.testNoOverwrite = {
        runTest: runOverwriteTest,
        monitor: monitorSelectorChanges,
        checkAttributes: checkDataI18nAttributes,
        simulate: simulateUserActions
    };

    // Автоматический запуск через 3 секунды
    setTimeout(() => {
        console.log('[No Overwrite Test] Автоматический запуск теста...');
        runOverwriteTest();
    }, 3000);

    console.log('[No Overwrite Test] Тест загружен. Доступные функции:');
    console.log('- testNoOverwrite.runTest() - полный тест');
    console.log('- testNoOverwrite.monitor() - мониторинг изменений');
    console.log('- testNoOverwrite.checkAttributes() - проверка атрибутов');

})();
