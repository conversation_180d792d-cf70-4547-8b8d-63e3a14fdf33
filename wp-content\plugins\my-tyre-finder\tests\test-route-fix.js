/**
 * Test script to verify the REST API route fix
 * Run this in browser console on the WordPress admin Appearance page
 */

(function() {
    'use strict';

    console.log('🔧 === TESTING ROUTE FIX ===');

    if (typeof wpApiSettings === 'undefined') {
        console.error('❌ wpApiSettings not found - run this on the admin Appearance page');
        return;
    }

    const apiBase = wpApiSettings.root + 'wheel-size/v1/themes';

    // Test the route fix by making requests to both endpoints
    async function testRoutes() {
        console.log('\n1️⃣ Testing route resolution...');

        try {
            // Test 1: GET /themes/active (should work)
            console.log('Testing GET /themes/active...');
            const getActiveResponse = await fetch(apiBase + '/active', {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (getActiveResponse.ok) {
                const activeData = await getActiveResponse.json();
                console.log('✅ GET /themes/active successful:', activeData);
            } else {
                console.error('❌ GET /themes/active failed:', getActiveResponse.status);
            }

            // Test 2: PUT /themes/active with correct parameters (should work now)
            console.log('\nTesting PUT /themes/active with correct parameters...');
            const putActiveResponse = await fetch(apiBase + '/active', {
                method: 'PUT',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ slug: 'light' })
            });

            console.log('PUT /themes/active response status:', putActiveResponse.status);

            if (putActiveResponse.ok) {
                const putData = await putActiveResponse.json();
                console.log('✅ PUT /themes/active successful:', putData);
            } else {
                const errorText = await putActiveResponse.text();
                console.error('❌ PUT /themes/active failed:', putActiveResponse.status);
                console.error('Error response:', errorText);
                
                // Check if it's still the wrong route
                if (errorText.includes('name') && errorText.includes('properties')) {
                    console.error('🚨 ROUTE CONFLICT STILL EXISTS - /active is being matched by /{slug} route');
                } else {
                    console.log('✅ Route conflict fixed - different error type');
                }
            }

            // Test 3: Now test dark theme activation
            console.log('\nTesting dark theme activation...');
            const darkResponse = await fetch(apiBase + '/active', {
                method: 'PUT',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ slug: 'dark' })
            });

            if (darkResponse.ok) {
                const darkData = await darkResponse.json();
                console.log('✅ Dark theme activation successful:', darkData);
                return true;
            } else {
                const darkError = await darkResponse.text();
                console.error('❌ Dark theme activation failed:', darkResponse.status);
                console.error('Dark theme error:', darkError);
                return false;
            }

        } catch (error) {
            console.error('❌ Test error:', error);
            return false;
        }
    }

    // Test route specificity
    async function testRouteSpecificity() {
        console.log('\n2️⃣ Testing route specificity...');

        try {
            // This should hit the /{slug} route, not /active route
            console.log('Testing GET /themes/light (should hit /{slug} route)...');
            const getLightResponse = await fetch(apiBase + '/light', {
                method: 'GET',
                headers: {
                    'X-WP-Nonce': wpApiSettings.nonce,
                    'Content-Type': 'application/json'
                }
            });

            if (getLightResponse.ok) {
                const lightData = await getLightResponse.json();
                console.log('✅ GET /themes/light successful (/{slug} route working):', lightData);
            } else {
                console.error('❌ GET /themes/light failed:', getLightResponse.status);
            }

        } catch (error) {
            console.error('❌ Route specificity test error:', error);
        }
    }

    // Run all tests
    async function runTests() {
        console.log('🚀 Starting route fix tests...\n');
        
        const routeTest = await testRoutes();
        await testRouteSpecificity();
        
        console.log('\n📊 === TEST RESULTS ===');
        console.log(`Route Fix: ${routeTest ? '✅ SUCCESS' : '❌ FAILED'}`);
        
        if (routeTest) {
            console.log('\n🎉 Route conflict fixed! Dark theme should now work.');
            console.log('💡 Try clicking the dark theme card in the admin panel.');
        } else {
            console.log('\n⚠️ Route conflict may still exist. Check the error messages above.');
        }
    }

    // Start the tests
    runTests();

})();
