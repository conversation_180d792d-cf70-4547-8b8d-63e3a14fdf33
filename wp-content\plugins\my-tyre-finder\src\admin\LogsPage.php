<?php
declare(strict_types=1);

namespace MyTyreFinder\Admin;

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> «Wheel‑Size → Logs»
 */
final class LogsPage
{
    /** slug в меню WP‑админки */
    public const SLUG = 'wheel-size-logs';

    /** Каталог, куда WheelSizeApi пишет файлы (chmod 775, владелец www-data) */
    public const LOG_DIR = WP_PLUGIN_DIR . '/my-tyre-finder/logs';

    /* --------------------------------------------------
       Public API
    -------------------------------------------------- */
    /** Подвязываем экраны, скрипты, AJAX */
    public function register(): void
    {
        add_action('admin_menu',            [$this, 'add_menu']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_assets']);

        // AJAX‑ендпоинты
        add_action('wp_ajax_wheel_size_clear_logs', [$this, 'ajax_clear_logs']);
        add_action('wp_ajax_wheel_size_get_logs',   [$this, 'ajax_get_logs']);

        // при активации плагина гарантируем наличие каталога
        if ( ! is_dir(self::LOG_DIR)) {
            wp_mkdir_p(self::LOG_DIR);
            @chmod(self::LOG_DIR, 0775);
        }
    }

    /* --------------------------------------------------
       Меню
    -------------------------------------------------- */
    public function add_menu(): void
    {
        add_submenu_page(
            'wheel-size',                      // parent‑slug (основное меню плагина)
            'Wheel‑Size — Logs',               // page‑title
            'Logs',                            // menu‑title
            'manage_options',                 // capability
            self::SLUG,                        // slug
            [$this, 'render_page']             // callback
        );
    }

    /* --------------------------------------------------
       Assets (CSS/JS) — пока пусто, но хук оставим
    -------------------------------------------------- */
    public function enqueue_assets(string $hook): void
    {
        if ($hook !== 'wheel-size_page_' . self::SLUG) {
            return;
        }
        // wp_enqueue_style()/script() по необходимости
    }

    /* --------------------------------------------------
       AJAX‑ендпоинты
    -------------------------------------------------- */
    public function ajax_clear_logs(): void
    {
        if ( ! current_user_can('manage_options')) {
            wp_send_json_error('no-permission', 403);
        }

        foreach (glob(self::LOG_DIR . '/wheel-size-*.log') ?: [] as $f) {
            @unlink($f);
        }
        wp_send_json_success();
    }

    public function ajax_get_logs(): void
    {
        if ( ! current_user_can('manage_options')) {
            wp_send_json_error('no-permission', 403);
        }

        ob_start();
        $this->display_logs();
        wp_send_json_success(['html' => ob_get_clean()]);
    }

    /* --------------------------------------------------
       Рендер страницы
    -------------------------------------------------- */
    public function render_page(): void
    {
        ?>
        <div class="wrap">
            <h1>🧾 <?php esc_html_e('Logs', 'wheel-size'); ?></h1>
            <p class="description" style="margin-bottom:15px;">
                <?php esc_html_e('Here you can review all API requests, responses and cache activity relevant to vehicle data.', 'wheel-size'); ?>
            </p>

            <div class="tablenav top">
                <div class="alignleft actions" style="display:flex;gap:10px;flex-wrap:wrap;align-items:center;">
                    <button type="button" id="clear-logs"   class="button">🗑️ <?php esc_html_e('Clear Logs', 'wheel-size'); ?></button>
                    <button type="button" id="refresh-logs" class="button">🔄 <?php esc_html_e('Refresh',    'wheel-size'); ?></button>

                    <!-- Filters -->
                    <select id="log-type-filter">
                        <option value="all"><?php esc_html_e('All types', 'wheel-size'); ?></option>
                        <option value="cached">Cached</option>
                        <option value="request">Request</option>
                        <option value="response">Response</option>
                        <option value="error">Error</option>
                    </select>

                    <input type="search" id="log-search" placeholder="<?php esc_attr_e('Search key...', 'wheel-size'); ?>" style="min-width:180px;">

                    <select id="log-limit">
                        <option value="10">Last 10</option>
                        <option value="25" selected>Last 25</option>
                        <option value="50">Last 50</option>
                        <option value="100">Last 100</option>
                    </select>
                </div>
            </div>

            <div id="logs-container">
                <?php $this->display_logs(); ?>
            </div>
            <p id="logs-summary" style="margin-top:10px; color:#646970;"></p>

            <script>
            (function($){
                const refresh = () =>
                    $.post(ajaxurl,{action:'wheel_size_get_logs'},res=>{
                        if(res.success){
                            $('#logs-container').html(res.data.html);
                            initLogsUI();
                        }
                    });

                $('#refresh-logs').on('click', refresh);
                $('#clear-logs').on('click',()=>{
                    if(confirm('Clear all Wheel-Size logs? This action is irreversible.'))
                        $.post(ajaxurl,{action:'wheel_size_clear_logs'},refresh);
                });

                function initLogsUI(){
                    const typeFilter = $('#log-type-filter');
                    const searchInput= $('#log-search');
                    const limitSel   = $('#log-limit');

                    function apply(){
                        const type  = typeFilter.val();
                        const term  = searchInput.val().toLowerCase();
                        const limit = parseInt(limitSel.val(),10);
                        let shown=0;
                        const rows = $('#logs-table tbody tr');
                        rows.each(function(){
                            const r=$(this);
                            let vis=true;
                            if(type!=='all' && r.data('type')!==type) vis=false;
                            if(term && !r.data('key').includes(term)) vis=false;
                            if(vis && shown<limit){ r.show(); shown++; }
                            else r.hide();
                        });
                        $('#logs-summary').text('Total records: '+rows.length+' • Showing '+shown);
                    }

                    typeFilter.off('change').on('change',apply);
                    searchInput.off('keyup').on('keyup',apply);
                    limitSel.off('change').on('change',apply);
                    apply();
                }

                initLogsUI();
            })(jQuery);
            </script>

            <style>
                /* Table / row styling */
                #logs-table{
                    width:100%; border-collapse:collapse;
                }
                #logs-table th,#logs-table td{
                    padding:6px 8px; border:1px solid #ccd0d4; font-size:13px;
                }
                #logs-table th{background:#f6f7f7; text-align:left;}
                .log-cached{background:#f6f6f6;}
                .log-cached-aux{background:#fafafa;opacity:0.8;}
                .log-request{background:#e7f3ff;}
                .log-response{background:#e8f5e9;}
                .log-error{background:#fdecea;}
            </style>
        </div>
        <?php
    }

    /* --------------------------------------------------
       Helpers
    -------------------------------------------------- */
    /** Путь к лог‑файлу за сегодня */
    public static function todayFile(): string
    {
        return self::LOG_DIR . '/wheel-size-' . date('Y-m-d') . '.log';
    }

    /** Собираем и выводим последние 100 строк из всех wheel-size‑логов */
    private function display_logs(): void
    {
        $files = glob(self::LOG_DIR . '/wheel-size-*.log');
        if (empty($files)) {
            echo '<p>' . esc_html__('Log directory is empty.', 'wheel-size') . '</p>';
            return;
        }

        natsort($files);            // старые → новые
        $lines = [];
        foreach ($files as $f) {
            $fileLines = file($f, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($fileLines as $l) {
                if (str_contains($l,'WheelSize') || str_contains($l,'Wheel-Size') || str_contains($l,'WheelFit')) {
                    $lines[] = $l;
                }
            }
        }

        if (empty($lines)) {
            echo '<p>' . esc_html__('No Wheel-Size logs found.', 'wheel-size') . '</p>';
            return;
        }

        $output = array_slice($lines, -100); // cap

        echo '<table id="logs-table"><thead><tr>'.
             '<th>🕒 '. esc_html__('Time','wheel-size') .'</th>'.
             '<th>'. esc_html__('Message','wheel-size') .'</th>'.
             '<th>🗝️ '. esc_html__('Key','wheel-size') .'</th>'.
             '<th>📡 '. esc_html__('Source','wheel-size') .'</th>'.
             '</tr></thead><tbody>';

        foreach ($output as $line) {
            // Parse time and message
            [$time,$rest] = array_pad(explode(' — ', $line, 2),2,'');
            $type='other';
            $source='';

            if (str_contains($rest,'Returning cached')) {
                if (str_contains($rest,'search')) {
                    $type='cached';
                    $source='Cache';
                } else {
                    $type='cached-aux';
                    $source='Cache (aux)';
                }
            }
            elseif (str_contains($rest,'Request:')) { $type='request'; $source='API'; }
            elseif (str_contains($rest,'Response:')) { $type='response'; $source='API'; }
            elseif (str_contains($rest,'error') || str_contains($rest,'Error') || str_contains($rest,'404')) { $type='error'; $source='API'; }

            // key
            $key='';
            if (preg_match('/key:\s*([a-zA-Z0-9_]+)/', $rest, $m)) $key=$m[1];

            $classes = 'log-'.$type;

            echo '<tr class="'.esc_attr($classes).'" data-type="'.esc_attr($type).'" data-key="'.esc_attr(strtolower($key)).'">'.
                 '<td style="white-space:nowrap;font-family:monospace;">'.esc_html($time).'</td>'.
                 '<td style="font-family:monospace;">'.esc_html($rest).'</td>'.
                 '<td>'.esc_html($key).'</td>'.
                 '<td>'.esc_html($source).'</td>'.
                 '</tr>';
        }

        echo '</tbody></table>';
    }
}
