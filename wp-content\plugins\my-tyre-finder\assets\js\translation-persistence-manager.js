/**
 * Translation Persistence Manager
 * Modeled after the theme system to ensure translations persist during form re-rendering
 */

(function() {
    'use strict';

    class TranslationPersistenceManager {
        constructor() {
            this.currentLocale = 'en';
            this.currentTranslations = {};
            this.isInitialized = false;
            this.mutationObserver = null;
            this.retryAttempts = 0;
            this.maxRetryAttempts = 5;
            this.retryDelay = 100;

            console.log('[Translation Manager] Initializing...');
            this.init();
        }

        init() {
            // Store initial translations and locale
            this.storeCurrentTranslations();

            // Set up DOM mutation observer (like theme system)
            this.setupMutationObserver();

            // Listen for preview updates
            this.setupPreviewUpdateListener();

            // Only apply initial translations if we have them available
            if (this.hasTranslationsAvailable()) {
                console.log('[Translation Manager] Initial translations available, applying...');
                this.applyTranslationsWithRetry();
            } else {
                console.log('[Translation Manager] No initial translations available, will wait for them to be loaded');
                // Set up a listener for when translations become available
                this.waitForTranslations();
            }

            this.isInitialized = true;
            console.log('[Translation Manager] Initialized successfully');
        }

        waitForTranslations() {
            // Check periodically for translations to become available
            const checkInterval = setInterval(() => {
                if (this.hasTranslationsAvailable()) {
                    console.log('[Translation Manager] Translations now available, applying...');
                    this.applyTranslationsWithRetry();
                    clearInterval(checkInterval);
                }
            }, 500);

            // Stop checking after 10 seconds to prevent infinite polling
            setTimeout(() => {
                clearInterval(checkInterval);
                console.log('[Translation Manager] Stopped waiting for translations');
            }, 10000);
        }

        storeCurrentTranslations() {
            // Store current translations from global object
            if (window.WheelFitI18n && typeof window.WheelFitI18n === 'object') {
                this.currentTranslations = { ...window.WheelFitI18n };
                console.log('[Translation Manager] Stored current translations:', this.currentTranslations);
            }

            // Detect current locale from various sources
            this.currentLocale = this.detectCurrentLocale();
            console.log('[Translation Manager] Current locale:', this.currentLocale);
        }

        detectCurrentLocale() {
            // Try multiple sources to detect current locale
            if (window.WheelFitData && window.WheelFitData.locale) {
                return window.WheelFitData.locale;
            }
            
            const localeSelector = document.getElementById('locale-selector');
            if (localeSelector && localeSelector.value) {
                return localeSelector.value;
            }
            
            // Check HTML lang attribute
            const htmlLang = document.documentElement.lang;
            if (htmlLang) {
                return htmlLang.split('-')[0]; // Convert 'en-US' to 'en'
            }
            
            return 'en'; // Default fallback
        }

        setupMutationObserver() {
            // Create mutation observer to watch for DOM changes (like theme system)
            this.mutationObserver = new MutationObserver((mutations) => {
                let shouldReapplyTranslations = false;
                let significantChange = false;

                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // Check if preview container content changed significantly
                        const previewContainer = document.getElementById('widget-preview');
                        if (previewContainer) {
                            // Only trigger if the change is in the preview container
                            if (mutation.target === previewContainer) {
                                significantChange = true;
                                shouldReapplyTranslations = true;
                            }
                            // Or if elements with translation attributes were added
                            else if (previewContainer.contains(mutation.target)) {
                                for (let node of mutation.addedNodes) {
                                    if (node.nodeType === Node.ELEMENT_NODE) {
                                        const hasTranslationAttrs = node.querySelector && (
                                            node.querySelector('[data-i18n]') ||
                                            node.querySelector('[data-i18n-placeholder]') ||
                                            node.hasAttribute('data-i18n') ||
                                            node.hasAttribute('data-i18n-placeholder')
                                        );
                                        if (hasTranslationAttrs) {
                                            shouldReapplyTranslations = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                });

                if (shouldReapplyTranslations && this.hasTranslationsAvailable()) {
                    console.log('[Translation Manager] Significant DOM change detected, reapplying translations');
                    // Use setTimeout to ensure DOM is fully updated
                    setTimeout(() => this.applyTranslationsWithRetry(), 100);
                } else if (shouldReapplyTranslations && !this.hasTranslationsAvailable()) {
                    console.log('[Translation Manager] DOM change detected but no translations available yet');
                }
            });

            // Start observing with more specific configuration
            this.mutationObserver.observe(document.body, {
                childList: true,
                subtree: true,
                // Only observe child list changes, not attributes or character data
                attributes: false,
                characterData: false
            });

            console.log('[Translation Manager] Mutation observer set up');
        }

        setupPreviewUpdateListener() {
            // Listen for custom events that indicate preview updates
            document.addEventListener('previewUpdated', () => {
                console.log('[Translation Manager] Preview update event received');
                this.applyTranslationsWithRetry();
            });

            // Also listen for widget ready events
            document.addEventListener('wheelFitWidgetReady', () => {
                console.log('[Translation Manager] Widget ready event received');
                this.applyTranslationsWithRetry();
            });
        }

        applyTranslationsWithRetry(container = null, attempt = 0) {
            if (attempt >= this.maxRetryAttempts) {
                console.warn('[Translation Manager] Max retry attempts reached, stopping retries');
                return;
            }

            // Check if we have translations available before attempting
            if (!this.hasTranslationsAvailable()) {
                if (attempt === 0) {
                    console.log('[Translation Manager] No translations available yet, waiting for translations to load...');
                }

                // Only retry if we're in the first few attempts and might be waiting for initial load
                if (attempt < 3) {
                    const delay = this.retryDelay * Math.pow(2, attempt);
                    setTimeout(() => this.applyTranslationsWithRetry(container, attempt + 1), delay);
                }
                return;
            }

            try {
                const success = this.applyTranslations(container);
                if (!success && attempt < this.maxRetryAttempts - 1) {
                    // Retry with exponential backoff only if we have translations but application failed
                    const delay = this.retryDelay * Math.pow(2, attempt);
                    console.log(`[Translation Manager] Translation application failed, retrying in ${delay}ms (attempt ${attempt + 1})`);
                    setTimeout(() => this.applyTranslationsWithRetry(container, attempt + 1), delay);
                } else if (success) {
                    console.log('[Translation Manager] Translations applied successfully');
                }
            } catch (error) {
                console.error('[Translation Manager] Error applying translations:', error);
                if (attempt < this.maxRetryAttempts - 1) {
                    setTimeout(() => this.applyTranslationsWithRetry(container, attempt + 1), this.retryDelay);
                }
            }
        }

        hasTranslationsAvailable() {
            // Check multiple sources for translation availability
            const hasGlobalTranslations = window.WheelFitI18n &&
                                         typeof window.WheelFitI18n === 'object' &&
                                         Object.keys(window.WheelFitI18n).length > 0;

            const hasStoredTranslations = this.currentTranslations &&
                                        Object.keys(this.currentTranslations).length > 0;

            return hasGlobalTranslations || hasStoredTranslations;
        }

        applyTranslations(container = null) {
            // Update current translations from global object if available
            if (window.WheelFitI18n && typeof window.WheelFitI18n === 'object' && Object.keys(window.WheelFitI18n).length > 0) {
                this.currentTranslations = { ...window.WheelFitI18n };
                console.log('[Translation Manager] Updated translations from global object:', Object.keys(this.currentTranslations).length, 'keys');
            }

            // Check if we have any translations to work with
            if (!this.hasTranslationsAvailable()) {
                // Don't log warning on every attempt - only log once per session
                if (!this._noTranslationsWarningLogged) {
                    console.warn('[Translation Manager] No translations available for application');
                    this._noTranslationsWarningLogged = true;
                }
                return false;
            }

            // Reset warning flag when we have translations
            this._noTranslationsWarningLogged = false;

            // Determine target container
            const targetContainer = container || this.findTargetContainer();
            if (!targetContainer) {
                console.warn('[Translation Manager] No target container found');
                return false;
            }

            console.log('[Translation Manager] Applying translations to container:', targetContainer);

            // Быстрая проверка: если переводы уже применялись недавно, пропускаем
            const now = Date.now();
            if (this.lastAppliedTime && (now - this.lastAppliedTime) < 1000) {
                console.log('[Translation Manager] Skipping - translations applied recently');
                return true;
            }

            let translationsApplied = 0;

            // Apply text content translations (только если нужно!)
            const textElements = targetContainer.querySelectorAll('[data-i18n]');
            textElements.forEach(el => {
                const key = el.dataset.i18n;
                if (this.currentTranslations[key]) {
                    const currentText = el.textContent.trim();
                    const translatedText = this.currentTranslations[key];

                    // Применяем перевод только если текст отличается
                    if (currentText !== translatedText) {
                        console.log(`[Translation Manager] Updating "${key}": "${currentText}" → "${translatedText}"`);
                        el.textContent = translatedText;
                        translationsApplied++;
                    } else {
                        console.log(`[Translation Manager] Skipping "${key}": already translated correctly`);
                    }
                }
            });

            // Apply placeholder translations (только если нужно!)
            const placeholderElements = targetContainer.querySelectorAll('[data-i18n-placeholder]');
            placeholderElements.forEach(el => {
                const key = el.dataset.i18nPlaceholder;
                if (this.currentTranslations[key]) {
                    const currentPlaceholder = el.placeholder || '';
                    const translatedPlaceholder = this.currentTranslations[key];

                    // Применяем перевод только если placeholder отличается
                    if (currentPlaceholder !== translatedPlaceholder) {
                        console.log(`[Translation Manager] Updating placeholder "${key}": "${currentPlaceholder}" → "${translatedPlaceholder}"`);
                        el.placeholder = translatedPlaceholder;
                        translationsApplied++;
                    }
                }
            });

            // Apply value translations (for buttons)
            const valueElements = targetContainer.querySelectorAll('[data-i18n-value]');
            valueElements.forEach(el => {
                const key = el.dataset.i18nValue;
                if (this.currentTranslations[key]) {
                    el.value = this.currentTranslations[key];
                    translationsApplied++;
                }
            });

            // Apply title translations (for tooltips)
            const titleElements = targetContainer.querySelectorAll('[data-i18n-title]');
            titleElements.forEach(el => {
                const key = el.dataset.i18nTitle;
                if (this.currentTranslations[key]) {
                    el.title = this.currentTranslations[key];
                    translationsApplied++;
                }
            });

            // Apply aria-label translations
            const ariaLabelElements = targetContainer.querySelectorAll('[data-i18n-aria-label]');
            ariaLabelElements.forEach(el => {
                const key = el.dataset.i18nAriaLabel;
                if (this.currentTranslations[key]) {
                    el.setAttribute('aria-label', this.currentTranslations[key]);
                    translationsApplied++;
                }
            });

            // Handle select option placeholders (только если нужно!)
            const selectElements = targetContainer.querySelectorAll('select');
            selectElements.forEach(select => {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    // Сначала проверяем, есть ли у option собственный атрибут перевода
                    if (placeholderOption.dataset.i18n && this.currentTranslations[placeholderOption.dataset.i18n]) {
                        const currentText = placeholderOption.textContent.trim();
                        const translatedText = this.currentTranslations[placeholderOption.dataset.i18n];

                        // Применяем перевод только если текст отличается
                        if (currentText !== translatedText) {
                            console.log(`[Translation Manager] Updating select option "${placeholderOption.dataset.i18n}": "${currentText}" → "${translatedText}"`);
                            placeholderOption.textContent = translatedText;
                            translationsApplied++;
                        } else {
                            console.log(`[Translation Manager] Skipping select option "${placeholderOption.dataset.i18n}": already translated correctly`);
                        }
                    } else {
                        // Fallback: пытаемся определить ключ перевода по ID селекта
                        const selectIdToKeyMap = {
                            'wf-make': 'select_make_placeholder',
                            'wf-model': 'select_model_placeholder',
                            'wf-year': 'select_year_placeholder',
                            'wf-modification': 'select_mods_placeholder',
                            'wf-generation': 'select_gen_placeholder'
                        };

                        const translationKey = selectIdToKeyMap[select.id];
                        if (translationKey && this.currentTranslations[translationKey]) {
                            const currentText = placeholderOption.textContent.trim();
                            const translatedText = this.currentTranslations[translationKey];

                            // Применяем перевод только если текст отличается
                            if (currentText !== translatedText) {
                                console.log(`[Translation Manager] Auto-updating select "${select.id}": "${currentText}" → "${translatedText}"`);
                                placeholderOption.textContent = translatedText;
                                placeholderOption.setAttribute('data-i18n', translationKey);
                                translationsApplied++;
                            }
                        }
                    }
                }
            });

            console.log(`[Translation Manager] Applied ${translationsApplied} translations`);
            this.lastAppliedTime = Date.now(); // Отмечаем время применения
            return translationsApplied > 0;
        }

        findTargetContainer() {
            // Priority order for finding target container
            const selectors = [
                '#widget-preview',
                '.wsf-finder-widget',
                '.wheel-fit-widget',
                '[data-wsf-theme]',
                '#wheel-fit-form',
                '#wheel-fit-wizard'
            ];

            for (const selector of selectors) {
                const container = document.querySelector(selector);
                if (container) {
                    return container;
                }
            }

            return document; // Fallback to entire document
        }

        updateTranslations(newTranslations, locale = null) {
            if (newTranslations && typeof newTranslations === 'object') {
                const translationCount = Object.keys(newTranslations).length;
                console.log(`[Translation Manager] Updating with ${translationCount} translations`);

                if (translationCount > 0) {
                    this.currentTranslations = { ...newTranslations };
                    window.WheelFitI18n = { ...newTranslations };

                    if (locale) {
                        this.currentLocale = locale;
                        console.log('[Translation Manager] Updated locale to:', locale);
                    }

                    console.log('[Translation Manager] Translations updated successfully');

                    // Reset the warning flag since we now have translations
                    this._noTranslationsWarningLogged = false;

                    // Immediately apply new translations
                    this.applyTranslationsWithRetry();
                } else {
                    console.warn('[Translation Manager] Received empty translations object');
                }
            } else {
                console.warn('[Translation Manager] Invalid translations object received:', newTranslations);
            }
        }

        // Fallback method for manual translation application
        applyTranslationsFallback(container = null) {
            console.log('[Translation Manager] Using fallback translation method');

            const targetContainer = container || this.findTargetContainer();
            if (!targetContainer) return false;

            // Basic translation application without advanced features
            try {
                const elements = targetContainer.querySelectorAll('[data-i18n]');
                elements.forEach(el => {
                    const key = el.dataset.i18n;
                    if (this.currentTranslations[key]) {
                        el.textContent = this.currentTranslations[key];
                    }
                });

                const placeholders = targetContainer.querySelectorAll('[data-i18n-placeholder]');
                placeholders.forEach(el => {
                    const key = el.dataset.i18nPlaceholder;
                    if (this.currentTranslations[key]) {
                        el.placeholder = this.currentTranslations[key];
                    }
                });

                return true;
            } catch (error) {
                console.error('[Translation Manager] Fallback method failed:', error);
                return false;
            }
        }

        // Health check method
        healthCheck() {
            const status = {
                initialized: this.isInitialized,
                hasTranslations: Object.keys(this.currentTranslations).length > 0,
                observerActive: this.mutationObserver !== null,
                targetContainerFound: !!this.findTargetContainer(),
                globalTranslationsAvailable: !!(window.WheelFitI18n && Object.keys(window.WheelFitI18n).length > 0),
                currentLocale: this.currentLocale,
                translationCount: Object.keys(this.currentTranslations).length,
                globalTranslationCount: window.WheelFitI18n ? Object.keys(window.WheelFitI18n).length : 0
            };

            console.log('[Translation Manager] Health check:', status);
            return status;
        }

        // Debug method to help troubleshoot issues
        debug() {
            console.group('[Translation Manager] Debug Information');
            console.log('Initialized:', this.isInitialized);
            console.log('Current Locale:', this.currentLocale);
            console.log('Stored Translations:', this.currentTranslations);
            console.log('Global Translations (window.WheelFitI18n):', window.WheelFitI18n);
            console.log('Mutation Observer Active:', !!this.mutationObserver);
            console.log('Target Container:', this.findTargetContainer());
            console.log('Has Translations Available:', this.hasTranslationsAvailable());

            // Check for translation elements in DOM
            const targetContainer = this.findTargetContainer();
            if (targetContainer) {
                const i18nElements = targetContainer.querySelectorAll('[data-i18n]');
                const placeholderElements = targetContainer.querySelectorAll('[data-i18n-placeholder]');
                console.log('Elements with data-i18n:', i18nElements.length);
                console.log('Elements with data-i18n-placeholder:', placeholderElements.length);

                if (i18nElements.length > 0) {
                    console.log('Sample i18n elements:', Array.from(i18nElements).slice(0, 3).map(el => ({
                        tag: el.tagName,
                        key: el.dataset.i18n,
                        text: el.textContent.trim()
                    })));
                }
            }

            console.groupEnd();
        }

        destroy() {
            if (this.mutationObserver) {
                this.mutationObserver.disconnect();
                this.mutationObserver = null;
            }
            console.log('[Translation Manager] Destroyed');
        }
    }

    // Global fallback function (for compatibility with existing code)
    window.applyStaticTranslations = function(container = document) {
        console.log('[Translation Manager] Global fallback function called');

        if (window.translationManager && typeof window.translationManager.applyTranslations === 'function') {
            return window.translationManager.applyTranslations(container);
        }

        // Ultimate fallback
        if (!window.WheelFitI18n) {
            console.warn('[Translation Manager] No translations available for fallback');
            return false;
        }

        try {
            const elements = container.querySelectorAll('[data-i18n]');
            elements.forEach(el => {
                const key = el.dataset.i18n;
                if (window.WheelFitI18n[key]) {
                    el.textContent = window.WheelFitI18n[key];
                }
            });

            const placeholders = container.querySelectorAll('[data-i18n-placeholder]');
            placeholders.forEach(el => {
                const key = el.dataset.i18nPlaceholder;
                if (window.WheelFitI18n[key]) {
                    el.placeholder = window.WheelFitI18n[key];
                }
            });

            console.log('[Translation Manager] Ultimate fallback applied');
            return true;
        } catch (error) {
            console.error('[Translation Manager] Ultimate fallback failed:', error);
            return false;
        }
    };

    // Create global instance
    window.TranslationPersistenceManager = TranslationPersistenceManager;

    // Global debug function
    window.debugTranslations = function() {
        if (window.translationManager && typeof window.translationManager.debug === 'function') {
            window.translationManager.debug();
        } else {
            console.log('[Translation Manager] Manager not available for debugging');
            console.log('Global translations:', window.WheelFitI18n);
        }
    };

    // Auto-initialize with error handling
    function initializeTranslationManager() {
        try {
            if (!window.translationManager) {
                window.translationManager = new TranslationPersistenceManager();
                console.log('[Translation Manager] Successfully initialized');
            }
        } catch (error) {
            console.error('[Translation Manager] Initialization failed:', error);

            // Create minimal fallback instance
            window.translationManager = {
                updateTranslations: function(translations) {
                    if (translations) {
                        window.WheelFitI18n = translations;
                        if (typeof window.applyStaticTranslations === 'function') {
                            window.applyStaticTranslations();
                        }
                    }
                },
                applyTranslations: function(container) {
                    return window.applyStaticTranslations(container);
                },
                healthCheck: function() {
                    return { fallbackMode: true, error: error.message };
                }
            };
            console.log('[Translation Manager] Fallback instance created');
        }
    }

    // Initialize with multiple fallback attempts
    function attemptInitialization(attempt = 0) {
        const maxAttempts = 3;

        try {
            initializeTranslationManager();
        } catch (error) {
            console.error(`[Translation Manager] Initialization attempt ${attempt + 1} failed:`, error);

            if (attempt < maxAttempts - 1) {
                setTimeout(() => attemptInitialization(attempt + 1), 1000);
            } else {
                console.error('[Translation Manager] All initialization attempts failed');
            }
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => attemptInitialization());
    } else {
        attemptInitialization();
    }

    console.log('[Translation Manager] Script loaded');
})();
