/**
 * Wizard Hierarchy Verification Test
 * 
 * This script verifies that the wizard vertical hierarchy fix is working correctly
 * by checking CSS rules and DOM structure in the admin preview.
 */

console.log('🔧 Starting Wizard Hierarchy Verification Test...');

function testWizardHierarchy() {
    console.log('\n📐 Testing Wizard Vertical Hierarchy...');
    
    // 1. Check if widget preview container exists
    const widgetPreview = document.getElementById('widget-preview');
    if (!widgetPreview) {
        console.error('❌ Widget preview container not found');
        return false;
    }
    console.log('✅ Widget preview container found');
    
    // 2. Check if wizard container exists
    const wizardContainer = document.getElementById('wheel-fit-wizard');
    if (!wizardContainer) {
        console.error('❌ Wizard container not found');
        return false;
    }
    console.log('✅ Wizard container found');
    
    // 3. Find wizard steps
    const wizardSteps = wizardContainer.querySelectorAll('.wizard-step');
    if (wizardSteps.length === 0) {
        console.error('❌ No wizard steps found');
        return false;
    }
    console.log(`✅ Found ${wizardSteps.length} wizard steps`);
    
    // 4. Check current visible step
    const visibleStep = Array.from(wizardSteps).find(step => 
        !step.classList.contains('hidden') && 
        getComputedStyle(step).display !== 'none'
    );
    
    if (!visibleStep) {
        console.error('❌ No visible wizard step found');
        return false;
    }
    console.log('✅ Found visible wizard step:', visibleStep.id);
    
    // 5. Test CSS properties of the visible step
    const stepStyles = getComputedStyle(visibleStep);
    
    console.log('\n🎨 Testing CSS Properties:');
    
    // Check display property
    const display = stepStyles.display;
    if (display === 'block') {
        console.log('✅ Display: block (correct)');
    } else {
        console.error(`❌ Display: ${display} (should be block)`);
        return false;
    }
    
    // Check flex property
    const flex = stepStyles.flex;
    if (flex === 'none' || flex === '0 0 auto') {
        console.log('✅ Flex: none/auto (correct)');
    } else {
        console.warn(`⚠️ Flex: ${flex} (might be problematic)`);
    }
    
    // Check align-items
    const alignItems = stepStyles.alignItems;
    if (alignItems === 'normal' || alignItems === 'stretch' || alignItems === 'initial') {
        console.log('✅ Align-items: normal/stretch/initial (correct)');
    } else {
        console.warn(`⚠️ Align-items: ${alignItems} (might cause horizontal layout)`);
    }
    
    // 6. Test vertical hierarchy of elements
    console.log('\n📋 Testing Vertical Hierarchy:');
    
    const hierarchyElements = [
        { selector: 'h2', name: 'Step Title', expectedOrder: 1 },
        { selector: 'nav', name: 'Breadcrumbs', expectedOrder: 2 },
        { selector: 'p[id*="-count"]', name: 'Count Label', expectedOrder: 3 },
        { selector: '.wizard-search-wrapper', name: 'Search Input', expectedOrder: 4 },
        { selector: '.grid, [class*="grid-cols"]', name: 'Main Content', expectedOrder: 5 }
    ];
    
    let hierarchyCorrect = true;
    let lastTop = -1;
    
    hierarchyElements.forEach((element, index) => {
        const el = visibleStep.querySelector(element.selector);
        if (el) {
            const rect = el.getBoundingClientRect();
            const styles = getComputedStyle(el);
            
            // Check if element is displayed as block
            if (styles.display === 'block' || styles.display === 'grid') {
                console.log(`✅ ${element.name}: display ${styles.display} (correct)`);
            } else {
                console.warn(`⚠️ ${element.name}: display ${styles.display} (might be problematic)`);
            }
            
            // Check vertical positioning
            if (rect.top > lastTop) {
                console.log(`✅ ${element.name}: positioned correctly (top: ${Math.round(rect.top)}px)`);
                lastTop = rect.top;
            } else {
                console.error(`❌ ${element.name}: wrong vertical order (top: ${Math.round(rect.top)}px)`);
                hierarchyCorrect = false;
            }
            
            // Check margins
            const marginBottom = parseInt(styles.marginBottom);
            if (marginBottom > 0) {
                console.log(`✅ ${element.name}: has bottom margin (${marginBottom}px)`);
            } else {
                console.warn(`⚠️ ${element.name}: no bottom margin`);
            }
        } else {
            console.log(`ℹ️ ${element.name}: not found in current step (normal)`);
        }
    });
    
    // 7. Check for horizontal layout indicators
    console.log('\n🚫 Checking for Horizontal Layout Issues:');
    
    const stepChildren = Array.from(visibleStep.children);
    let horizontalIssues = false;
    
    if (stepChildren.length > 1) {
        // Check if multiple children are on the same horizontal line
        const firstChildRect = stepChildren[0].getBoundingClientRect();
        
        for (let i = 1; i < stepChildren.length; i++) {
            const childRect = stepChildren[i].getBoundingClientRect();
            const verticalGap = childRect.top - firstChildRect.bottom;
            
            if (Math.abs(childRect.top - firstChildRect.top) < 10 && childRect.left > firstChildRect.right) {
                console.error(`❌ Horizontal layout detected: ${stepChildren[0].tagName} and ${stepChildren[i].tagName} are side by side`);
                horizontalIssues = true;
            }
        }
    }
    
    if (!horizontalIssues) {
        console.log('✅ No horizontal layout issues detected');
    }
    
    // 8. Final result
    console.log('\n📊 Test Results:');
    
    const success = hierarchyCorrect && !horizontalIssues && display === 'block';
    
    if (success) {
        console.log('🎉 WIZARD HIERARCHY TEST PASSED!');
        console.log('✅ Vertical hierarchy is working correctly');
        console.log('✅ No horizontal layout issues detected');
        console.log('✅ CSS properties are correct');
    } else {
        console.error('❌ WIZARD HIERARCHY TEST FAILED!');
        console.error('❌ Issues detected with vertical hierarchy');
    }
    
    return success;
}

function testCSSRules() {
    console.log('\n🎨 Testing CSS Rules Specificity...');
    
    // Check if our high-specificity rules are present
    const testSelectors = [
        '#widget-preview #wheel-fit-wizard .wizard-step',
        '#widget-preview .wizard-step',
        '#widget-preview #wheel-fit-wizard .wizard-step h2',
        '#widget-preview #wheel-fit-wizard .wizard-step nav',
        '#widget-preview #wheel-fit-wizard .wizard-step p[id*="-count"]'
    ];
    
    let rulesFound = 0;
    
    try {
        for (const sheet of document.styleSheets) {
            try {
                const rules = sheet.cssRules || sheet.rules;
                for (const rule of rules) {
                    if (rule.selectorText) {
                        for (const selector of testSelectors) {
                            if (rule.selectorText.includes(selector.replace(/\s+/g, ' '))) {
                                console.log(`✅ Found rule: ${rule.selectorText}`);
                                rulesFound++;
                                break;
                            }
                        }
                    }
                }
            } catch (e) {
                // Cross-origin stylesheet, skip
            }
        }
    } catch (e) {
        console.warn('⚠️ Could not check CSS rules:', e.message);
    }
    
    if (rulesFound > 0) {
        console.log(`✅ Found ${rulesFound} relevant CSS rules`);
    } else {
        console.warn('⚠️ Could not verify CSS rules (might be in external stylesheet)');
    }
}

function runFullTest() {
    console.log('🚀 Running Full Wizard Hierarchy Test Suite...');
    console.log('=' .repeat(60));
    
    testCSSRules();
    const hierarchyResult = testWizardHierarchy();
    
    console.log('\n' + '=' .repeat(60));
    
    if (hierarchyResult) {
        console.log('🎉 ALL TESTS PASSED! Wizard hierarchy is working correctly.');
        console.log('✅ The horizontal layout issue has been resolved.');
        console.log('✅ Elements are properly arranged vertically.');
    } else {
        console.error('❌ TESTS FAILED! Wizard hierarchy needs attention.');
        console.error('❌ Please check the CSS rules and DOM structure.');
    }
    
    return hierarchyResult;
}

// Auto-run test when script is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runFullTest);
} else {
    runFullTest();
}

// Export for manual testing
window.testWizardHierarchy = runFullTest;
