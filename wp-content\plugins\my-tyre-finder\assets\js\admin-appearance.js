jQuery(function ($) {
    const $flow   = $('#search_flow');
    const $layout = $('#form_layout');
    const $wizard = $layout.find('option[value="wizard"]');

    function toggleWizard() {
        const isYearFlow = $flow.val() === 'by_year';
        const isGenerationFlow = $flow.val() === 'by_generation';
        const shouldDisableWizard = isYearFlow || isGenerationFlow;
        $wizard.prop('disabled', shouldDisableWizard);
        if (shouldDisableWizard && $layout.val() === 'wizard') {
            $layout.val('popup-horizontal'); // fallback to Inline (1×4), чтобы не сохранялся wizard
        }
    }

    $flow.on('change', toggleWizard);
    toggleWizard();      // инициализация при загрузке страницы

    function getPreviewLocale() {
        if (window.WheelFitData && window.WheelFitData.locale) return window.WheelFitData.locale;
        var sel = document.getElementById('locale-selector');
        if (sel) return sel.value;
        return 'en';
    }

    function updatePreview(scroll=false) {
        const previewContainer = document.getElementById('widget-preview');
        if (!previewContainer) {
            console.warn('[Admin Preview] Preview container not found');
            return;
        }

        // Show loading state
        previewContainer.innerHTML = '<p>Loading preview…</p>';

        // Update color in-place for instant feedback
        if (colorInput && colorInput.value) {
            document.documentElement.style.setProperty('--wf-primary-color', colorInput.value);
        }

        const formData = new URLSearchParams({
            action: 'wheel_size_render_preview',
            layout: layoutSelect.value,
            primary_color: colorInput.value,
            show_by_vehicle: '1',  // Force By Vehicle mode
            show_by_size: '',      // Force disable By Size mode
            auto_search: autoSearchCheckbox.checked ? '1' : '',
            search_flow: searchFlowSelect ? searchFlowSelect.value : '',
            preview_locale: getPreviewLocale()
        });

        fetch(ajaxurl, {
            method: 'POST',
            headers: {'Content-Type':'application/x-www-form-urlencoded'},
            body: formData
        })
        .then(res => {
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            return res.json();
        })
        .then(res => {
            if (res.success) {
                console.log('[Admin Preview] Received successful response:', res);

                // ИСПОЛЬЗУЕМ ТОЧНО ТОТ ЖЕ КОД, ЧТО И ПРИ ПЕРВОЙ ЗАГРУЗКЕ!
                // Обновляем переводы перед рендерингом (как в старом коде)
                const newTranslations = res.data.i18n || {};
                try {
                    window.WheelFitI18n = newTranslations;
                    console.log('[Admin Preview] Updated global translations:', Object.keys(window.WheelFitI18n).length, 'keys');
                    console.log('[Admin Preview] Sample translations:', {
                        select_make_placeholder: window.WheelFitI18n.select_make_placeholder,
                        select_model_placeholder: window.WheelFitI18n.select_model_placeholder,
                        select_year_placeholder: window.WheelFitI18n.select_year_placeholder
                    });
                } catch(e) {
                    console.error('[Admin Preview] Error updating translations:', e);
                }

                // Вставляем новый HTML (как в старом коде)
                previewContainer.innerHTML = res.data.html;
                console.log('[Admin Preview] HTML updated');

                // ПРОВЕРЯЕМ ПЕРЕВОДЫ ПЕРЕД ПРИМЕНЕНИЕМ
                console.log('[Admin Preview] Checking translations before applying:', {
                    available: !!window.WheelFitI18n,
                    count: window.WheelFitI18n ? Object.keys(window.WheelFitI18n).length : 0,
                    sampleKey: window.WheelFitI18n ? window.WheelFitI18n.select_make_placeholder : 'N/A'
                });

                // Применяем переводы к статическим элементам (как в старом коде и при первой загрузке)
                if (typeof applyStaticTranslations === 'function') {
                    console.log('[Admin Preview] Applying translations...');
                    applyStaticTranslations(previewContainer);
                    console.log('[Admin Preview] Translations applied');
                } else {
                    console.warn('[Admin Preview] applyStaticTranslations function not found');
                }

                // Update WheelFitData with current form values before initializing widgets
                if (window.WheelFitData) {
                    window.WheelFitData.autoSearch = autoSearchCheckbox.checked;
                    window.WheelFitData.formLayout = layoutSelect.value;
                    console.log('[Admin Preview] Updated WheelFitData:', {
                        autoSearch: window.WheelFitData.autoSearch,
                        formLayout: window.WheelFitData.formLayout
                    });
                }

                // Инициализируем виджеты (как в старом коде и при первой загрузке)
                if (typeof WheelFitWidget !== 'undefined') {
                    try {
                        window.wheelFitWidget = new WheelFitWidget();
                        console.log('[Admin Preview] WheelFitWidget initialized');

                        // Update submit button visibility and form layout based on auto-search setting
                        const submitButtons = document.querySelectorAll('#widget-preview button[type="submit"]');
                        const forms = document.querySelectorAll('#widget-preview #tab-by-car');

                        if (autoSearchCheckbox.checked) {
                            submitButtons.forEach(btn => btn.classList.add('hidden'));
                            forms.forEach(form => form.classList.add('auto-search-mode'));
                            console.log('[Admin Preview] Submit buttons hidden, auto-search-mode class added');
                        } else {
                            submitButtons.forEach(btn => btn.classList.remove('hidden'));
                            forms.forEach(form => form.classList.remove('auto-search-mode'));
                            console.log('[Admin Preview] Submit buttons shown, auto-search-mode class removed');
                        }
                    } catch(e) {
                        console.error('[Admin Preview] Error initializing WheelFitWidget:', e);
                    }
                }
                if (typeof WheelWizard !== 'undefined') {
                    if (document.getElementById('wheel-fit-wizard')) {
                        try {
                            window.wheelFitWizard = new WheelWizard();
                            console.log('[Admin Preview] WheelWizard initialized');
                        } catch(e) {
                            console.error('[Admin Preview] Error initializing WheelWizard:', e);
                        }
                    }
                }



                // Initialize garage if enabled and elements are present
                if (window.WheelFitData && window.WheelFitData.garageEnabled) {
                    console.log('[Admin Preview] Garage enabled, checking for garage elements...');
                    const hasGarageElements = document.getElementById('garage-drawer') ||
                                             document.getElementById('garage-overlay') ||
                                             document.querySelectorAll('[data-garage-trigger]').length > 0;

                    if (hasGarageElements) {
                        console.log('[Admin Preview] Garage elements found, reinitializing...');
                        try {
                            // Use reinit function if available, otherwise force init
                            if (typeof window.reinitGarage === 'function') {
                                window.reinitGarage();
                            } else if (typeof initGarageFeature === 'function') {
                                // Reset state manually and force reinit
                                window.__garageInitiated = false;
                                initGarageFeature(true);
                            }
                            console.log('[Admin Preview] Garage reinitialized successfully');
                        } catch(e) {
                            console.error('[Admin Preview] Error reinitializing garage:', e);
                        }
                    }
                }

                // Scroll to preview if requested
                if (scroll) {
                    previewContainer.scrollIntoView({ behavior: 'smooth' });
                }

                console.log('[Admin Preview] Preview update completed successfully');
            } else {
                console.error('[Admin Preview] Server returned error:', res);
                previewContainer.innerHTML = '<p style="color: red;">Error loading preview: ' + (res.data || 'Unknown error') + '</p>';
            }
        })
        .catch(err => {
            console.error('[Admin Preview] Fetch error:', err);
            previewContainer.innerHTML = '<p style="color: red;">Error loading preview. Check console for details.</p>';
        });
    }

    // Direct translation application function for admin preview
    function applyDirectTranslations(container, translations) {
        if (!container || !translations || typeof translations !== 'object') {
            console.warn('[Admin Preview] Invalid parameters for direct translation');
            return false;
        }

        let applied = 0;

        try {
            // Apply text content translations
            const textElements = container.querySelectorAll('[data-i18n]');
            console.log(`[Admin Preview] Found ${textElements.length} text elements to translate`);

            textElements.forEach(el => {
                const key = el.dataset.i18n;
                if (key && translations[key]) {
                    const oldText = el.textContent.trim();
                    el.textContent = translations[key];
                    console.log(`[Admin Preview] Translated "${key}": "${oldText}" → "${translations[key]}"`);
                    applied++;
                }
            });

            // Apply placeholder translations for input elements
            const placeholderElements = container.querySelectorAll('[data-i18n-placeholder]');
            console.log(`[Admin Preview] Found ${placeholderElements.length} placeholder elements to translate`);

            placeholderElements.forEach(el => {
                const key = el.dataset.i18nPlaceholder;
                if (key && translations[key]) {
                    const oldPlaceholder = el.placeholder || '';
                    el.placeholder = translations[key];
                    console.log(`[Admin Preview] Translated placeholder "${key}": "${oldPlaceholder}" → "${translations[key]}"`);
                    applied++;
                }
            });

            // Special handling for select option placeholders
            const selectElements = container.querySelectorAll('select');
            console.log(`[Admin Preview] Found ${selectElements.length} select elements to check`);

            selectElements.forEach(select => {
                const placeholderOption = select.querySelector('option[value=""]');
                if (placeholderOption) {
                    // Check if option has translation attribute
                    const key = placeholderOption.dataset.i18n;
                    if (key && translations[key]) {
                        const oldText = placeholderOption.textContent.trim();
                        placeholderOption.textContent = translations[key];
                        console.log(`[Admin Preview] Translated select option "${key}": "${oldText}" → "${translations[key]}"`);
                        applied++;
                    } else {
                        // Try to determine translation key from select ID
                        const selectIdToKeyMap = {
                            'wf-make': 'select_make_placeholder',
                            'wf-model': 'select_model_placeholder',
                            'wf-year': 'select_year_placeholder',
                            'wf-modification': 'select_mods_placeholder',
                            'wf-generation': 'select_gen_placeholder'
                        };

                        const translationKey = selectIdToKeyMap[select.id];
                        if (translationKey && translations[translationKey]) {
                            const oldText = placeholderOption.textContent.trim();
                            placeholderOption.textContent = translations[translationKey];
                            placeholderOption.setAttribute('data-i18n', translationKey);
                            console.log(`[Admin Preview] Auto-translated select "${select.id}": "${oldText}" → "${translations[translationKey]}"`);
                            applied++;
                        }
                    }
                }
            });

            console.log(`[Admin Preview] Applied ${applied} direct translations`);
            return applied > 0;

        } catch (error) {
            console.error('[Admin Preview] Error in direct translation application:', error);
            return false;
        }
    }

});