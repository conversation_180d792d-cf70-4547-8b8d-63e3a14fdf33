# Brand Cards Hover State Fix Summary

## Проблема
Карточки брендов в виджете поиска шин имели неправильные hover состояния:
- **Базовый фон**: чёрный (`bg-wsf-bg` в тёмной теме)
- **Hover фон**: белый (`var(--wsf-surface-hover)` был белым)
- **Результат**: чёрная карточка становилась белой при наведении

## Решение

### 1. Исправлены CSS правила в `templates/finder-wizard.twig`

**До:**
```css
#wheel-fit-wizard #wizard-makes-grid > button {
  background-color: transparent !important;
}
#wheel-fit-wizard #wizard-makes-grid > button:hover {
  background-color: var(--wsf-surface-hover) !important;
}
```

**После:**
```css
#wheel-fit-wizard #wizard-makes-grid > button {
  background-color: transparent !important;
}
#wheel-fit-wizard #wizard-makes-grid > button:hover {
  background-color: var(--wsf-surface) !important;
}
#wheel-fit-wizard #wizard-makes-grid > button:focus-visible {
  outline: 2px solid var(--wsf-primary);
  outline-offset: 2px;
}
```

### 2. Исправлены классы в JavaScript `assets/js/wizard.js`

**Изменения в `createGridItem()`:**
- `bg-wsf-bg` → `bg-transparent`
- `wsf-text-primary` → `text-wsf-primary`

**Исправлены все классы для единообразия:**
- Все `wsf-text-*` заменены на `text-wsf-*`
- Все `wsf-bg-*` заменены на `bg-wsf-*` (кроме специальных случаев)

### 3. Добавлена поддержка доступности
- Добавлено `focus-visible` состояние для навигации с клавиатуры
- Контур фокуса использует основной цвет темы

## Результат

### Тёмная тема:
- **Базовый фон**: прозрачный
- **Hover фон**: `var(--wsf-surface)` = `#2d2d2d` (тёмно-серый)
- **Активный фон**: `var(--wsf-primary)` (синий)

### Светлая тема:
- **Базовый фон**: прозрачный  
- **Hover фон**: `var(--wsf-surface)` = `#f9fafb` (светло-серый)
- **Активный фон**: `var(--wsf-primary)` (синий)

## Файлы изменены
1. `templates/finder-wizard.twig` - CSS правила hover состояний
2. `assets/js/wizard.js` - классы карточек и единообразие

## Тестирование
Создан тестовый скрипт: `tests/test-brand-cards-hover-fix.js`

### Автоматические тесты:
- Проверка корректности CSS классов
- Проверка CSS custom properties
- Проверка CSS правил hover/focus

### Ручные тесты:
```javascript
// В консоли браузера:
testBrandHover()    // Тест hover состояний
testThemeSwitch()   // Переключение тем
```

## Совместимость
- ✅ Работает с системой тем WordPress
- ✅ Поддерживает светлую и тёмную темы
- ✅ Совместимо с Tailwind CSS
- ✅ Доступность (a11y) улучшена

## Рекомендации для дальнейшего развития

1. **Единообразие классов**: Всегда использовать формат `text-wsf-*`, `bg-wsf-*`, `border-wsf-*`

2. **Тематические переменные**: Использовать семантические переменные:
   - `--wsf-surface` для лёгких hover состояний
   - `--wsf-surface-hover` для более выраженных hover состояний
   - `--wsf-primary` для активных состояний

3. **Доступность**: Всегда добавлять `focus-visible` состояния для интерактивных элементов

4. **Тестирование**: Проверять hover состояния в обеих темах при внесении изменений
