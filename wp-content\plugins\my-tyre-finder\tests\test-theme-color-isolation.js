/**
 * Test script to verify that certain elements are NOT affected by theme colors
 * These elements should remain with fixed colors regardless of theme changes
 */

console.log('🧪 Testing theme color isolation...');

function testSearchResultsHeaders() {
    console.log('\n📋 Testing Search Results headers...');
    
    const headers = [
        { selector: 'h2[data-i18n="section_results"]', name: 'Search Results' },
        { selector: 'h3[data-i18n="section_factory"]', name: 'Factory Sizes' },
        { selector: 'h3[data-i18n="section_optional"]', name: 'Optional Sizes' },
        { selector: 'h3[data-i18n="text_no_results_header"]', name: 'No Results' }
    ];
    
    let allPassed = true;
    
    headers.forEach(({ selector, name }) => {
        const element = document.querySelector(selector);
        if (!element) {
            console.log(`ℹ️ ${name} header not found (may not be visible)`);
            return;
        }
        
        const style = window.getComputedStyle(element);
        const color = style.color;
        
        // Check if color is NOT using CSS variables (should be fixed color)
        const isFixedColor = !color.includes('var(--wsf-') && !element.classList.contains('text-wsf-text');
        const hasGrayColor = element.classList.contains('text-gray-900');
        
        console.log(`   ${name}: ${color} - ${hasGrayColor ? '✅' : '❌'} Fixed color`);
        
        if (!hasGrayColor) {
            allPassed = false;
        }
    });
    
    return allPassed;
}

function testOptionalBadges() {
    console.log('\n🏷️ Testing OPTIONAL badges...');
    
    // Look for OPTIONAL badges in size cards
    const badges = document.querySelectorAll('.size-card span[class*="bg-blue"]');
    let allPassed = true;
    
    if (badges.length === 0) {
        console.log('ℹ️ No OPTIONAL badges found (may not be visible)');
        return true;
    }
    
    badges.forEach((badge, index) => {
        const style = window.getComputedStyle(badge);
        const backgroundColor = style.backgroundColor;
        const color = style.color;
        
        // Check if badge uses fixed colors instead of theme variables
        const hasFixedBg = badge.classList.contains('bg-blue-100');
        const hasFixedText = badge.classList.contains('text-blue-700');
        const notUsingThemeVars = !badge.classList.contains('wsf-text-accent');
        
        console.log(`   Badge ${index + 1}: bg=${backgroundColor}, color=${color}`);
        console.log(`   Fixed colors: ${hasFixedBg && hasFixedText && notUsingThemeVars ? '✅' : '❌'}`);
        
        if (!hasFixedBg || !hasFixedText || !notUsingThemeVars) {
            allPassed = false;
        }
    });
    
    return allPassed;
}

function testGarageButtons() {
    console.log('\n🚗 Testing Garage add buttons...');
    
    const garageButtons = document.querySelectorAll('.save-to-garage i[data-lucide="plus-square"]');
    let allPassed = true;
    
    if (garageButtons.length === 0) {
        console.log('ℹ️ No Garage add buttons found (may not be visible)');
        return true;
    }
    
    garageButtons.forEach((icon, index) => {
        const style = window.getComputedStyle(icon);
        const color = style.color;
        
        // Check if icon uses fixed color instead of theme variable
        const hasFixedColor = icon.classList.contains('text-blue-600');
        const notUsingThemeVar = !icon.classList.contains('wsf-text-accent');
        
        console.log(`   Garage button ${index + 1}: ${color} - ${hasFixedColor && notUsingThemeVar ? '✅' : '❌'} Fixed color`);
        
        if (!hasFixedColor || !notUsingThemeVar) {
            allPassed = false;
        }
    });
    
    return allPassed;
}

function testTireSearchElements() {
    console.log('\n🔍 Testing Tire Search elements...');
    
    // Test "Show all brands" buttons
    const showAllButtons = document.querySelectorAll('button[data-toggle-all-brands]');
    let allPassed = true;
    
    showAllButtons.forEach((button, index) => {
        const hasFixedColor = button.classList.contains('text-blue-600');
        const notUsingThemeVar = !button.classList.contains('wsf-text-accent');
        
        console.log(`   Show all brands ${index + 1}: ${hasFixedColor && notUsingThemeVar ? '✅' : '❌'} Fixed color`);
        
        if (!hasFixedColor || !notUsingThemeVar) {
            allPassed = false;
        }
    });
    
    // Test model expansion buttons
    const modelButtons = document.querySelectorAll('button[data-toggle-models-make]');
    
    modelButtons.forEach((button, index) => {
        const hasFixedColor = button.classList.contains('text-blue-600');
        const notUsingThemeVar = !button.classList.contains('wsf-text-accent');
        
        console.log(`   Model expansion ${index + 1}: ${hasFixedColor && notUsingThemeVar ? '✅' : '❌'} Fixed color`);
        
        if (!hasFixedColor || !notUsingThemeVar) {
            allPassed = false;
        }
    });
    
    return allPassed;
}

function testHistoryElements() {
    console.log('\n📚 Testing History elements...');
    
    const historyItems = document.querySelectorAll('.history-load');
    let allPassed = true;
    
    if (historyItems.length === 0) {
        console.log('ℹ️ No history items found');
        return true;
    }
    
    historyItems.forEach((item, index) => {
        const hasFixedColor = item.classList.contains('text-blue-600');
        const notUsingThemeVar = !item.classList.contains('text-wsf-primary');
        
        console.log(`   History item ${index + 1}: ${hasFixedColor && notUsingThemeVar ? '✅' : '❌'} Fixed color`);
        
        if (!hasFixedColor || !notUsingThemeVar) {
            allPassed = false;
        }
    });
    
    return allPassed;
}

function runThemeColorIsolationTests() {
    console.log('🚀 Starting theme color isolation tests...\n');
    
    const results = {
        searchHeaders: testSearchResultsHeaders(),
        optionalBadges: testOptionalBadges(),
        garageButtons: testGarageButtons(),
        tireSearchElements: testTireSearchElements(),
        historyElements: testHistoryElements()
    };
    
    const passed = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Elements are properly isolated from theme colors.');
    } else {
        console.log('⚠️ Some tests failed. These elements are still affected by theme colors.');
    }
    
    console.log('\n📋 Fixed Elements Summary:');
    console.log('• Search Results headers: text-gray-900 (fixed)');
    console.log('• Factory/Optional Sizes headers: text-gray-900 (fixed)');
    console.log('• OPTIONAL badges: bg-blue-100 text-blue-700 (fixed)');
    console.log('• Garage add buttons: text-blue-600 (fixed)');
    console.log('• Tire search buttons: text-blue-600 (fixed)');
    console.log('• History load buttons: text-blue-600 (fixed)');
    
    return results;
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runThemeColorIsolationTests);
} else {
    runThemeColorIsolationTests();
}

// Export for manual testing
window.testThemeColorIsolation = runThemeColorIsolationTests;
